# 🚀 快速开始指南

> **5分钟体验高性能微服务网关**

## 📋 环境要求

### 基础环境
- **Go 1.21+**：支持泛型和最新特性
- **Redis**：配置存储（可选）
- **Docker**：容器化部署（可选）

### 开发工具推荐
- **IDE**：VS Code + Go 插件 / GoLand
- **监控**：Prometheus + Grafana
- **压测**：wrk / hey / ab

## ⚡ 快速体验

### 1. 克隆项目
```bash
git clone https://github.com/chungzy/servicemesh-go.git
cd servicemesh-go
```

### 2. 安装依赖
```bash
go mod tidy
```

### 3. 启动网关
```bash
# 使用默认配置启动
go run cmd/servicemesh/main.go -config configs/config.yaml

# 或者使用 make 命令
make run
```

### 4. 验证运行
```bash
# 健康检查
curl http://localhost:8080/health

# 查看监控指标
curl http://localhost:8080/metrics

# 测试代理功能（需要先启动后端服务）
curl -H "X-Service-Name: demo-service" http://localhost:8080/api/test
```

## 🎯 核心功能演示

### 负载均衡演示

#### 1. 启动多个后端服务
```bash
# 终端1：启动后端服务1
python3 -m http.server 8001

# 终端2：启动后端服务2  
python3 -m http.server 8002

# 终端3：启动后端服务3
python3 -m http.server 8003
```

#### 2. 配置服务
```bash
# 添加服务配置到 Redis（如果启用）
redis-cli set "servicemesh:config:service:demo-service" '{
  "name": "demo-service",
  "strategy": "round_robin",
  "endpoints": [
    {"id": "demo-1", "host": "127.0.0.1", "port": 8001, "weight": 100},
    {"id": "demo-2", "host": "127.0.0.1", "port": 8002, "weight": 100},
    {"id": "demo-3", "host": "127.0.0.1", "port": 8003, "weight": 50}
  ]
}'
```

#### 3. 测试负载均衡
```bash
# 发送多个请求，观察负载均衡效果
for i in {1..10}; do
  curl -H "X-Service-Name: demo-service" http://localhost:8080/
  echo ""
done
```

### 熔断器演示

#### 1. 模拟服务故障
```bash
# 停止一个后端服务
# Ctrl+C 停止 8001 端口的服务
```

#### 2. 观察熔断效果
```bash
# 持续发送请求
while true; do
  curl -H "X-Service-Name: demo-service" http://localhost:8080/
  sleep 1
done
```

#### 3. 查看熔断指标
```bash
# 查看熔断器状态
curl http://localhost:8080/metrics | grep circuit_breaker
```

### 性能测试

#### 1. 基础性能测试
```bash
# 安装 wrk
brew install wrk  # macOS
# sudo apt-get install wrk  # Ubuntu

# 性能测试
wrk -t12 -c400 -d30s -H "X-Service-Name: demo-service" http://localhost:8080/
```

#### 2. 查看性能指标
```bash
# 实时监控
curl http://localhost:8080/metrics | grep -E "(requests_total|request_duration)"
```

## 🔧 配置说明

### 基础配置文件
```yaml
# configs/config.yaml
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"

loadbalance:
  strategy: "round_robin"  # round_robin, random, consistent_hash, least_conn
  health_check_interval: "30s"
  max_retries: 3

circuit:
  failure_threshold: 5
  timeout: "30s"
  max_requests: 100

metrics:
  enable: true
  path: "/metrics"
  port: 9090
```

### 服务配置示例
```json
{
  "name": "user-service",
  "strategy": "round_robin",
  "timeout": "5s",
  "max_retries": 3,
  "endpoints": [
    {
      "id": "user-1",
      "host": "*************",
      "port": 8080,
      "weight": 100,
      "healthy": true
    },
    {
      "id": "user-2", 
      "host": "*************",
      "port": 8080,
      "weight": 100,
      "healthy": true
    }
  ]
}
```

## 📊 监控面板

### Prometheus 配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'servicemesh'
    static_configs:
      - targets: ['localhost:9090']
```

### Grafana 仪表盘
导入预配置的仪表盘：`deployments/grafana/dashboards/servicemesh.json`

关键指标：
- **QPS**：每秒请求数
- **延迟**：P50/P95/P99 延迟
- **错误率**：4xx/5xx 错误比例
- **熔断状态**：熔断器开关状态

## 🐳 Docker 部署

### 1. 构建镜像
```bash
# 构建网关镜像
make docker

# 或者手动构建
docker build -t servicemesh:latest .
```

### 2. 使用 Docker Compose
```bash
# 启动完整环境（网关 + Redis + Prometheus + Grafana）
cd deployments
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f servicemesh-1
```

### 3. 访问服务
- **网关**：http://localhost:8080
- **Prometheus**：http://localhost:9092
- **Grafana**：http://localhost:3000 (admin/admin)

## 🔥 热重启演示

### 1. 获取进程 PID
```bash
ps aux | grep servicemesh
```

### 2. 触发热重启
```bash
# 发送 SIGUSR1 信号
kill -USR1 <pid>

# 观察日志输出
tail -f /var/log/servicemesh.log
```

### 3. 验证零停机
```bash
# 在热重启过程中持续发送请求
while true; do
  curl -w "%{http_code}\n" http://localhost:8080/health
  sleep 0.1
done
```

## 🎯 学习建议

### 新手入门路径
1. **理解架构**：阅读 `docs/LEARNING_PATH.md`
2. **运行示例**：按照本指南操作
3. **阅读代码**：从 `cmd/servicemesh/main.go` 开始
4. **修改配置**：尝试不同的负载均衡策略
5. **性能测试**：使用 wrk 进行压测

### 进阶学习
1. **源码分析**：深入理解核心组件实现
2. **功能扩展**：添加新的中间件或插件
3. **性能优化**：使用 pprof 进行性能分析
4. **生产部署**：在 Kubernetes 中部署

### 面试准备
1. **项目介绍**：参考 `docs/INTERVIEW_GUIDE.md`
2. **技术深度**：理解每个组件的设计原理
3. **性能数据**：记录压测结果和优化效果
4. **问题解决**：总结开发过程中遇到的挑战

## 🆘 常见问题

### Q1: 启动失败，端口被占用
```bash
# 查看端口占用
lsof -i :8080

# 杀死占用进程
kill -9 <pid>

# 或者修改配置文件中的端口
```

### Q2: Redis 连接失败
```bash
# 检查 Redis 是否启动
redis-cli ping

# 启动 Redis
redis-server

# 或者禁用 Redis（修改配置文件）
```

### Q3: 性能不达预期
```bash
# 检查系统资源
top
iostat

# 调整配置参数
# - 增加并发数
# - 调整超时时间
# - 优化连接池大小
```

### Q4: 监控数据异常
```bash
# 检查 Prometheus 配置
curl http://localhost:9090/targets

# 重启监控服务
docker-compose restart prometheus grafana
```

## 📚 下一步

- 📖 **深入学习**：阅读完整的学习路径
- 🎓 **参加课程**：报名实战课程
- 💬 **加入社区**：微信群技术交流
- 🚀 **贡献代码**：提交 PR 和 Issue

---

**开始你的微服务网关学习之旅吧！** 🎉
