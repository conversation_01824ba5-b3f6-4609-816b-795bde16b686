# 热重启和配置管理

本文档介绍 ServiceMesh Go 的热重启功能和通用配置管理系统。

## 热重启功能

### 概述

热重启（Hot Restart）允许在不中断服务的情况下重新启动 ServiceMesh 进程，实现零停机时间的更新和配置重载。

### 工作原理

1. **监听器继承**：新进程继承旧进程的网络监听器
2. **优雅切换**：新进程启动后，旧进程优雅关闭
3. **信号处理**：通过 Unix 信号触发热重启

### 使用方法

#### 1. 启动服务

```bash
./servicemesh -config configs/config.yaml
```

#### 2. 触发热重启

```bash
# 发送 SIGUSR1 信号触发热重启
kill -USR1 <pid>

# 或者使用 systemctl（如果使用 systemd）
systemctl reload servicemesh
```

#### 3. 优雅停止

```bash
# 发送 SIGTERM 信号优雅停止
kill -TERM <pid>

# 或者
systemctl stop servicemesh
```

### 代码示例

```go
package main

import (
    "context"
    "github.com/chungzy/servicemesh-go/pkg/hotrestart"
    "github.com/chungzy/servicemesh-go/pkg/proxy"
)

func main() {
    // 创建热重启管理器
    hotRestartMgr := hotrestart.NewManager(logger, "/tmp/servicemesh.pid", 30*time.Second)
    
    // 创建代理服务器
    proxyServer := proxy.NewServer(config, logger, loadBalancer, metrics)
    
    // 设置热重启管理器
    proxyServer.SetHotRestartManager(hotRestartMgr)
    
    // 注册服务器
    hotRestartMgr.RegisterServer(proxyServer)
    
    // 启动热重启管理器
    hotRestartMgr.Start(ctx)
    
    // 启动代理服务器
    proxyServer.Start(ctx)
}
```

### 环境变量

- `HOT_RESTART=1`：标识这是热重启的子进程
- `LISTENER_FDS`：继承的监听器文件描述符
- `PARENT_PID`：父进程 PID

### 注意事项

1. **文件描述符限制**：确保系统允许足够的文件描述符
2. **权限要求**：需要发送信号的权限
3. **状态保持**：内存中的状态不会保持，需要持久化重要数据
4. **监控集成**：确保监控系统能正确处理进程 PID 变化

## 通用配置管理系统

### 概述

新的配置管理系统提供了业界标准的配置管理接口，支持多种配置源和实时配置更新。

### 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Config         │    │  Config         │    │  Config         │
│  Providers      │───▶│  Manager        │───▶│  Consumers      │
│                 │    │                 │    │                 │
│ • Redis         │    │ • Validation    │    │ • LoadBalancer  │
│ • Etcd          │    │ • Caching       │    │ • CircuitBreaker│
│ • Consul        │    │ • Events        │    │ • Proxy         │
│ • File          │    │ • Metrics       │    │ • Routes        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心接口

#### ConfigProvider

配置提供者接口，支持多种配置源：

```go
type ConfigProvider interface {
    Name() string
    Priority() int
    Watch(ctx context.Context) (<-chan ConfigEvent, error)
    Get(ctx context.Context, key string) (*ConfigItem, error)
    Set(ctx context.Context, key string, value interface{}) error
    Delete(ctx context.Context, key string) error
    List(ctx context.Context, prefix string) ([]string, error)
    Health(ctx context.Context) error
    Close() error
}
```

#### ConfigConsumer

配置消费者接口，处理配置变更：

```go
type ConfigConsumer interface {
    Name() string
    ConfigKeys() []string
    OnConfigChange(ctx context.Context, event ConfigEvent) error
    Validate(ctx context.Context, config *ConfigItem) error
}
```

### 支持的配置源

#### 1. Redis Provider

```go
redisConfig := providers.RedisConfig{
    Addr:      "localhost:6379",
    DB:        0,
    KeyPrefix: "servicemesh:config:",
    Channel:   "servicemesh:config:changes",
}

redisProvider, err := providers.NewRedisProvider("redis", 100, redisConfig, logger)
configMgr.RegisterProvider(redisProvider)
```

#### 2. Etcd Provider（计划中）

```go
etcdConfig := providers.EtcdConfig{
    Endpoints: []string{"localhost:2379"},
    KeyPrefix: "/servicemesh/config/",
}

etcdProvider, err := providers.NewEtcdProvider("etcd", 90, etcdConfig, logger)
configMgr.RegisterProvider(etcdProvider)
```

#### 3. Consul Provider（计划中）

```go
consulConfig := providers.ConsulConfig{
    Address:   "localhost:8500",
    KeyPrefix: "servicemesh/config/",
}

consulProvider, err := providers.NewConsulProvider("consul", 80, consulConfig, logger)
configMgr.RegisterProvider(consulProvider)
```

### 配置模型

#### 服务配置

```go
type ServiceConfig struct {
    Name            string                 `json:"name"`
    Namespace       string                 `json:"namespace,omitempty"`
    Version         string                 `json:"version,omitempty"`
    Endpoints       []Endpoint             `json:"endpoints"`
    LoadBalancer    LoadBalancerConfig     `json:"load_balancer"`
    CircuitBreaker  CircuitBreakerConfig   `json:"circuit_breaker"`
    Retry           RetryConfig            `json:"retry"`
    Timeout         TimeoutConfig          `json:"timeout"`
    RateLimit       RateLimitConfig        `json:"rate_limit"`
    HealthCheck     HealthCheckConfig      `json:"health_check"`
    Metadata        map[string]interface{} `json:"metadata,omitempty"`
    Tags            []string               `json:"tags,omitempty"`
    CreatedAt       time.Time              `json:"created_at"`
    UpdatedAt       time.Time              `json:"updated_at"`
}
```

#### 路由配置

```go
type RouteConfig struct {
    ID          string                 `json:"id"`
    Name        string                 `json:"name"`
    Path        string                 `json:"path"`
    Method      string                 `json:"method"`
    Service     string                 `json:"service"`
    Rewrite     *RewriteConfig         `json:"rewrite,omitempty"`
    Headers     map[string]string      `json:"headers,omitempty"`
    Middleware  []string               `json:"middleware,omitempty"`
    Metadata    map[string]interface{} `json:"metadata,omitempty"`
    Priority    int                    `json:"priority"`
    Enabled     bool                   `json:"enabled"`
}
```

### 使用示例

#### 1. 初始化配置管理器

```go
// 创建配置管理器
managerConfig := configmanager.ManagerConfig{
    EventBufferSize:     1000,
    CacheTTL:           5 * time.Minute,
    HealthCheckInterval: 30 * time.Second,
    MaxRetries:         3,
    RetryDelay:         time.Second,
}

configMgr := configmanager.NewDefaultConfigManager(logger, managerConfig)

// 注册 Redis 提供者
redisProvider, _ := providers.NewRedisProvider("redis", 100, redisConfig, logger)
configMgr.RegisterProvider(redisProvider)

// 启动配置管理器
configMgr.Start(ctx)
```

#### 2. 创建配置消费者

```go
type ServiceConsumer struct {
    name string
    loadBalancer *loadbalancer.LoadBalancer
}

func (c *ServiceConsumer) Name() string {
    return c.name
}

func (c *ServiceConsumer) ConfigKeys() []string {
    return []string{"service:*"}
}

func (c *ServiceConsumer) OnConfigChange(ctx context.Context, event configmanager.ConfigEvent) error {
    if serviceConfig, ok := event.Value.(*configmanager.ServiceConfig); ok {
        // 更新负载均衡器配置
        c.loadBalancer.UpdateServiceConfig(serviceConfig)
    }
    return nil
}

// 注册消费者
consumer := &ServiceConsumer{name: "service-manager", loadBalancer: lb}
configMgr.RegisterConsumer(consumer)
```

#### 3. 配置 CRUD 操作

```go
// 创建服务配置
serviceConfig := &configmanager.ServiceConfig{
    Name: "user-service",
    Endpoints: []configmanager.Endpoint{
        {
            ID:      "endpoint-1",
            Address: "*************",
            Port:    8080,
            Weight:  100,
            Healthy: true,
        },
    },
    LoadBalancer: configmanager.LoadBalancerConfig{
        Strategy: "round_robin",
    },
    // ... 其他配置
}

// 设置配置
err := configMgr.Set(ctx, "service:user-service", serviceConfig)

// 获取配置
item, err := configMgr.Get(ctx, "service:user-service")

// 删除配置
err := configMgr.Delete(ctx, "service:user-service")
```

### 配置验证

系统提供了内置的配置验证器：

```go
validator := configmanager.NewDefaultValidator()

// 验证服务配置
err := validator.ValidateService(serviceConfig)

// 验证路由配置
err := validator.ValidateRoute(routeConfig)

// 验证中间件配置
err := validator.ValidateMiddleware(middlewareConfig)
```

### 监控和指标

配置管理器提供了丰富的监控指标：

- `configmanager_config_changes_total`：配置变更总数
- `configmanager_config_errors_total`：配置错误总数
- `configmanager_provider_health`：提供者健康状态
- `configmanager_consumer_latency_seconds`：消费者处理延迟

### 最佳实践

#### 1. 配置分层

```
servicemesh:config:
├── global/          # 全局配置
├── services/        # 服务配置
├── routes/          # 路由配置
├── middleware/      # 中间件配置
└── policies/        # 策略配置
```

#### 2. 配置版本管理

```go
type ConfigItem struct {
    Key       string      `json:"key"`
    Value     interface{} `json:"value"`
    Version   int64       `json:"version"`
    Timestamp time.Time   `json:"timestamp"`
    Metadata  map[string]interface{} `json:"metadata,omitempty"`
}
```

#### 3. 配置回滚

```go
// 保存配置快照
snapshot := configMgr.CreateSnapshot()

// 应用新配置
err := configMgr.Set(ctx, key, newConfig)

// 如果出错，回滚到快照
if err != nil {
    configMgr.RollbackToSnapshot(snapshot)
}
```

### 故障排除

#### 1. 配置更新失败

```bash
# 检查提供者健康状态
curl http://localhost:9090/metrics | grep configmanager_provider_health

# 查看错误日志
journalctl -u servicemesh -f | grep "config error"
```

#### 2. 配置不一致

```bash
# 检查配置版本
redis-cli get "servicemesh:config:service:user-service"

# 强制重新加载配置
kill -USR2 <pid>
```

#### 3. 性能问题

```bash
# 检查消费者延迟
curl http://localhost:9090/metrics | grep configmanager_consumer_latency

# 检查缓存命中率
curl http://localhost:9090/metrics | grep configmanager_cache
```

## 总结

新的热重启和配置管理系统提供了：

1. **零停机更新**：通过热重启实现无缝更新
2. **统一配置接口**：支持多种配置源的标准化接口
3. **实时配置更新**：配置变更实时生效
4. **配置验证**：确保配置的正确性和一致性
5. **监控集成**：丰富的监控指标和健康检查
6. **扩展性**：易于添加新的配置源和消费者

这些功能使 ServiceMesh Go 更适合生产环境的使用，提供了企业级的配置管理和运维能力。
