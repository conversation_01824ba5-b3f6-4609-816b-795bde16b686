package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/chungzy/servicemesh-go/internal/app"
	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/chungzy/servicemesh-go/pkg/logger"
	"go.uber.org/zap"
)

var (
	configFile = flag.String("config", "configs/config.yaml", "Configuration file path")
	version    = flag.Bool("version", false, "Show version information")
)

const (
	AppName    = "ServiceMesh"
	AppVersion = "1.0.0"
)

func main() {
	flag.Parse()

	if *version {
		fmt.Printf("%s version %s\n", AppName, AppVersion)
		os.Exit(0)
	}

	// Initialize logger
	log, err := logger.NewLogger()
	if err != nil {
		fmt.Printf("Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer log.Sync()

	// Load configuration
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		log.Fatal("Failed to load configuration", zap.Error(err))
	}

	// Create application
	application, err := app.NewApp(cfg, log)
	if err != nil {
		log.Fatal("Failed to create application", zap.Error(err))
	}

	// Start application
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := application.Start(ctx); err != nil {
		log.Fatal("Failed to start application", zap.Error(err))
	}

	log.Info("ServiceMesh started successfully",
		zap.String("version", AppVersion),
		zap.String("config", *configFile))

	// Wait for shutdown signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	log.Info("Shutting down ServiceMesh...")

	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	if err := application.Stop(shutdownCtx); err != nil {
		log.Error("Error during shutdown", zap.Error(err))
	} else {
		log.Info("ServiceMesh stopped gracefully")
	}
}
