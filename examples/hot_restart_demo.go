package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/chungzy/servicemesh-go/pkg/hotrestart"
	"github.com/chungzy/servicemesh-go/pkg/loadbalancer"
	"github.com/chungzy/servicemesh-go/pkg/logger"
	"github.com/chungzy/servicemesh-go/pkg/metrics"
	"github.com/chungzy/servicemesh-go/pkg/proxy"
)

func main() {
	fmt.Println("🔥 ServiceMesh Hot Restart Demo")

	// Create logger
	logger, err := logger.NewDevelopmentLogger()
	if err != nil {
		log.Fatal(err)
	}

	// Create basic configuration
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host:               "localhost",
			Port:               8080,
			ReadTimeout:        30 * time.Second,
			WriteTimeout:       30 * time.Second,
			IdleTimeout:        120 * time.Second,
			MaxRequestBodySize: 4 * 1024 * 1024,
			Concurrency:        256 * 1024,
		},
		LoadBalance: config.LoadBalanceConfig{
			Strategy:            "round_robin",
			HealthCheckPath:     "/health",
			HealthCheckInterval: 30 * time.Second,
			MaxRetries:          3,
			RetryTimeout:        5 * time.Second,
		},
		Metrics: config.MetricsConfig{
			Enable: true,
			Path:   "/metrics",
		},
	}

	// Create hot restart manager
	pidFile := "/tmp/servicemesh.pid"
	gracefulStop := 30 * time.Second
	hotRestartMgr := hotrestart.NewManager(logger, pidFile, gracefulStop)

	// Create components
	lb := loadbalancer.NewLoadBalancer(cfg, logger)
	metricsCollector := metrics.NewCollector()
	proxyServer := proxy.NewServer(cfg, logger, lb, metricsCollector)

	// Set hot restart manager for proxy server
	proxyServer.SetHotRestartManager(hotRestartMgr)

	// Register server with hot restart manager
	hotRestartMgr.RegisterServer(proxyServer)

	// Add some demo services
	lb.AddService("demo-service", loadbalancer.RoundRobin)
	lb.AddBackend("demo-service", "127.0.0.1", 8001, 100)
	lb.AddBackend("demo-service", "127.0.0.1", 8002, 100)

	fmt.Println("✅ Components initialized")

	// Start hot restart manager
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := hotRestartMgr.Start(ctx); err != nil {
		log.Fatal("Failed to start hot restart manager:", err)
	}

	fmt.Println("✅ Hot restart manager started")

	// Check if this is a child process
	if hotRestartMgr.IsChild() {
		fmt.Println("🔄 This is a child process from hot restart")
	} else {
		fmt.Println("🆕 This is the initial process")
	}

	// Start proxy server
	go func() {
		if err := proxyServer.Start(ctx); err != nil {
			logger.Error("Proxy server error", err)
		}
	}()

	fmt.Printf("✅ Proxy server started on http://localhost:%d\n", cfg.Server.Port)

	// Print instructions
	fmt.Println("\n📋 Hot Restart Instructions:")
	fmt.Println("   1. The server is now running")
	fmt.Println("   2. You can send requests to http://localhost:8080")
	fmt.Println("   3. To trigger hot restart, send SIGUSR1 signal:")
	fmt.Printf("      kill -USR1 %d\n", os.Getpid())
	fmt.Println("   4. To gracefully stop, send SIGTERM signal:")
	fmt.Printf("      kill -TERM %d\n", os.Getpid())
	fmt.Println("   5. Or press Ctrl+C to stop")

	// Print current PID
	fmt.Printf("\n🆔 Current Process ID: %d\n", os.Getpid())

	// Print sample curl commands
	fmt.Println("\n🌐 Sample requests:")
	fmt.Println("   curl -H 'X-Service-Name: demo-service' http://localhost:8080/")
	fmt.Println("   curl http://localhost:8080/health")
	fmt.Println("   curl http://localhost:8080/metrics")

	// Keep the main process running
	select {
	case <-ctx.Done():
		fmt.Println("\n🛑 Context cancelled, shutting down...")
	}

	// Stop hot restart manager
	if err := hotRestartMgr.Stop(); err != nil {
		logger.Error("Error stopping hot restart manager", err)
	}

	fmt.Println("✅ Hot restart demo completed")
}
