package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/chungzy/servicemesh-go/pkg/loadbalancer"
	"github.com/chungzy/servicemesh-go/pkg/logger"
)

func main() {
	fmt.Println("ServiceMesh Go - Simple Demo")

	// Create logger
	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		panic(err)
	}

	// Create basic configuration
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host:               "localhost",
			Port:               8080,
			ReadTimeout:        30 * time.Second,
			WriteTimeout:       30 * time.Second,
			IdleTimeout:        120 * time.Second,
			MaxRequestBodySize: 4 * 1024 * 1024,
			Concurrency:        256 * 1024,
		},
		LoadBalance: config.LoadBalanceConfig{
			Strategy:            "round_robin",
			HealthCheckPath:     "/health",
			HealthCheckInterval: 30 * time.Second,
			MaxRetries:          3,
			RetryTimeout:        5 * time.Second,
		},
	}

	// Create load balancer
	lb := loadbalancer.NewLoadBalancer(cfg, log)

	// Add a service
	lb.AddService("demo-service", loadbalancer.RoundRobin)

	// Add some backends
	err = lb.AddBackend("demo-service", "127.0.0.1", 8001, 100)
	if err != nil {
		log.Error("Failed to add backend", err)
		return
	}

	err = lb.AddBackend("demo-service", "127.0.0.1", 8002, 100)
	if err != nil {
		log.Error("Failed to add backend", err)
		return
	}

	fmt.Println("✅ Load balancer configured with demo-service")
	fmt.Println("   - Backend 1: 127.0.0.1:8001")
	fmt.Println("   - Backend 2: 127.0.0.1:8002")

	// Start health checking
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	go lb.StartHealthCheck(ctx)

	fmt.Println("✅ Health checking started")
	fmt.Println("✅ ServiceMesh Go demo completed successfully!")
	fmt.Println("\nTo run the full ServiceMesh:")
	fmt.Println("  go run ./cmd/servicemesh -config configs/config.yaml")
}
