package main

import (
	"fmt"
	"log"
	"math/rand"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/circuitbreaker"
	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/chungzy/servicemesh-go/pkg/logger"
)

func main() {
	fmt.Println("🚫 封禁计数和自适应熔断演示")
	fmt.Println("================================")

	// 1. 创建日志器
	logger, err := logger.NewDevelopmentLogger()
	if err != nil {
		log.Fatal("❌ 日志器初始化失败:", err)
	}

	// 2. 创建熔断器配置
	circuitConfig := &config.CircuitConfig{
		FailureThreshold: 5,
		Timeout:          30 * time.Second,
		MaxRequests:      10,
		Interval:         60 * time.Second,
	}

	// 3. 创建自定义封禁配置
	banConfig := &circuitbreaker.BanConfig{
		ErrorRateThreshold:    0.4,  // 40% 错误率触发封禁
		ErrorRateWindowSize:   20,   // 20 个请求的窗口
		MinRequestsForBan:     10,   // 最少 10 个请求才考虑封禁
		LatencyThreshold:      2 * time.Second,
		LatencyMultiplier:     3.0,  // 延迟超过基线 3 倍
		FiveXXThreshold:       0.3,  // 30% 5XX 错误率
		InitialBanDuration:    10 * time.Second,
		MaxBanDuration:        60 * time.Second,
		BanMultiplier:         2.0,
		RecoverySuccessCount:  3,    // 3 次成功请求解封
		RecoveryTestInterval:  5 * time.Second,
		RecoveryTestRatio:     0.2,  // 20% 流量用于恢复测试
	}

	// 4. 创建熔断器管理器
	manager := circuitbreaker.NewManagerWithBanConfig(circuitConfig, banConfig, logger)

	// 5. 创建服务熔断器
	serviceBreaker := circuitbreaker.NewServiceCircuitBreaker(manager, "demo-service")

	fmt.Println("✅ 封禁系统初始化完成")

	// 6. 演示不同的封禁场景
	demonstrateBanScenarios(serviceBreaker)

	fmt.Println("\n🎉 封禁系统演示完成!")
}

func demonstrateBanScenarios(scb *circuitbreaker.ServiceCircuitBreaker) {
	backends := []string{
		"*************:8080",
		"*************:8080", 
		"*************:8080",
	}

	// 场景1: 错误率封禁
	fmt.Println("\n📊 场景1: 错误率封禁演示")
	demonstrateErrorRateBan(scb, backends[0])

	// 场景2: 延迟封禁
	fmt.Println("\n⏱️  场景2: 延迟封禁演示")
	demonstrateLatencyBan(scb, backends[1])

	// 场景3: 5XX错误封禁
	fmt.Println("\n🔥 场景3: 5XX错误封禁演示")
	demonstrateFiveXXBan(scb, backends[2])

	// 场景4: 恢复测试
	fmt.Println("\n🔄 场景4: 自动恢复演示")
	demonstrateRecovery(scb, backends[0])

	// 场景5: 手动封禁/解封
	fmt.Println("\n🔧 场景5: 手动封禁/解封演示")
	demonstrateManualBan(scb, backends[1])

	// 显示最终统计
	fmt.Println("\n📈 最终统计信息:")
	showFinalStats(scb)
}

func demonstrateErrorRateBan(scb *circuitbreaker.ServiceCircuitBreaker, backend string) {
	fmt.Printf("   测试后端: %s\n", backend)
	
	// 发送一些正常请求建立基线
	for i := 0; i < 5; i++ {
		scb.RecordRequestWithDetails(backend, 200, 100*time.Millisecond)
	}
	
	// 发送高错误率请求
	for i := 0; i < 15; i++ {
		if i < 10 {
			// 70% 错误率
			statusCode := 500
			if i%10 < 3 {
				statusCode = 200
			}
			scb.RecordRequestWithDetails(backend, statusCode, 150*time.Millisecond)
		}
	}
	
	// 检查封禁状态
	if scb.IsBackendBanned(backend) {
		fmt.Printf("   ✅ 后端 %s 因错误率过高被封禁\n", backend)
		stats := scb.GetBackendBanStats(backend)
		if stats != nil {
			fmt.Printf("   📊 错误率: %.2f%%, 总请求: %d, 错误请求: %d\n", 
				stats.ErrorRate*100, stats.TotalRequests, stats.ErrorRequests)
		}
	} else {
		fmt.Printf("   ❌ 后端 %s 未被封禁\n", backend)
	}
}

func demonstrateLatencyBan(scb *circuitbreaker.ServiceCircuitBreaker, backend string) {
	fmt.Printf("   测试后端: %s\n", backend)
	
	// 发送一些正常延迟的请求建立基线
	for i := 0; i < 10; i++ {
		scb.RecordRequestWithDetails(backend, 200, 100*time.Millisecond)
	}
	
	// 发送高延迟请求
	for i := 0; i < 10; i++ {
		latency := 3 * time.Second // 超过阈值
		scb.RecordRequestWithDetails(backend, 200, latency)
	}
	
	// 检查封禁状态
	if scb.IsBackendBanned(backend) {
		fmt.Printf("   ✅ 后端 %s 因延迟过高被封禁\n", backend)
		stats := scb.GetBackendBanStats(backend)
		if stats != nil {
			fmt.Printf("   📊 平均延迟: %v, 最大延迟: %v\n", 
				stats.AvgLatency, stats.MaxLatency)
		}
	} else {
		fmt.Printf("   ❌ 后端 %s 未被封禁\n", backend)
	}
}

func demonstrateFiveXXBan(scb *circuitbreaker.ServiceCircuitBreaker, backend string) {
	fmt.Printf("   测试后端: %s\n", backend)
	
	// 发送大量5XX错误
	for i := 0; i < 20; i++ {
		statusCode := 200
		if i%10 < 4 { // 40% 5XX错误率
			statusCode = 503
		}
		scb.RecordRequestWithDetails(backend, statusCode, 200*time.Millisecond)
	}
	
	// 检查封禁状态
	if scb.IsBackendBanned(backend) {
		fmt.Printf("   ✅ 后端 %s 因5XX错误率过高被封禁\n", backend)
		stats := scb.GetBackendBanStats(backend)
		if stats != nil {
			fmt.Printf("   📊 错误码统计: %v\n", stats.ErrorCodes)
		}
	} else {
		fmt.Printf("   ❌ 后端 %s 未被封禁\n", backend)
	}
}

func demonstrateRecovery(scb *circuitbreaker.ServiceCircuitBreaker, backend string) {
	fmt.Printf("   测试后端: %s (应该已被封禁)\n", backend)
	
	if !scb.IsBackendBanned(backend) {
		fmt.Printf("   ⚠️  后端未被封禁，跳过恢复测试\n")
		return
	}
	
	stats := scb.GetBackendBanStats(backend)
	if stats != nil {
		fmt.Printf("   ⏳ 封禁剩余时间: %v\n", stats.BanTimeLeft)
	}
	
	// 等待恢复测试间隔
	fmt.Printf("   ⏳ 等待恢复测试间隔...\n")
	time.Sleep(6 * time.Second)
	
	// 模拟恢复测试请求
	fmt.Printf("   🔄 开始恢复测试...\n")
	for i := 0; i < 5; i++ {
		if scb.IsBackendBanned(backend) {
			// 模拟成功的恢复请求
			scb.RecordRequestWithDetails(backend, 200, 100*time.Millisecond)
			fmt.Printf("   ✅ 恢复测试请求 %d: 成功\n", i+1)
			time.Sleep(1 * time.Second)
		} else {
			fmt.Printf("   🎉 后端 %s 已自动解封!\n", backend)
			break
		}
	}
}

func demonstrateManualBan(scb *circuitbreaker.ServiceCircuitBreaker, backend string) {
	fmt.Printf("   测试后端: %s\n", backend)
	
	// 手动封禁
	fmt.Printf("   🔧 手动封禁后端...\n")
	scb.ManualBanBackend(backend)
	
	if scb.IsBackendBanned(backend) {
		fmt.Printf("   ✅ 后端 %s 已被手动封禁\n", backend)
		stats := scb.GetBackendBanStats(backend)
		if stats != nil {
			fmt.Printf("   📊 封禁原因: %s\n", stats.BanReason)
		}
	}
	
	// 等待一下
	time.Sleep(2 * time.Second)
	
	// 手动解封
	fmt.Printf("   🔧 手动解封后端...\n")
	scb.ManualUnbanBackend(backend)
	
	if !scb.IsBackendBanned(backend) {
		fmt.Printf("   ✅ 后端 %s 已被手动解封\n", backend)
	}
}

func showFinalStats(scb *circuitbreaker.ServiceCircuitBreaker) {
	allStats := scb.GetAllBanStats()
	
	for backend, stats := range allStats {
		fmt.Printf("\n   后端: %s\n", backend)
		fmt.Printf("   ├─ 总请求数: %d\n", stats.TotalRequests)
		fmt.Printf("   ├─ 错误请求数: %d\n", stats.ErrorRequests)
		fmt.Printf("   ├─ 错误率: %.2f%%\n", stats.ErrorRate*100)
		fmt.Printf("   ├─ 平均延迟: %v\n", stats.AvgLatency)
		fmt.Printf("   ├─ 最大延迟: %v\n", stats.MaxLatency)
		fmt.Printf("   ├─ 是否被封禁: %v\n", stats.IsBanned)
		if stats.IsBanned {
			fmt.Printf("   ├─ 封禁原因: %s\n", stats.BanReason)
			fmt.Printf("   ├─ 封禁次数: %d\n", stats.BanCount)
			fmt.Printf("   └─ 剩余封禁时间: %v\n", stats.BanTimeLeft)
		} else {
			fmt.Printf("   └─ 状态: 正常\n")
		}
	}
}

// 模拟真实的请求执行
func simulateRequest(scb *circuitbreaker.ServiceCircuitBreaker, backend string) (interface{}, error) {
	return scb.ExecuteRequest(backend, func() (interface{}, error) {
		// 模拟请求处理
		time.Sleep(time.Duration(rand.Intn(500)) * time.Millisecond)
		
		// 随机返回成功或失败
		if rand.Float64() < 0.3 { // 30% 失败率
			return nil, fmt.Errorf("request failed")
		}
		
		return "success", nil
	})
}
