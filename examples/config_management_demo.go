package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/configmanager"
	"github.com/chungzy/servicemesh-go/pkg/configmanager/providers"
	"github.com/chungzy/servicemesh-go/pkg/logger"
)

// ServiceConsumer implements ConfigConsumer for service configuration
type ServiceConsumer struct {
	name string
}

func (c *ServiceConsumer) Name() string {
	return c.name
}

func (c *ServiceConsumer) ConfigKeys() []string {
	return []string{"service:*", "route:*"}
}

func (c *ServiceConsumer) OnConfigChange(ctx context.Context, event configmanager.ConfigEvent) error {
	fmt.Printf("🔄 Service consumer received config change: %s = %v (type: %s)\n",
		event.Key, event.Value, event.Type)
	return nil
}

func (c *ServiceConsumer) Validate(ctx context.Context, config *configmanager.ConfigItem) error {
	// Add validation logic here
	return nil
}

func main() {
	fmt.Println("🚀 ServiceMesh Configuration Management Demo")

	// Create logger
	logger, err := logger.NewDevelopmentLogger()
	if err != nil {
		log.Fatal(err)
	}

	// Create configuration manager
	managerConfig := configmanager.ManagerConfig{
		EventBufferSize:     1000,
		CacheTTL:            5 * time.Minute,
		HealthCheckInterval: 30 * time.Second,
		MaxRetries:          3,
		RetryDelay:          time.Second,
	}

	configMgr := configmanager.NewDefaultConfigManager(logger, managerConfig)

	// Create Redis provider (if Redis is available)
	redisConfig := providers.RedisConfig{
		Addr:      "localhost:6379",
		DB:        0,
		KeyPrefix: "servicemesh:config:",
		Channel:   "servicemesh:config:changes",
	}

	redisProvider, err := providers.NewRedisProvider("redis", 100, redisConfig, logger)
	if err != nil {
		fmt.Printf("Redis provider not available: %v\n", err)
		fmt.Println("Continuing with in-memory demo...")
	} else {
		// Register Redis provider
		if err := configMgr.RegisterProvider(redisProvider); err != nil {
			log.Fatal(err)
		}
		fmt.Println("Redis provider registered")
	}

	// Register service consumer
	serviceConsumer := &ServiceConsumer{name: "service-manager"}
	if err := configMgr.RegisterConsumer(serviceConsumer); err != nil {
		log.Fatal(err)
	}
	fmt.Println("Service consumer registered")

	// Start configuration manager
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := configMgr.Start(ctx); err != nil {
		log.Fatal(err)
	}
	fmt.Println("Configuration manager started")

	// Demo: Create service configuration
	serviceConfig := &configmanager.ServiceConfig{
		Name:      "user-service",
		Namespace: "production",
		Version:   "v1.0.0",
		Endpoints: []configmanager.Endpoint{
			{
				ID:      "endpoint-1",
				Address: "*************",
				Port:    8080,
				Weight:  100,
				Healthy: true,
			},
			{
				ID:      "endpoint-2",
				Address: "*************",
				Port:    8080,
				Weight:  100,
				Healthy: true,
			},
		},
		LoadBalancer: configmanager.LoadBalancerConfig{
			Strategy: "round_robin",
		},
		CircuitBreaker: configmanager.CircuitBreakerConfig{
			Enabled:          true,
			FailureThreshold: 5,
			SuccessThreshold: 3,
			Timeout:          30 * time.Second,
			MaxRequests:      100,
		},
		Retry: configmanager.RetryConfig{
			Enabled:     true,
			MaxRetries:  3,
			RetryDelay:  time.Second,
			BackoffType: "exponential",
		},
		Timeout: configmanager.TimeoutConfig{
			Connect: 5 * time.Second,
			Read:    30 * time.Second,
			Write:   30 * time.Second,
			Idle:    60 * time.Second,
		},
		RateLimit: configmanager.RateLimitConfig{
			Enabled: true,
			Rate:    1000,
			Burst:   1500,
			Key:     "ip",
		},
		HealthCheck: configmanager.HealthCheckConfig{
			Enabled:  true,
			Path:     "/health",
			Interval: 30 * time.Second,
			Timeout:  5 * time.Second,
			Method:   "GET",
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Set service configuration
	fmt.Println("\n📝 Setting service configuration...")
	if err := configMgr.Set(ctx, "service:user-service", serviceConfig); err != nil {
		log.Printf("Failed to set service config: %v", err)
	} else {
		fmt.Println("✅ Service configuration set successfully")
	}

	// Get service configuration
	fmt.Println("\n📖 Getting service configuration...")
	item, err := configMgr.Get(ctx, "service:user-service")
	if err != nil {
		log.Printf("Failed to get service config: %v", err)
	} else {
		fmt.Printf("✅ Retrieved service configuration: %+v\n", item.Key)
	}

	// Demo: Create route configuration
	routeConfig := &configmanager.RouteConfig{
		ID:       "route-1",
		Name:     "user-api",
		Path:     "/api/users",
		Method:   "GET",
		Service:  "user-service",
		Priority: 100,
		Enabled:  true,
		Headers: map[string]string{
			"X-Service": "user-service",
		},
	}

	fmt.Println("\n📝 Setting route configuration...")
	if err := configMgr.Set(ctx, "route:user-api", routeConfig); err != nil {
		log.Printf("Failed to set route config: %v", err)
	} else {
		fmt.Println("✅ Route configuration set successfully")
	}

	// Wait a bit to see events
	time.Sleep(2 * time.Second)

	// Update service configuration
	fmt.Println("\n🔄 Updating service configuration...")
	serviceConfig.Endpoints = append(serviceConfig.Endpoints, configmanager.Endpoint{
		ID:      "endpoint-3",
		Address: "*************",
		Port:    8080,
		Weight:  50,
		Healthy: true,
	})
	serviceConfig.UpdatedAt = time.Now()

	if err := configMgr.Set(ctx, "service:user-service", serviceConfig); err != nil {
		log.Printf("Failed to update service config: %v", err)
	} else {
		fmt.Println("✅ Service configuration updated successfully")
	}

	// Check provider health
	fmt.Println("\n🏥 Checking provider health...")
	health := configMgr.Health(ctx)
	for provider, err := range health {
		if err != nil {
			fmt.Printf("❌ Provider %s: unhealthy (%v)\n", provider, err)
		} else {
			fmt.Printf("✅ Provider %s: healthy\n", provider)
		}
	}

	// Wait a bit more to see events
	time.Sleep(2 * time.Second)

	// Delete configuration
	fmt.Println("\n🗑️  Deleting route configuration...")
	if err := configMgr.Delete(ctx, "route:user-api"); err != nil {
		log.Printf("Failed to delete route config: %v", err)
	} else {
		fmt.Println("✅ Route configuration deleted successfully")
	}

	// Wait for final events
	time.Sleep(1 * time.Second)

	// Stop configuration manager
	fmt.Println("\n🛑 Stopping configuration manager...")
	stopCtx, stopCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer stopCancel()

	if err := configMgr.Stop(stopCtx); err != nil {
		log.Printf("Error stopping config manager: %v", err)
	} else {
		fmt.Println("✅ Configuration manager stopped successfully")
	}

	fmt.Println("\n🎉 Configuration management demo completed!")
	fmt.Println("\n💡 Key Features Demonstrated:")
	fmt.Println("   - ✅ Provider registration (Redis)")
	fmt.Println("   - ✅ Consumer registration and event handling")
	fmt.Println("   - ✅ Configuration validation")
	fmt.Println("   - ✅ Configuration CRUD operations")
	fmt.Println("   - ✅ Real-time configuration updates")
	fmt.Println("   - ✅ Provider health monitoring")
	fmt.Println("   - ✅ Graceful shutdown")
}
