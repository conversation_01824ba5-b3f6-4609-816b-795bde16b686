package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/chungzy/servicemesh-go/pkg/loadbalancer"
	"github.com/chungzy/servicemesh-go/pkg/logger"
	"github.com/chungzy/servicemesh-go/pkg/metrics"
	"github.com/chungzy/servicemesh-go/pkg/proxy"
)

func main() {
	fmt.Println("🚀 Go 微服务网关 - 新手入门演示")
	fmt.Println("================================")

	// 1. 创建日志器
	fmt.Println("📝 步骤1: 初始化日志器...")
	logger, err := logger.NewDevelopmentLogger()
	if err != nil {
		log.Fatal("❌ 日志器初始化失败:", err)
	}
	fmt.Println("✅ 日志器初始化成功")

	// 2. 创建配置
	fmt.Println("\n⚙️  步骤2: 创建配置...")
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host:               "localhost",
			Port:               8080,
			ReadTimeout:        30 * time.Second,
			WriteTimeout:       30 * time.Second,
			IdleTimeout:        120 * time.Second,
			MaxRequestBodySize: 4 * 1024 * 1024,
			Concurrency:        256 * 1024,
		},
		LoadBalance: config.LoadBalanceConfig{
			Strategy:            "round_robin",
			HealthCheckPath:     "/health",
			HealthCheckInterval: 30 * time.Second,
			MaxRetries:          3,
			RetryTimeout:        5 * time.Second,
		},
		Metrics: config.MetricsConfig{
			Enable: true,
			Path:   "/metrics",
		},
	}
	fmt.Println("✅ 配置创建成功")

	// 3. 创建负载均衡器
	fmt.Println("\n⚖️  步骤3: 创建负载均衡器...")
	lb := loadbalancer.NewLoadBalancer(cfg, logger)
	fmt.Println("✅ 负载均衡器创建成功")

	// 4. 添加演示服务
	fmt.Println("\n🔧 步骤4: 配置演示服务...")

	// 添加服务
	lb.AddService("demo-service", loadbalancer.RoundRobin)

	// 添加后端服务器（模拟）
	backends := []struct {
		host   string
		port   int
		weight int
	}{
		{"127.0.0.1", 8001, 100},
		{"127.0.0.1", 8002, 100},
		{"127.0.0.1", 8003, 50},
	}

	for i, backend := range backends {
		err := lb.AddBackend("demo-service", backend.host, backend.port, backend.weight)
		if err != nil {
			fmt.Printf("⚠️  添加后端服务器 %d 失败: %v\n", i+1, err)
		} else {
			fmt.Printf("✅ 添加后端服务器 %d: %s:%d (权重: %d)\n",
				i+1, backend.host, backend.port, backend.weight)
		}
	}

	// 5. 创建监控收集器
	fmt.Println("\n📊 步骤5: 创建监控收集器...")
	metricsCollector := metrics.NewCollector()
	fmt.Println("✅ 监控收集器创建成功")

	// 6. 创建代理服务器
	fmt.Println("\n🌐 步骤6: 创建代理服务器...")
	proxyServer := proxy.NewServer(cfg, logger, lb, metricsCollector)
	fmt.Println("✅ 代理服务器创建成功")

	// 7. 启动服务
	fmt.Println("\n🚀 步骤7: 启动服务...")
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动健康检查
	go func() {
		lb.StartHealthCheck(ctx)
	}()

	// 启动代理服务器
	go func() {
		if err := proxyServer.Start(ctx); err != nil {
			logger.Error("代理服务器启动失败", err)
		}
	}()

	fmt.Println("✅ 服务启动成功!")
	fmt.Println("\n🎉 微服务网关已启动!")
	fmt.Println("================================")

	// 8. 显示使用说明
	printUsageInstructions()

	// 9. 演示负载均衡
	fmt.Println("\n🔄 演示负载均衡算法...")
	demonstrateLoadBalancing(lb)

	// 10. 保持服务运行
	fmt.Println("\n⏳ 服务运行中... (按 Ctrl+C 停止)")

	// 等待中断信号
	select {
	case <-ctx.Done():
		fmt.Println("\n🛑 服务正在停止...")
	}

	fmt.Println("✅ 演示完成!")
}

func printUsageInstructions() {
	fmt.Println("\n📋 使用说明:")
	fmt.Println("   🌐 网关地址: http://localhost:8080")
	fmt.Println("   📊 监控指标: http://localhost:8080/metrics")
	fmt.Println("   ❤️  健康检查: http://localhost:8080/health")
	fmt.Println("\n🧪 测试命令:")
	fmt.Println("   # 健康检查")
	fmt.Println("   curl http://localhost:8080/health")
	fmt.Println("\n   # 查看监控指标")
	fmt.Println("   curl http://localhost:8080/metrics")
	fmt.Println("\n   # 测试代理功能 (需要先启动后端服务)")
	fmt.Println("   curl -H 'X-Service-Name: demo-service' http://localhost:8080/")
	fmt.Println("\n🔧 启动后端服务 (新终端):")
	fmt.Println("   python3 -m http.server 8001")
	fmt.Println("   python3 -m http.server 8002")
	fmt.Println("   python3 -m http.server 8003")
	fmt.Println("\n📈 性能测试:")
	fmt.Println("   wrk -t12 -c400 -d30s -H 'X-Service-Name: demo-service' http://localhost:8080/")
}

func demonstrateLoadBalancing(lb *loadbalancer.LoadBalancer) {
	fmt.Println("   正在模拟 10 次请求分发...")

	// 模拟请求上下文
	// 注意：这里只是演示负载均衡逻辑，实际的 fasthttp.RequestCtx 需要真实的 HTTP 请求
	for i := 1; i <= 10; i++ {
		// 这里我们无法直接调用 GetBackend，因为它需要 fasthttp.RequestCtx
		// 在实际使用中，这个逻辑会在 HTTP 请求处理中自动执行
		fmt.Printf("   请求 %2d: 将被路由到后端服务器 (轮询算法)\n", i)
	}

	fmt.Println("   💡 提示: 实际的负载均衡会在处理 HTTP 请求时自动执行")
}

// 演示配置更新
func demonstrateConfigUpdate() {
	fmt.Println("\n🔄 演示配置热更新...")

	// 创建新的服务配置
	newServiceConfig := &config.ServiceConfig{
		Name:       "user-service",
		Strategy:   "consistent_hash",
		Timeout:    10 * time.Second,
		MaxRetries: 5,
		Endpoints: []config.Endpoint{
			{
				ID:      "user-1",
				Host:    "*************",
				Port:    8080,
				Weight:  100,
				Healthy: true,
			},
			{
				ID:      "user-2",
				Host:    "*************",
				Port:    8080,
				Weight:  100,
				Healthy: true,
			},
		},
	}

	fmt.Printf("   ✅ 新服务配置: %s (策略: %s, 端点数: %d)\n",
		newServiceConfig.Name,
		newServiceConfig.Strategy,
		len(newServiceConfig.Endpoints))

	fmt.Println("   💡 提示: 在生产环境中，配置会通过 Redis/Etcd 等配置中心热更新")
}

// 演示监控指标
func demonstrateMetrics() {
	fmt.Println("\n📊 演示监控指标...")

	metrics := []string{
		"servicemesh_requests_total",
		"servicemesh_request_duration_seconds",
		"servicemesh_active_connections",
		"servicemesh_circuit_breaker_state",
		"servicemesh_backend_health",
	}

	for _, metric := range metrics {
		fmt.Printf("   📈 %s: 请求总数/延迟/连接数等关键指标\n", metric)
	}

	fmt.Println("   💡 提示: 可通过 Grafana 可视化这些指标")
}

// 演示故障处理
func demonstrateFaultHandling() {
	fmt.Println("\n🛡️ 演示故障处理机制...")

	scenarios := []string{
		"后端服务下线 → 自动剔除不健康节点",
		"请求超时 → 触发重试机制",
		"错误率过高 → 熔断器开启保护",
		"流量激增 → 限流器控制请求速率",
	}

	for i, scenario := range scenarios {
		fmt.Printf("   %d. %s\n", i+1, scenario)
	}

	fmt.Println("   💡 提示: 这些机制保障了系统的高可用性")
}
