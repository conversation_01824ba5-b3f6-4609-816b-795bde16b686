# ServiceMesh Configuration

server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  max_request_body_size: 4194304  # 4MB
  concurrency: 262144

loadbalance:
  strategy: "round_robin"  # round_robin, random, consistent_hash, least_connections
  health_check_path: "/health"
  health_check_interval: "30s"
  max_retries: 3
  retry_timeout: "5s"

circuit:
  max_requests: 100
  interval: "60s"
  timeout: "30s"
  failure_threshold: 5
  success_threshold: 3

gossip:
  enable: true
  bind_addr: "0.0.0.0"
  bind_port: 7946
  seeds: []  # Add seed nodes here, e.g., ["************:7946", "************:7946"]
  sync_interval: "30s"

redis:
  addr: "localhost:6379"
  password: ""
  db: 0

metrics:
  enable: true
  path: "/metrics"
  port: 9090

logging:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, console
  output: "stdout"  # stdout, stderr, file path

fault:
  enable: false  # Enable fault injection for testing

# Example service configurations (these would typically be loaded from Redis)
services:
  user-service:
    name: "user-service"
    strategy: "round_robin"
    timeout: "5s"
    max_retries: 3
    endpoints:
      - id: "user-1"
        host: "************0"
        port: 8080
        weight: 100
        healthy: true
      - id: "user-2"
        host: "************1"
        port: 8080
        weight: 100
        healthy: true
      - id: "user-3"
        host: "************2"
        port: 8080
        weight: 50
        healthy: true

  order-service:
    name: "order-service"
    strategy: "consistent_hash"
    timeout: "10s"
    max_retries: 5
    endpoints:
      - id: "order-1"
        host: "*************"
        port: 8080
        weight: 100
        healthy: true
      - id: "order-2"
        host: "*************"
        port: 8080
        weight: 100
        healthy: true
