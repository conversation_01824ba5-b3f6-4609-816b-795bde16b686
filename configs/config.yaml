# ServiceMesh Configuration

server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  max_request_body_size: 4194304  # 4MB
  concurrency: 262144

loadbalance:
  strategy: "round_robin"  # round_robin, random, consistent_hash, least_connections
  health_check_path: "/health"
  health_check_interval: "30s"
  max_retries: 3
  retry_timeout: "5s"

circuit:
  max_requests: 100
  interval: "60s"
  timeout: "30s"
  failure_threshold: 5
  success_threshold: 3

gossip:
  enable: true
  bind_addr: "0.0.0.0"
  bind_port: 7946
  seeds: []  # Add seed nodes here, e.g., ["************:7946", "************:7946"]
  sync_interval: "30s"

redis:
  addr: "localhost:6379"
  password: ""
  db: 0

metrics:
  enable: true
  path: "/metrics"
  port: 9090

logging:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, console
  output: "stdout"  # stdout, stderr, file path

fault:
  enable: false  # Enable fault injection for testing

# Example service configurations (these would typically be loaded from Redis)
services:
  user-service:
    name: "user-service"
    bns_list:
      user-bns: 100
    black_list: {}
    strategy: 1  # Round robin
    timeout: "5s"
    max_fail: 3
    small_flow: []

  order-service:
    name: "order-service"
    bns_list:
      order-bns: 100
    black_list: {}
    strategy: 7  # Consistent hash
    timeout: "10s"
    max_fail: 5
    small_flow:
      - bns_list:
          order-test-bns: 100
        percent: 10

# Example BNS configurations
bns:
  user-bns:
    name: "user-bns"
    bns_nodes:
      - host: "************0"
        port: 8080
        weight: 100
      - host: "************1"
        port: 8080
        weight: 100
      - host: "************2"
        port: 8080
        weight: 50

  order-bns:
    name: "order-bns"
    bns_nodes:
      - host: "*************"
        port: 8080
        weight: 100
      - host: "*************"
        port: 8080
        weight: 100

  order-test-bns:
    name: "order-test-bns"
    bns_nodes:
      - host: "*************"
        port: 8080
        weight: 100
