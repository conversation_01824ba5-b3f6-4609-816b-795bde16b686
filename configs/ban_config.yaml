# 封禁系统配置文件
ban_system:
  # 全局封禁配置
  global:
    # 错误率封禁配置
    error_rate:
      threshold: 0.5              # 50% 错误率触发封禁
      window_size: 100            # 统计窗口大小（请求数）
      min_requests: 20            # 最小请求数才考虑封禁
    
    # 延迟封禁配置
    latency:
      threshold: "5s"             # 绝对延迟阈值
      multiplier: 3.0             # 相对于基线的倍数
      window_size: 50             # 延迟统计窗口
    
    # 5XX错误封禁配置
    five_xx:
      threshold: 0.3              # 30% 5XX错误率
      window_size: 50             # 统计窗口大小
    
    # 封禁时长配置
    ban_duration:
      initial: "30s"              # 初始封禁时长
      max: "300s"                 # 最大封禁时长
      multiplier: 2.0             # 重复封禁时长倍数
    
    # 恢复配置
    recovery:
      success_count: 5            # 恢复需要的成功请求数
      test_interval: "10s"        # 恢复测试间隔
      test_ratio: 0.1             # 恢复测试流量比例

  # 服务级别配置（覆盖全局配置）
  services:
    # 用户服务配置
    user-service:
      error_rate:
        threshold: 0.4            # 用户服务容忍度更高
        window_size: 50
        min_requests: 10
      latency:
        threshold: "3s"           # 用户服务延迟要求更严格
        multiplier: 2.5
      ban_duration:
        initial: "20s"            # 更短的初始封禁时间
        max: "120s"
    
    # 订单服务配置
    order-service:
      error_rate:
        threshold: 0.3            # 订单服务要求更高可靠性
        window_size: 200
        min_requests: 30
      five_xx:
        threshold: 0.2            # 更严格的5XX错误率
      ban_duration:
        initial: "60s"            # 更长的封禁时间
        max: "600s"
    
    # 支付服务配置
    payment-service:
      error_rate:
        threshold: 0.1            # 支付服务要求极高可靠性
        window_size: 100
        min_requests: 50
      latency:
        threshold: "1s"           # 支付服务延迟要求极严格
        multiplier: 2.0
      ban_duration:
        initial: "120s"           # 支付服务封禁时间更长
        max: "1800s"              # 30分钟最大封禁
      recovery:
        success_count: 10         # 需要更多成功请求才能恢复
        test_interval: "30s"      # 更长的恢复测试间隔

  # 自定义封禁规则
  custom_rules:
    # 规则1: 特定错误码封禁
    - name: "service_unavailable"
      condition: "status_code == 503"
      threshold: 0.2              # 20% 503错误率
      window_size: 30
      ban_duration: "60s"
      enabled: true
    
    # 规则2: 超时错误封禁
    - name: "timeout_errors"
      condition: "status_code == 504"
      threshold: 0.1              # 10% 超时错误率
      window_size: 20
      ban_duration: "120s"
      enabled: true
    
    # 规则3: 认证错误封禁
    - name: "auth_errors"
      condition: "status_code == 401 OR status_code == 403"
      threshold: 0.3              # 30% 认证错误率
      window_size: 50
      ban_duration: "30s"
      enabled: false              # 默认禁用

  # 监控和告警配置
  monitoring:
    # 指标收集
    metrics:
      enabled: true
      collection_interval: "10s"
      retention_period: "24h"
    
    # 告警规则
    alerts:
      # 高封禁率告警
      - name: "high_ban_rate"
        condition: "ban_rate > 0.3"        # 超过30%后端被封禁
        severity: "warning"
        notification:
          - "webhook:http://alertmanager:9093/api/v1/alerts"
          - "email:<EMAIL>"
      
      # 频繁封禁告警
      - name: "frequent_bans"
        condition: "ban_count > 5 in 1h"   # 1小时内封禁超过5次
        severity: "critical"
        notification:
          - "webhook:http://alertmanager:9093/api/v1/alerts"
          - "slack:#ops-alerts"
      
      # 长时间封禁告警
      - name: "long_term_ban"
        condition: "ban_duration > 300s"   # 封禁时间超过5分钟
        severity: "warning"
        notification:
          - "email:<EMAIL>"

  # 管理接口配置
  management:
    # HTTP API配置
    api:
      enabled: true
      port: 8081
      endpoints:
        - "/api/v1/ban/stats"              # 获取封禁统计
        - "/api/v1/ban/backends"           # 获取所有后端状态
        - "/api/v1/ban/clear"              # 清除封禁计数
        - "/api/v1/ban/manual"             # 手动封禁/解封
    
    # 权限控制
    auth:
      enabled: true
      type: "basic"                        # basic, jwt, oauth2
      credentials:
        username: "admin"
        password: "admin123"
    
    # 操作日志
    audit:
      enabled: true
      log_file: "/var/log/ban_audit.log"
      max_size: "100MB"
      max_backups: 10

# 环境特定配置
environments:
  # 开发环境
  development:
    ban_system:
      global:
        error_rate:
          threshold: 0.8            # 开发环境更宽松
        ban_duration:
          initial: "10s"            # 更短的封禁时间
          max: "60s"
  
  # 测试环境
  testing:
    ban_system:
      global:
        error_rate:
          threshold: 0.6
        ban_duration:
          initial: "15s"
          max: "120s"
  
  # 生产环境
  production:
    ban_system:
      global:
        error_rate:
          threshold: 0.5            # 生产环境标准配置
        ban_duration:
          initial: "30s"
          max: "300s"
      monitoring:
        alerts:
          - name: "production_ban_alert"
            condition: "any_backend_banned"
            severity: "critical"
            notification:
              - "pagerduty:production-alerts"
              - "slack:#production-alerts"

# 实验性功能
experimental:
  # 机器学习增强封禁
  ml_enhanced:
    enabled: false
    model_path: "/models/ban_predictor.pkl"
    features:
      - "error_rate_trend"
      - "latency_trend" 
      - "request_pattern"
      - "time_of_day"
    
  # 自适应阈值调整
  adaptive_thresholds:
    enabled: false
    adjustment_interval: "1h"
    min_threshold: 0.1
    max_threshold: 0.9
    
  # 分布式封禁协调
  distributed_coordination:
    enabled: false
    consensus_algorithm: "raft"
    cluster_nodes:
      - "node1:8080"
      - "node2:8080"
      - "node3:8080"
