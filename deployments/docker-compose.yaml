version: '3.8'

services:
  # Redis for configuration storage
  redis:
    image: redis:7-alpine
    container_name: servicemesh-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - servicemesh

  # ServiceMesh instance 1
  servicemesh-1:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: servicemesh-1
    ports:
      - "8080:8080"
      - "7946:7946"
      - "9090:9090"
    environment:
      - SERVICEMESH_REDIS_ADDR=redis:6379
      - SERVICEMESH_GOSSIP_BIND_ADDR=0.0.0.0
      - SERVICEMESH_GOSSIP_BIND_PORT=7946
    volumes:
      - ../configs:/app/configs
    depends_on:
      - redis
    networks:
      - servicemesh
    restart: unless-stopped

  # ServiceMesh instance 2 (for cluster testing)
  servicemesh-2:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: servicemesh-2
    ports:
      - "8081:8080"
      - "7947:7946"
      - "9091:9090"
    environment:
      - SERVICEMESH_REDIS_ADDR=redis:6379
      - SERVICEMESH_GOSSIP_BIND_ADDR=0.0.0.0
      - SERVICEMESH_GOSSIP_BIND_PORT=7946
      - SERVICEMESH_GOSSIP_SEEDS=servicemesh-1:7946
    volumes:
      - ../configs:/app/configs
    depends_on:
      - redis
      - servicemesh-1
    networks:
      - servicemesh
    restart: unless-stopped

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: servicemesh-prometheus
    ports:
      - "9092:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - servicemesh
    restart: unless-stopped

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    container_name: servicemesh-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - servicemesh
    restart: unless-stopped

  # Example backend service 1
  backend-1:
    image: nginx:alpine
    container_name: backend-1
    ports:
      - "8001:80"
    volumes:
      - ./nginx/backend-1.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/index-1.html:/usr/share/nginx/html/index.html
    networks:
      - servicemesh
    restart: unless-stopped

  # Example backend service 2
  backend-2:
    image: nginx:alpine
    container_name: backend-2
    ports:
      - "8002:80"
    volumes:
      - ./nginx/backend-2.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/index-2.html:/usr/share/nginx/html/index.html
    networks:
      - servicemesh
    restart: unless-stopped

volumes:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  servicemesh:
    driver: bridge
