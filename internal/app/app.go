package app

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/circuitbreaker"
	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/chungzy/servicemesh-go/pkg/configmanager"
	"github.com/chungzy/servicemesh-go/pkg/fault"
	"github.com/chungzy/servicemesh-go/pkg/gossip"
	"github.com/chungzy/servicemesh-go/pkg/hotrestart"
	"github.com/chungzy/servicemesh-go/pkg/loadbalancer"
	"github.com/chungzy/servicemesh-go/pkg/metrics"
	"github.com/chungzy/servicemesh-go/pkg/proxy"
	"go.uber.org/zap"
)

// App represents the main application
type App struct {
	config           *config.Config
	logger           *zap.Logger
	proxyServer      *proxy.Server
	loadBalancer     *loadbalancer.LoadBalancer
	circuitBreaker   *circuitbreaker.Manager
	metricsCollector *metrics.Collector
	gossipManager    *gossip.Manager
	faultManager     *fault.Manager
	configManager    *ConfigManager
	newConfigManager configmanager.ConfigManager
	hotRestartMgr    *hotrestart.Manager
	wg               sync.WaitGroup
	ctx              context.Context
	cancel           context.CancelFunc
}

// NewApp creates a new application instance
func NewApp(cfg *config.Config, logger *zap.Logger) (*App, error) {
	ctx, cancel := context.WithCancel(context.Background())

	app := &App{
		config: cfg,
		logger: logger,
		ctx:    ctx,
		cancel: cancel,
	}

	if err := app.initializeComponents(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize components: %w", err)
	}

	return app, nil
}

// initializeComponents initializes all application components
func (a *App) initializeComponents() error {
	// Initialize metrics collector
	a.metricsCollector = metrics.NewCollector()

	// Initialize load balancer
	a.loadBalancer = loadbalancer.NewLoadBalancer(a.config, a.logger)

	// Initialize circuit breaker manager
	a.circuitBreaker = circuitbreaker.NewManager(&a.config.Circuit, a.logger)

	// Initialize fault injection manager
	a.faultManager = fault.NewManager(&a.config.Fault, a.logger)

	// Initialize gossip manager
	a.gossipManager = gossip.NewManager(&a.config.Gossip, a.logger)

	// Initialize config manager (legacy)
	a.configManager = NewConfigManager(a.config, a.logger, a.loadBalancer, a.gossipManager)

	// Initialize new config manager
	managerConfig := configmanager.ManagerConfig{
		EventBufferSize:     1000,
		CacheTTL:            5 * time.Minute,
		HealthCheckInterval: 30 * time.Second,
		MaxRetries:          3,
		RetryDelay:          time.Second,
	}
	a.newConfigManager = configmanager.NewDefaultConfigManager(a.logger, managerConfig)

	// Initialize hot restart manager
	pidFile := "/tmp/servicemesh.pid"
	gracefulStop := 30 * time.Second
	a.hotRestartMgr = hotrestart.NewManager(a.logger, pidFile, gracefulStop)

	// Initialize proxy server
	a.proxyServer = proxy.NewServer(a.config, a.logger, a.loadBalancer, a.metricsCollector)

	// Set hot restart manager for proxy server
	a.proxyServer.SetHotRestartManager(a.hotRestartMgr)

	// Register proxy server with hot restart manager
	a.hotRestartMgr.RegisterServer(a.proxyServer)

	a.logger.Info("All components initialized successfully")
	return nil
}

// Start starts the application
func (a *App) Start(ctx context.Context) error {
	a.logger.Info("Starting ServiceMesh application")

	// Start gossip manager
	if err := a.gossipManager.Start(a.ctx); err != nil {
		return fmt.Errorf("failed to start gossip manager: %w", err)
	}

	// Start config manager
	if err := a.configManager.Start(a.ctx); err != nil {
		return fmt.Errorf("failed to start config manager: %w", err)
	}

	// Start circuit breaker monitoring
	a.wg.Add(1)
	go func() {
		defer a.wg.Done()
		a.circuitBreaker.StartMonitoring(a.ctx)
	}()

	// Start load balancer health checking
	a.wg.Add(1)
	go func() {
		defer a.wg.Done()
		a.loadBalancer.StartHealthCheck(a.ctx)
	}()

	// Start fault injection monitoring
	a.wg.Add(1)
	go func() {
		defer a.wg.Done()
		a.faultManager.StartMonitoring(a.ctx)
	}()

	// Start proxy server
	a.wg.Add(1)
	go func() {
		defer a.wg.Done()
		if err := a.proxyServer.Start(a.ctx); err != nil {
			a.logger.Error("Proxy server error", zap.Error(err))
		}
	}()

	a.logger.Info("ServiceMesh application started successfully")
	return nil
}

// Stop stops the application
func (a *App) Stop(ctx context.Context) error {
	a.logger.Info("Stopping ServiceMesh application")

	// Cancel context to stop all goroutines
	a.cancel()

	// Stop proxy server
	if err := a.proxyServer.Stop(ctx); err != nil {
		a.logger.Error("Error stopping proxy server", zap.Error(err))
	}

	// Stop gossip manager
	if err := a.gossipManager.Stop(); err != nil {
		a.logger.Error("Error stopping gossip manager", zap.Error(err))
	}

	// Stop config manager
	if err := a.configManager.Stop(); err != nil {
		a.logger.Error("Error stopping config manager", zap.Error(err))
	}

	// Wait for all goroutines to finish
	done := make(chan struct{})
	go func() {
		a.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		a.logger.Info("ServiceMesh application stopped successfully")
	case <-ctx.Done():
		a.logger.Warn("Shutdown timeout exceeded")
	}

	return nil
}

// GetLoadBalancer returns the load balancer instance
func (a *App) GetLoadBalancer() *loadbalancer.LoadBalancer {
	return a.loadBalancer
}

// GetCircuitBreaker returns the circuit breaker manager
func (a *App) GetCircuitBreaker() *circuitbreaker.Manager {
	return a.circuitBreaker
}

// GetMetricsCollector returns the metrics collector
func (a *App) GetMetricsCollector() *metrics.Collector {
	return a.metricsCollector
}

// GetFaultManager returns the fault injection manager
func (a *App) GetFaultManager() *fault.Manager {
	return a.faultManager
}

// GetGossipManager returns the gossip manager
func (a *App) GetGossipManager() *gossip.Manager {
	return a.gossipManager
}

// GetConfigManager returns the config manager
func (a *App) GetConfigManager() *ConfigManager {
	return a.configManager
}

// AddService adds a new service to the mesh
func (a *App) AddService(serviceConfig *config.ServiceConfig) error {
	// Update load balancer
	a.loadBalancer.UpdateServiceConfig(serviceConfig)

	// Broadcast configuration update via gossip
	if err := a.gossipManager.BroadcastUpdate("service", serviceConfig.Name, serviceConfig); err != nil {
		a.logger.Error("Failed to broadcast service update",
			zap.String("service", serviceConfig.Name),
			zap.Error(err))
	}

	a.logger.Info("Service added to mesh", zap.String("service", serviceConfig.Name))
	return nil
}

// RemoveService removes a service from the mesh
func (a *App) RemoveService(serviceName string) error {
	// Remove from load balancer
	a.loadBalancer.RemoveService(serviceName)

	// Broadcast removal via gossip
	if err := a.gossipManager.BroadcastUpdate("service_remove", serviceName, nil); err != nil {
		a.logger.Error("Failed to broadcast service removal",
			zap.String("service", serviceName),
			zap.Error(err))
	}

	a.logger.Info("Service removed from mesh", zap.String("service", serviceName))
	return nil
}

// AddBackend adds a backend to a service
func (a *App) AddBackend(serviceName, host string, port, weight int) error {
	if err := a.loadBalancer.AddBackend(serviceName, host, port, weight); err != nil {
		return err
	}

	// Broadcast backend update via gossip
	backendInfo := map[string]interface{}{
		"service": serviceName,
		"host":    host,
		"port":    port,
		"weight":  weight,
	}

	if err := a.gossipManager.BroadcastUpdate("backend_add", serviceName, backendInfo); err != nil {
		a.logger.Error("Failed to broadcast backend addition",
			zap.String("service", serviceName),
			zap.String("backend", fmt.Sprintf("%s:%d", host, port)),
			zap.Error(err))
	}

	return nil
}

// RemoveBackend removes a backend from a service
func (a *App) RemoveBackend(serviceName, address string) error {
	if err := a.loadBalancer.RemoveBackend(serviceName, address); err != nil {
		return err
	}

	// Broadcast backend removal via gossip
	backendInfo := map[string]interface{}{
		"service": serviceName,
		"address": address,
	}

	if err := a.gossipManager.BroadcastUpdate("backend_remove", serviceName, backendInfo); err != nil {
		a.logger.Error("Failed to broadcast backend removal",
			zap.String("service", serviceName),
			zap.String("backend", address),
			zap.Error(err))
	}

	return nil
}

// AddFaultRule adds a fault injection rule
func (a *App) AddFaultRule(serviceName string, rule *fault.FaultRule) {
	a.faultManager.AddRule(serviceName, rule)

	// Broadcast fault rule via gossip
	if err := a.gossipManager.BroadcastUpdate("fault_rule", serviceName, rule); err != nil {
		a.logger.Error("Failed to broadcast fault rule",
			zap.String("service", serviceName),
			zap.String("rule_id", rule.ID),
			zap.Error(err))
	}
}

// RemoveFaultRule removes a fault injection rule
func (a *App) RemoveFaultRule(serviceName, ruleID string) {
	a.faultManager.RemoveRule(serviceName, ruleID)

	// Broadcast fault rule removal via gossip
	ruleInfo := map[string]interface{}{
		"service": serviceName,
		"rule_id": ruleID,
	}

	if err := a.gossipManager.BroadcastUpdate("fault_rule_remove", serviceName, ruleInfo); err != nil {
		a.logger.Error("Failed to broadcast fault rule removal",
			zap.String("service", serviceName),
			zap.String("rule_id", ruleID),
			zap.Error(err))
	}
}
