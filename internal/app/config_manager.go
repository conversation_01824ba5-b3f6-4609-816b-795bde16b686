package app

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/chungzy/servicemesh-go/pkg/gossip"
	"github.com/chungzy/servicemesh-go/pkg/loadbalancer"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// ConfigManager manages configuration updates from multiple sources
type ConfigManager struct {
	config        *config.Config
	logger        *zap.Logger
	loadBalancer  *loadbalancer.LoadBalancer
	gossipManager *gossip.Manager
	redisClient   *redis.Client
	services      map[string]*config.ServiceConfig
	mu            sync.RWMutex
	updateChan    chan ConfigUpdate
	ctx           context.Context
	cancel        context.CancelFunc
}

// ConfigUpdate represents a configuration update
type ConfigUpdate struct {
	Type      string      `json:"type"`
	Name      string      `json:"name"`
	Data      interface{} `json:"data"`
	Source    string      `json:"source"`
	Timestamp time.Time   `json:"timestamp"`
}

// NewConfigManager creates a new configuration manager
func NewConfigManager(cfg *config.Config, logger *zap.Logger, lb *loadbalancer.LoadBalancer, gm *gossip.Manager) *ConfigManager {
	ctx, cancel := context.WithCancel(context.Background())

	cm := &ConfigManager{
		config:        cfg,
		logger:        logger,
		loadBalancer:  lb,
		gossipManager: gm,
		services:      make(map[string]*config.ServiceConfig),
		updateChan:    make(chan ConfigUpdate, 100),
		ctx:           ctx,
		cancel:        cancel,
	}

	// Initialize Redis client if configured
	if cfg.Redis.Addr != "" {
		cm.redisClient = redis.NewClient(&redis.Options{
			Addr:     cfg.Redis.Addr,
			Password: cfg.Redis.Password,
			DB:       cfg.Redis.DB,
		})
	}

	return cm
}

// Start starts the configuration manager
func (cm *ConfigManager) Start(ctx context.Context) error {
	cm.logger.Info("Starting configuration manager")

	// Load initial configuration from Redis
	if cm.redisClient != nil {
		if err := cm.loadInitialConfig(); err != nil {
			cm.logger.Error("Failed to load initial config from Redis", zap.Error(err))
		}
	}

	// Subscribe to gossip updates
	if cm.gossipManager != nil {
		go cm.handleGossipUpdates()
	}

	// Start periodic Redis sync
	if cm.redisClient != nil {
		go cm.periodicRedisSync()
	}

	// Start config update processor
	go cm.processConfigUpdates()

	cm.logger.Info("Configuration manager started")
	return nil
}

// Stop stops the configuration manager
func (cm *ConfigManager) Stop() error {
	cm.logger.Info("Stopping configuration manager")

	cm.cancel()

	if cm.redisClient != nil {
		cm.redisClient.Close()
	}

	cm.logger.Info("Configuration manager stopped")
	return nil
}

// loadInitialConfig loads initial configuration from Redis
func (cm *ConfigManager) loadInitialConfig() error {
	cm.logger.Info("Loading initial configuration from Redis")

	// Load services
	if err := cm.loadServicesFromRedis(); err != nil {
		return fmt.Errorf("failed to load services: %w", err)
	}

	cm.logger.Info("Initial configuration loaded successfully")
	return nil
}

// loadServicesFromRedis loads service configurations from Redis
func (cm *ConfigManager) loadServicesFromRedis() error {
	ctx, cancel := context.WithTimeout(cm.ctx, 10*time.Second)
	defer cancel()

	// Get all service keys
	keys, err := cm.redisClient.Keys(ctx, "service:*").Result()
	if err != nil {
		return err
	}

	for _, key := range keys {
		data, err := cm.redisClient.Get(ctx, key).Result()
		if err != nil {
			cm.logger.Error("Failed to get service config", zap.String("key", key), zap.Error(err))
			continue
		}

		var serviceConfig config.ServiceConfig
		if err := json.Unmarshal([]byte(data), &serviceConfig); err != nil {
			cm.logger.Error("Failed to unmarshal service config", zap.String("key", key), zap.Error(err))
			continue
		}

		cm.updateServiceConfig(&serviceConfig, "redis")
	}

	return nil
}

// handleGossipUpdates handles configuration updates from gossip
func (cm *ConfigManager) handleGossipUpdates() {
	serviceChan := cm.gossipManager.Subscribe("service")

	for {
		select {
		case <-cm.ctx.Done():
			return
		case update := <-serviceChan:
			cm.handleGossipServiceUpdate(update)
		}
	}
}

// handleGossipServiceUpdate handles service updates from gossip
func (cm *ConfigManager) handleGossipServiceUpdate(update *gossip.ConfigUpdate) {
	data, err := json.Marshal(update.Data)
	if err != nil {
		cm.logger.Error("Failed to marshal gossip service update", zap.Error(err))
		return
	}

	var serviceConfig config.ServiceConfig
	if err := json.Unmarshal(data, &serviceConfig); err != nil {
		cm.logger.Error("Failed to unmarshal gossip service update", zap.Error(err))
		return
	}

	cm.updateServiceConfig(&serviceConfig, "gossip")
}

// periodicRedisSync performs periodic synchronization with Redis
func (cm *ConfigManager) periodicRedisSync() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-cm.ctx.Done():
			return
		case <-ticker.C:
			if err := cm.syncWithRedis(); err != nil {
				cm.logger.Error("Failed to sync with Redis", zap.Error(err))
			}
		}
	}
}

// syncWithRedis synchronizes configuration with Redis
func (cm *ConfigManager) syncWithRedis() error {
	ctx, cancel := context.WithTimeout(cm.ctx, 30*time.Second)
	defer cancel()

	// Check for service updates
	if err := cm.checkServiceUpdates(ctx); err != nil {
		return fmt.Errorf("failed to check service updates: %w", err)
	}

	return nil
}

// checkServiceUpdates checks for service configuration updates in Redis
func (cm *ConfigManager) checkServiceUpdates(ctx context.Context) error {
	keys, err := cm.redisClient.Keys(ctx, "service:*").Result()
	if err != nil {
		return err
	}

	for _, key := range keys {
		serviceName := key[8:] // Remove "service:" prefix

		// For simplicity, always reload the configuration
		// In production, you might want to use Redis WATCH or timestamps
		data, err := cm.redisClient.Get(ctx, key).Result()
		if err != nil {
			cm.logger.Error("Failed to get service config",
				zap.String("service", serviceName), zap.Error(err))
			continue
		}

		var serviceConfig config.ServiceConfig
		if err := json.Unmarshal([]byte(data), &serviceConfig); err != nil {
			cm.logger.Error("Failed to unmarshal service config",
				zap.String("service", serviceName), zap.Error(err))
			continue
		}

		cm.updateServiceConfig(&serviceConfig, "redis")
	}

	return nil
}

// processConfigUpdates processes configuration updates
func (cm *ConfigManager) processConfigUpdates() {
	for {
		select {
		case <-cm.ctx.Done():
			return
		case update := <-cm.updateChan:
			cm.processConfigUpdate(update)
		}
	}
}

// processConfigUpdate processes a single configuration update
func (cm *ConfigManager) processConfigUpdate(update ConfigUpdate) {
	cm.logger.Debug("Processing config update",
		zap.String("type", update.Type),
		zap.String("name", update.Name),
		zap.String("source", update.Source))

	switch update.Type {
	case "service":
		// Service configuration has been updated
		cm.logger.Info("Service configuration updated",
			zap.String("service", update.Name),
			zap.String("source", update.Source))
	case "bns":
		// BNS configuration has been updated
		cm.logger.Info("BNS configuration updated",
			zap.String("bns", update.Name),
			zap.String("source", update.Source))
	}
}

// updateServiceConfig updates a service configuration
func (cm *ConfigManager) updateServiceConfig(serviceConfig *config.ServiceConfig, source string) {
	cm.mu.Lock()
	cm.services[serviceConfig.Name] = serviceConfig
	cm.mu.Unlock()

	// Update load balancer
	cm.loadBalancer.UpdateServiceConfig(serviceConfig)

	// Send update notification
	update := ConfigUpdate{
		Type:      "service",
		Name:      serviceConfig.Name,
		Data:      serviceConfig,
		Source:    source,
		Timestamp: time.Now(),
	}

	select {
	case cm.updateChan <- update:
	default:
		cm.logger.Warn("Config update channel full, dropping update")
	}
}

// GetService returns a service configuration
func (cm *ConfigManager) GetService(name string) (*config.ServiceConfig, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	service, exists := cm.services[name]
	return service, exists
}

// GetAllServices returns all service configurations
func (cm *ConfigManager) GetAllServices() map[string]*config.ServiceConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	services := make(map[string]*config.ServiceConfig)
	for name, service := range cm.services {
		services[name] = service
	}

	return services
}
