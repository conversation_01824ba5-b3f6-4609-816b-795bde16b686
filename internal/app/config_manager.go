package app

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/chungzy/servicemesh-go/pkg/gossip"
	"github.com/chungzy/servicemesh-go/pkg/loadbalancer"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// ConfigManager manages configuration updates from multiple sources
type ConfigManager struct {
	config        *config.Config
	logger        *zap.Logger
	loadBalancer  *loadbalancer.LoadBalancer
	gossipManager *gossip.Manager
	redisClient   *redis.Client
	services      map[string]*config.ServiceConfig
	bnss          map[string]*config.BNSConfig
	mu            sync.RWMutex
	updateChan    chan ConfigUpdate
	ctx           context.Context
	cancel        context.CancelFunc
}

// ConfigUpdate represents a configuration update
type ConfigUpdate struct {
	Type      string      `json:"type"`
	Name      string      `json:"name"`
	Data      interface{} `json:"data"`
	Source    string      `json:"source"`
	Timestamp time.Time   `json:"timestamp"`
}

// NewConfigManager creates a new configuration manager
func NewConfigManager(cfg *config.Config, logger *zap.Logger, lb *loadbalancer.LoadBalancer, gm *gossip.Manager) *ConfigManager {
	ctx, cancel := context.WithCancel(context.Background())

	cm := &ConfigManager{
		config:        cfg,
		logger:        logger,
		loadBalancer:  lb,
		gossipManager: gm,
		services:      make(map[string]*config.ServiceConfig),
		bnss:          make(map[string]*config.BNSConfig),
		updateChan:    make(chan ConfigUpdate, 100),
		ctx:           ctx,
		cancel:        cancel,
	}

	// Initialize Redis client if configured
	if cfg.Redis.Addr != "" {
		cm.redisClient = redis.NewClient(&redis.Options{
			Addr:     cfg.Redis.Addr,
			Password: cfg.Redis.Password,
			DB:       cfg.Redis.DB,
		})
	}

	return cm
}

// Start starts the configuration manager
func (cm *ConfigManager) Start(ctx context.Context) error {
	cm.logger.Info("Starting configuration manager")

	// Load initial configuration from Redis
	if cm.redisClient != nil {
		if err := cm.loadInitialConfig(); err != nil {
			cm.logger.Error("Failed to load initial config from Redis", zap.Error(err))
		}
	}

	// Subscribe to gossip updates
	if cm.gossipManager != nil {
		go cm.handleGossipUpdates()
	}

	// Start periodic Redis sync
	if cm.redisClient != nil {
		go cm.periodicRedisSync()
	}

	// Start config update processor
	go cm.processConfigUpdates()

	cm.logger.Info("Configuration manager started")
	return nil
}

// Stop stops the configuration manager
func (cm *ConfigManager) Stop() error {
	cm.logger.Info("Stopping configuration manager")

	cm.cancel()

	if cm.redisClient != nil {
		cm.redisClient.Close()
	}

	cm.logger.Info("Configuration manager stopped")
	return nil
}

// loadInitialConfig loads initial configuration from Redis
func (cm *ConfigManager) loadInitialConfig() error {
	cm.logger.Info("Loading initial configuration from Redis")

	// Load services
	if err := cm.loadServicesFromRedis(); err != nil {
		return fmt.Errorf("failed to load services: %w", err)
	}

	// Load BNS configurations
	if err := cm.loadBNSFromRedis(); err != nil {
		return fmt.Errorf("failed to load BNS configs: %w", err)
	}

	cm.logger.Info("Initial configuration loaded successfully")
	return nil
}

// loadServicesFromRedis loads service configurations from Redis
func (cm *ConfigManager) loadServicesFromRedis() error {
	ctx, cancel := context.WithTimeout(cm.ctx, 10*time.Second)
	defer cancel()

	// Get all service keys
	keys, err := cm.redisClient.Keys(ctx, "service:*").Result()
	if err != nil {
		return err
	}

	for _, key := range keys {
		data, err := cm.redisClient.Get(ctx, key).Result()
		if err != nil {
			cm.logger.Error("Failed to get service config", zap.String("key", key), zap.Error(err))
			continue
		}

		var serviceConfig config.ServiceConfig
		if err := json.Unmarshal([]byte(data), &serviceConfig); err != nil {
			cm.logger.Error("Failed to unmarshal service config", zap.String("key", key), zap.Error(err))
			continue
		}

		cm.updateServiceConfig(&serviceConfig, "redis")
	}

	return nil
}

// loadBNSFromRedis loads BNS configurations from Redis
func (cm *ConfigManager) loadBNSFromRedis() error {
	ctx, cancel := context.WithTimeout(cm.ctx, 10*time.Second)
	defer cancel()

	// Get all BNS keys
	keys, err := cm.redisClient.Keys(ctx, "bns:*").Result()
	if err != nil {
		return err
	}

	for _, key := range keys {
		data, err := cm.redisClient.Get(ctx, key).Result()
		if err != nil {
			cm.logger.Error("Failed to get BNS config", zap.String("key", key), zap.Error(err))
			continue
		}

		var bnsConfig config.BNSConfig
		if err := json.Unmarshal([]byte(data), &bnsConfig); err != nil {
			cm.logger.Error("Failed to unmarshal BNS config", zap.String("key", key), zap.Error(err))
			continue
		}

		cm.updateBNSConfig(&bnsConfig, "redis")
	}

	return nil
}

// handleGossipUpdates handles configuration updates from gossip
func (cm *ConfigManager) handleGossipUpdates() {
	serviceChan := cm.gossipManager.Subscribe("service")
	bnsChain := cm.gossipManager.Subscribe("bns")

	for {
		select {
		case <-cm.ctx.Done():
			return
		case update := <-serviceChan:
			cm.handleGossipServiceUpdate(update)
		case update := <-bnsChain:
			cm.handleGossipBNSUpdate(update)
		}
	}
}

// handleGossipServiceUpdate handles service updates from gossip
func (cm *ConfigManager) handleGossipServiceUpdate(update *gossip.ConfigUpdate) {
	data, err := json.Marshal(update.Data)
	if err != nil {
		cm.logger.Error("Failed to marshal gossip service update", zap.Error(err))
		return
	}

	var serviceConfig config.ServiceConfig
	if err := json.Unmarshal(data, &serviceConfig); err != nil {
		cm.logger.Error("Failed to unmarshal gossip service update", zap.Error(err))
		return
	}

	cm.updateServiceConfig(&serviceConfig, "gossip")
}

// handleGossipBNSUpdate handles BNS updates from gossip
func (cm *ConfigManager) handleGossipBNSUpdate(update *gossip.ConfigUpdate) {
	data, err := json.Marshal(update.Data)
	if err != nil {
		cm.logger.Error("Failed to marshal gossip BNS update", zap.Error(err))
		return
	}

	var bnsConfig config.BNSConfig
	if err := json.Unmarshal(data, &bnsConfig); err != nil {
		cm.logger.Error("Failed to unmarshal gossip BNS update", zap.Error(err))
		return
	}

	cm.updateBNSConfig(&bnsConfig, "gossip")
}

// periodicRedisSync performs periodic synchronization with Redis
func (cm *ConfigManager) periodicRedisSync() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-cm.ctx.Done():
			return
		case <-ticker.C:
			if err := cm.syncWithRedis(); err != nil {
				cm.logger.Error("Failed to sync with Redis", zap.Error(err))
			}
		}
	}
}

// syncWithRedis synchronizes configuration with Redis
func (cm *ConfigManager) syncWithRedis() error {
	ctx, cancel := context.WithTimeout(cm.ctx, 30*time.Second)
	defer cancel()

	// Check for service updates
	if err := cm.checkServiceUpdates(ctx); err != nil {
		return fmt.Errorf("failed to check service updates: %w", err)
	}

	// Check for BNS updates
	if err := cm.checkBNSUpdates(ctx); err != nil {
		return fmt.Errorf("failed to check BNS updates: %w", err)
	}

	return nil
}

// checkServiceUpdates checks for service configuration updates in Redis
func (cm *ConfigManager) checkServiceUpdates(ctx context.Context) error {
	keys, err := cm.redisClient.Keys(ctx, "service:*").Result()
	if err != nil {
		return err
	}

	for _, key := range keys {
		serviceName := key[8:] // Remove "service:" prefix

		// For simplicity, always reload the configuration
		// In production, you might want to use Redis WATCH or timestamps
		data, err := cm.redisClient.Get(ctx, key).Result()
		if err != nil {
			cm.logger.Error("Failed to get service config",
				zap.String("service", serviceName), zap.Error(err))
			continue
		}

		var serviceConfig config.ServiceConfig
		if err := json.Unmarshal([]byte(data), &serviceConfig); err != nil {
			cm.logger.Error("Failed to unmarshal service config",
				zap.String("service", serviceName), zap.Error(err))
			continue
		}

		cm.updateServiceConfig(&serviceConfig, "redis")
	}

	return nil
}

// checkBNSUpdates checks for BNS configuration updates in Redis
func (cm *ConfigManager) checkBNSUpdates(ctx context.Context) error {
	keys, err := cm.redisClient.Keys(ctx, "bns:*").Result()
	if err != nil {
		return err
	}

	for _, key := range keys {
		// Similar logic to checkServiceUpdates but for BNS configs
		bnsName := key[4:] // Remove "bns:" prefix

		data, err := cm.redisClient.Get(ctx, key).Result()
		if err != nil {
			cm.logger.Error("Failed to get BNS config", zap.String("bns", bnsName), zap.Error(err))
			continue
		}

		var bnsConfig config.BNSConfig
		if err := json.Unmarshal([]byte(data), &bnsConfig); err != nil {
			cm.logger.Error("Failed to unmarshal BNS config", zap.String("bns", bnsName), zap.Error(err))
			continue
		}

		cm.updateBNSConfig(&bnsConfig, "redis")
	}

	return nil
}

// processConfigUpdates processes configuration updates
func (cm *ConfigManager) processConfigUpdates() {
	for {
		select {
		case <-cm.ctx.Done():
			return
		case update := <-cm.updateChan:
			cm.processConfigUpdate(update)
		}
	}
}

// processConfigUpdate processes a single configuration update
func (cm *ConfigManager) processConfigUpdate(update ConfigUpdate) {
	cm.logger.Debug("Processing config update",
		zap.String("type", update.Type),
		zap.String("name", update.Name),
		zap.String("source", update.Source))

	switch update.Type {
	case "service":
		// Service configuration has been updated
		cm.logger.Info("Service configuration updated",
			zap.String("service", update.Name),
			zap.String("source", update.Source))
	case "bns":
		// BNS configuration has been updated
		cm.logger.Info("BNS configuration updated",
			zap.String("bns", update.Name),
			zap.String("source", update.Source))
	}
}

// updateServiceConfig updates a service configuration
func (cm *ConfigManager) updateServiceConfig(serviceConfig *config.ServiceConfig, source string) {
	cm.mu.Lock()
	cm.services[serviceConfig.Name] = serviceConfig
	cm.mu.Unlock()

	// Update load balancer
	cm.loadBalancer.UpdateServiceConfig(serviceConfig)

	// Send update notification
	update := ConfigUpdate{
		Type:      "service",
		Name:      serviceConfig.Name,
		Data:      serviceConfig,
		Source:    source,
		Timestamp: time.Now(),
	}

	select {
	case cm.updateChan <- update:
	default:
		cm.logger.Warn("Config update channel full, dropping update")
	}
}

// updateBNSConfig updates a BNS configuration
func (cm *ConfigManager) updateBNSConfig(bnsConfig *config.BNSConfig, source string) {
	cm.mu.Lock()
	cm.bnss[bnsConfig.Name] = bnsConfig
	cm.mu.Unlock()

	// Update backends in load balancer based on BNS configuration
	cm.updateBackendsFromBNS(bnsConfig)

	// Send update notification
	update := ConfigUpdate{
		Type:      "bns",
		Name:      bnsConfig.Name,
		Data:      bnsConfig,
		Source:    source,
		Timestamp: time.Now(),
	}

	select {
	case cm.updateChan <- update:
	default:
		cm.logger.Warn("Config update channel full, dropping update")
	}
}

// updateBackendsFromBNS updates backends in load balancer based on BNS configuration
func (cm *ConfigManager) updateBackendsFromBNS(bnsConfig *config.BNSConfig) {
	// Find services that use this BNS
	cm.mu.RLock()
	affectedServices := make([]*config.ServiceConfig, 0)
	for _, service := range cm.services {
		if _, exists := service.BNSList[bnsConfig.Name]; exists {
			affectedServices = append(affectedServices, service)
		}
	}
	cm.mu.RUnlock()

	// Update backends for affected services
	for _, service := range affectedServices {
		for _, node := range bnsConfig.BNSNodes {
			if err := cm.loadBalancer.AddBackend(service.Name, node.Host, node.Port, node.Weight); err != nil {
				cm.logger.Error("Failed to add backend",
					zap.String("service", service.Name),
					zap.String("backend", fmt.Sprintf("%s:%d", node.Host, node.Port)),
					zap.Error(err))
			}
		}
	}
}

// GetService returns a service configuration
func (cm *ConfigManager) GetService(name string) (*config.ServiceConfig, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	service, exists := cm.services[name]
	return service, exists
}

// GetBNS returns a BNS configuration
func (cm *ConfigManager) GetBNS(name string) (*config.BNSConfig, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	bns, exists := cm.bnss[name]
	return bns, exists
}

// GetAllServices returns all service configurations
func (cm *ConfigManager) GetAllServices() map[string]*config.ServiceConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	services := make(map[string]*config.ServiceConfig)
	for name, service := range cm.services {
		services[name] = service
	}

	return services
}

// GetAllBNS returns all BNS configurations
func (cm *ConfigManager) GetAllBNS() map[string]*config.BNSConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	bnss := make(map[string]*config.BNSConfig)
	for name, bns := range cm.bnss {
		bnss[name] = bns
	}

	return bnss
}
