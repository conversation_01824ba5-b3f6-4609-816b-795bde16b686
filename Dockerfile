# Build stage
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o servicemesh ./cmd/servicemesh

# Final stage
FROM alpine:latest

# Install runtime dependencies
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1001 servicemesh && \
    adduser -D -u 1001 -G servicemesh servicemesh

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/servicemesh .

# Copy configuration files
COPY --from=builder /app/configs ./configs

# Change ownership
RUN chown -R servicemesh:servicemesh /app

# Switch to non-root user
USER servicemesh

# Expose ports
EXPOSE 8080 7946 9090

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./servicemesh", "-config", "configs/config.yaml"]
