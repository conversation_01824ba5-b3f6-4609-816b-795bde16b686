#!/bin/bash
pwd=`pwd`

#openssl 编译需要用到高版本的perl，需要从源码编译
#如果指定编译器版本perl会编译失败，因此先不指定，等perl编译完之后再指定
#注意：openresty 1.1.1以上版本（包括1.1.1可能在获取随机数信息时导致nginx在某些机器启动卡死）
#openresty 1.1.1 c 编译时，需要把所有perl脚本中 qw/glob/ 替换成 qw/:glob/ 要不编译不过（可能和perl版本语法有关系）
perl_version=perl-5.32.0
mkdir perl
tar -zxvf extra_files/thirdlib/$perl_version.tar.gz
cd $perl_version
./configure.gnu -des -Dprefix=$pwd/perl
make -j4 && make install
#指定使用我们从源码编译的perl版本
export PATH=$pwd/perl/bin:$PATH
cd $pwd

export PATH=/opt/compiler/gcc-4.8.2/bin:$PATH

openresty_tar=`find . -name "openresty-*.tar.gz" | sort | tail -1`
if [ "x"$openresty_tar == "x" ]
then
    echo "no openresty found, exit ..."
    exit -1
fi
openresty_version=`echo $openresty_tar|awk -F "-" '{print $2}'|awk -F ".tar.gz" '{print $1}'`
f_version=`echo $openresty_version|awk -F "." '{print $1}'`
s_version=`echo $openresty_version|awk -F "." '{print $2}'`

if [ "x"$f_version == "x" ]
then
    echo "get openresty first version fail"
    exit -1
fi
if [ "x"$s_version == "x" ]
then
    echo "get openresty second version fail"
    exit -1
fi

if [ $f_version -lt 1 ]
then
    echo "openresty version at least 1.13"
    exit -1
fi
if [ $f_version -eq 1 ]
then
    if [ $s_version -lt 13 ]
    then
        echo "openresty version at least 1.13"
        exit -1
    fi
fi

tar xzvf $openresty_tar
openresty_src="openresty-"$openresty_version


# as ldconfig is here
export PATH=$PATH:/sbin/

# thirdlib version
pcre_version=8.37
openssl=openssl-OpenSSL_1_1_0c

mkdir -p build
mkdir -p thirdlib-build
# copy thirdlib
cp --preserve -r extra_files/thirdlib/* thirdlib-build
cd thirdlib-build && tar -zxvf $openssl.tar.gz

cd $pwd

#升级gcc版本的时候可能会出现pcre原configure文件执行报错的情况
#重新使用autoconf 生成一份configure文件即可
#http://www.linuxcoming.com/blog/2019/08/01/gnu_autotools_tutorials.html
pcre_path=`pwd`/thirdlib-build/pcre-$pcre_version
cd $pcre_path
rm -rf configure
autoreconf --install

cd $pwd

# build openresty
rm -rf output
mkdir -p output
cd $openresty_src
ngx_http_request_c=`find . -name "ngx_http_request.c"`
if [ "x"$ngx_http_request_c == "x" ]
then
    echo "ngx_http_request_c file not found, please check it manualy, maybe patch need to be re write"
    exit -1
fi
patch $ngx_http_request_c ../extra_files/ngx_http_request_transfer_encoding.patch
./configure --with-luajit --prefix=$pwd/output --with-luajit-xcflags='-std=gnu99 -DLUAJIT_USE_PERFTOOLS=1 -DLUAJIT_NUMMODE=2' --with-openssl=$pwd/thirdlib-build/$openssl --with-pcre=$pwd/thirdlib-build/pcre-$pcre_version --with-pcre-jit
make -j12
make install

cd $pwd
chash_path=`pwd`/extra_files/thirdlib/lua-resty-balancer
cd $chash_path
make -f Makefile

cd $pwd
# copy conf file and control file
cp extra_files/nginx.conf* output/nginx/conf/   # 包含多个配置
cp extra_files/start.sh output/nginx/start.sh
cp extra_files/stop.sh output/nginx/stop.sh
cp extra_files/restart.sh output/nginx/restart.sh

cp -r extra_files/thirdlib/lua-resty-balancer/lib/resty/* ../wwwdata/ufc/resty/
mkdir -p ../wwwdata/ufc/luaso
cp extra_files/thirdlib/lua-resty-balancer/librestychash.so ../wwwdata/ufc/luaso/

mv output/nginx/sbin/nginx output/nginx/sbin/nginx_ufc

rm -rf build
rm -rf thirdlib-build
rm -rf $openresty_src

# change dir struct
mv output/nginx output/nginx_ufc
mv output/* output/nginx_ufc

rm output/nginx_ufc/luajit/bin/luajit
cp output/nginx_ufc/luajit/bin/luajit-2.1.0-beta1 output/nginx_ufc/luajit/bin/luajit
rm output/nginx_ufc/luajit/lib/libluajit-5.1.so.2
cp output/nginx_ufc/luajit/lib/libluajit-5.1.so.2.1.0 output/nginx_ufc/luajit/lib/libluajit-5.1.so.2
cp output/nginx_ufc/luajit/lib/libluajit-5.1.so.2.1.0 output/nginx_ufc/luajit/lib/libluajit-5.1.so
mkdir -p output/nginx_ufc/logs

cp -r ../wwwdata/ufc output/nginx_ufc/
mkdir -p output/noahdes
cp extra_files/operation_list_restart_nginx_ufc output/noahdes
cp extra_files/nginx_ufc_control output/nginx_ufc
chmod +x output/nginx_ufc/nginx_ufc_control
cp extra_files/operation_list_restart output/noahdes
cp extra_files/operation_list_start output/noahdes
cp extra_files/operation_list_stop output/noahdes
cp extra_files/operation_list_update output/noahdes
cp extra_files/noah_control output/nginx_ufc/bin
chmod +x output/nginx_ufc/bin/noah_control
mv output/nginx_ufc/* output/
rm -rf nginx_ufc
sed -i "s:ngx.log(ngx.DEBUG:--ngx.log(ngx.DEBUG:g" output/ufc/*.lua
v=`git log|head -1|awk '{print $2}'`
v=${v:0:7}
sed -i "s:git_version:$v:" output/ufc/conf.lua

#根据shell脚本参数判断是沙盒代码还是线上代码
#沙盒代码用一份精简的本地配置
if [ $1 == "sandbox" ]
then
    sed -i "s:\"sandbox_flag\":true:" output/ufc/conf.lua
    rm output/ufc/meta_cache/conf_cache_file_back
    mv output/ufc/meta_cache/conf_cache_file_back_sandbox output/ufc/meta_cache/conf_cache_file_back
else
    sed -i "s:\"sandbox_flag\":false:" output/ufc/conf.lua
    rm output/ufc/meta_cache/conf_cache_file_back_sandbox
fi

#把ebpf代码产出放进去
cp -rf ../ebpf output/

# 产出包链接的 output.tar.gz 需要改成 ufc-watch-dog-output.tar.gz
wget -O ufc-watch-dog-output.tar.gz --no-check-certificate --header "IREPO-TOKEN:fe4f7fea-4dac-45a9-8443-9d04b60db3dc" "https://irepo.baidu-int.com/rest/prod/v3/baidu/netdisk/ufc-watchdog-agent/releases/1.0.2.1/files"
mkdir -p watchdog-output
tar -zxvf ufc-watch-dog-output.tar.gz -C watchdog-output
mv watchdog-output/output output/watchdog

