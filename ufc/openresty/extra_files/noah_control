#!/bin/sh
BIN_DIR=`dirname "$0"`
BIN_DIR=`cd "${BIN_DIR}"/../; pwd`

version() {
  version=`md5sum ${BIN_DIR}/mammoth-worker/bin/worker-v2-mammoth | awk '{print $1}'`
  echo $version
  exit 0
}

status() {
   nc -zv 127.0.0.1 8240
   if [[ $? -eq 0 ]]; then
        exit 0
   fi
   exit 2
}

start() {
    cd /home/<USER>/nginx_ufc
    /bin/bash /home/<USER>/nginx_ufc/restart.sh
}

restart() {
    cd /home/<USER>/nginx_ufc
    /bin/bash /home/<USER>/nginx_ufc/restart.sh
}

stop() {
    cd /home/<USER>/nginx_ufc
    /bin/bash /home/<USER>/nginx_ufc/stop.sh
}

case "$1" in
    version)
        version
    ;;
    status)
        status
    ;;
    start)
        restart
    ;;
    restart)
        restart
    ;;
    reload)
        reload
    ;;
    stop)
        stop
    ;;
    *)
        echo "Usage: $0 {version|status|start|restart|reload|stop}"
    ;;
esac