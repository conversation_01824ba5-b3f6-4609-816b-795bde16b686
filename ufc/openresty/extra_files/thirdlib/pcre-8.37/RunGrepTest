#! /bin/sh

# Run pcregrep tests. The assumption is that the PCRE tests check the library
# itself. What we are checking here is the file handling and options that are
# supported by pcregrep. This script must be run in the build directory.

# Set the C locale, so that sort(1) behaves predictably.

LC_ALL=C
export LC_ALL

# Remove any non-default colouring and aliases that the caller may have set.

unset PCREGREP_COLOUR PCREGREP_COLOR
unset cp ls mv rm

# Remember the current (build) directory, set the program to be tested, and
# valgrind settings when requested.

builddir=`pwd`
pcregrep=$builddir/pcregrep

valgrind=
while [ $# -gt 0 ] ; do
  case $1 in
    valgrind) valgrind="valgrind -q --leak-check=no --smc-check=all";;
    *) echo "RunGrepTest: Unknown argument $1"; exit 1;;
  esac
  shift
done

echo " "
pcregrep_version=`$pcregrep -V`
if [ "$valgrind" = "" ] ; then
  echo "Testing $pcregrep_version"
else
  echo "Testing $pcregrep_version using valgrind"
fi

# Set up a suitable "diff" command for comparison. Some systems have a diff
# that lacks a -u option. Try to deal with this; better do the test for the -b
# option as well.

cf="diff"
diff -b  /dev/null /dev/null 2>/dev/null && cf="diff -b"
diff -u  /dev/null /dev/null 2>/dev/null && cf="diff -u"
diff -ub /dev/null /dev/null 2>/dev/null && cf="diff -ub"

# If this test is being run from "make check", $srcdir will be set. If not, set
# it to the current or parent directory, whichever one contains the test data.
# Subsequently, we run most of the pcregrep tests in the source directory so
# that the file names in the output are always the same.

if [ -z "$srcdir" -o ! -d "$srcdir/testdata" ] ; then
  if [ -d "./testdata" ] ; then
    srcdir=.
  elif [ -d "../testdata" ] ; then
    srcdir=..
  else
    echo "Cannot find the testdata directory"
    exit 1
  fi
fi

# Check for the availability of UTF-8 support

./pcretest -C utf >/dev/null
utf8=$?

echo "Testing pcregrep main features"

echo "---------------------------- Test 1 ------------------------------" >testtrygrep
(cd $srcdir; $valgrind $pcregrep PATTERN ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 2 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep '^PATTERN' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 3 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -in PATTERN ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 4 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -ic PATTERN ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 5 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -in PATTERN ./testdata/grepinput ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 6 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -inh PATTERN ./testdata/grepinput ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 7 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -il PATTERN ./testdata/grepinput ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 8 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -l PATTERN ./testdata/grepinput ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 9 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -q PATTERN ./testdata/grepinput ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 10 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -q NEVER-PATTERN ./testdata/grepinput ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 11 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -vn pattern ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 12 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -ix pattern ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 13 -----------------------------" >>testtrygrep
echo seventeen >testtemp1grep
(cd $srcdir; $valgrind $pcregrep -f./testdata/greplist -f $builddir/testtemp1grep ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 14 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -w pat ./testdata/grepinput ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 15 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep 'abc^*' ./testdata/grepinput) 2>>testtrygrep >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 16 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep abc ./testdata/grepinput ./testdata/nonexistfile) 2>>testtrygrep >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 17 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -M 'the\noutput' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 18 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -Mn '(the\noutput|dog\.\n--)' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 19 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -Mix 'Pattern' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 20 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -Mixn 'complete pair\nof lines' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 21 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -nA3 'four' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 22 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -nB3 'four' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 23 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -C3 'four' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 24 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -A9 'four' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 25 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -nB9 'four' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 26 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -A9 -B9 'four' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 27 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -A10 'four' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 28 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -nB10 'four' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 29 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -C12 -B10 'four' ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 30 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -inB3 'pattern' ./testdata/grepinput ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 31 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -inA3 'pattern' ./testdata/grepinput ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 32 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -L 'fox' ./testdata/grepinput ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 33 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep 'fox' ./testdata/grepnonexist) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 34 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -s 'fox' ./testdata/grepnonexist) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 35 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -L -r --include=grepinputx --include grepinput8 --exclude-dir='^\.' 'fox' ./testdata | sort) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 36 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -L -r --include=grepinput --exclude 'grepinput$' --exclude=grepinput8 --exclude-dir='^\.' 'fox' ./testdata | sort) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 37 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep  '^(a+)*\d' ./testdata/grepinput) >>testtrygrep 2>teststderrgrep
echo "RC=$?" >>testtrygrep
echo "======== STDERR ========" >>testtrygrep
cat teststderrgrep >>testtrygrep

echo "---------------------------- Test 38 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep '>\x00<' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 39 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -A1 'before the binary zero' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 40 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -B1 'after the binary zero' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 41 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -B1 -o '\w+ the binary zero' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 42 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -B1 -onH '\w+ the binary zero' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 43 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -on 'before|zero|after' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 44 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -on -e before -ezero -e after ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 45 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -on -f ./testdata/greplist -e binary ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 46 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -eabc -e '(unclosed' ./testdata/grepinput) 2>>testtrygrep >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 47 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -Fx "AB.VE
elephant" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 48 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -F "AB.VE
elephant" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 49 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -F -e DATA -e "AB.VE
elephant" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 50 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep "^(abc|def|ghi|jkl)" ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 51 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -Mv "brown\sfox" ./testdata/grepinputv) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 52 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --colour=always jumps ./testdata/grepinputv) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 53 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --file-offsets 'before|zero|after' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 54 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --line-offsets 'before|zero|after' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 55 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -f./testdata/greplist --color=always ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 56 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -c lazy ./testdata/grepinput*) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 57 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -c -l lazy ./testdata/grepinput*) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 58 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --regex=PATTERN ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 59 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --regexp=PATTERN ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 60 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --regex PATTERN ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 61 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --regexp PATTERN ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 62 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --match-limit=1000 --no-jit -M 'This is a file(.|\R)*file.' ./testdata/grepinput) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 63 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --recursion-limit=1000 --no-jit -M 'This is a file(.|\R)*file.' ./testdata/grepinput) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 64 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o1 '(?<=PAT)TERN (ap(pear)s)' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 65 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o2 '(?<=PAT)TERN (ap(pear)s)' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 66 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o3 '(?<=PAT)TERN (ap(pear)s)' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 67 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o12 '(?<=PAT)TERN (ap(pear)s)' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 68 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --only-matching=2 '(?<=PAT)TERN (ap(pear)s)' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 69 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -vn --colour=always pattern ./testdata/grepinputx) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 70 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --color=always -M "triple:\t.*\n\n" ./testdata/grepinput3) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 71 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o "^01|^02|^03" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 72 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --color=always "^01|^02|^03" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 73 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o --colour=always "^01|^02|^03" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 74 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o "^01|02|^03" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 75 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --color=always "^01|02|^03" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 76 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o --colour=always "^01|02|^03" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 77 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o "^01|^02|03" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 78 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --color=always "^01|^02|03" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 79 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o --colour=always "^01|^02|03" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 80 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o "\b01|\b02" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 81 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --color=always "\\b01|\\b02" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 82 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o --colour=always "\\b01|\\b02" ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 83 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --buffer-size=100 "^a" ./testdata/grepinput3) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 84 -----------------------------" >>testtrygrep
echo testdata/grepinput3 >testtemp1grep
(cd $srcdir; $valgrind $pcregrep --file-list ./testdata/grepfilelist --file-list $builddir/testtemp1grep "fox|complete|t7") >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 85 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --file-list=./testdata/grepfilelist "dolor" ./testdata/grepinput3) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 86 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep "dog" ./testdata/grepbinary) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 87 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep "cat" ./testdata/grepbinary) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 88 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -v "cat" ./testdata/grepbinary) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 89 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -I "dog" ./testdata/grepbinary) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 90 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --binary-files=without-match "dog" ./testdata/grepbinary) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 91 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -a "dog" ./testdata/grepbinary) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 92 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --binary-files=text "dog" ./testdata/grepbinary) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 93 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --text "dog" ./testdata/grepbinary) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 94 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -L -r --include=grepinputx --include grepinput8 'fox' ./testdata/grepinput* | sort) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 95 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --file-list ./testdata/grepfilelist --exclude grepinputv "fox|complete") >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 96 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -L -r --include-dir=testdata --exclude '^(?!grepinput)' 'fox' ./test* | sort) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 97 -----------------------------" >>testtrygrep
echo "grepinput$" >testtemp1grep
echo "grepinput8" >>testtemp1grep
(cd $srcdir; $valgrind $pcregrep -L -r --include=grepinput --exclude-from $builddir/testtemp1grep --exclude-dir='^\.' 'fox' ./testdata | sort) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 98 -----------------------------" >>testtrygrep
echo "grepinput$" >testtemp1grep
echo "grepinput8" >>testtemp1grep
(cd $srcdir; $valgrind $pcregrep -L -r --exclude=grepinput3 --include=grepinput --exclude-from $builddir/testtemp1grep --exclude-dir='^\.' 'fox' ./testdata | sort) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 99 -----------------------------" >>testtrygrep
echo "grepinput$" >testtemp1grep
echo "grepinput8" >testtemp2grep
(cd $srcdir; $valgrind $pcregrep -L -r --include grepinput --exclude-from $builddir/testtemp1grep --exclude-from=$builddir/testtemp2grep --exclude-dir='^\.' 'fox' ./testdata | sort) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 100 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -Ho2 --only-matching=1 -o3 '(\w+) binary (\w+)(\.)?' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 101 ------------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -o3 -Ho2 -o12 --only-matching=1 -o3 --colour=always --om-separator='|' '(\w+) binary (\w+)(\.)?' ./testdata/grepinput) >>testtrygrep
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 102 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -n "^$" ./testdata/grepinput3) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 103 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --only-matching "^$" ./testdata/grepinput3) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 104 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep -n --only-matching "^$" ./testdata/grepinput3) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 105 -----------------------------" >>testtrygrep
(cd $srcdir; $valgrind $pcregrep --colour=always "ipsum|" ./testdata/grepinput3) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 106 -----------------------------" >>testtrygrep
(cd $srcdir; echo "a" | $valgrind $pcregrep -M "|a" ) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

echo "---------------------------- Test 107 -----------------------------" >>testtrygrep
echo "a" >testtemp1grep
echo "aaaaa" >>testtemp1grep
(cd $srcdir; $valgrind $pcregrep  --line-offsets '(?<=\Ka)' $builddir/testtemp1grep) >>testtrygrep 2>&1
echo "RC=$?" >>testtrygrep

# Now compare the results.

$cf $srcdir/testdata/grepoutput testtrygrep
if [ $? != 0 ] ; then exit 1; fi


# These tests require UTF-8 support

if [ $utf8 -ne 0 ] ; then
  echo "Testing pcregrep UTF-8 features"

  echo "---------------------------- Test U1 ------------------------------" >testtrygrep
  (cd $srcdir; $valgrind $pcregrep -n -u --newline=any "^X" ./testdata/grepinput8) >>testtrygrep
  echo "RC=$?" >>testtrygrep

  echo "---------------------------- Test U2 ------------------------------" >>testtrygrep
  (cd $srcdir; $valgrind $pcregrep -n -u -C 3 --newline=any "Match" ./testdata/grepinput8) >>testtrygrep
  echo "RC=$?" >>testtrygrep

  $cf $srcdir/testdata/grepoutput8 testtrygrep
  if [ $? != 0 ] ; then exit 1; fi

else
  echo "Skipping pcregrep UTF-8 tests: no UTF-8 support in PCRE library"
fi


# We go to some contortions to try to ensure that the tests for the various
# newline settings will work in environments where the normal newline sequence
# is not \n. Do not use exported files, whose line endings might be changed.
# Instead, create an input file using printf so that its contents are exactly
# what we want. Note the messy fudge to get printf to write a string that
# starts with a hyphen. These tests are run in the build directory.

echo "Testing pcregrep newline settings"
printf "abc\rdef\r\nghi\njkl" >testNinputgrep

printf "%c--------------------------- Test N1 ------------------------------\r\n" - >testtrygrep
$valgrind $pcregrep -n -N CR "^(abc|def|ghi|jkl)" testNinputgrep >>testtrygrep

printf "%c--------------------------- Test N2 ------------------------------\r\n" - >>testtrygrep
$valgrind $pcregrep -n --newline=crlf "^(abc|def|ghi|jkl)" testNinputgrep >>testtrygrep

printf "%c--------------------------- Test N3 ------------------------------\r\n" - >>testtrygrep
pattern=`printf 'def\rjkl'`
$valgrind $pcregrep -n --newline=cr -F "$pattern" testNinputgrep >>testtrygrep

printf "%c--------------------------- Test N4 ------------------------------\r\n" - >>testtrygrep
$valgrind $pcregrep -n --newline=crlf -F -f $srcdir/testdata/greppatN4 testNinputgrep >>testtrygrep

printf "%c--------------------------- Test N5 ------------------------------\r\n" - >>testtrygrep
$valgrind $pcregrep -n --newline=any "^(abc|def|ghi|jkl)" testNinputgrep >>testtrygrep

printf "%c--------------------------- Test N6 ------------------------------\r\n" - >>testtrygrep
$valgrind $pcregrep -n --newline=anycrlf "^(abc|def|ghi|jkl)" testNinputgrep >>testtrygrep

$cf $srcdir/testdata/grepoutputN testtrygrep
if [ $? != 0 ] ; then exit 1; fi

exit 0

# End
