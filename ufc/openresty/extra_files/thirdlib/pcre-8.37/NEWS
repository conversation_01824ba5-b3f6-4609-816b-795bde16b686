News about PCRE releases
------------------------

Release 8.37 28-April-2015
--------------------------

This is bug-fix release. Note that this library (now called PCRE1) is now being
maintained for bug fixes only. New projects are advised to use the new PCRE2
libraries.


Release 8.36 26-September-2014
------------------------------

This is primarily a bug-fix release. However, in addition, the Unicode data
tables have been updated to Unicode 7.0.0.


Release 8.35 04-April-2014
--------------------------

There have been performance improvements for classes containing non-ASCII
characters and the "auto-possessification" feature has been extended. Other
minor improvements have been implemented and bugs fixed. There is a new callout
feature to enable applications to do detailed stack checks at compile time, to
avoid running out of stack for deeply nested parentheses. The JIT compiler has
been extended with experimental support for ARM-64, MIPS-64, and PPC-LE.


Release 8.34 15-December-2013
-----------------------------

As well as fixing the inevitable bugs, performance has been improved by
refactoring and extending the amount of "auto-possessification" that PCRE does.
Other notable changes:

.  Implemented PCRE_INFO_MATCH_EMPTY, which yields 1 if the pattern can match
   an empty string. If it can, pcretest shows this in its information output.

.  A back reference to a named subpattern when there is more than one of the
   same name now checks them in the order in which they appear in the pattern.
   The first one that is set is used for the reference. Previously only the
   first one was inspected. This change makes PCRE more compatible with Perl.

.  Unicode character properties were updated from Unicode 6.3.0.

.  The character VT has been added to the set of characters that match \s and
   are generally treated as white space, following this same change in Perl
   5.18. There is now no difference between "Perl space" and "POSIX space".

.  Perl has changed its handling of \8 and \9. If there is no previously
   encountered capturing group of those numbers, they are treated as the
   literal characters 8 and 9 instead of a binary zero followed by the
   literals. PCRE now does the same.

.  Following Perl, added \o{} to specify codepoints in octal, making it
   possible to specify values greater than 0777 and also making them
   unambiguous.

.  In UCP mode, \s was not matching two of the characters that Perl matches,
   namely NEL (U+0085) and MONGOLIAN VOWEL SEPARATOR (U+180E), though they
   were matched by \h.

.  Add JIT support for the 64 bit TileGX architecture.

.  Upgraded the handling of the POSIX classes [:graph:], [:print:], and
   [:punct:] when PCRE_UCP is set so as to include the same characters as Perl
   does in Unicode mode.

.  Perl no longer allows group names to start with digits, so I have made this
   change also in PCRE.

.  Added support for [[:<:]] and [[:>:]] as used in the BSD POSIX library to
   mean "start of word" and "end of word", respectively, as a transition aid.


Release 8.33 28-May-2013
--------------------------

A number of bugs are fixed, and some performance improvements have been made.
There are also some new features, of which these are the most important:

.  The behaviour of the backtracking verbs has been rationalized and
   documented in more detail.

.  JIT now supports callouts and all of the backtracking verbs.

.  Unicode validation has been updated in the light of Unicode Corrigendum #9,
   which points out that "non characters" are not "characters that may not
   appear in Unicode strings" but rather "characters that are reserved for
   internal use and have only local meaning".

.  (*LIMIT_MATCH=d) and (*LIMIT_RECURSION=d) have been added so that the
   creator of a pattern can specify lower (but not higher) limits for the
   matching process.

.  The PCRE_NEVER_UTF option is available to prevent pattern-writers from using
   the (*UTF) feature, as this could be a security issue.


Release 8.32 30-November-2012
-----------------------------

This release fixes a number of bugs, but also has some new features. These are
the highlights:

.  There is now support for 32-bit character strings and UTF-32. Like the
   16-bit support, this is done by compiling a separate 32-bit library.

.  \X now matches a Unicode extended grapheme cluster.

.  Case-independent matching of Unicode characters that have more than one
   "other case" now makes all three (or more) characters equivalent. This
   applies, for example, to Greek Sigma, which has two lowercase versions.

.  Unicode character properties are updated to Unicode 6.2.0.

.  The EBCDIC support, which had decayed, has had a spring clean.

.  A number of JIT optimizations have been added, which give faster JIT
   execution speed. In addition, a new direct interface to JIT execution is
   available. This bypasses some of the sanity checks of pcre_exec() to give a
   noticeable speed-up.

.  A number of issues in pcregrep have been fixed, making it more compatible
   with GNU grep. In particular, --exclude and --include (and variants) apply
   to all files now, not just those obtained from scanning a directory
   recursively. In Windows environments, the default action for directories is
   now "skip" instead of "read" (which provokes an error).

.  If the --only-matching (-o) option in pcregrep is specified multiple
   times, each one causes appropriate output. For example, -o1 -o2 outputs the
   substrings matched by the 1st and 2nd capturing parentheses. A separating
   string can be specified by --om-separator (default empty).

.  When PCRE is built via Autotools using a version of gcc that has the
   "visibility" feature, it is used to hide internal library functions that are
   not part of the public API.


Release 8.31 06-July-2012
-------------------------

This is mainly a bug-fixing release, with a small number of developments:

. The JIT compiler now supports partial matching and the (*MARK) and
  (*COMMIT) verbs.

. PCRE_INFO_MAXLOOKBEHIND can be used to find the longest lookbehind in a
  pattern.

. There should be a performance improvement when using the heap instead of the
  stack for recursion.

. pcregrep can now be linked with libedit as an alternative to libreadline.

. pcregrep now has a --file-list option where the list of files to scan is
  given as a file.

. pcregrep now recognizes binary files and there are related options.

. The Unicode tables have been updated to 6.1.0.

As always, the full list of changes is in the ChangeLog file.


Release 8.30 04-February-2012
-----------------------------

Release 8.30 introduces a major new feature: support for 16-bit character
strings, compiled as a separate library. There are a few changes to the
8-bit library, in addition to some bug fixes.

. The pcre_info() function, which has been obsolete for over 10 years, has
  been removed.

. When a compiled pattern was saved to a file and later reloaded on a host
  with different endianness, PCRE used automatically to swap the bytes in some
  of the data fields. With the advent of the 16-bit library, where more of this
  swapping is needed, it is no longer done automatically. Instead, the bad
  endianness is detected and a specific error is given. The user can then call
  a new function called pcre_pattern_to_host_byte_order() (or an equivalent
  16-bit function) to do the swap.

. In UTF-8 mode, the values 0xd800 to 0xdfff are not legal Unicode
  code points and are now faulted. (They are the so-called "surrogates"
  that are reserved for coding high values in UTF-16.)


Release 8.21 12-Dec-2011
------------------------

This is almost entirely a bug-fix release. The only new feature is the ability
to obtain the size of the memory used by the JIT compiler.


Release 8.20 21-Oct-2011
------------------------

The main change in this release is the inclusion of Zoltan Herczeg's
just-in-time compiler support, which can be accessed by building PCRE with
--enable-jit. Large performance benefits can be had in many situations. 8.20
also fixes an unfortunate bug that was introduced in 8.13 as well as tidying up
a number of infelicities and differences from Perl.


Release 8.13 16-Aug-2011
------------------------

This is mainly a bug-fix release. There has been a lot of internal refactoring.
The Unicode tables have been updated. The only new feature in the library is
the passing of *MARK information to callouts. Some additions have been made to
pcretest to make testing easier and more comprehensive. There is a new option
for pcregrep to adjust its internal buffer size.


Release 8.12 15-Jan-2011
------------------------

This release fixes some bugs in pcregrep, one of which caused the tests to fail
on 64-bit big-endian systems. There are no changes to the code of the library.


Release 8.11 10-Dec-2010
------------------------

A number of bugs in the library and in pcregrep have been fixed. As always, see
ChangeLog for details. The following are the non-bug-fix changes:

. Added --match-limit and --recursion-limit to pcregrep.

. Added an optional parentheses number to the -o and --only-matching options
  of pcregrep.

. Changed the way PCRE_PARTIAL_HARD affects the matching of $, \z, \Z, \b, and
  \B.

. Added PCRE_ERROR_SHORTUTF8 to make it possible to distinguish between a
  bad UTF-8 sequence and one that is incomplete when using PCRE_PARTIAL_HARD.

. Recognize (*NO_START_OPT) at the start of a pattern to set the PCRE_NO_
  START_OPTIMIZE option, which is now allowed at compile time


Release 8.10 25-Jun-2010
------------------------

There are two major additions: support for (*MARK) and friends, and the option
PCRE_UCP, which changes the behaviour of \b, \d, \s, and \w (and their
opposites) so that they make use of Unicode properties. There are also a number
of lesser new features, and several bugs have been fixed. A new option,
--line-buffered, has been added to pcregrep, for use when it is connected to
pipes.


Release 8.02 19-Mar-2010
------------------------

Another bug-fix release.


Release 8.01 19-Jan-2010
------------------------

This is a bug-fix release. Several bugs in the code itself and some bugs and
infelicities in the build system have been fixed.


Release 8.00 19-Oct-09
----------------------

Bugs have been fixed in the library and in pcregrep. There are also some
enhancements. Restrictions on patterns used for partial matching have been
removed, extra information is given for partial matches, the partial matching
process has been improved, and an option to make a partial match override a
full match is available. The "study" process has been enhanced by finding a
lower bound matching length. Groups with duplicate numbers may now have
duplicated names without the use of PCRE_DUPNAMES. However, they may not have
different names. The documentation has been revised to reflect these changes.
The version number has been expanded to 3 digits as it is clear that the rate
of change is not slowing down.


Release 7.9 11-Apr-09
---------------------

Mostly bugfixes and tidies with just a couple of minor functional additions.


Release 7.8 05-Sep-08
---------------------

More bug fixes, plus a performance improvement in Unicode character property
lookup.


Release 7.7 07-May-08
---------------------

This is once again mainly a bug-fix release, but there are a couple of new
features.


Release 7.6 28-Jan-08
---------------------

The main reason for having this release so soon after 7.5 is because it fixes a
potential buffer overflow problem in pcre_compile() when run in UTF-8 mode. In
addition, the CMake configuration files have been brought up to date.


Release 7.5 10-Jan-08
---------------------

This is mainly a bug-fix release. However the ability to link pcregrep with
libz or libbz2 and the ability to link pcretest with libreadline have been
added. Also the --line-offsets and --file-offsets options were added to
pcregrep.


Release 7.4 21-Sep-07
---------------------

The only change of specification is the addition of options to control whether
\R matches any Unicode line ending (the default) or just CR, LF, and CRLF.
Otherwise, the changes are bug fixes and a refactoring to reduce the number of
relocations needed in a shared library. There have also been some documentation
updates, in particular, some more information about using CMake to build PCRE
has been added to the NON-UNIX-USE file.


Release 7.3 28-Aug-07
---------------------

Most changes are bug fixes. Some that are not:

1. There is some support for Perl 5.10's experimental "backtracking control
   verbs" such as (*PRUNE).

2. UTF-8 checking is now as per RFC 3629 instead of RFC 2279; this is more
   restrictive in the strings it accepts.

3. Checking for potential integer overflow has been made more dynamic, and as a
   consequence there is no longer a hard limit on the size of a subpattern that
   has a limited repeat count.

4. When CRLF is a valid line-ending sequence, pcre_exec() and pcre_dfa_exec()
   no longer advance by two characters instead of one when an unanchored match
   fails at CRLF if there are explicit CR or LF matches within the pattern.
   This gets rid of some anomalous effects that previously occurred.

5. Some PCRE-specific settings for varying the newline options at the start of
   a pattern have been added.


Release 7.2 19-Jun-07
---------------------

WARNING: saved patterns that were compiled by earlier versions of PCRE must be
recompiled for use with 7.2 (necessitated by the addition of \K, \h, \H, \v,
and \V).

Correction to the notes for 7.1: the note about shared libraries for Windows is
wrong. Previously, three libraries were built, but each could function
independently. For example, the pcreposix library also included all the
functions from the basic pcre library. The change is that the three libraries
are no longer independent. They are like the Unix libraries. To use the
pcreposix functions, for example, you need to link with both the pcreposix and
the basic pcre library.

Some more features from Perl 5.10 have been added:

  (?-n) and (?+n) relative references for recursion and subroutines.

  (?(-n) and (?(+n) relative references as conditions.

  \k{name} and \g{name} are synonyms for \k<name>.

  \K to reset the start of the matched string; for example, (foo)\Kbar
  matches bar preceded by foo, but only sets bar as the matched string.

  (?| introduces a group where the capturing parentheses in each alternative
  start from the same number; for example, (?|(abc)|(xyz)) sets capturing
  parentheses number 1 in both cases.

  \h, \H, \v, \V match horizontal and vertical whitespace, respectively.


Release 7.1 24-Apr-07
---------------------

There is only one new feature in this release: a linebreak setting of
PCRE_NEWLINE_ANYCRLF. It is a cut-down version of PCRE_NEWLINE_ANY, which
recognizes only CRLF, CR, and LF as linebreaks.

A few bugs are fixed (see ChangeLog for details), but the major change is a
complete re-implementation of the build system. This now has full Autotools
support and so is now "standard" in some sense. It should help with compiling
PCRE in a wide variety of environments.

NOTE: when building shared libraries for Windows, three dlls are now built,
called libpcre, libpcreposix, and libpcrecpp. Previously, everything was
included in a single dll.

Another important change is that the dftables auxiliary program is no longer
compiled and run at "make" time by default. Instead, a default set of character
tables (assuming ASCII coding) is used. If you want to use dftables to generate
the character tables as previously, add --enable-rebuild-chartables to the
"configure" command. You must do this if you are compiling PCRE to run on a
system that uses EBCDIC code.

There is a discussion about character tables in the README file. The default is
not to use dftables so that that there is no problem when cross-compiling.


Release 7.0 19-Dec-06
---------------------

This release has a new major number because there have been some internal
upheavals to facilitate the addition of new optimizations and other facilities,
and to make subsequent maintenance and extension easier. Compilation is likely
to be a bit slower, but there should be no major effect on runtime performance.
Previously compiled patterns are NOT upwards compatible with this release. If
you have saved compiled patterns from a previous release, you will have to
re-compile them. Important changes that are visible to users are:

1. The Unicode property tables have been updated to Unicode 5.0.0, which adds
   some more scripts.

2. The option PCRE_NEWLINE_ANY causes PCRE to recognize any Unicode newline
   sequence as a newline.

3. The \R escape matches a single Unicode newline sequence as a single unit.

4. New features that will appear in Perl 5.10 are now in PCRE. These include
   alternative Perl syntax for named parentheses, and Perl syntax for
   recursion.

5. The C++ wrapper interface has been extended by the addition of a
   QuoteMeta function and the ability to allow copy construction and
   assignment.

For a complete list of changes, see the ChangeLog file.


Release 6.7 04-Jul-06
---------------------

The main additions to this release are the ability to use the same name for
multiple sets of parentheses, and support for CRLF line endings in both the
library and pcregrep (and in pcretest for testing).

Thanks to Ian Taylor, the stack usage for many kinds of pattern has been
significantly reduced for certain subject strings.


Release 6.5 01-Feb-06
---------------------

Important changes in this release:

1. A number of new features have been added to pcregrep.

2. The Unicode property tables have been updated to Unicode 4.1.0, and the
   supported properties have been extended with script names such as "Arabic",
   and the derived properties "Any" and "L&". This has necessitated a change to
   the interal format of compiled patterns. Any saved compiled patterns that
   use \p or \P must be recompiled.

3. The specification of recursion in patterns has been changed so that all
   recursive subpatterns are automatically treated as atomic groups. Thus, for
   example, (?R) is treated as if it were (?>(?R)). This is necessary because
   otherwise there are situations where recursion does not work.

See the ChangeLog for a complete list of changes, which include a number of bug
fixes and tidies.


Release 6.0 07-Jun-05
---------------------

The release number has been increased to 6.0 because of the addition of several
major new pieces of functionality.

A new function, pcre_dfa_exec(), which implements pattern matching using a DFA
algorithm, has been added. This has a number of advantages for certain cases,
though it does run more slowly, and lacks the ability to capture substrings. On
the other hand, it does find all matches, not just the first, and it works
better for partial matching. The pcrematching man page discusses the
differences.

The pcretest program has been enhanced so that it can make use of the new
pcre_dfa_exec() matching function and the extra features it provides.

The distribution now includes a C++ wrapper library. This is built
automatically if a C++ compiler is found. The pcrecpp man page discusses this
interface.

The code itself has been re-organized into many more files, one for each
function, so it no longer requires everything to be linked in when static
linkage is used. As a consequence, some internal functions have had to have
their names exposed. These functions all have names starting with _pcre_. They
are undocumented, and are not intended for use by outside callers.

The pcregrep program has been enhanced with new functionality such as
multiline-matching and options for output more matching context. See the
ChangeLog for a complete list of changes to the library and the utility
programs.


Release 5.0 13-Sep-04
---------------------

The licence under which PCRE is released has been changed to the more
conventional "BSD" licence.

In the code, some bugs have been fixed, and there are also some major changes
in this release (which is why I've increased the number to 5.0). Some changes
are internal rearrangements, and some provide a number of new facilities. The
new features are:

1. There's an "automatic callout" feature that inserts callouts before every
   item in the regex, and there's a new callout field that gives the position
   in the pattern - useful for debugging and tracing.

2. The extra_data structure can now be used to pass in a set of character
   tables at exec time. This is useful if compiled regex are saved and re-used
   at a later time when the tables may not be at the same address. If the
   default internal tables are used, the pointer saved with the compiled
   pattern is now set to NULL, which means that you don't need to do anything
   special unless you are using custom tables.

3. It is possible, with some restrictions on the content of the regex, to
   request "partial" matching. A special return code is given if all of the
   subject string matched part of the regex. This could be useful for testing
   an input field as it is being typed.

4. There is now some optional support for Unicode character properties, which
   means that the patterns items such as \p{Lu} and \X can now be used. Only
   the general category properties are supported. If PCRE is compiled with this
   support, an additional 90K data structure is include, which increases the
   size of the library dramatically.

5. There is support for saving compiled patterns and re-using them later.

6. There is support for running regular expressions that were compiled on a
   different host with the opposite endianness.

7. The pcretest program has been extended to accommodate the new features.

The main internal rearrangement is that sequences of literal characters are no
longer handled as strings. Instead, each character is handled on its own. This
makes some UTF-8 handling easier, and makes the support of partial matching
possible. Compiled patterns containing long literal strings will be larger as a
result of this change; I hope that performance will not be much affected.


Release 4.5 01-Dec-03
---------------------

Again mainly a bug-fix and tidying release, with only a couple of new features:

1. It's possible now to compile PCRE so that it does not use recursive
function calls when matching. Instead it gets memory from the heap. This slows
things down, but may be necessary on systems with limited stacks.

2. UTF-8 string checking has been tightened to reject overlong sequences and to
check that a starting offset points to the start of a character. Failure of the
latter returns a new error code: PCRE_ERROR_BADUTF8_OFFSET.

3. PCRE can now be compiled for systems that use EBCDIC code.


Release 4.4 21-Aug-03
---------------------

This is mainly a bug-fix and tidying release. The only new feature is that PCRE
checks UTF-8 strings for validity by default. There is an option to suppress
this, just in case anybody wants that teeny extra bit of performance.


Releases 4.1 - 4.3
------------------

Sorry, I forgot about updating the NEWS file for these releases. Please take a
look at ChangeLog.


Release 4.0 17-Feb-03
---------------------

There have been a lot of changes for the 4.0 release, adding additional
functionality and mending bugs. Below is a list of the highlights of the new
functionality. For full details of these features, please consult the
documentation. For a complete list of changes, see the ChangeLog file.

1. Support for Perl's \Q...\E escapes.

2. "Possessive quantifiers" ?+, *+, ++, and {,}+ which come from Sun's Java
package. They provide some syntactic sugar for simple cases of "atomic
grouping".

3. Support for the \G assertion. It is true when the current matching position
is at the start point of the match.

4. A new feature that provides some of the functionality that Perl provides
with (?{...}). The facility is termed a "callout". The way it is done in PCRE
is for the caller to provide an optional function, by setting pcre_callout to
its entry point. To get the function called, the regex must include (?C) at
appropriate points.

5. Support for recursive calls to individual subpatterns. This makes it really
easy to get totally confused.

6. Support for named subpatterns. The Python syntax (?P<name>...) is used to
name a group.

7. Several extensions to UTF-8 support; it is now fairly complete. There is an
option for pcregrep to make it operate in UTF-8 mode.

8. The single man page has been split into a number of separate man pages.
These also give rise to individual HTML pages which are put in a separate
directory. There is an index.html page that lists them all. Some hyperlinking
between the pages has been installed.


Release 3.5 15-Aug-01
---------------------

1. The configuring system has been upgraded to use later versions of autoconf
and libtool. By default it builds both a shared and a static library if the OS
supports it. You can use --disable-shared or --disable-static on the configure
command if you want only one of them.

2. The pcretest utility is now installed along with pcregrep because it is
useful for users (to test regexs) and by doing this, it automatically gets
relinked by libtool. The documentation has been turned into a man page, so
there are now .1, .txt, and .html versions in /doc.

3. Upgrades to pcregrep:
   (i)   Added long-form option names like gnu grep.
   (ii)  Added --help to list all options with an explanatory phrase.
   (iii) Added -r, --recursive to recurse into sub-directories.
   (iv)  Added -f, --file to read patterns from a file.

4. Added --enable-newline-is-cr and --enable-newline-is-lf to the configure
script, to force use of CR or LF instead of \n in the source. On non-Unix
systems, the value can be set in config.h.

5. The limit of 200 on non-capturing parentheses is a _nesting_ limit, not an
absolute limit. Changed the text of the error message to make this clear, and
likewise updated the man page.

6. The limit of 99 on the number of capturing subpatterns has been removed.
The new limit is 65535, which I hope will not be a "real" limit.


Release 3.3 01-Aug-00
---------------------

There is some support for UTF-8 character strings. This is incomplete and
experimental. The documentation describes what is and what is not implemented.
Otherwise, this is just a bug-fixing release.


Release 3.0 01-Feb-00
---------------------

1. A "configure" script is now used to configure PCRE for Unix systems. It
builds a Makefile, a config.h file, and the pcre-config script.

2. PCRE is built as a shared library by default.

3. There is support for POSIX classes such as [:alpha:].

5. There is an experimental recursion feature.

----------------------------------------------------------------------------
          IMPORTANT FOR THOSE UPGRADING FROM VERSIONS BEFORE 2.00

Please note that there has been a change in the API such that a larger
ovector is required at matching time, to provide some additional workspace.
The new man page has details. This change was necessary in order to support
some of the new functionality in Perl 5.005.

          IMPORTANT FOR THOSE UPGRADING FROM VERSION 2.00

Another (I hope this is the last!) change has been made to the API for the
pcre_compile() function. An additional argument has been added to make it
possible to pass over a pointer to character tables built in the current
locale by pcre_maketables(). To use the default tables, this new argument
should be passed as NULL.

          IMPORTANT FOR THOSE UPGRADING FROM VERSION 2.05

Yet another (and again I hope this really is the last) change has been made
to the API for the pcre_exec() function. An additional argument has been
added to make it possible to start the match other than at the start of the
subject string. This is important if there are lookbehinds. The new man
page has the details, but you just want to convert existing programs, all
you need to do is to stick in a new fifth argument to pcre_exec(), with a
value of zero. For example, change

  pcre_exec(pattern, extra, subject, length, options, ovec, ovecsize)
to
  pcre_exec(pattern, extra, subject, length, 0, options, ovec, ovecsize)

****
