.TH PCRE_EXEC 3 "31 October 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.nf
.B int pcre_jit_exec(const pcre *\fIcode\fP, "const pcre_extra *\fIextra\fP,"
.B "     const char *\fIsubject\fP, int \fIlength\fP, int \fIstartoffset\fP,"
.B "     int \fIoptions\fP, int *\fIovector\fP, int \fIovecsize\fP,"
.B "     pcre_jit_stack *\fIjstack\fP);"
.sp
.B int pcre16_jit_exec(const pcre16 *\fIcode\fP, "const pcre16_extra *\fIextra\fP,"
.B "     PCRE_SPTR16 \fIsubject\fP, int \fIlength\fP, int \fIstartoffset\fP,"
.B "     int \fIoptions\fP, int *\fIovector\fP, int \fIovecsize\fP,"
.B "     pcre_jit_stack *\fIjstack\fP);"
.sp
.B int pcre32_jit_exec(const pcre32 *\fIcode\fP, "const pcre32_extra *\fIextra\fP,"
.B "     PCRE_SPTR32 \fIsubject\fP, int \fIlength\fP, int \fIstartoffset\fP,"
.B "     int \fIoptions\fP, int *\fIovector\fP, int \fIovecsize\fP,"
.B "     pcre_jit_stack *\fIjstack\fP);"
.fi
.
.SH DESCRIPTION
.rs
.sp
This function matches a compiled regular expression that has been successfully
studied with one of the JIT options against a given subject string, using a
matching algorithm that is similar to Perl's. It is a "fast path" interface to
JIT, and it bypasses some of the sanity checks that \fBpcre_exec()\fP applies.
It returns offsets to captured substrings. Its arguments are:
.sp
  \fIcode\fP         Points to the compiled pattern
  \fIextra\fP        Points to an associated \fBpcre[16|32]_extra\fP structure,
                 or is NULL
  \fIsubject\fP      Points to the subject string
  \fIlength\fP       Length of the subject string, in bytes
  \fIstartoffset\fP  Offset in bytes in the subject at which to
                 start matching
  \fIoptions\fP      Option bits
  \fIovector\fP      Points to a vector of ints for result offsets
  \fIovecsize\fP     Number of elements in the vector (a multiple of 3)
  \fIjstack\fP       Pointer to a JIT stack
.sp
The allowed options are:
.sp
  PCRE_NOTBOL            Subject string is not the beginning of a line
  PCRE_NOTEOL            Subject string is not the end of a line
  PCRE_NOTEMPTY          An empty string is not a valid match
  PCRE_NOTEMPTY_ATSTART  An empty string at the start of the subject
                           is not a valid match
  PCRE_NO_UTF16_CHECK    Do not check the subject for UTF-16
                           validity (only relevant if PCRE_UTF16
                           was set at compile time)
  PCRE_NO_UTF32_CHECK    Do not check the subject for UTF-32
                           validity (only relevant if PCRE_UTF32
                           was set at compile time)
  PCRE_NO_UTF8_CHECK     Do not check the subject for UTF-8
                           validity (only relevant if PCRE_UTF8
                           was set at compile time)
  PCRE_PARTIAL           ) Return PCRE_ERROR_PARTIAL for a partial
  PCRE_PARTIAL_SOFT      )   match if no full matches are found
  PCRE_PARTIAL_HARD      Return PCRE_ERROR_PARTIAL for a partial match
                           if that is found before a full match
.sp
However, the PCRE_NO_UTF[8|16|32]_CHECK options have no effect, as this check
is never applied. For details of partial matching, see the
.\" HREF
\fBpcrepartial\fP
.\"
page. A \fBpcre_extra\fP structure contains the following fields:
.sp
  \fIflags\fP            Bits indicating which fields are set
  \fIstudy_data\fP       Opaque data from \fBpcre[16|32]_study()\fP
  \fImatch_limit\fP      Limit on internal resource use
  \fImatch_limit_recursion\fP  Limit on internal recursion depth
  \fIcallout_data\fP     Opaque data passed back to callouts
  \fItables\fP           Points to character tables or is NULL
  \fImark\fP             For passing back a *MARK pointer
  \fIexecutable_jit\fP   Opaque data from JIT compilation
.sp
The flag bits are PCRE_EXTRA_STUDY_DATA, PCRE_EXTRA_MATCH_LIMIT,
PCRE_EXTRA_MATCH_LIMIT_RECURSION, PCRE_EXTRA_CALLOUT_DATA,
PCRE_EXTRA_TABLES, PCRE_EXTRA_MARK and PCRE_EXTRA_EXECUTABLE_JIT.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the JIT API in the
.\" HREF
\fBpcrejit\fP
.\"
page.
