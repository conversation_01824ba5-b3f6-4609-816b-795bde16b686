.TH PCRE_VERSION 3 "24 June 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.SM
.B const char *pcre_version(void);
.PP
.B const char *pcre16_version(void);
.PP
.B const char *pcre32_version(void);
.
.SH DESCRIPTION
.rs
.sp
This function (even in the 16-bit and 32-bit libraries) returns a
zero-terminated, 8-bit character string that gives the version number of the
PCRE library and the date of its release.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
