.TH PCRE_REFCOUNT 3 "24 June 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.SM
.B int pcre_refcount(pcre *\fIcode\fP, int \fIadjust\fP);
.PP
.B int pcre16_refcount(pcre16 *\fIcode\fP, int \fIadjust\fP);
.PP
.B int pcre32_refcount(pcre32 *\fIcode\fP, int \fIadjust\fP);
.
.SH DESCRIPTION
.rs
.sp
This function is used to maintain a reference count inside a data block that
contains a compiled pattern. Its arguments are:
.sp
  \fIcode\fP                      Compiled regular expression
  \fIadjust\fP                    Adjustment to reference value
.sp
The yield of the function is the adjusted reference value, which is constrained
to lie between 0 and 65535.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
