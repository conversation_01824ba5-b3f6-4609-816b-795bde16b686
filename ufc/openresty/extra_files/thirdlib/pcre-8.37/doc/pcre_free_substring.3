.TH PCRE_FREE_SUBSTRING 3 "24 June 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.SM
.B void pcre_free_substring(const char *\fIstringptr\fP);
.PP
.B void pcre16_free_substring(PCRE_SPTR16 \fIstringptr\fP);
.PP
.B void pcre32_free_substring(PCRE_SPTR32 \fIstringptr\fP);
.
.SH DESCRIPTION
.rs
.sp
This is a convenience function for freeing the store obtained by a previous
call to \fBpcre[16|32]_get_substring()\fP or \fBpcre[16|32]_get_named_substring()\fP.
Its only argument is a pointer to the string.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
