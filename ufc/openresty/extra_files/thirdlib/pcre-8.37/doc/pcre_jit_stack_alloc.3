.TH PCRE_JIT_STACK_ALLOC 3 "24 June 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.nf
.B pcre_jit_stack *pcre_jit_stack_alloc(int \fIstartsize\fP,
.B "     int \fImaxsize\fP);"
.sp
.B pcre16_jit_stack *pcre16_jit_stack_alloc(int \fIstartsize\fP,
.B "     int \fImaxsize\fP);"
.sp
.B pcre32_jit_stack *pcre32_jit_stack_alloc(int \fIstartsize\fP,
.B "     int \fImaxsize\fP);"
.fi
.
.SH DESCRIPTION
.rs
.sp
This function is used to create a stack for use by the code compiled by the JIT
optimization of \fBpcre[16|32]_study()\fP. The arguments are a starting size for
the stack, and a maximum size to which it is allowed to grow. The result can be
passed to the JIT run-time code by \fBpcre[16|32]_assign_jit_stack()\fP, or that
function can set up a callback for obtaining a stack. A maximum stack size of
512K to 1M should be more than enough for any pattern. For more details, see
the
.\" HREF
\fBpcrejit\fP
.\"
page.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
