<html>
<head>
<title>pcre_config specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre_config man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
SYNOPSIS
</b><br>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<P>
<b>int pcre_config(int <i>what</i>, void *<i>where</i>);</b>
</P>
<P>
<b>int pcre16_config(int <i>what</i>, void *<i>where</i>);</b>
</P>
<P>
<b>int pcre32_config(int <i>what</i>, void *<i>where</i>);</b>
</P>
<br><b>
DESCRIPTION
</b><br>
<P>
This function makes it possible for a client program to find out which optional
features are available in the version of the PCRE library it is using. The
arguments are as follows:
<pre>
  <i>what</i>     A code specifying what information is required
  <i>where</i>    Points to where to put the data
</pre>
The <i>where</i> argument must point to an integer variable, except for
PCRE_CONFIG_MATCH_LIMIT, PCRE_CONFIG_MATCH_LIMIT_RECURSION, and
PCRE_CONFIG_PARENS_LIMIT, when it must point to an unsigned long integer,
and for PCRE_CONFIG_JITTARGET, when it must point to a const char*.
The available codes are:
<pre>
  PCRE_CONFIG_JIT           Availability of just-in-time compiler
                              support (1=yes 0=no)
  PCRE_CONFIG_JITTARGET     String containing information about the
                              target architecture for the JIT compiler,
                              or NULL if there is no JIT support
  PCRE_CONFIG_LINK_SIZE     Internal link size: 2, 3, or 4
  PCRE_CONFIG_PARENS_LIMIT  Parentheses nesting limit
  PCRE_CONFIG_MATCH_LIMIT   Internal resource limit
  PCRE_CONFIG_MATCH_LIMIT_RECURSION
                            Internal recursion depth limit
  PCRE_CONFIG_NEWLINE       Value of the default newline sequence:
                                13 (0x000d)    for CR
                                10 (0x000a)    for LF
                              3338 (0x0d0a)    for CRLF
                                -2             for ANYCRLF
                                -1             for ANY
  PCRE_CONFIG_BSR           Indicates what \R matches by default:
                                 0             all Unicode line endings
                                 1             CR, LF, or CRLF only
  PCRE_CONFIG_POSIX_MALLOC_THRESHOLD
                            Threshold of return slots, above which
                              <b>malloc()</b> is used by the POSIX API
  PCRE_CONFIG_STACKRECURSE  Recursion implementation (1=stack 0=heap)
  PCRE_CONFIG_UTF16         Availability of UTF-16 support (1=yes
                               0=no); option for <b>pcre16_config()</b>
  PCRE_CONFIG_UTF32         Availability of UTF-32 support (1=yes
                               0=no); option for <b>pcre32_config()</b>
  PCRE_CONFIG_UTF8          Availability of UTF-8 support (1=yes 0=no);
                              option for <b>pcre_config()</b>
  PCRE_CONFIG_UNICODE_PROPERTIES
                            Availability of Unicode property support
                              (1=yes 0=no)
</pre>
The function yields 0 on success or PCRE_ERROR_BADOPTION otherwise. That error
is also given if PCRE_CONFIG_UTF16 or PCRE_CONFIG_UTF32 is passed to
<b>pcre_config()</b>, if PCRE_CONFIG_UTF8 or PCRE_CONFIG_UTF32 is passed to
<b>pcre16_config()</b>, or if PCRE_CONFIG_UTF8 or PCRE_CONFIG_UTF16 is passed to
<b>pcre32_config()</b>.
</P>
<P>
There is a complete description of the PCRE native API in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page and a description of the POSIX API in the
<a href="pcreposix.html"><b>pcreposix</b></a>
page.
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
