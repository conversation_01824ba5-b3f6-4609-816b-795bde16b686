<html>
<!-- This is a manually maintained file that is the root of the HTML version of
     the PCRE documentation. When the HTML documents are built from the man
     page versions, the entire doc/html directory is emptied, this file is then
     copied into doc/html/index.html, and the remaining files therein are
     created by the 132html script.
-->
<head>
<title>PCRE specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>Perl-compatible Regular Expressions (PCRE)</h1>
<p>
The HTML documentation for PCRE consists of a number of pages that are listed
below in alphabetical order. If you are new to PCRE, please read the first one
first.
</p>

<table>
<tr><td><a href="pcre.html">pcre</a></td>
    <td>&nbsp;&nbsp;Introductory page</td></tr>

<tr><td><a href="pcre-config.html">pcre-config</a></td>
    <td>&nbsp;&nbsp;Information about the installation configuration</td></tr>

<tr><td><a href="pcre16.html">pcre16</a></td>
    <td>&nbsp;&nbsp;Discussion of the 16-bit PCRE library</td></tr>

<tr><td><a href="pcre32.html">pcre32</a></td>
    <td>&nbsp;&nbsp;Discussion of the 32-bit PCRE library</td></tr>

<tr><td><a href="pcreapi.html">pcreapi</a></td>
    <td>&nbsp;&nbsp;PCRE's native API</td></tr>

<tr><td><a href="pcrebuild.html">pcrebuild</a></td>
    <td>&nbsp;&nbsp;Building PCRE</td></tr>

<tr><td><a href="pcrecallout.html">pcrecallout</a></td>
    <td>&nbsp;&nbsp;The <i>callout</i> facility</td></tr>

<tr><td><a href="pcrecompat.html">pcrecompat</a></td>
    <td>&nbsp;&nbsp;Compability with Perl</td></tr>

<tr><td><a href="pcrecpp.html">pcrecpp</a></td>
    <td>&nbsp;&nbsp;The C++ wrapper for the PCRE library</td></tr>

<tr><td><a href="pcredemo.html">pcredemo</a></td>
    <td>&nbsp;&nbsp;A demonstration C program that uses the PCRE library</td></tr>

<tr><td><a href="pcregrep.html">pcregrep</a></td>
    <td>&nbsp;&nbsp;The <b>pcregrep</b> command</td></tr>

<tr><td><a href="pcrejit.html">pcrejit</a></td>
    <td>&nbsp;&nbsp;Discussion of the just-in-time optimization support</td></tr>

<tr><td><a href="pcrelimits.html">pcrelimits</a></td>
    <td>&nbsp;&nbsp;Details of size and other limits</td></tr>

<tr><td><a href="pcrematching.html">pcrematching</a></td>
    <td>&nbsp;&nbsp;Discussion of the two matching algorithms</td></tr>

<tr><td><a href="pcrepartial.html">pcrepartial</a></td>
    <td>&nbsp;&nbsp;Using PCRE for partial matching</td></tr>

<tr><td><a href="pcrepattern.html">pcrepattern</a></td>
    <td>&nbsp;&nbsp;Specification of the regular expressions supported by PCRE</td></tr>

<tr><td><a href="pcreperform.html">pcreperform</a></td>
    <td>&nbsp;&nbsp;Some comments on performance</td></tr>

<tr><td><a href="pcreposix.html">pcreposix</a></td>
    <td>&nbsp;&nbsp;The POSIX API to the PCRE 8-bit library</td></tr>

<tr><td><a href="pcreprecompile.html">pcreprecompile</a></td>
    <td>&nbsp;&nbsp;How to save and re-use compiled patterns</td></tr>

<tr><td><a href="pcresample.html">pcresample</a></td>
    <td>&nbsp;&nbsp;Discussion of the pcredemo program</td></tr>

<tr><td><a href="pcrestack.html">pcrestack</a></td>
    <td>&nbsp;&nbsp;Discussion of PCRE's stack usage</td></tr>

<tr><td><a href="pcresyntax.html">pcresyntax</a></td>
    <td>&nbsp;&nbsp;Syntax quick-reference summary</td></tr>

<tr><td><a href="pcretest.html">pcretest</a></td>
    <td>&nbsp;&nbsp;The <b>pcretest</b> command for testing PCRE</td></tr>

<tr><td><a href="pcreunicode.html">pcreunicode</a></td>
    <td>&nbsp;&nbsp;Discussion of Unicode and UTF-8/UTF-16/UTF-32 support</td></tr>
</table>

<p>
There are also individual pages that summarize the interface for each function
in the library. There is a single page for each triple of 8-bit/16-bit/32-bit
functions.
</p>

<table>

<tr><td><a href="pcre_assign_jit_stack.html">pcre_assign_jit_stack</a></td>
    <td>&nbsp;&nbsp;Assign stack for JIT matching</td></tr>

<tr><td><a href="pcre_compile.html">pcre_compile</a></td>
    <td>&nbsp;&nbsp;Compile a regular expression</td></tr>

<tr><td><a href="pcre_compile2.html">pcre_compile2</a></td>
    <td>&nbsp;&nbsp;Compile a regular expression (alternate interface)</td></tr>

<tr><td><a href="pcre_config.html">pcre_config</a></td>
    <td>&nbsp;&nbsp;Show build-time configuration options</td></tr>

<tr><td><a href="pcre_copy_named_substring.html">pcre_copy_named_substring</a></td>
    <td>&nbsp;&nbsp;Extract named substring into given buffer</td></tr>

<tr><td><a href="pcre_copy_substring.html">pcre_copy_substring</a></td>
    <td>&nbsp;&nbsp;Extract numbered substring into given buffer</td></tr>

<tr><td><a href="pcre_dfa_exec.html">pcre_dfa_exec</a></td>
    <td>&nbsp;&nbsp;Match a compiled pattern to a subject string
    (DFA algorithm; <i>not</i> Perl compatible)</td></tr>

<tr><td><a href="pcre_exec.html">pcre_exec</a></td>
    <td>&nbsp;&nbsp;Match a compiled pattern to a subject string
    (Perl compatible)</td></tr>

<tr><td><a href="pcre_free_study.html">pcre_free_study</a></td>
    <td>&nbsp;&nbsp;Free study data</td></tr>

<tr><td><a href="pcre_free_substring.html">pcre_free_substring</a></td>
    <td>&nbsp;&nbsp;Free extracted substring</td></tr>

<tr><td><a href="pcre_free_substring_list.html">pcre_free_substring_list</a></td>
    <td>&nbsp;&nbsp;Free list of extracted substrings</td></tr>

<tr><td><a href="pcre_fullinfo.html">pcre_fullinfo</a></td>
    <td>&nbsp;&nbsp;Extract information about a pattern</td></tr>

<tr><td><a href="pcre_get_named_substring.html">pcre_get_named_substring</a></td>
    <td>&nbsp;&nbsp;Extract named substring into new memory</td></tr>

<tr><td><a href="pcre_get_stringnumber.html">pcre_get_stringnumber</a></td>
    <td>&nbsp;&nbsp;Convert captured string name to number</td></tr>

<tr><td><a href="pcre_get_stringtable_entries.html">pcre_get_stringtable_entries</a></td>
    <td>&nbsp;&nbsp;Find table entries for given string name</td></tr>

<tr><td><a href="pcre_get_substring.html">pcre_get_substring</a></td>
    <td>&nbsp;&nbsp;Extract numbered substring into new memory</td></tr>

<tr><td><a href="pcre_get_substring_list.html">pcre_get_substring_list</a></td>
    <td>&nbsp;&nbsp;Extract all substrings into new memory</td></tr>

<tr><td><a href="pcre_jit_exec.html">pcre_jit_exec</a></td>
    <td>&nbsp;&nbsp;Fast path interface to JIT matching</td></tr>

<tr><td><a href="pcre_jit_stack_alloc.html">pcre_jit_stack_alloc</a></td>
    <td>&nbsp;&nbsp;Create a stack for JIT matching</td></tr>

<tr><td><a href="pcre_jit_stack_free.html">pcre_jit_stack_free</a></td>
    <td>&nbsp;&nbsp;Free a JIT matching stack</td></tr>

<tr><td><a href="pcre_maketables.html">pcre_maketables</a></td>
    <td>&nbsp;&nbsp;Build character tables in current locale</td></tr>

<tr><td><a href="pcre_pattern_to_host_byte_order.html">pcre_pattern_to_host_byte_order</a></td>
    <td>&nbsp;&nbsp;Convert compiled pattern to host byte order if necessary</td></tr>

<tr><td><a href="pcre_refcount.html">pcre_refcount</a></td>
    <td>&nbsp;&nbsp;Maintain reference count in compiled pattern</td></tr>

<tr><td><a href="pcre_study.html">pcre_study</a></td>
    <td>&nbsp;&nbsp;Study a compiled pattern</td></tr>

<tr><td><a href="pcre_utf16_to_host_byte_order.html">pcre_utf16_to_host_byte_order</a></td>
    <td>&nbsp;&nbsp;Convert UTF-16 string to host byte order if necessary</td></tr>

<tr><td><a href="pcre_utf32_to_host_byte_order.html">pcre_utf32_to_host_byte_order</a></td>
    <td>&nbsp;&nbsp;Convert UTF-32 string to host byte order if necessary</td></tr>

<tr><td><a href="pcre_version.html">pcre_version</a></td>
    <td>&nbsp;&nbsp;Return PCRE version and release date</td></tr>
</table>

</html>
