<html>
<head>
<title>pcre_utf16_to_host_byte_order specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre_utf16_to_host_byte_order man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
SYNOPSIS
</b><br>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<P>
<b>int pcre16_utf16_to_host_byte_order(PCRE_UCHAR16 *<i>output</i>,</b>
<b>     PCRE_SPTR16 <i>input</i>, int <i>length</i>, int *<i>host_byte_order</i>,</b>
<b>     int <i>keep_boms</i>);</b>
</P>
<br><b>
DESCRIPTION
</b><br>
<P>
This function, which exists only in the 16-bit library, converts a UTF-16
string to the correct order for the current host, taking account of any byte
order marks (BOMs) within the string. Its arguments are:
<pre>
  <i>output</i>           pointer to output buffer, may be the same as <i>input</i>
  <i>input</i>            pointer to input buffer
  <i>length</i>           number of 16-bit units in the input, or negative for
                     a zero-terminated string
  <i>host_byte_order</i>  a NULL value or a non-zero value pointed to means
                     start in host byte order
  <i>keep_boms</i>        if non-zero, BOMs are copied to the output string
</pre>
The result of the function is the number of 16-bit units placed into the output
buffer, including the zero terminator if the string was zero-terminated.
</P>
<P>
If <i>host_byte_order</i> is not NULL, it is set to indicate the byte order that
is current at the end of the string.
</P>
<P>
There is a complete description of the PCRE native API in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page and a description of the POSIX API in the
<a href="pcreposix.html"><b>pcreposix</b></a>
page.
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
