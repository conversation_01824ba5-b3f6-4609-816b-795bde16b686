<html>
<head>
<title>pcre_pattern_to_host_byte_order specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre_pattern_to_host_byte_order man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
SYNOPSIS
</b><br>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<P>
<b>int pcre_pattern_to_host_byte_order(pcre *<i>code</i>,</b>
<b>     pcre_extra *<i>extra</i>, const unsigned char *<i>tables</i>);</b>
<br>
<br>
<b>int pcre16_pattern_to_host_byte_order(pcre16 *<i>code</i>,</b>
<b>     pcre16_extra *<i>extra</i>, const unsigned char *<i>tables</i>);</b>
<br>
<br>
<b>int pcre32_pattern_to_host_byte_order(pcre32 *<i>code</i>,</b>
<b>     pcre32_extra *<i>extra</i>, const unsigned char *<i>tables</i>);</b>
</P>
<br><b>
DESCRIPTION
</b><br>
<P>
This function ensures that the bytes in 2-byte and 4-byte values in a compiled
pattern are in the correct order for the current host. It is useful when a
pattern that has been compiled on one host is transferred to another that might
have different endianness. The arguments are:
<pre>
  <i>code</i>         A compiled regular expression
  <i>extra</i>        Points to an associated <b>pcre[16|32]_extra</b> structure,
                 or is NULL
  <i>tables</i>       Pointer to character tables, or NULL to
                 set the built-in default
</pre>
The result is 0 for success, a negative PCRE_ERROR_xxx value otherwise.
</P>
<P>
There is a complete description of the PCRE native API in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page and a description of the POSIX API in the
<a href="pcreposix.html"><b>pcreposix</b></a>
page.
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
