<html>
<head>
<title>pcre specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<ul>
<li><a name="TOC1" href="#SEC1">PLEASE TAKE NOTE</a>
<li><a name="TOC2" href="#SEC2">INTRODUCTION</a>
<li><a name="TOC3" href="#SEC3">SECURITY CONSIDERATIONS</a>
<li><a name="TOC4" href="#SEC4">USER DOCUMENTATION</a>
<li><a name="TOC5" href="#SEC5">AUTHOR</a>
<li><a name="TOC6" href="#SEC6">REVISION</a>
</ul>
<br><a name="SEC1" href="#TOC1">PLEASE TAKE NOTE</a><br>
<P>
This document relates to PCRE releases that use the original API,
with library names libpcre, libpcre16, and libpcre32. January 2015 saw the
first release of a new API, known as PCRE2, with release numbers starting at
10.00 and library names libpcre2-8, libpcre2-16, and libpcre2-32. The old
libraries (now called PCRE1) are still being maintained for bug fixes, but
there will be no new development. New projects are advised to use the new PCRE2
libraries.
</P>
<br><a name="SEC2" href="#TOC1">INTRODUCTION</a><br>
<P>
The PCRE library is a set of functions that implement regular expression
pattern matching using the same syntax and semantics as Perl, with just a few
differences. Some features that appeared in Python and PCRE before they
appeared in Perl are also available using the Python syntax, there is some
support for one or two .NET and Oniguruma syntax items, and there is an option
for requesting some minor changes that give better JavaScript compatibility.
</P>
<P>
Starting with release 8.30, it is possible to compile two separate PCRE
libraries: the original, which supports 8-bit character strings (including
UTF-8 strings), and a second library that supports 16-bit character strings
(including UTF-16 strings). The build process allows either one or both to be
built. The majority of the work to make this possible was done by Zoltan
Herczeg.
</P>
<P>
Starting with release 8.32 it is possible to compile a third separate PCRE
library that supports 32-bit character strings (including UTF-32 strings). The
build process allows any combination of the 8-, 16- and 32-bit libraries. The
work to make this possible was done by Christian Persch.
</P>
<P>
The three libraries contain identical sets of functions, except that the names
in the 16-bit library start with <b>pcre16_</b> instead of <b>pcre_</b>, and the
names in the 32-bit library start with <b>pcre32_</b> instead of <b>pcre_</b>. To
avoid over-complication and reduce the documentation maintenance load, most of
the documentation describes the 8-bit library, with the differences for the
16-bit and 32-bit libraries described separately in the
<a href="pcre16.html"><b>pcre16</b></a>
and
<a href="pcre32.html"><b>pcre32</b></a>
pages. References to functions or structures of the form <i>pcre[16|32]_xxx</i>
should be read as meaning "<i>pcre_xxx</i> when using the 8-bit library,
<i>pcre16_xxx</i> when using the 16-bit library, or <i>pcre32_xxx</i> when using
the 32-bit library".
</P>
<P>
The current implementation of PCRE corresponds approximately with Perl 5.12,
including support for UTF-8/16/32 encoded strings and Unicode general category
properties. However, UTF-8/16/32 and Unicode support has to be explicitly
enabled; it is not the default. The Unicode tables correspond to Unicode
release 6.3.0.
</P>
<P>
In addition to the Perl-compatible matching function, PCRE contains an
alternative function that matches the same compiled patterns in a different
way. In certain circumstances, the alternative function has some advantages.
For a discussion of the two matching algorithms, see the
<a href="pcrematching.html"><b>pcrematching</b></a>
page.
</P>
<P>
PCRE is written in C and released as a C library. A number of people have
written wrappers and interfaces of various kinds. In particular, Google Inc.
have provided a comprehensive C++ wrapper for the 8-bit library. This is now
included as part of the PCRE distribution. The
<a href="pcrecpp.html"><b>pcrecpp</b></a>
page has details of this interface. Other people's contributions can be found
in the <i>Contrib</i> directory at the primary FTP site, which is:
<a href="ftp://ftp.csx.cam.ac.uk/pub/software/programming/pcre">ftp://ftp.csx.cam.ac.uk/pub/software/programming/pcre</a>
</P>
<P>
Details of exactly which Perl regular expression features are and are not
supported by PCRE are given in separate documents. See the
<a href="pcrepattern.html"><b>pcrepattern</b></a>
and
<a href="pcrecompat.html"><b>pcrecompat</b></a>
pages. There is a syntax summary in the
<a href="pcresyntax.html"><b>pcresyntax</b></a>
page.
</P>
<P>
Some features of PCRE can be included, excluded, or changed when the library is
built. The
<a href="pcre_config.html"><b>pcre_config()</b></a>
function makes it possible for a client to discover which features are
available. The features themselves are described in the
<a href="pcrebuild.html"><b>pcrebuild</b></a>
page. Documentation about building PCRE for various operating systems can be
found in the
<a href="README.txt"><b>README</b></a>
and
<a href="NON-AUTOTOOLS-BUILD.txt"><b>NON-AUTOTOOLS_BUILD</b></a>
files in the source distribution.
</P>
<P>
The libraries contains a number of undocumented internal functions and data
tables that are used by more than one of the exported external functions, but
which are not intended for use by external callers. Their names all begin with
"_pcre_" or "_pcre16_" or "_pcre32_", which hopefully will not provoke any name
clashes. In some environments, it is possible to control which external symbols
are exported when a shared library is built, and in these cases the
undocumented symbols are not exported.
</P>
<br><a name="SEC3" href="#TOC1">SECURITY CONSIDERATIONS</a><br>
<P>
If you are using PCRE in a non-UTF application that permits users to supply
arbitrary patterns for compilation, you should be aware of a feature that
allows users to turn on UTF support from within a pattern, provided that PCRE
was built with UTF support. For example, an 8-bit pattern that begins with
"(*UTF8)" or "(*UTF)" turns on UTF-8 mode, which interprets patterns and
subjects as strings of UTF-8 characters instead of individual 8-bit characters.
This causes both the pattern and any data against which it is matched to be
checked for UTF-8 validity. If the data string is very long, such a check might
use sufficiently many resources as to cause your application to lose
performance.
</P>
<P>
One way of guarding against this possibility is to use the
<b>pcre_fullinfo()</b> function to check the compiled pattern's options for UTF.
Alternatively, from release 8.33, you can set the PCRE_NEVER_UTF option at
compile time. This causes an compile time error if a pattern contains a
UTF-setting sequence.
</P>
<P>
If your application is one that supports UTF, be aware that validity checking
can take time. If the same data string is to be matched many times, you can use
the PCRE_NO_UTF[8|16|32]_CHECK option for the second and subsequent matches to
save redundant checks.
</P>
<P>
Another way that performance can be hit is by running a pattern that has a very
large search tree against a string that will never match. Nested unlimited
repeats in a pattern are a common example. PCRE provides some protection
against this: see the PCRE_EXTRA_MATCH_LIMIT feature in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page.
</P>
<br><a name="SEC4" href="#TOC1">USER DOCUMENTATION</a><br>
<P>
The user documentation for PCRE comprises a number of different sections. In
the "man" format, each of these is a separate "man page". In the HTML format,
each is a separate page, linked from the index page. In the plain text format,
the descriptions of the <b>pcregrep</b> and <b>pcretest</b> programs are in files
called <b>pcregrep.txt</b> and <b>pcretest.txt</b>, respectively. The remaining
sections, except for the <b>pcredemo</b> section (which is a program listing),
are concatenated in <b>pcre.txt</b>, for ease of searching. The sections are as
follows:
<pre>
  pcre              this document
  pcre-config       show PCRE installation configuration information
  pcre16            details of the 16-bit library
  pcre32            details of the 32-bit library
  pcreapi           details of PCRE's native C API
  pcrebuild         building PCRE
  pcrecallout       details of the callout feature
  pcrecompat        discussion of Perl compatibility
  pcrecpp           details of the C++ wrapper for the 8-bit library
  pcredemo          a demonstration C program that uses PCRE
  pcregrep          description of the <b>pcregrep</b> command (8-bit only)
  pcrejit           discussion of the just-in-time optimization support
  pcrelimits        details of size and other limits
  pcrematching      discussion of the two matching algorithms
  pcrepartial       details of the partial matching facility
  pcrepattern       syntax and semantics of supported regular expressions
  pcreperform       discussion of performance issues
  pcreposix         the POSIX-compatible C API for the 8-bit library
  pcreprecompile    details of saving and re-using precompiled patterns
  pcresample        discussion of the pcredemo program
  pcrestack         discussion of stack usage
  pcresyntax        quick syntax reference
  pcretest          description of the <b>pcretest</b> testing command
  pcreunicode       discussion of Unicode and UTF-8/16/32 support
</pre>
In the "man" and HTML formats, there is also a short page for each C library
function, listing its arguments and results.
</P>
<br><a name="SEC5" href="#TOC1">AUTHOR</a><br>
<P>
Philip Hazel
<br>
University Computing Service
<br>
Cambridge CB2 3QH, England.
<br>
</P>
<P>
Putting an actual email address here seems to have been a spam magnet, so I've
taken it away. If you want to email me, use my two initials, followed by the
two digits 10, at the domain cam.ac.uk.
</P>
<br><a name="SEC6" href="#TOC1">REVISION</a><br>
<P>
Last updated: 10 February 2015
<br>
Copyright &copy; 1997-2015 University of Cambridge.
<br>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
