<html>
<head>
<title>pcre_compile specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre_compile man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
SYNOPSIS
</b><br>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<P>
<b>pcre *pcre_compile(const char *<i>pattern</i>, int <i>options</i>,</b>
<b>     const char **<i>errptr</i>, int *<i>erroffset</i>,</b>
<b>     const unsigned char *<i>tableptr</i>);</b>
<br>
<br>
<b>pcre16 *pcre16_compile(PCRE_SPTR16 <i>pattern</i>, int <i>options</i>,</b>
<b>     const char **<i>errptr</i>, int *<i>erroffset</i>,</b>
<b>     const unsigned char *<i>tableptr</i>);</b>
<br>
<br>
<b>pcre32 *pcre32_compile(PCRE_SPTR32 <i>pattern</i>, int <i>options</i>,</b>
<b>     const char **<i>errptr</i>, int *<i>erroffset</i>,</b>
<b>     const unsigned char *<i>tableptr</i>);</b>
</P>
<br><b>
DESCRIPTION
</b><br>
<P>
This function compiles a regular expression into an internal form. It is the
same as <b>pcre[16|32]_compile2()</b>, except for the absence of the
<i>errorcodeptr</i> argument. Its arguments are:
<pre>
  <i>pattern</i>       A zero-terminated string containing the
                  regular expression to be compiled
  <i>options</i>       Zero or more option bits
  <i>errptr</i>        Where to put an error message
  <i>erroffset</i>     Offset in pattern where error was found
  <i>tableptr</i>      Pointer to character tables, or NULL to
                  use the built-in default
</pre>
The option bits are:
<pre>
  PCRE_ANCHORED           Force pattern anchoring
  PCRE_AUTO_CALLOUT       Compile automatic callouts
  PCRE_BSR_ANYCRLF        \R matches only CR, LF, or CRLF
  PCRE_BSR_UNICODE        \R matches all Unicode line endings
  PCRE_CASELESS           Do caseless matching
  PCRE_DOLLAR_ENDONLY     $ not to match newline at end
  PCRE_DOTALL             . matches anything including NL
  PCRE_DUPNAMES           Allow duplicate names for subpatterns
  PCRE_EXTENDED           Ignore white space and # comments
  PCRE_EXTRA              PCRE extra features
                            (not much use currently)
  PCRE_FIRSTLINE          Force matching to be before newline
  PCRE_JAVASCRIPT_COMPAT  JavaScript compatibility
  PCRE_MULTILINE          ^ and $ match newlines within data
  PCRE_NEVER_UTF          Lock out UTF, e.g. via (*UTF)
  PCRE_NEWLINE_ANY        Recognize any Unicode newline sequence
  PCRE_NEWLINE_ANYCRLF    Recognize CR, LF, and CRLF as newline
                            sequences
  PCRE_NEWLINE_CR         Set CR as the newline sequence
  PCRE_NEWLINE_CRLF       Set CRLF as the newline sequence
  PCRE_NEWLINE_LF         Set LF as the newline sequence
  PCRE_NO_AUTO_CAPTURE    Disable numbered capturing paren-
                            theses (named ones available)
  PCRE_NO_AUTO_POSSESS    Disable auto-possessification
  PCRE_NO_START_OPTIMIZE  Disable match-time start optimizations
  PCRE_NO_UTF16_CHECK     Do not check the pattern for UTF-16
                            validity (only relevant if
                            PCRE_UTF16 is set)
  PCRE_NO_UTF32_CHECK     Do not check the pattern for UTF-32
                            validity (only relevant if
                            PCRE_UTF32 is set)
  PCRE_NO_UTF8_CHECK      Do not check the pattern for UTF-8
                            validity (only relevant if
                            PCRE_UTF8 is set)
  PCRE_UCP                Use Unicode properties for \d, \w, etc.
  PCRE_UNGREEDY           Invert greediness of quantifiers
  PCRE_UTF16              Run in <b>pcre16_compile()</b> UTF-16 mode
  PCRE_UTF32              Run in <b>pcre32_compile()</b> UTF-32 mode
  PCRE_UTF8               Run in <b>pcre_compile()</b> UTF-8 mode
</pre>
PCRE must be built with UTF support in order to use PCRE_UTF8/16/32 and
PCRE_NO_UTF8/16/32_CHECK, and with UCP support if PCRE_UCP is used.
</P>
<P>
The yield of the function is a pointer to a private data structure that
contains the compiled pattern, or NULL if an error was detected. Note that
compiling regular expressions with one version of PCRE for use with a different
version is not guaranteed to work and may cause crashes.
</P>
<P>
There is a complete description of the PCRE native API in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page and a description of the POSIX API in the
<a href="pcreposix.html"><b>pcreposix</b></a>
page.
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
