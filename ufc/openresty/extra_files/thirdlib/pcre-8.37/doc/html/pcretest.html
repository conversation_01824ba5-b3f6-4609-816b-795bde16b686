<html>
<head>
<title>pcretest specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcretest man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<ul>
<li><a name="TOC1" href="#SEC1">SYNOPSIS</a>
<li><a name="TOC2" href="#SEC2">INPUT DATA FORMAT</a>
<li><a name="TOC3" href="#SEC3">PCRE's 8-BIT, 16-BIT AND 32-BIT LIBRARIES</a>
<li><a name="TOC4" href="#SEC4">COMMAND LINE OPTIONS</a>
<li><a name="TOC5" href="#SEC5">DESCRIPTION</a>
<li><a name="TOC6" href="#SEC6">PATTERN MODIFIERS</a>
<li><a name="TOC7" href="#SEC7">DATA LINES</a>
<li><a name="TOC8" href="#SEC8">THE ALTERNATIVE MATCHING FUNCTION</a>
<li><a name="TOC9" href="#SEC9">DEFAULT OUTPUT FROM PCRETEST</a>
<li><a name="TOC10" href="#SEC10">OUTPUT FROM THE ALTERNATIVE MATCHING FUNCTION</a>
<li><a name="TOC11" href="#SEC11">RESTARTING AFTER A PARTIAL MATCH</a>
<li><a name="TOC12" href="#SEC12">CALLOUTS</a>
<li><a name="TOC13" href="#SEC13">NON-PRINTING CHARACTERS</a>
<li><a name="TOC14" href="#SEC14">SAVING AND RELOADING COMPILED PATTERNS</a>
<li><a name="TOC15" href="#SEC15">SEE ALSO</a>
<li><a name="TOC16" href="#SEC16">AUTHOR</a>
<li><a name="TOC17" href="#SEC17">REVISION</a>
</ul>
<br><a name="SEC1" href="#TOC1">SYNOPSIS</a><br>
<P>
<b>pcretest [options] [input file [output file]]</b>
<br>
<br>
<b>pcretest</b> was written as a test program for the PCRE regular expression
library itself, but it can also be used for experimenting with regular
expressions. This document describes the features of the test program; for
details of the regular expressions themselves, see the
<a href="pcrepattern.html"><b>pcrepattern</b></a>
documentation. For details of the PCRE library function calls and their
options, see the
<a href="pcreapi.html"><b>pcreapi</b></a>
,
<a href="pcre16.html"><b>pcre16</b></a>
and
<a href="pcre32.html"><b>pcre32</b></a>
documentation.
</P>
<P>
The input for <b>pcretest</b> is a sequence of regular expression patterns and
strings to be matched, as described below. The output shows the result of each
match. Options on the command line and the patterns control PCRE options and
exactly what is output.
</P>
<P>
As PCRE has evolved, it has acquired many different features, and as a result,
<b>pcretest</b> now has rather a lot of obscure options for testing every
possible feature. Some of these options are specifically designed for use in
conjunction with the test script and data files that are distributed as part of
PCRE, and are unlikely to be of use otherwise. They are all documented here,
but without much justification.
</P>
<br><a name="SEC2" href="#TOC1">INPUT DATA FORMAT</a><br>
<P>
Input to <b>pcretest</b> is processed line by line, either by calling the C
library's <b>fgets()</b> function, or via the <b>libreadline</b> library (see
below). In Unix-like environments, <b>fgets()</b> treats any bytes other than
newline as data characters. However, in some Windows environments character 26
(hex 1A) causes an immediate end of file, and no further data is read. For
maximum portability, therefore, it is safest to use only ASCII characters in
<b>pcretest</b> input files.
</P>
<br><a name="SEC3" href="#TOC1">PCRE's 8-BIT, 16-BIT AND 32-BIT LIBRARIES</a><br>
<P>
From release 8.30, two separate PCRE libraries can be built. The original one
supports 8-bit character strings, whereas the newer 16-bit library supports
character strings encoded in 16-bit units. From release 8.32, a third library
can be built, supporting character strings encoded in 32-bit units. The
<b>pcretest</b> program can be used to test all three libraries. However, it is
itself still an 8-bit program, reading 8-bit input and writing 8-bit output.
When testing the 16-bit or 32-bit library, the patterns and data strings are
converted to 16- or 32-bit format before being passed to the PCRE library
functions. Results are converted to 8-bit for output.
</P>
<P>
References to functions and structures of the form <b>pcre[16|32]_xx</b> below
mean "<b>pcre_xx</b> when using the 8-bit library, <b>pcre16_xx</b> when using
the 16-bit library, or <b>pcre32_xx</b> when using the 32-bit library".
</P>
<br><a name="SEC4" href="#TOC1">COMMAND LINE OPTIONS</a><br>
<P>
<b>-8</b>
If both the 8-bit library has been built, this option causes the 8-bit library
to be used (which is the default); if the 8-bit library has not been built,
this option causes an error.
</P>
<P>
<b>-16</b>
If both the 8-bit or the 32-bit, and the 16-bit libraries have been built, this
option causes the 16-bit library to be used. If only the 16-bit library has been
built, this is the default (so has no effect). If only the 8-bit or the 32-bit
library has been built, this option causes an error.
</P>
<P>
<b>-32</b>
If both the 8-bit or the 16-bit, and the 32-bit libraries have been built, this
option causes the 32-bit library to be used. If only the 32-bit library has been
built, this is the default (so has no effect). If only the 8-bit or the 16-bit
library has been built, this option causes an error.
</P>
<P>
<b>-b</b>
Behave as if each pattern has the <b>/B</b> (show byte code) modifier; the
internal form is output after compilation.
</P>
<P>
<b>-C</b>
Output the version number of the PCRE library, and all available information
about the optional features that are included, and then exit with zero exit
code. All other options are ignored.
</P>
<P>
<b>-C</b> <i>option</i>
Output information about a specific build-time option, then exit. This
functionality is intended for use in scripts such as <b>RunTest</b>. The
following options output the value and set the exit code as indicated:
<pre>
  ebcdic-nl  the code for LF (= NL) in an EBCDIC environment:
               0x15 or 0x25
               0 if used in an ASCII environment
               exit code is always 0
  linksize   the configured internal link size (2, 3, or 4)
               exit code is set to the link size
  newline    the default newline setting:
               CR, LF, CRLF, ANYCRLF, or ANY
               exit code is always 0
  bsr        the default setting for what \R matches:
               ANYCRLF or ANY
               exit code is always 0
</pre>
The following options output 1 for true or 0 for false, and set the exit code
to the same value:
<pre>
  ebcdic     compiled for an EBCDIC environment
  jit        just-in-time support is available
  pcre16     the 16-bit library was built
  pcre32     the 32-bit library was built
  pcre8      the 8-bit library was built
  ucp        Unicode property support is available
  utf        UTF-8 and/or UTF-16 and/or UTF-32 support
               is available
</pre>
If an unknown option is given, an error message is output; the exit code is 0.
</P>
<P>
<b>-d</b>
Behave as if each pattern has the <b>/D</b> (debug) modifier; the internal
form and information about the compiled pattern is output after compilation;
<b>-d</b> is equivalent to <b>-b -i</b>.
</P>
<P>
<b>-dfa</b>
Behave as if each data line contains the \D escape sequence; this causes the
alternative matching function, <b>pcre[16|32]_dfa_exec()</b>, to be used instead
of the standard <b>pcre[16|32]_exec()</b> function (more detail is given below).
</P>
<P>
<b>-help</b>
Output a brief summary these options and then exit.
</P>
<P>
<b>-i</b>
Behave as if each pattern has the <b>/I</b> modifier; information about the
compiled pattern is given after compilation.
</P>
<P>
<b>-M</b>
Behave as if each data line contains the \M escape sequence; this causes
PCRE to discover the minimum MATCH_LIMIT and MATCH_LIMIT_RECURSION settings by
calling <b>pcre[16|32]_exec()</b> repeatedly with different limits.
</P>
<P>
<b>-m</b>
Output the size of each compiled pattern after it has been compiled. This is
equivalent to adding <b>/M</b> to each regular expression. The size is given in
bytes for both libraries.
</P>
<P>
<b>-O</b>
Behave as if each pattern has the <b>/O</b> modifier, that is disable
auto-possessification for all patterns.
</P>
<P>
<b>-o</b> <i>osize</i>
Set the number of elements in the output vector that is used when calling
<b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b> to be <i>osize</i>. The
default value is 45, which is enough for 14 capturing subexpressions for
<b>pcre[16|32]_exec()</b> or 22 different matches for
<b>pcre[16|32]_dfa_exec()</b>.
The vector size can be changed for individual matching calls by including \O
in the data line (see below).
</P>
<P>
<b>-p</b>
Behave as if each pattern has the <b>/P</b> modifier; the POSIX wrapper API is
used to call PCRE. None of the other options has any effect when <b>-p</b> is
set. This option can be used only with the 8-bit library.
</P>
<P>
<b>-q</b>
Do not output the version number of <b>pcretest</b> at the start of execution.
</P>
<P>
<b>-S</b> <i>size</i>
On Unix-like systems, set the size of the run-time stack to <i>size</i>
megabytes.
</P>
<P>
<b>-s</b> or <b>-s+</b>
Behave as if each pattern has the <b>/S</b> modifier; in other words, force each
pattern to be studied. If <b>-s+</b> is used, all the JIT compile options are
passed to <b>pcre[16|32]_study()</b>, causing just-in-time optimization to be set
up if it is available, for both full and partial matching. Specific JIT compile
options can be selected by following <b>-s+</b> with a digit in the range 1 to
7, which selects the JIT compile modes as follows:
<pre>
  1  normal match only
  2  soft partial match only
  3  normal match and soft partial match
  4  hard partial match only
  6  soft and hard partial match
  7  all three modes (default)
</pre>
If <b>-s++</b> is used instead of <b>-s+</b> (with or without a following digit),
the text "(JIT)" is added to the first output line after a match or no match
when JIT-compiled code was actually used.
<br>
<br>
Note that there are pattern options that can override <b>-s</b>, either
specifying no studying at all, or suppressing JIT compilation.
<br>
<br>
If the <b>/I</b> or <b>/D</b> option is present on a pattern (requesting output
about the compiled pattern), information about the result of studying is not
included when studying is caused only by <b>-s</b> and neither <b>-i</b> nor
<b>-d</b> is present on the command line. This behaviour means that the output
from tests that are run with and without <b>-s</b> should be identical, except
when options that output information about the actual running of a match are
set.
<br>
<br>
The <b>-M</b>, <b>-t</b>, and <b>-tm</b> options, which give information about
resources used, are likely to produce different output with and without
<b>-s</b>. Output may also differ if the <b>/C</b> option is present on an
individual pattern. This uses callouts to trace the the matching process, and
this may be different between studied and non-studied patterns. If the pattern
contains (*MARK) items there may also be differences, for the same reason. The
<b>-s</b> command line option can be overridden for specific patterns that
should never be studied (see the <b>/S</b> pattern modifier below).
</P>
<P>
<b>-t</b>
Run each compile, study, and match many times with a timer, and output the
resulting times per compile, study, or match (in milliseconds). Do not set
<b>-m</b> with <b>-t</b>, because you will then get the size output a zillion
times, and the timing will be distorted. You can control the number of
iterations that are used for timing by following <b>-t</b> with a number (as a
separate item on the command line). For example, "-t 1000" iterates 1000 times.
The default is to iterate 500000 times.
</P>
<P>
<b>-tm</b>
This is like <b>-t</b> except that it times only the matching phase, not the
compile or study phases.
</P>
<P>
<b>-T</b> <b>-TM</b>
These behave like <b>-t</b> and <b>-tm</b>, but in addition, at the end of a run,
the total times for all compiles, studies, and matches are output.
</P>
<br><a name="SEC5" href="#TOC1">DESCRIPTION</a><br>
<P>
If <b>pcretest</b> is given two filename arguments, it reads from the first and
writes to the second. If it is given only one filename argument, it reads from
that file and writes to stdout. Otherwise, it reads from stdin and writes to
stdout, and prompts for each line of input, using "re&#62;" to prompt for regular
expressions, and "data&#62;" to prompt for data lines.
</P>
<P>
When <b>pcretest</b> is built, a configuration option can specify that it should
be linked with the <b>libreadline</b> library. When this is done, if the input
is from a terminal, it is read using the <b>readline()</b> function. This
provides line-editing and history facilities. The output from the <b>-help</b>
option states whether or not <b>readline()</b> will be used.
</P>
<P>
The program handles any number of sets of input on a single input file. Each
set starts with a regular expression, and continues with any number of data
lines to be matched against that pattern.
</P>
<P>
Each data line is matched separately and independently. If you want to do
multi-line matches, you have to use the \n escape sequence (or \r or \r\n,
etc., depending on the newline setting) in a single line of input to encode the
newline sequences. There is no limit on the length of data lines; the input
buffer is automatically extended if it is too small.
</P>
<P>
An empty line signals the end of the data lines, at which point a new regular
expression is read. The regular expressions are given enclosed in any
non-alphanumeric delimiters other than backslash, for example:
<pre>
  /(a|bc)x+yz/
</pre>
White space before the initial delimiter is ignored. A regular expression may
be continued over several input lines, in which case the newline characters are
included within it. It is possible to include the delimiter within the pattern
by escaping it, for example
<pre>
  /abc\/def/
</pre>
If you do so, the escape and the delimiter form part of the pattern, but since
delimiters are always non-alphanumeric, this does not affect its interpretation.
If the terminating delimiter is immediately followed by a backslash, for
example,
<pre>
  /abc/\
</pre>
then a backslash is added to the end of the pattern. This is done to provide a
way of testing the error condition that arises if a pattern finishes with a
backslash, because
<pre>
  /abc\/
</pre>
is interpreted as the first line of a pattern that starts with "abc/", causing
pcretest to read the next line as a continuation of the regular expression.
</P>
<br><a name="SEC6" href="#TOC1">PATTERN MODIFIERS</a><br>
<P>
A pattern may be followed by any number of modifiers, which are mostly single
characters, though some of these can be qualified by further characters.
Following Perl usage, these are referred to below as, for example, "the
<b>/i</b> modifier", even though the delimiter of the pattern need not always be
a slash, and no slash is used when writing modifiers. White space may appear
between the final pattern delimiter and the first modifier, and between the
modifiers themselves. For reference, here is a complete list of modifiers. They
fall into several groups that are described in detail in the following
sections.
<pre>
  <b>/8</b>              set UTF mode
  <b>/9</b>              set PCRE_NEVER_UTF (locks out UTF mode)
  <b>/?</b>              disable UTF validity check
  <b>/+</b>              show remainder of subject after match
  <b>/=</b>              show all captures (not just those that are set)

  <b>/A</b>              set PCRE_ANCHORED
  <b>/B</b>              show compiled code
  <b>/C</b>              set PCRE_AUTO_CALLOUT
  <b>/D</b>              same as <b>/B</b> plus <b>/I</b>
  <b>/E</b>              set PCRE_DOLLAR_ENDONLY
  <b>/F</b>              flip byte order in compiled pattern
  <b>/f</b>              set PCRE_FIRSTLINE
  <b>/G</b>              find all matches (shorten string)
  <b>/g</b>              find all matches (use startoffset)
  <b>/I</b>              show information about pattern
  <b>/i</b>              set PCRE_CASELESS
  <b>/J</b>              set PCRE_DUPNAMES
  <b>/K</b>              show backtracking control names
  <b>/L</b>              set locale
  <b>/M</b>              show compiled memory size
  <b>/m</b>              set PCRE_MULTILINE
  <b>/N</b>              set PCRE_NO_AUTO_CAPTURE
  <b>/O</b>              set PCRE_NO_AUTO_POSSESS
  <b>/P</b>              use the POSIX wrapper
  <b>/Q</b>              test external stack check function
  <b>/S</b>              study the pattern after compilation
  <b>/s</b>              set PCRE_DOTALL
  <b>/T</b>              select character tables
  <b>/U</b>              set PCRE_UNGREEDY
  <b>/W</b>              set PCRE_UCP
  <b>/X</b>              set PCRE_EXTRA
  <b>/x</b>              set PCRE_EXTENDED
  <b>/Y</b>              set PCRE_NO_START_OPTIMIZE
  <b>/Z</b>              don't show lengths in <b>/B</b> output

  <b>/&#60;any&#62;</b>          set PCRE_NEWLINE_ANY
  <b>/&#60;anycrlf&#62;</b>      set PCRE_NEWLINE_ANYCRLF
  <b>/&#60;cr&#62;</b>           set PCRE_NEWLINE_CR
  <b>/&#60;crlf&#62;</b>         set PCRE_NEWLINE_CRLF
  <b>/&#60;lf&#62;</b>           set PCRE_NEWLINE_LF
  <b>/&#60;bsr_anycrlf&#62;</b>  set PCRE_BSR_ANYCRLF
  <b>/&#60;bsr_unicode&#62;</b>  set PCRE_BSR_UNICODE
  <b>/&#60;JS&#62;</b>           set PCRE_JAVASCRIPT_COMPAT

</PRE>
</P>
<br><b>
Perl-compatible modifiers
</b><br>
<P>
The <b>/i</b>, <b>/m</b>, <b>/s</b>, and <b>/x</b> modifiers set the PCRE_CASELESS,
PCRE_MULTILINE, PCRE_DOTALL, or PCRE_EXTENDED options, respectively, when
<b>pcre[16|32]_compile()</b> is called. These four modifier letters have the same
effect as they do in Perl. For example:
<pre>
  /caseless/i

</PRE>
</P>
<br><b>
Modifiers for other PCRE options
</b><br>
<P>
The following table shows additional modifiers for setting PCRE compile-time
options that do not correspond to anything in Perl:
<pre>
  <b>/8</b>              PCRE_UTF8           ) when using the 8-bit
  <b>/?</b>              PCRE_NO_UTF8_CHECK  )   library

  <b>/8</b>              PCRE_UTF16          ) when using the 16-bit
  <b>/?</b>              PCRE_NO_UTF16_CHECK )   library

  <b>/8</b>              PCRE_UTF32          ) when using the 32-bit
  <b>/?</b>              PCRE_NO_UTF32_CHECK )   library

  <b>/9</b>              PCRE_NEVER_UTF
  <b>/A</b>              PCRE_ANCHORED
  <b>/C</b>              PCRE_AUTO_CALLOUT
  <b>/E</b>              PCRE_DOLLAR_ENDONLY
  <b>/f</b>              PCRE_FIRSTLINE
  <b>/J</b>              PCRE_DUPNAMES
  <b>/N</b>              PCRE_NO_AUTO_CAPTURE
  <b>/O</b>              PCRE_NO_AUTO_POSSESS
  <b>/U</b>              PCRE_UNGREEDY
  <b>/W</b>              PCRE_UCP
  <b>/X</b>              PCRE_EXTRA
  <b>/Y</b>              PCRE_NO_START_OPTIMIZE
  <b>/&#60;any&#62;</b>          PCRE_NEWLINE_ANY
  <b>/&#60;anycrlf&#62;</b>      PCRE_NEWLINE_ANYCRLF
  <b>/&#60;cr&#62;</b>           PCRE_NEWLINE_CR
  <b>/&#60;crlf&#62;</b>         PCRE_NEWLINE_CRLF
  <b>/&#60;lf&#62;</b>           PCRE_NEWLINE_LF
  <b>/&#60;bsr_anycrlf&#62;</b>  PCRE_BSR_ANYCRLF
  <b>/&#60;bsr_unicode&#62;</b>  PCRE_BSR_UNICODE
  <b>/&#60;JS&#62;</b>           PCRE_JAVASCRIPT_COMPAT
</pre>
The modifiers that are enclosed in angle brackets are literal strings as shown,
including the angle brackets, but the letters within can be in either case.
This example sets multiline matching with CRLF as the line ending sequence:
<pre>
  /^abc/m&#60;CRLF&#62;
</pre>
As well as turning on the PCRE_UTF8/16/32 option, the <b>/8</b> modifier causes
all non-printing characters in output strings to be printed using the
\x{hh...} notation. Otherwise, those less than 0x100 are output in hex without
the curly brackets.
</P>
<P>
Full details of the PCRE options are given in the
<a href="pcreapi.html"><b>pcreapi</b></a>
documentation.
</P>
<br><b>
Finding all matches in a string
</b><br>
<P>
Searching for all possible matches within each subject string can be requested
by the <b>/g</b> or <b>/G</b> modifier. After finding a match, PCRE is called
again to search the remainder of the subject string. The difference between
<b>/g</b> and <b>/G</b> is that the former uses the <i>startoffset</i> argument to
<b>pcre[16|32]_exec()</b> to start searching at a new point within the entire
string (which is in effect what Perl does), whereas the latter passes over a
shortened substring. This makes a difference to the matching process if the
pattern begins with a lookbehind assertion (including \b or \B).
</P>
<P>
If any call to <b>pcre[16|32]_exec()</b> in a <b>/g</b> or <b>/G</b> sequence matches
an empty string, the next call is done with the PCRE_NOTEMPTY_ATSTART and
PCRE_ANCHORED flags set in order to search for another, non-empty, match at the
same point. If this second match fails, the start offset is advanced, and the
normal match is retried. This imitates the way Perl handles such cases when
using the <b>/g</b> modifier or the <b>split()</b> function. Normally, the start
offset is advanced by one character, but if the newline convention recognizes
CRLF as a newline, and the current character is CR followed by LF, an advance
of two is used.
</P>
<br><b>
Other modifiers
</b><br>
<P>
There are yet more modifiers for controlling the way <b>pcretest</b>
operates.
</P>
<P>
The <b>/+</b> modifier requests that as well as outputting the substring that
matched the entire pattern, <b>pcretest</b> should in addition output the
remainder of the subject string. This is useful for tests where the subject
contains multiple copies of the same substring. If the <b>+</b> modifier appears
twice, the same action is taken for captured substrings. In each case the
remainder is output on the following line with a plus character following the
capture number. Note that this modifier must not immediately follow the /S
modifier because /S+ and /S++ have other meanings.
</P>
<P>
The <b>/=</b> modifier requests that the values of all potential captured
parentheses be output after a match. By default, only those up to the highest
one actually used in the match are output (corresponding to the return code
from <b>pcre[16|32]_exec()</b>). Values in the offsets vector corresponding to
higher numbers should be set to -1, and these are output as "&#60;unset&#62;". This
modifier gives a way of checking that this is happening.
</P>
<P>
The <b>/B</b> modifier is a debugging feature. It requests that <b>pcretest</b>
output a representation of the compiled code after compilation. Normally this
information contains length and offset values; however, if <b>/Z</b> is also
present, this data is replaced by spaces. This is a special feature for use in
the automatic test scripts; it ensures that the same output is generated for
different internal link sizes.
</P>
<P>
The <b>/D</b> modifier is a PCRE debugging feature, and is equivalent to
<b>/BI</b>, that is, both the <b>/B</b> and the <b>/I</b> modifiers.
</P>
<P>
The <b>/F</b> modifier causes <b>pcretest</b> to flip the byte order of the
2-byte and 4-byte fields in the compiled pattern. This facility is for testing
the feature in PCRE that allows it to execute patterns that were compiled on a
host with a different endianness. This feature is not available when the POSIX
interface to PCRE is being used, that is, when the <b>/P</b> pattern modifier is
specified. See also the section about saving and reloading compiled patterns
below.
</P>
<P>
The <b>/I</b> modifier requests that <b>pcretest</b> output information about the
compiled pattern (whether it is anchored, has a fixed first character, and
so on). It does this by calling <b>pcre[16|32]_fullinfo()</b> after compiling a
pattern. If the pattern is studied, the results of that are also output. In
this output, the word "char" means a non-UTF character, that is, the value of a
single data item (8-bit, 16-bit, or 32-bit, depending on the library that is
being tested).
</P>
<P>
The <b>/K</b> modifier requests <b>pcretest</b> to show names from backtracking
control verbs that are returned from calls to <b>pcre[16|32]_exec()</b>. It causes
<b>pcretest</b> to create a <b>pcre[16|32]_extra</b> block if one has not already
been created by a call to <b>pcre[16|32]_study()</b>, and to set the
PCRE_EXTRA_MARK flag and the <b>mark</b> field within it, every time that
<b>pcre[16|32]_exec()</b> is called. If the variable that the <b>mark</b> field
points to is non-NULL for a match, non-match, or partial match, <b>pcretest</b>
prints the string to which it points. For a match, this is shown on a line by
itself, tagged with "MK:". For a non-match it is added to the message.
</P>
<P>
The <b>/L</b> modifier must be followed directly by the name of a locale, for
example,
<pre>
  /pattern/Lfr_FR
</pre>
For this reason, it must be the last modifier. The given locale is set,
<b>pcre[16|32]_maketables()</b> is called to build a set of character tables for
the locale, and this is then passed to <b>pcre[16|32]_compile()</b> when compiling
the regular expression. Without an <b>/L</b> (or <b>/T</b>) modifier, NULL is
passed as the tables pointer; that is, <b>/L</b> applies only to the expression
on which it appears.
</P>
<P>
The <b>/M</b> modifier causes the size in bytes of the memory block used to hold
the compiled pattern to be output. This does not include the size of the
<b>pcre[16|32]</b> block; it is just the actual compiled data. If the pattern is
successfully studied with the PCRE_STUDY_JIT_COMPILE option, the size of the
JIT compiled code is also output.
</P>
<P>
The <b>/Q</b> modifier is used to test the use of <b>pcre_stack_guard</b>. It
must be followed by '0' or '1', specifying the return code to be given from an
external function that is passed to PCRE and used for stack checking during
compilation (see the
<a href="pcreapi.html"><b>pcreapi</b></a>
documentation for details).
</P>
<P>
The <b>/S</b> modifier causes <b>pcre[16|32]_study()</b> to be called after the
expression has been compiled, and the results used when the expression is
matched. There are a number of qualifying characters that may follow <b>/S</b>.
They may appear in any order.
</P>
<P>
If <b>/S</b> is followed by an exclamation mark, <b>pcre[16|32]_study()</b> is
called with the PCRE_STUDY_EXTRA_NEEDED option, causing it always to return a
<b>pcre_extra</b> block, even when studying discovers no useful information.
</P>
<P>
If <b>/S</b> is followed by a second S character, it suppresses studying, even
if it was requested externally by the <b>-s</b> command line option. This makes
it possible to specify that certain patterns are always studied, and others are
never studied, independently of <b>-s</b>. This feature is used in the test
files in a few cases where the output is different when the pattern is studied.
</P>
<P>
If the <b>/S</b> modifier is followed by a + character, the call to
<b>pcre[16|32]_study()</b> is made with all the JIT study options, requesting
just-in-time optimization support if it is available, for both normal and
partial matching. If you want to restrict the JIT compiling modes, you can
follow <b>/S+</b> with a digit in the range 1 to 7:
<pre>
  1  normal match only
  2  soft partial match only
  3  normal match and soft partial match
  4  hard partial match only
  6  soft and hard partial match
  7  all three modes (default)
</pre>
If <b>/S++</b> is used instead of <b>/S+</b> (with or without a following digit),
the text "(JIT)" is added to the first output line after a match or no match
when JIT-compiled code was actually used.
</P>
<P>
Note that there is also an independent <b>/+</b> modifier; it must not be given
immediately after <b>/S</b> or <b>/S+</b> because this will be misinterpreted.
</P>
<P>
If JIT studying is successful, the compiled JIT code will automatically be used
when <b>pcre[16|32]_exec()</b> is run, except when incompatible run-time options
are specified. For more details, see the
<a href="pcrejit.html"><b>pcrejit</b></a>
documentation. See also the <b>\J</b> escape sequence below for a way of
setting the size of the JIT stack.
</P>
<P>
Finally, if <b>/S</b> is followed by a minus character, JIT compilation is
suppressed, even if it was requested externally by the <b>-s</b> command line
option. This makes it possible to specify that JIT is never to be used for
certain patterns.
</P>
<P>
The <b>/T</b> modifier must be followed by a single digit. It causes a specific
set of built-in character tables to be passed to <b>pcre[16|32]_compile()</b>. It
is used in the standard PCRE tests to check behaviour with different character
tables. The digit specifies the tables as follows:
<pre>
  0   the default ASCII tables, as distributed in
        pcre_chartables.c.dist
  1   a set of tables defining ISO 8859 characters
</pre>
In table 1, some characters whose codes are greater than 128 are identified as
letters, digits, spaces, etc.
</P>
<br><b>
Using the POSIX wrapper API
</b><br>
<P>
The <b>/P</b> modifier causes <b>pcretest</b> to call PCRE via the POSIX wrapper
API rather than its native API. This supports only the 8-bit library. When
<b>/P</b> is set, the following modifiers set options for the <b>regcomp()</b>
function:
<pre>
  /i    REG_ICASE
  /m    REG_NEWLINE
  /N    REG_NOSUB
  /s    REG_DOTALL     )
  /U    REG_UNGREEDY   ) These options are not part of
  /W    REG_UCP        )   the POSIX standard
  /8    REG_UTF8       )
</pre>
The <b>/+</b> modifier works as described above. All other modifiers are
ignored.
</P>
<br><b>
Locking out certain modifiers
</b><br>
<P>
PCRE can be compiled with or without support for certain features such as
UTF-8/16/32 or Unicode properties. Accordingly, the standard tests are split up
into a number of different files that are selected for running depending on
which features are available. When updating the tests, it is all too easy to
put a new test into the wrong file by mistake; for example, to put a test that
requires UTF support into a file that is used when it is not available. To help
detect such mistakes as early as possible, there is a facility for locking out
specific modifiers. If an input line for <b>pcretest</b> starts with the string
"&#60; forbid " the following sequence of characters is taken as a list of
forbidden modifiers. For example, in the test files that must not use UTF or
Unicode property support, this line appears:
<pre>
  &#60; forbid 8W
</pre>
This locks out the /8 and /W modifiers. An immediate error is given if they are
subsequently encountered. If the character string contains &#60; but not &#62;, all the
multi-character modifiers that begin with &#60; are locked out. Otherwise, such
modifiers must be explicitly listed, for example:
<pre>
  &#60; forbid &#60;JS&#62;&#60;cr&#62;
</pre>
There must be a single space between &#60; and "forbid" for this feature to be
recognised. If there is not, the line is interpreted either as a request to
re-load a pre-compiled pattern (see "SAVING AND RELOADING COMPILED PATTERNS"
below) or, if there is a another &#60; character, as a pattern that uses &#60; as its
delimiter.
</P>
<br><a name="SEC7" href="#TOC1">DATA LINES</a><br>
<P>
Before each data line is passed to <b>pcre[16|32]_exec()</b>, leading and trailing
white space is removed, and it is then scanned for \ escapes. Some of these
are pretty esoteric features, intended for checking out some of the more
complicated features of PCRE. If you are just testing "ordinary" regular
expressions, you probably don't need any of these. The following escapes are
recognized:
<pre>
  \a         alarm (BEL, \x07)
  \b         backspace (\x08)
  \e         escape (\x27)
  \f         form feed (\x0c)
  \n         newline (\x0a)
  \qdd       set the PCRE_MATCH_LIMIT limit to dd (any number of digits)
  \r         carriage return (\x0d)
  \t         tab (\x09)
  \v         vertical tab (\x0b)
  \nnn       octal character (up to 3 octal digits); always
               a byte unless &#62; 255 in UTF-8 or 16-bit or 32-bit mode
  \o{dd...}  octal character (any number of octal digits}
  \xhh       hexadecimal byte (up to 2 hex digits)
  \x{hh...}  hexadecimal character (any number of hex digits)
  \A         pass the PCRE_ANCHORED option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
  \B         pass the PCRE_NOTBOL option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
  \Cdd       call pcre[16|32]_copy_substring() for substring dd after a successful match (number less than 32)
  \Cname     call pcre[16|32]_copy_named_substring() for substring "name" after a successful match (name termin-
               ated by next non alphanumeric character)
  \C+        show the current captured substrings at callout time
  \C-        do not supply a callout function
  \C!n       return 1 instead of 0 when callout number n is reached
  \C!n!m     return 1 instead of 0 when callout number n is reached for the nth time
  \C*n       pass the number n (may be negative) as callout data; this is used as the callout return value
  \D         use the <b>pcre[16|32]_dfa_exec()</b> match function
  \F         only shortest match for <b>pcre[16|32]_dfa_exec()</b>
  \Gdd       call pcre[16|32]_get_substring() for substring dd after a successful match (number less than 32)
  \Gname     call pcre[16|32]_get_named_substring() for substring "name" after a successful match (name termin-
               ated by next non-alphanumeric character)
  \Jdd       set up a JIT stack of dd kilobytes maximum (any number of digits)
  \L         call pcre[16|32]_get_substringlist() after a successful match
  \M         discover the minimum MATCH_LIMIT and MATCH_LIMIT_RECURSION settings
  \N         pass the PCRE_NOTEMPTY option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>; if used twice, pass the
               PCRE_NOTEMPTY_ATSTART option
  \Odd       set the size of the output vector passed to <b>pcre[16|32]_exec()</b> to dd (any number of digits)
  \P         pass the PCRE_PARTIAL_SOFT option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>; if used twice, pass the
               PCRE_PARTIAL_HARD option
  \Qdd       set the PCRE_MATCH_LIMIT_RECURSION limit to dd (any number of digits)
  \R         pass the PCRE_DFA_RESTART option to <b>pcre[16|32]_dfa_exec()</b>
  \S         output details of memory get/free calls during matching
  \Y         pass the PCRE_NO_START_OPTIMIZE option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
  \Z         pass the PCRE_NOTEOL option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
  \?         pass the PCRE_NO_UTF[8|16|32]_CHECK option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
  \&#62;dd       start the match at offset dd (optional "-"; then any number of digits); this sets the <i>startoffset</i>
               argument for <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
  \&#60;cr&#62;      pass the PCRE_NEWLINE_CR option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
  \&#60;lf&#62;      pass the PCRE_NEWLINE_LF option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
  \&#60;crlf&#62;    pass the PCRE_NEWLINE_CRLF option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
  \&#60;anycrlf&#62; pass the PCRE_NEWLINE_ANYCRLF option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
  \&#60;any&#62;     pass the PCRE_NEWLINE_ANY option to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b>
</pre>
The use of \x{hh...} is not dependent on the use of the <b>/8</b> modifier on
the pattern. It is recognized always. There may be any number of hexadecimal
digits inside the braces; invalid values provoke error messages.
</P>
<P>
Note that \xhh specifies one byte rather than one character in UTF-8 mode;
this makes it possible to construct invalid UTF-8 sequences for testing
purposes. On the other hand, \x{hh} is interpreted as a UTF-8 character in
UTF-8 mode, generating more than one byte if the value is greater than 127.
When testing the 8-bit library not in UTF-8 mode, \x{hh} generates one byte
for values less than 256, and causes an error for greater values.
</P>
<P>
In UTF-16 mode, all 4-digit \x{hhhh} values are accepted. This makes it
possible to construct invalid UTF-16 sequences for testing purposes.
</P>
<P>
In UTF-32 mode, all 4- to 8-digit \x{...} values are accepted. This makes it
possible to construct invalid UTF-32 sequences for testing purposes.
</P>
<P>
The escapes that specify line ending sequences are literal strings, exactly as
shown. No more than one newline setting should be present in any data line.
</P>
<P>
A backslash followed by anything else just escapes the anything else. If
the very last character is a backslash, it is ignored. This gives a way of
passing an empty line as data, since a real empty line terminates the data
input.
</P>
<P>
The <b>\J</b> escape provides a way of setting the maximum stack size that is
used by the just-in-time optimization code. It is ignored if JIT optimization
is not being used. Providing a stack that is larger than the default 32K is
necessary only for very complicated patterns.
</P>
<P>
If \M is present, <b>pcretest</b> calls <b>pcre[16|32]_exec()</b> several times,
with different values in the <i>match_limit</i> and <i>match_limit_recursion</i>
fields of the <b>pcre[16|32]_extra</b> data structure, until it finds the minimum
numbers for each parameter that allow <b>pcre[16|32]_exec()</b> to complete without
error. Because this is testing a specific feature of the normal interpretive
<b>pcre[16|32]_exec()</b> execution, the use of any JIT optimization that might
have been set up by the <b>/S+</b> qualifier of <b>-s+</b> option is disabled.
</P>
<P>
The <i>match_limit</i> number is a measure of the amount of backtracking
that takes place, and checking it out can be instructive. For most simple
matches, the number is quite small, but for patterns with very large numbers of
matching possibilities, it can become large very quickly with increasing length
of subject string. The <i>match_limit_recursion</i> number is a measure of how
much stack (or, if PCRE is compiled with NO_RECURSE, how much heap) memory is
needed to complete the match attempt.
</P>
<P>
When \O is used, the value specified may be higher or lower than the size set
by the <b>-O</b> command line option (or defaulted to 45); \O applies only to
the call of <b>pcre[16|32]_exec()</b> for the line in which it appears.
</P>
<P>
If the <b>/P</b> modifier was present on the pattern, causing the POSIX wrapper
API to be used, the only option-setting sequences that have any effect are \B,
\N, and \Z, causing REG_NOTBOL, REG_NOTEMPTY, and REG_NOTEOL, respectively,
to be passed to <b>regexec()</b>.
</P>
<br><a name="SEC8" href="#TOC1">THE ALTERNATIVE MATCHING FUNCTION</a><br>
<P>
By default, <b>pcretest</b> uses the standard PCRE matching function,
<b>pcre[16|32]_exec()</b> to match each data line. PCRE also supports an
alternative matching function, <b>pcre[16|32]_dfa_test()</b>, which operates in a
different way, and has some restrictions. The differences between the two
functions are described in the
<a href="pcrematching.html"><b>pcrematching</b></a>
documentation.
</P>
<P>
If a data line contains the \D escape sequence, or if the command line
contains the <b>-dfa</b> option, the alternative matching function is used.
This function finds all possible matches at a given point. If, however, the \F
escape sequence is present in the data line, it stops after the first match is
found. This is always the shortest possible match.
</P>
<br><a name="SEC9" href="#TOC1">DEFAULT OUTPUT FROM PCRETEST</a><br>
<P>
This section describes the output when the normal matching function,
<b>pcre[16|32]_exec()</b>, is being used.
</P>
<P>
When a match succeeds, <b>pcretest</b> outputs the list of captured substrings
that <b>pcre[16|32]_exec()</b> returns, starting with number 0 for the string that
matched the whole pattern. Otherwise, it outputs "No match" when the return is
PCRE_ERROR_NOMATCH, and "Partial match:" followed by the partially matching
substring when <b>pcre[16|32]_exec()</b> returns PCRE_ERROR_PARTIAL. (Note that
this is the entire substring that was inspected during the partial match; it
may include characters before the actual match start if a lookbehind assertion,
\K, \b, or \B was involved.) For any other return, <b>pcretest</b> outputs
the PCRE negative error number and a short descriptive phrase. If the error is
a failed UTF string check, the offset of the start of the failing character and
the reason code are also output, provided that the size of the output vector is
at least two. Here is an example of an interactive <b>pcretest</b> run.
<pre>
  $ pcretest
  PCRE version 8.13 2011-04-30

    re&#62; /^abc(\d+)/
  data&#62; abc123
   0: abc123
   1: 123
  data&#62; xyz
  No match
</pre>
Unset capturing substrings that are not followed by one that is set are not
returned by <b>pcre[16|32]_exec()</b>, and are not shown by <b>pcretest</b>. In the
following example, there are two capturing substrings, but when the first data
line is matched, the second, unset substring is not shown. An "internal" unset
substring is shown as "&#60;unset&#62;", as for the second data line.
<pre>
    re&#62; /(a)|(b)/
  data&#62; a
   0: a
   1: a
  data&#62; b
   0: b
   1: &#60;unset&#62;
   2: b
</pre>
If the strings contain any non-printing characters, they are output as \xhh
escapes if the value is less than 256 and UTF mode is not set. Otherwise they
are output as \x{hh...} escapes. See below for the definition of non-printing
characters. If the pattern has the <b>/+</b> modifier, the output for substring
0 is followed by the the rest of the subject string, identified by "0+" like
this:
<pre>
    re&#62; /cat/+
  data&#62; cataract
   0: cat
   0+ aract
</pre>
If the pattern has the <b>/g</b> or <b>/G</b> modifier, the results of successive
matching attempts are output in sequence, like this:
<pre>
    re&#62; /\Bi(\w\w)/g
  data&#62; Mississippi
   0: iss
   1: ss
   0: iss
   1: ss
   0: ipp
   1: pp
</pre>
"No match" is output only if the first match attempt fails. Here is an example
of a failure message (the offset 4 that is specified by \&#62;4 is past the end of
the subject string):
<pre>
    re&#62; /xyz/
  data&#62; xyz\&#62;4
  Error -24 (bad offset value)
</PRE>
</P>
<P>
If any of the sequences <b>\C</b>, <b>\G</b>, or <b>\L</b> are present in a
data line that is successfully matched, the substrings extracted by the
convenience functions are output with C, G, or L after the string number
instead of a colon. This is in addition to the normal full list. The string
length (that is, the return from the extraction function) is given in
parentheses after each string for <b>\C</b> and <b>\G</b>.
</P>
<P>
Note that whereas patterns can be continued over several lines (a plain "&#62;"
prompt is used for continuations), data lines may not. However newlines can be
included in data by means of the \n escape (or \r, \r\n, etc., depending on
the newline sequence setting).
</P>
<br><a name="SEC10" href="#TOC1">OUTPUT FROM THE ALTERNATIVE MATCHING FUNCTION</a><br>
<P>
When the alternative matching function, <b>pcre[16|32]_dfa_exec()</b>, is used (by
means of the \D escape sequence or the <b>-dfa</b> command line option), the
output consists of a list of all the matches that start at the first point in
the subject where there is at least one match. For example:
<pre>
    re&#62; /(tang|tangerine|tan)/
  data&#62; yellow tangerine\D
   0: tangerine
   1: tang
   2: tan
</pre>
(Using the normal matching function on this data finds only "tang".) The
longest matching string is always given first (and numbered zero). After a
PCRE_ERROR_PARTIAL return, the output is "Partial match:", followed by the
partially matching substring. (Note that this is the entire substring that was
inspected during the partial match; it may include characters before the actual
match start if a lookbehind assertion, \K, \b, or \B was involved.)
</P>
<P>
If <b>/g</b> is present on the pattern, the search for further matches resumes
at the end of the longest match. For example:
<pre>
    re&#62; /(tang|tangerine|tan)/g
  data&#62; yellow tangerine and tangy sultana\D
   0: tangerine
   1: tang
   2: tan
   0: tang
   1: tan
   0: tan
</pre>
Since the matching function does not support substring capture, the escape
sequences that are concerned with captured substrings are not relevant.
</P>
<br><a name="SEC11" href="#TOC1">RESTARTING AFTER A PARTIAL MATCH</a><br>
<P>
When the alternative matching function has given the PCRE_ERROR_PARTIAL return,
indicating that the subject partially matched the pattern, you can restart the
match with additional subject data by means of the \R escape sequence. For
example:
<pre>
    re&#62; /^\d?\d(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\d\d$/
  data&#62; 23ja\P\D
  Partial match: 23ja
  data&#62; n05\R\D
   0: n05
</pre>
For further information about partial matching, see the
<a href="pcrepartial.html"><b>pcrepartial</b></a>
documentation.
</P>
<br><a name="SEC12" href="#TOC1">CALLOUTS</a><br>
<P>
If the pattern contains any callout requests, <b>pcretest</b>'s callout function
is called during matching. This works with both matching functions. By default,
the called function displays the callout number, the start and current
positions in the text at the callout time, and the next pattern item to be
tested. For example:
<pre>
  ---&#62;pqrabcdef
    0    ^  ^     \d
</pre>
This output indicates that callout number 0 occurred for a match attempt
starting at the fourth character of the subject string, when the pointer was at
the seventh character of the data, and when the next pattern item was \d. Just
one circumflex is output if the start and current positions are the same.
</P>
<P>
Callouts numbered 255 are assumed to be automatic callouts, inserted as a
result of the <b>/C</b> pattern modifier. In this case, instead of showing the
callout number, the offset in the pattern, preceded by a plus, is output. For
example:
<pre>
    re&#62; /\d?[A-E]\*/C
  data&#62; E*
  ---&#62;E*
   +0 ^      \d?
   +3 ^      [A-E]
   +8 ^^     \*
  +10 ^ ^
   0: E*
</pre>
If a pattern contains (*MARK) items, an additional line is output whenever
a change of latest mark is passed to the callout function. For example:
<pre>
    re&#62; /a(*MARK:X)bc/C
  data&#62; abc
  ---&#62;abc
   +0 ^       a
   +1 ^^      (*MARK:X)
  +10 ^^      b
  Latest Mark: X
  +11 ^ ^     c
  +12 ^  ^
   0: abc
</pre>
The mark changes between matching "a" and "b", but stays the same for the rest
of the match, so nothing more is output. If, as a result of backtracking, the
mark reverts to being unset, the text "&#60;unset&#62;" is output.
</P>
<P>
The callout function in <b>pcretest</b> returns zero (carry on matching) by
default, but you can use a \C item in a data line (as described above) to
change this and other parameters of the callout.
</P>
<P>
Inserting callouts can be helpful when using <b>pcretest</b> to check
complicated regular expressions. For further information about callouts, see
the
<a href="pcrecallout.html"><b>pcrecallout</b></a>
documentation.
</P>
<br><a name="SEC13" href="#TOC1">NON-PRINTING CHARACTERS</a><br>
<P>
When <b>pcretest</b> is outputting text in the compiled version of a pattern,
bytes other than 32-126 are always treated as non-printing characters are are
therefore shown as hex escapes.
</P>
<P>
When <b>pcretest</b> is outputting text that is a matched part of a subject
string, it behaves in the same way, unless a different locale has been set for
the pattern (using the <b>/L</b> modifier). In this case, the <b>isprint()</b>
function to distinguish printing and non-printing characters.
</P>
<br><a name="SEC14" href="#TOC1">SAVING AND RELOADING COMPILED PATTERNS</a><br>
<P>
The facilities described in this section are not available when the POSIX
interface to PCRE is being used, that is, when the <b>/P</b> pattern modifier is
specified.
</P>
<P>
When the POSIX interface is not in use, you can cause <b>pcretest</b> to write a
compiled pattern to a file, by following the modifiers with &#62; and a file name.
For example:
<pre>
  /pattern/im &#62;/some/file
</pre>
See the
<a href="pcreprecompile.html"><b>pcreprecompile</b></a>
documentation for a discussion about saving and re-using compiled patterns.
Note that if the pattern was successfully studied with JIT optimization, the
JIT data cannot be saved.
</P>
<P>
The data that is written is binary. The first eight bytes are the length of the
compiled pattern data followed by the length of the optional study data, each
written as four bytes in big-endian order (most significant byte first). If
there is no study data (either the pattern was not studied, or studying did not
return any data), the second length is zero. The lengths are followed by an
exact copy of the compiled pattern. If there is additional study data, this
(excluding any JIT data) follows immediately after the compiled pattern. After
writing the file, <b>pcretest</b> expects to read a new pattern.
</P>
<P>
A saved pattern can be reloaded into <b>pcretest</b> by specifying &#60; and a file
name instead of a pattern. There must be no space between &#60; and the file name,
which must not contain a &#60; character, as otherwise <b>pcretest</b> will
interpret the line as a pattern delimited by &#60; characters. For example:
<pre>
   re&#62; &#60;/some/file
  Compiled pattern loaded from /some/file
  No study data
</pre>
If the pattern was previously studied with the JIT optimization, the JIT
information cannot be saved and restored, and so is lost. When the pattern has
been loaded, <b>pcretest</b> proceeds to read data lines in the usual way.
</P>
<P>
You can copy a file written by <b>pcretest</b> to a different host and reload it
there, even if the new host has opposite endianness to the one on which the
pattern was compiled. For example, you can compile on an i86 machine and run on
a SPARC machine. When a pattern is reloaded on a host with different
endianness, the confirmation message is changed to:
<pre>
  Compiled pattern (byte-inverted) loaded from /some/file
</pre>
The test suite contains some saved pre-compiled patterns with different
endianness. These are reloaded using "&#60;!" instead of just "&#60;". This suppresses
the "(byte-inverted)" text so that the output is the same on all hosts. It also
forces debugging output once the pattern has been reloaded.
</P>
<P>
File names for saving and reloading can be absolute or relative, but note that
the shell facility of expanding a file name that starts with a tilde (~) is not
available.
</P>
<P>
The ability to save and reload files in <b>pcretest</b> is intended for testing
and experimentation. It is not intended for production use because only a
single pattern can be written to a file. Furthermore, there is no facility for
supplying custom character tables for use with a reloaded pattern. If the
original pattern was compiled with custom tables, an attempt to match a subject
string using a reloaded pattern is likely to cause <b>pcretest</b> to crash.
Finally, if you attempt to load a file that is not in the correct format, the
result is undefined.
</P>
<br><a name="SEC15" href="#TOC1">SEE ALSO</a><br>
<P>
<b>pcre</b>(3), <b>pcre16</b>(3), <b>pcre32</b>(3), <b>pcreapi</b>(3),
<b>pcrecallout</b>(3),
<b>pcrejit</b>, <b>pcrematching</b>(3), <b>pcrepartial</b>(d),
<b>pcrepattern</b>(3), <b>pcreprecompile</b>(3).
</P>
<br><a name="SEC16" href="#TOC1">AUTHOR</a><br>
<P>
Philip Hazel
<br>
University Computing Service
<br>
Cambridge CB2 3QH, England.
<br>
</P>
<br><a name="SEC17" href="#TOC1">REVISION</a><br>
<P>
Last updated: 09 February 2014
<br>
Copyright &copy; 1997-2014 University of Cambridge.
<br>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
