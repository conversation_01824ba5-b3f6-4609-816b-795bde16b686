<html>
<head>
<title>pcre_get_substring_list specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre_get_substring_list man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
SYNOPSIS
</b><br>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<P>
<b>int pcre_get_substring_list(const char *<i>subject</i>,</b>
<b>     int *<i>ovector</i>, int <i>stringcount</i>, const char ***<i>listptr</i>);</b>
<br>
<br>
<b>int pcre16_get_substring_list(PCRE_SPTR16 <i>subject</i>,</b>
<b>     int *<i>ovector</i>, int <i>stringcount</i>, PCRE_SPTR16 **<i>listptr</i>);</b>
<br>
<br>
<b>int pcre32_get_substring_list(PCRE_SPTR32 <i>subject</i>,</b>
<b>     int *<i>ovector</i>, int <i>stringcount</i>, PCRE_SPTR32 **<i>listptr</i>);</b>
</P>
<br><b>
DESCRIPTION
</b><br>
<P>
This is a convenience function for extracting a list of all the captured
substrings. The arguments are:
<pre>
  <i>subject</i>       Subject that has been successfully matched
  <i>ovector</i>       Offset vector that <b>pcre[16|32]_exec</b> used
  <i>stringcount</i>   Value returned by <b>pcre[16|32]_exec</b>
  <i>listptr</i>       Where to put a pointer to the list
</pre>
The memory in which the substrings and the list are placed is obtained by
calling <b>pcre[16|32]_malloc()</b>. The convenience function
<b>pcre[16|32]_free_substring_list()</b> can be used to free it when it is no
longer needed. A pointer to a list of pointers is put in the variable whose
address is in <i>listptr</i>. The list is terminated by a NULL pointer. The
yield of the function is zero on success or PCRE_ERROR_NOMEMORY if sufficient
memory could not be obtained.
</P>
<P>
There is a complete description of the PCRE native API in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page and a description of the POSIX API in the
<a href="pcreposix.html"><b>pcreposix</b></a>
page.
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
