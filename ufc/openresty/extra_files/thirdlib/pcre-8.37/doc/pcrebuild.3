.TH PCREBUILD 3 "12 May 2013" "PCRE 8.33"
.SH NAME
PCRE - Perl-compatible regular expressions
.
.
.SH "BUILDING PCRE"
.rs
.sp
PCRE is distributed with a \fBconfigure\fP script that can be used to build the
library in Unix-like environments using the applications known as Autotools.
Also in the distribution are files to support building using \fBCMake\fP
instead of \fBconfigure\fP. The text file
.\" HTML <a href="README.txt">
.\" </a>
\fBREADME\fP
.\"
contains general information about building with Autotools (some of which is
repeated below), and also has some comments about building on various operating
systems. There is a lot more information about building PCRE without using
Autotools (including information about using \fBCMake\fP and building "by
hand") in the text file called
.\" HTML <a href="NON-AUTOTOOLS-BUILD.txt">
.\" </a>
\fBNON-AUTOTOOLS-BUILD\fP.
.\"
You should consult this file as well as the
.\" HTML <a href="README.txt">
.\" </a>
\fBREADME\fP
.\"
file if you are building in a non-Unix-like environment.
.
.
.SH "PCRE BUILD-TIME OPTIONS"
.rs
.sp
The rest of this document describes the optional features of PCRE that can be
selected when the library is compiled. It assumes use of the \fBconfigure\fP
script, where the optional features are selected or deselected by providing
options to \fBconfigure\fP before running the \fBmake\fP command. However, the
same options can be selected in both Unix-like and non-Unix-like environments
using the GUI facility of \fBcmake-gui\fP if you are using \fBCMake\fP instead
of \fBconfigure\fP to build PCRE.
.P
If you are not using Autotools or \fBCMake\fP, option selection can be done by
editing the \fBconfig.h\fP file, or by passing parameter settings to the
compiler, as described in
.\" HTML <a href="NON-AUTOTOOLS-BUILD.txt">
.\" </a>
\fBNON-AUTOTOOLS-BUILD\fP.
.\"
.P
The complete list of options for \fBconfigure\fP (which includes the standard
ones such as the selection of the installation directory) can be obtained by
running
.sp
  ./configure --help
.sp
The following sections include descriptions of options whose names begin with
--enable or --disable. These settings specify changes to the defaults for the
\fBconfigure\fP command. Because of the way that \fBconfigure\fP works,
--enable and --disable always come in pairs, so the complementary option always
exists as well, but as it specifies the default, it is not described.
.
.
.SH "BUILDING 8-BIT, 16-BIT AND 32-BIT LIBRARIES"
.rs
.sp
By default, a library called \fBlibpcre\fP is built, containing functions that
take string arguments contained in vectors of bytes, either as single-byte
characters, or interpreted as UTF-8 strings. You can also build a separate
library, called \fBlibpcre16\fP, in which strings are contained in vectors of
16-bit data units and interpreted either as single-unit characters or UTF-16
strings, by adding
.sp
  --enable-pcre16
.sp
to the \fBconfigure\fP command. You can also build yet another separate
library, called \fBlibpcre32\fP, in which strings are contained in vectors of
32-bit data units and interpreted either as single-unit characters or UTF-32
strings, by adding
.sp
  --enable-pcre32
.sp
to the \fBconfigure\fP command. If you do not want the 8-bit library, add
.sp
  --disable-pcre8
.sp
as well. At least one of the three libraries must be built. Note that the C++
and POSIX wrappers are for the 8-bit library only, and that \fBpcregrep\fP is
an 8-bit program. None of these are built if you select only the 16-bit or
32-bit libraries.
.
.
.SH "BUILDING SHARED AND STATIC LIBRARIES"
.rs
.sp
The Autotools PCRE building process uses \fBlibtool\fP to build both shared and
static libraries by default. You can suppress one of these by adding one of
.sp
  --disable-shared
  --disable-static
.sp
to the \fBconfigure\fP command, as required.
.
.
.SH "C++ SUPPORT"
.rs
.sp
By default, if the 8-bit library is being built, the \fBconfigure\fP script
will search for a C++ compiler and C++ header files. If it finds them, it
automatically builds the C++ wrapper library (which supports only 8-bit
strings). You can disable this by adding
.sp
  --disable-cpp
.sp
to the \fBconfigure\fP command.
.
.
.SH "UTF-8, UTF-16 AND UTF-32 SUPPORT"
.rs
.sp
To build PCRE with support for UTF Unicode character strings, add
.sp
  --enable-utf
.sp
to the \fBconfigure\fP command. This setting applies to all three libraries,
adding support for UTF-8 to the 8-bit library, support for UTF-16 to the 16-bit
library, and support for UTF-32 to the to the 32-bit library. There are no
separate options for enabling UTF-8, UTF-16 and UTF-32 independently because
that would allow ridiculous settings such as requesting UTF-16 support while
building only the 8-bit library. It is not possible to build one library with
UTF support and another without in the same configuration. (For backwards
compatibility, --enable-utf8 is a synonym of --enable-utf.)
.P
Of itself, this setting does not make PCRE treat strings as UTF-8, UTF-16 or
UTF-32. As well as compiling PCRE with this option, you also have have to set
the PCRE_UTF8, PCRE_UTF16 or PCRE_UTF32 option (as appropriate) when you call
one of the pattern compiling functions.
.P
If you set --enable-utf when compiling in an EBCDIC environment, PCRE expects
its input to be either ASCII or UTF-8 (depending on the run-time option). It is
not possible to support both EBCDIC and UTF-8 codes in the same version of the
library. Consequently, --enable-utf and --enable-ebcdic are mutually
exclusive.
.
.
.SH "UNICODE CHARACTER PROPERTY SUPPORT"
.rs
.sp
UTF support allows the libraries to process character codepoints up to 0x10ffff
in the strings that they handle. On its own, however, it does not provide any
facilities for accessing the properties of such characters. If you want to be
able to use the pattern escapes \eP, \ep, and \eX, which refer to Unicode
character properties, you must add
.sp
  --enable-unicode-properties
.sp
to the \fBconfigure\fP command. This implies UTF support, even if you have
not explicitly requested it.
.P
Including Unicode property support adds around 30K of tables to the PCRE
library. Only the general category properties such as \fILu\fP and \fINd\fP are
supported. Details are given in the
.\" HREF
\fBpcrepattern\fP
.\"
documentation.
.
.
.SH "JUST-IN-TIME COMPILER SUPPORT"
.rs
.sp
Just-in-time compiler support is included in the build by specifying
.sp
  --enable-jit
.sp
This support is available only for certain hardware architectures. If this
option is set for an unsupported architecture, a compile time error occurs.
See the
.\" HREF
\fBpcrejit\fP
.\"
documentation for a discussion of JIT usage. When JIT support is enabled,
pcregrep automatically makes use of it, unless you add
.sp
  --disable-pcregrep-jit
.sp
to the "configure" command.
.
.
.SH "CODE VALUE OF NEWLINE"
.rs
.sp
By default, PCRE interprets the linefeed (LF) character as indicating the end
of a line. This is the normal newline character on Unix-like systems. You can
compile PCRE to use carriage return (CR) instead, by adding
.sp
  --enable-newline-is-cr
.sp
to the \fBconfigure\fP command. There is also a --enable-newline-is-lf option,
which explicitly specifies linefeed as the newline character.
.sp
Alternatively, you can specify that line endings are to be indicated by the two
character sequence CRLF. If you want this, add
.sp
  --enable-newline-is-crlf
.sp
to the \fBconfigure\fP command. There is a fourth option, specified by
.sp
  --enable-newline-is-anycrlf
.sp
which causes PCRE to recognize any of the three sequences CR, LF, or CRLF as
indicating a line ending. Finally, a fifth option, specified by
.sp
  --enable-newline-is-any
.sp
causes PCRE to recognize any Unicode newline sequence.
.P
Whatever line ending convention is selected when PCRE is built can be
overridden when the library functions are called. At build time it is
conventional to use the standard for your operating system.
.
.
.SH "WHAT \eR MATCHES"
.rs
.sp
By default, the sequence \eR in a pattern matches any Unicode newline sequence,
whatever has been selected as the line ending sequence. If you specify
.sp
  --enable-bsr-anycrlf
.sp
the default is changed so that \eR matches only CR, LF, or CRLF. Whatever is
selected when PCRE is built can be overridden when the library functions are
called.
.
.
.SH "POSIX MALLOC USAGE"
.rs
.sp
When the 8-bit library is called through the POSIX interface (see the
.\" HREF
\fBpcreposix\fP
.\"
documentation), additional working storage is required for holding the pointers
to capturing substrings, because PCRE requires three integers per substring,
whereas the POSIX interface provides only two. If the number of expected
substrings is small, the wrapper function uses space on the stack, because this
is faster than using \fBmalloc()\fP for each call. The default threshold above
which the stack is no longer used is 10; it can be changed by adding a setting
such as
.sp
  --with-posix-malloc-threshold=20
.sp
to the \fBconfigure\fP command.
.
.
.SH "HANDLING VERY LARGE PATTERNS"
.rs
.sp
Within a compiled pattern, offset values are used to point from one part to
another (for example, from an opening parenthesis to an alternation
metacharacter). By default, in the 8-bit and 16-bit libraries, two-byte values
are used for these offsets, leading to a maximum size for a compiled pattern of
around 64K. This is sufficient to handle all but the most gigantic patterns.
Nevertheless, some people do want to process truly enormous patterns, so it is
possible to compile PCRE to use three-byte or four-byte offsets by adding a
setting such as
.sp
  --with-link-size=3
.sp
to the \fBconfigure\fP command. The value given must be 2, 3, or 4. For the
16-bit library, a value of 3 is rounded up to 4. In these libraries, using
longer offsets slows down the operation of PCRE because it has to load
additional data when handling them. For the 32-bit library the value is always
4 and cannot be overridden; the value of --with-link-size is ignored.
.
.
.SH "AVOIDING EXCESSIVE STACK USAGE"
.rs
.sp
When matching with the \fBpcre_exec()\fP function, PCRE implements backtracking
by making recursive calls to an internal function called \fBmatch()\fP. In
environments where the size of the stack is limited, this can severely limit
PCRE's operation. (The Unix environment does not usually suffer from this
problem, but it may sometimes be necessary to increase the maximum stack size.
There is a discussion in the
.\" HREF
\fBpcrestack\fP
.\"
documentation.) An alternative approach to recursion that uses memory from the
heap to remember data, instead of using recursive function calls, has been
implemented to work round the problem of limited stack size. If you want to
build a version of PCRE that works this way, add
.sp
  --disable-stack-for-recursion
.sp
to the \fBconfigure\fP command. With this configuration, PCRE will use the
\fBpcre_stack_malloc\fP and \fBpcre_stack_free\fP variables to call memory
management functions. By default these point to \fBmalloc()\fP and
\fBfree()\fP, but you can replace the pointers so that your own functions are
used instead.
.P
Separate functions are provided rather than using \fBpcre_malloc\fP and
\fBpcre_free\fP because the usage is very predictable: the block sizes
requested are always the same, and the blocks are always freed in reverse
order. A calling program might be able to implement optimized functions that
perform better than \fBmalloc()\fP and \fBfree()\fP. PCRE runs noticeably more
slowly when built in this way. This option affects only the \fBpcre_exec()\fP
function; it is not relevant for \fBpcre_dfa_exec()\fP.
.
.
.SH "LIMITING PCRE RESOURCE USAGE"
.rs
.sp
Internally, PCRE has a function called \fBmatch()\fP, which it calls repeatedly
(sometimes recursively) when matching a pattern with the \fBpcre_exec()\fP
function. By controlling the maximum number of times this function may be
called during a single matching operation, a limit can be placed on the
resources used by a single call to \fBpcre_exec()\fP. The limit can be changed
at run time, as described in the
.\" HREF
\fBpcreapi\fP
.\"
documentation. The default is 10 million, but this can be changed by adding a
setting such as
.sp
  --with-match-limit=500000
.sp
to the \fBconfigure\fP command. This setting has no effect on the
\fBpcre_dfa_exec()\fP matching function.
.P
In some environments it is desirable to limit the depth of recursive calls of
\fBmatch()\fP more strictly than the total number of calls, in order to
restrict the maximum amount of stack (or heap, if --disable-stack-for-recursion
is specified) that is used. A second limit controls this; it defaults to the
value that is set for --with-match-limit, which imposes no additional
constraints. However, you can set a lower limit by adding, for example,
.sp
  --with-match-limit-recursion=10000
.sp
to the \fBconfigure\fP command. This value can also be overridden at run time.
.
.
.SH "CREATING CHARACTER TABLES AT BUILD TIME"
.rs
.sp
PCRE uses fixed tables for processing characters whose code values are less
than 256. By default, PCRE is built with a set of tables that are distributed
in the file \fIpcre_chartables.c.dist\fP. These tables are for ASCII codes
only. If you add
.sp
  --enable-rebuild-chartables
.sp
to the \fBconfigure\fP command, the distributed tables are no longer used.
Instead, a program called \fBdftables\fP is compiled and run. This outputs the
source for new set of tables, created in the default locale of your C run-time
system. (This method of replacing the tables does not work if you are cross
compiling, because \fBdftables\fP is run on the local host. If you need to
create alternative tables when cross compiling, you will have to do so "by
hand".)
.
.
.SH "USING EBCDIC CODE"
.rs
.sp
PCRE assumes by default that it will run in an environment where the character
code is ASCII (or Unicode, which is a superset of ASCII). This is the case for
most computer operating systems. PCRE can, however, be compiled to run in an
EBCDIC environment by adding
.sp
  --enable-ebcdic
.sp
to the \fBconfigure\fP command. This setting implies
--enable-rebuild-chartables. You should only use it if you know that you are in
an EBCDIC environment (for example, an IBM mainframe operating system). The
--enable-ebcdic option is incompatible with --enable-utf.
.P
The EBCDIC character that corresponds to an ASCII LF is assumed to have the
value 0x15 by default. However, in some EBCDIC environments, 0x25 is used. In
such an environment you should use
.sp
  --enable-ebcdic-nl25
.sp
as well as, or instead of, --enable-ebcdic. The EBCDIC character for CR has the
same value as in ASCII, namely, 0x0d. Whichever of 0x15 and 0x25 is \fInot\fP
chosen as LF is made to correspond to the Unicode NEL character (which, in
Unicode, is 0x85).
.P
The options that select newline behaviour, such as --enable-newline-is-cr,
and equivalent run-time options, refer to these character values in an EBCDIC
environment.
.
.
.SH "PCREGREP OPTIONS FOR COMPRESSED FILE SUPPORT"
.rs
.sp
By default, \fBpcregrep\fP reads all files as plain text. You can build it so
that it recognizes files whose names end in \fB.gz\fP or \fB.bz2\fP, and reads
them with \fBlibz\fP or \fBlibbz2\fP, respectively, by adding one or both of
.sp
  --enable-pcregrep-libz
  --enable-pcregrep-libbz2
.sp
to the \fBconfigure\fP command. These options naturally require that the
relevant libraries are installed on your system. Configuration will fail if
they are not.
.
.
.SH "PCREGREP BUFFER SIZE"
.rs
.sp
\fBpcregrep\fP uses an internal buffer to hold a "window" on the file it is
scanning, in order to be able to output "before" and "after" lines when it
finds a match. The size of the buffer is controlled by a parameter whose
default value is 20K. The buffer itself is three times this size, but because
of the way it is used for holding "before" lines, the longest line that is
guaranteed to be processable is the parameter size. You can change the default
parameter value by adding, for example,
.sp
  --with-pcregrep-bufsize=50K
.sp
to the \fBconfigure\fP command. The caller of \fPpcregrep\fP can, however,
override this value by specifying a run-time option.
.
.
.SH "PCRETEST OPTION FOR LIBREADLINE SUPPORT"
.rs
.sp
If you add
.sp
  --enable-pcretest-libreadline
.sp
to the \fBconfigure\fP command, \fBpcretest\fP is linked with the
\fBlibreadline\fP library, and when its input is from a terminal, it reads it
using the \fBreadline()\fP function. This provides line-editing and history
facilities. Note that \fBlibreadline\fP is GPL-licensed, so if you distribute a
binary of \fBpcretest\fP linked in this way, there may be licensing issues.
.P
Setting this option causes the \fB-lreadline\fP option to be added to the
\fBpcretest\fP build. In many operating environments with a sytem-installed
\fBlibreadline\fP this is sufficient. However, in some environments (e.g.
if an unmodified distribution version of readline is in use), some extra
configuration may be necessary. The INSTALL file for \fBlibreadline\fP says
this:
.sp
  "Readline uses the termcap functions, but does not link with the
  termcap or curses library itself, allowing applications which link
  with readline the to choose an appropriate library."
.sp
If your environment has not been set up so that an appropriate library is
automatically included, you may need to add something like
.sp
  LIBS="-ncurses"
.sp
immediately before the \fBconfigure\fP command.
.
.
.SH "DEBUGGING WITH VALGRIND SUPPORT"
.rs
.sp
By adding the
.sp
  --enable-valgrind
.sp
option to to the \fBconfigure\fP command, PCRE will use valgrind annotations
to mark certain memory regions as unaddressable. This allows it to detect
invalid memory accesses, and is mostly useful for debugging PCRE itself.
.
.
.SH "CODE COVERAGE REPORTING"
.rs
.sp
If your C compiler is gcc, you can build a version of PCRE that can generate a
code coverage report for its test suite. To enable this, you must install
\fBlcov\fP version 1.6 or above. Then specify
.sp
  --enable-coverage
.sp
to the \fBconfigure\fP command and build PCRE in the usual way.
.P
Note that using \fBccache\fP (a caching C compiler) is incompatible with code
coverage reporting. If you have configured \fBccache\fP to run automatically
on your system, you must set the environment variable
.sp
  CCACHE_DISABLE=1
.sp
before running \fBmake\fP to build PCRE, so that \fBccache\fP is not used.
.P
When --enable-coverage is used, the following addition targets are added to the
\fIMakefile\fP:
.sp
  make coverage
.sp
This creates a fresh coverage report for the PCRE test suite. It is equivalent
to running "make coverage-reset", "make coverage-baseline", "make check", and
then "make coverage-report".
.sp
  make coverage-reset
.sp
This zeroes the coverage counters, but does nothing else.
.sp
  make coverage-baseline
.sp
This captures baseline coverage information.
.sp
  make coverage-report
.sp
This creates the coverage report.
.sp
  make coverage-clean-report
.sp
This removes the generated coverage report without cleaning the coverage data
itself.
.sp
  make coverage-clean-data
.sp
This removes the captured coverage data without removing the coverage files
created at compile time (*.gcno).
.sp
  make coverage-clean
.sp
This cleans all coverage data including the generated coverage report. For more
information about code coverage, see the \fBgcov\fP and \fBlcov\fP
documentation.
.
.
.SH "SEE ALSO"
.rs
.sp
\fBpcreapi\fP(3), \fBpcre16\fP, \fBpcre32\fP, \fBpcre_config\fP(3).
.
.
.SH AUTHOR
.rs
.sp
.nf
Philip Hazel
University Computing Service
Cambridge CB2 3QH, England.
.fi
.
.
.SH REVISION
.rs
.sp
.nf
Last updated: 12 May 2013
Copyright (c) 1997-2013 University of Cambridge.
.fi
