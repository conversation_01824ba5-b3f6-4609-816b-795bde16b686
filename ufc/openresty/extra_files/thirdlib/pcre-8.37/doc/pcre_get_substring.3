.TH PCRE_GET_SUBSTRING 3 "24 June 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.nf
.B int pcre_get_substring(const char *\fIsubject\fP, int *\fIovector\fP,
.B "     int \fIstringcount\fP, int \fIstringnumber\fP,"
.B "     const char **\fIstringptr\fP);"
.sp
.B int pcre16_get_substring(PCRE_SPTR16 \fIsubject\fP, int *\fIovector\fP,
.B "     int \fIstringcount\fP, int \fIstringnumber\fP,"
.B "     PCRE_SPTR16 *\fIstringptr\fP);"
.sp
.B int pcre32_get_substring(PCRE_SPTR32 \fIsubject\fP, int *\fIovector\fP,
.B "     int \fIstringcount\fP, int \fIstringnumber\fP,"
.B "     PCRE_SPTR32 *\fIstringptr\fP);"
.fi
.
.SH DESCRIPTION
.rs
.sp
This is a convenience function for extracting a captured substring. The
arguments are:
.sp
  \fIsubject\fP       Subject that has been successfully matched
  \fIovector\fP       Offset vector that \fBpcre[16|32]_exec()\fP used
  \fIstringcount\fP   Value returned by \fBpcre[16|32]_exec()\fP
  \fIstringnumber\fP  Number of the required substring
  \fIstringptr\fP     Where to put the string pointer
.sp
The memory in which the substring is placed is obtained by calling
\fBpcre[16|32]_malloc()\fP. The convenience function
\fBpcre[16|32]_free_substring()\fP can be used to free it when it is no longer
needed. The yield of the function is the length of the substring,
PCRE_ERROR_NOMEMORY if sufficient memory could not be obtained, or
PCRE_ERROR_NOSUBSTRING if the string number is invalid.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
