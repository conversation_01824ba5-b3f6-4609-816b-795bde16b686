.TH PCRE_FULLINFO 3 "21 April 2014" "PCRE 8.36"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.nf
.B int pcre_fullinfo(const pcre *\fIcode\fP, "const pcre_extra *\fIextra\fP,"
.B "     int \fIwhat\fP, void *\fIwhere\fP);"
.sp
.B int pcre16_fullinfo(const pcre16 *\fIcode\fP, "const pcre16_extra *\fIextra\fP,"
.B "     int \fIwhat\fP, void *\fIwhere\fP);"
.sp
.B int pcre32_fullinfo(const pcre32 *\fIcode\fP, "const pcre32_extra *\fIextra\fP,"
.B "     int \fIwhat\fP, void *\fIwhere\fP);"
.fi
.
.SH DESCRIPTION
.rs
.sp
This function returns information about a compiled pattern. Its arguments are:
.sp
  \fIcode\fP                      Compiled regular expression
  \fIextra\fP                     Result of \fBpcre[16|32]_study()\fP or NULL
  \fIwhat\fP                      What information is required
  \fIwhere\fP                     Where to put the information
.sp
The following information is available:
.sp
  PCRE_INFO_BACKREFMAX      Number of highest back reference
  PCRE_INFO_CAPTURECOUNT    Number of capturing subpatterns
  PCRE_INFO_DEFAULT_TABLES  Pointer to default tables
  PCRE_INFO_FIRSTBYTE       Fixed first data unit for a match, or
                              -1 for start of string
                                 or after newline, or
                              -2 otherwise
  PCRE_INFO_FIRSTTABLE      Table of first data units (after studying)
  PCRE_INFO_HASCRORLF       Return 1 if explicit CR or LF matches exist
  PCRE_INFO_JCHANGED        Return 1 if (?J) or (?-J) was used
  PCRE_INFO_JIT             Return 1 after successful JIT compilation
  PCRE_INFO_JITSIZE         Size of JIT compiled code
  PCRE_INFO_LASTLITERAL     Literal last data unit required
  PCRE_INFO_MINLENGTH       Lower bound length of matching strings
  PCRE_INFO_MATCHEMPTY      Return 1 if the pattern can match an empty string,
                               0 otherwise
  PCRE_INFO_MATCHLIMIT      Match limit if set, otherwise PCRE_RROR_UNSET
  PCRE_INFO_MAXLOOKBEHIND   Length (in characters) of the longest lookbehind assertion
  PCRE_INFO_NAMECOUNT       Number of named subpatterns
  PCRE_INFO_NAMEENTRYSIZE   Size of name table entry
  PCRE_INFO_NAMETABLE       Pointer to name table
  PCRE_INFO_OKPARTIAL       Return 1 if partial matching can be tried
                              (always returns 1 after release 8.00)
  PCRE_INFO_OPTIONS         Option bits used for compilation
  PCRE_INFO_SIZE            Size of compiled pattern
  PCRE_INFO_STUDYSIZE       Size of study data
  PCRE_INFO_FIRSTCHARACTER      Fixed first data unit for a match
  PCRE_INFO_FIRSTCHARACTERFLAGS Returns
                                  1 if there is a first data character set, which can
                                    then be retrieved using PCRE_INFO_FIRSTCHARACTER,
                                  2 if the first character is at the start of the data
                                    string or after a newline, and
                                  0 otherwise
  PCRE_INFO_RECURSIONLIMIT    Recursion limit if set, otherwise PCRE_ERROR_UNSET
  PCRE_INFO_REQUIREDCHAR      Literal last data unit required
  PCRE_INFO_REQUIREDCHARFLAGS Returns 1 if the last data character is set (which can then
                              be retrieved using PCRE_INFO_REQUIREDCHAR); 0 otherwise
.sp
The \fIwhere\fP argument must point to an integer variable, except for the
following \fIwhat\fP values:
.sp
  PCRE_INFO_DEFAULT_TABLES  const uint8_t *
  PCRE_INFO_FIRSTCHARACTER  uint32_t
  PCRE_INFO_FIRSTTABLE      const uint8_t *
  PCRE_INFO_JITSIZE         size_t
  PCRE_INFO_MATCHLIMIT      uint32_t
  PCRE_INFO_NAMETABLE       PCRE_SPTR16           (16-bit library)
  PCRE_INFO_NAMETABLE       PCRE_SPTR32           (32-bit library)
  PCRE_INFO_NAMETABLE       const unsigned char * (8-bit library)
  PCRE_INFO_OPTIONS         unsigned long int
  PCRE_INFO_SIZE            size_t
  PCRE_INFO_STUDYSIZE       size_t
  PCRE_INFO_RECURSIONLIMIT  uint32_t
  PCRE_INFO_REQUIREDCHAR    uint32_t
.sp
The yield of the function is zero on success or:
.sp
  PCRE_ERROR_NULL           the argument \fIcode\fP was NULL
                            the argument \fIwhere\fP was NULL
  PCRE_ERROR_BADMAGIC       the "magic number" was not found
  PCRE_ERROR_BADOPTION      the value of \fIwhat\fP was invalid
  PCRE_ERROR_UNSET          the option was not set
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
