.TH PCRESAMPLE 3 "10 January 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH "PCRE SAMPLE PROGRAM"
.rs
.sp
A simple, complete demonstration program, to get you started with using PCRE,
is supplied in the file \fIpcredemo.c\fP in the PCRE distribution. A listing of
this program is given in the
.\" HREF
\fBpcredemo\fP
.\"
documentation. If you do not have a copy of the PCRE distribution, you can save
this listing to re-create \fIpcredemo.c\fP.
.P
The demonstration program, which uses the original PCRE 8-bit library, compiles
the regular expression that is its first argument, and matches it against the
subject string in its second argument. No PCRE options are set, and default
character tables are used. If matching succeeds, the program outputs the
portion of the subject that matched, together with the contents of any captured
substrings.
.P
If the -g option is given on the command line, the program then goes on to
check for further matches of the same regular expression in the same subject
string. The logic is a little bit tricky because of the possibility of matching
an empty string. Comments in the code explain what is going on.
.P
If PCRE is installed in the standard include and library directories for your
operating system, you should be able to compile the demonstration program using
this command:
.sp
  gcc -o pcredemo pcredemo.c -lpcre
.sp
If PCRE is installed elsewhere, you may need to add additional options to the
command line. For example, on a Unix-like system that has PCRE installed in
\fI/usr/local\fP, you can compile the demonstration program using a command
like this:
.sp
.\" JOINSH
  gcc -o pcredemo -I/usr/local/include pcredemo.c \e
      -L/usr/local/lib -lpcre
.sp
In a Windows environment, if you want to statically link the program against a
non-dll \fBpcre.a\fP file, you must uncomment the line that defines PCRE_STATIC
before including \fBpcre.h\fP, because otherwise the \fBpcre_malloc()\fP and
\fBpcre_free()\fP exported functions will be declared
\fB__declspec(dllimport)\fP, with unwanted results.
.P
Once you have compiled and linked the demonstration program, you can run simple
tests like this:
.sp
  ./pcredemo 'cat|dog' 'the cat sat on the mat'
  ./pcredemo -g 'cat|dog' 'the dog sat on the cat'
.sp
Note that there is a much more comprehensive test program, called
.\" HREF
\fBpcretest\fP,
.\"
which supports many more facilities for testing regular expressions and both
PCRE libraries. The
.\" HREF
\fBpcredemo\fP
.\"
program is provided as a simple coding example.
.P
If you try to run
.\" HREF
\fBpcredemo\fP
.\"
when PCRE is not installed in the standard library directory, you may get an
error like this on some operating systems (e.g. Solaris):
.sp
  ld.so.1: a.out: fatal: libpcre.so.0: open failed: No such file or directory
.sp
This is caused by the way shared library support works on those systems. You
need to add
.sp
  -R/usr/local/lib
.sp
(for example) to the compile command to get round this problem.
.
.
.SH AUTHOR
.rs
.sp
.nf
Philip Hazel
University Computing Service
Cambridge CB2 3QH, England.
.fi
.
.
.SH REVISION
.rs
.sp
.nf
Last updated: 10 January 2012
Copyright (c) 1997-2012 University of Cambridge.
.fi
