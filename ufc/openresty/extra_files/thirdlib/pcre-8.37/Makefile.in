# Makefile.in generated by automake 1.15 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2014 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@





VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
TESTS = $(am__EXEEXT_3) $(am__EXEEXT_4) RunTest $(am__append_38)
bin_PROGRAMS = pcretest$(EXEEXT) $(am__EXEEXT_1)
noinst_PROGRAMS = $(am__EXEEXT_2) $(am__EXEEXT_3) $(am__EXEEXT_4)

# These additional headers will be be installed if C++ support is enabled. We
# do not distribute pcrecpparg.h or pcre_stringpiece.h, as these are generated
# from corresponding .h.in files (which we do distribute).
@WITH_PCRE_CPP_TRUE@am__append_1 = \
@WITH_PCRE_CPP_TRUE@  pcrecpparg.h \
@WITH_PCRE_CPP_TRUE@  pcre_stringpiece.h

@WITH_PCRE_CPP_TRUE@am__append_2 = \
@WITH_PCRE_CPP_TRUE@  pcrecpp.h \
@WITH_PCRE_CPP_TRUE@  pcre_scanner.h

@WITH_REBUILD_CHARTABLES_TRUE@am__append_3 = dftables

# Build the 8 bit library if it is enabled.
@WITH_PCRE8_TRUE@am__append_4 = libpcre.la

# Build the 16 bit library if it is enabled.
@WITH_PCRE16_TRUE@am__append_5 = libpcre16.la

# Build the 32 bit library if it is enabled.
@WITH_PCRE32_TRUE@am__append_6 = libpcre32.la
@WITH_PCRE8_TRUE@@WITH_VALGRIND_TRUE@am__append_7 = $(VALGRIND_CFLAGS)
@WITH_PCRE16_TRUE@@WITH_VALGRIND_TRUE@am__append_8 = $(VALGRIND_CFLAGS)
@WITH_PCRE32_TRUE@@WITH_VALGRIND_TRUE@am__append_9 = $(VALGRIND_CFLAGS)
@WITH_GCOV_TRUE@@WITH_PCRE8_TRUE@am__append_10 = $(GCOV_CFLAGS)
@WITH_GCOV_TRUE@@WITH_PCRE16_TRUE@am__append_11 = $(GCOV_CFLAGS)
@WITH_GCOV_TRUE@@WITH_PCRE32_TRUE@am__append_12 = $(GCOV_CFLAGS)
@WITH_JIT_TRUE@am__append_13 = pcre_jit_test
@WITH_JIT_TRUE@am__append_14 = pcre_jit_test
@WITH_JIT_TRUE@@WITH_PCRE8_TRUE@am__append_15 = libpcre.la
@WITH_JIT_TRUE@@WITH_PCRE16_TRUE@am__append_16 = libpcre16.la
@WITH_JIT_TRUE@@WITH_PCRE32_TRUE@am__append_17 = libpcre32.la
@WITH_GCOV_TRUE@@WITH_JIT_TRUE@am__append_18 = $(GCOV_CFLAGS)
@WITH_GCOV_TRUE@@WITH_JIT_TRUE@am__append_19 = $(GCOV_LIBS)
@WITH_PCRE8_TRUE@am__append_20 = libpcreposix.la
@WITH_GCOV_TRUE@@WITH_PCRE8_TRUE@am__append_21 = $(GCOV_CFLAGS)
@WITH_PCRE_CPP_TRUE@am__append_22 = libpcrecpp.la
@WITH_PCRE_CPP_TRUE@am__append_23 = pcrecpp_unittest \
@WITH_PCRE_CPP_TRUE@	pcre_scanner_unittest \
@WITH_PCRE_CPP_TRUE@	pcre_stringpiece_unittest
@WITH_PCRE_CPP_TRUE@am__append_24 = pcrecpp_unittest \
@WITH_PCRE_CPP_TRUE@	pcre_scanner_unittest \
@WITH_PCRE_CPP_TRUE@	pcre_stringpiece_unittest
@WITH_GCOV_TRUE@@WITH_PCRE_CPP_TRUE@am__append_25 = $(GCOV_CXXFLAGS)
@WITH_GCOV_TRUE@@WITH_PCRE_CPP_TRUE@am__append_26 = $(GCOV_LIBS)
@WITH_GCOV_TRUE@@WITH_PCRE_CPP_TRUE@am__append_27 = $(GCOV_LIBS)
@WITH_GCOV_TRUE@@WITH_PCRE_CPP_TRUE@am__append_28 = $(GCOV_LIBS)
@WITH_PCRE8_TRUE@am__append_29 = pcre_printint.c
@WITH_PCRE8_TRUE@am__append_30 = libpcre.la libpcreposix.la
@WITH_PCRE16_TRUE@am__append_31 = pcre16_printint.c
@WITH_PCRE16_TRUE@am__append_32 = libpcre16.la
@WITH_PCRE32_TRUE@am__append_33 = pcre32_printint.c
@WITH_PCRE32_TRUE@am__append_34 = libpcre32.la
@WITH_VALGRIND_TRUE@am__append_35 = $(VALGRIND_CFLAGS)
@WITH_GCOV_TRUE@am__append_36 = $(GCOV_CFLAGS)
@WITH_GCOV_TRUE@am__append_37 = $(GCOV_LIBS)
@WITH_PCRE8_TRUE@am__append_38 = RunGrepTest
@WITH_PCRE8_TRUE@am__append_39 = RunGrepTest
@WITH_PCRE8_TRUE@am__append_40 = pcregrep
@WITH_GCOV_TRUE@@WITH_PCRE8_TRUE@am__append_41 = $(GCOV_CFLAGS)
@WITH_GCOV_TRUE@@WITH_PCRE8_TRUE@am__append_42 = $(GCOV_LIBS)
@WITH_PCRE16_TRUE@am__append_43 = libpcre16.pc
@WITH_PCRE32_TRUE@am__append_44 = libpcre32.pc
@WITH_PCRE_CPP_TRUE@am__append_45 = libpcrecpp.pc
subdir = .
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_pthread.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/pcre_visibility.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(top_srcdir)/configure \
	$(am__configure_deps) $(am__dist_noinst_SCRIPTS_DIST) \
	$(dist_doc_DATA) $(dist_html_DATA) $(dist_noinst_DATA) \
	$(am__include_HEADERS_DIST) $(am__DIST_COMMON)
am__CONFIG_DISTCLEAN_FILES = config.status config.cache config.log \
 configure.lineno config.status.lineno
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = config.h
CONFIG_CLEAN_FILES = libpcre.pc libpcre16.pc libpcre32.pc \
	libpcreposix.pc libpcrecpp.pc pcre-config pcre.h \
	pcre_stringpiece.h pcrecpparg.h
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)" "$(DESTDIR)$(bindir)" \
	"$(DESTDIR)$(bindir)" "$(DESTDIR)$(man1dir)" \
	"$(DESTDIR)$(man3dir)" "$(DESTDIR)$(docdir)" \
	"$(DESTDIR)$(htmldir)" "$(DESTDIR)$(htmldir)" \
	"$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(includedir)" \
	"$(DESTDIR)$(includedir)"
LTLIBRARIES = $(lib_LTLIBRARIES)
libpcre_la_DEPENDENCIES =
am__libpcre_la_SOURCES_DIST = pcre_byte_order.c pcre_compile.c \
	pcre_config.c pcre_dfa_exec.c pcre_exec.c pcre_fullinfo.c \
	pcre_get.c pcre_globals.c pcre_internal.h pcre_jit_compile.c \
	pcre_maketables.c pcre_newline.c pcre_ord2utf8.c \
	pcre_refcount.c pcre_string_utils.c pcre_study.c pcre_tables.c \
	pcre_ucd.c pcre_valid_utf8.c pcre_version.c pcre_xclass.c \
	ucp.h
@WITH_PCRE8_TRUE@am_libpcre_la_OBJECTS =  \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_byte_order.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_compile.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_config.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_dfa_exec.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_exec.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_fullinfo.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_get.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_globals.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_jit_compile.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_maketables.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_newline.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_ord2utf8.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_refcount.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_string_utils.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_study.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_tables.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_ucd.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_valid_utf8.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_version.lo \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_xclass.lo
@WITH_PCRE8_TRUE@nodist_libpcre_la_OBJECTS =  \
@WITH_PCRE8_TRUE@	libpcre_la-pcre_chartables.lo
libpcre_la_OBJECTS = $(am_libpcre_la_OBJECTS) \
	$(nodist_libpcre_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 =
libpcre_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(libpcre_la_CFLAGS) \
	$(CFLAGS) $(libpcre_la_LDFLAGS) $(LDFLAGS) -o $@
@WITH_PCRE8_TRUE@am_libpcre_la_rpath = -rpath $(libdir)
libpcre16_la_DEPENDENCIES =
am__libpcre16_la_SOURCES_DIST = pcre16_byte_order.c \
	pcre16_chartables.c pcre16_compile.c pcre16_config.c \
	pcre16_dfa_exec.c pcre16_exec.c pcre16_fullinfo.c pcre16_get.c \
	pcre16_globals.c pcre16_jit_compile.c pcre16_maketables.c \
	pcre16_newline.c pcre16_ord2utf16.c pcre16_refcount.c \
	pcre16_string_utils.c pcre16_study.c pcre16_tables.c \
	pcre16_ucd.c pcre16_utf16_utils.c pcre16_valid_utf16.c \
	pcre16_version.c pcre16_xclass.c
@WITH_PCRE16_TRUE@am_libpcre16_la_OBJECTS =  \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_byte_order.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_chartables.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_compile.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_config.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_dfa_exec.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_exec.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_fullinfo.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_get.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_globals.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_jit_compile.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_maketables.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_newline.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_ord2utf16.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_refcount.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_string_utils.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_study.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_tables.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_ucd.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_utf16_utils.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_valid_utf16.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_version.lo \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre16_xclass.lo
@WITH_PCRE16_TRUE@nodist_libpcre16_la_OBJECTS =  \
@WITH_PCRE16_TRUE@	libpcre16_la-pcre_chartables.lo
libpcre16_la_OBJECTS = $(am_libpcre16_la_OBJECTS) \
	$(nodist_libpcre16_la_OBJECTS)
libpcre16_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(libpcre16_la_CFLAGS) \
	$(CFLAGS) $(libpcre16_la_LDFLAGS) $(LDFLAGS) -o $@
@WITH_PCRE16_TRUE@am_libpcre16_la_rpath = -rpath $(libdir)
libpcre32_la_DEPENDENCIES =
am__libpcre32_la_SOURCES_DIST = pcre32_byte_order.c \
	pcre32_chartables.c pcre32_compile.c pcre32_config.c \
	pcre32_dfa_exec.c pcre32_exec.c pcre32_fullinfo.c pcre32_get.c \
	pcre32_globals.c pcre32_jit_compile.c pcre32_maketables.c \
	pcre32_newline.c pcre32_ord2utf32.c pcre32_refcount.c \
	pcre32_string_utils.c pcre32_study.c pcre32_tables.c \
	pcre32_ucd.c pcre32_utf32_utils.c pcre32_valid_utf32.c \
	pcre32_version.c pcre32_xclass.c
@WITH_PCRE32_TRUE@am_libpcre32_la_OBJECTS =  \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_byte_order.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_chartables.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_compile.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_config.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_dfa_exec.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_exec.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_fullinfo.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_get.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_globals.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_jit_compile.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_maketables.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_newline.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_ord2utf32.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_refcount.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_string_utils.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_study.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_tables.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_ucd.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_utf32_utils.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_valid_utf32.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_version.lo \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre32_xclass.lo
@WITH_PCRE32_TRUE@nodist_libpcre32_la_OBJECTS =  \
@WITH_PCRE32_TRUE@	libpcre32_la-pcre_chartables.lo
libpcre32_la_OBJECTS = $(am_libpcre32_la_OBJECTS) \
	$(nodist_libpcre32_la_OBJECTS)
libpcre32_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(libpcre32_la_CFLAGS) \
	$(CFLAGS) $(libpcre32_la_LDFLAGS) $(LDFLAGS) -o $@
@WITH_PCRE32_TRUE@am_libpcre32_la_rpath = -rpath $(libdir)
@WITH_PCRE_CPP_TRUE@libpcrecpp_la_DEPENDENCIES = libpcre.la
am__libpcrecpp_la_SOURCES_DIST = pcrecpp_internal.h pcrecpp.cc \
	pcre_scanner.cc pcre_stringpiece.cc
@WITH_PCRE_CPP_TRUE@am_libpcrecpp_la_OBJECTS =  \
@WITH_PCRE_CPP_TRUE@	libpcrecpp_la-pcrecpp.lo \
@WITH_PCRE_CPP_TRUE@	libpcrecpp_la-pcre_scanner.lo \
@WITH_PCRE_CPP_TRUE@	libpcrecpp_la-pcre_stringpiece.lo
libpcrecpp_la_OBJECTS = $(am_libpcrecpp_la_OBJECTS)
libpcrecpp_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(libpcrecpp_la_CXXFLAGS) $(CXXFLAGS) $(libpcrecpp_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@WITH_PCRE_CPP_TRUE@am_libpcrecpp_la_rpath = -rpath $(libdir)
@WITH_PCRE8_TRUE@libpcreposix_la_DEPENDENCIES = libpcre.la
am__libpcreposix_la_SOURCES_DIST = pcreposix.c
@WITH_PCRE8_TRUE@am_libpcreposix_la_OBJECTS =  \
@WITH_PCRE8_TRUE@	libpcreposix_la-pcreposix.lo
libpcreposix_la_OBJECTS = $(am_libpcreposix_la_OBJECTS)
libpcreposix_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(libpcreposix_la_CFLAGS) $(CFLAGS) $(libpcreposix_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@WITH_PCRE8_TRUE@am_libpcreposix_la_rpath = -rpath $(libdir)
@WITH_PCRE8_TRUE@am__EXEEXT_1 = pcregrep$(EXEEXT)
@WITH_REBUILD_CHARTABLES_TRUE@am__EXEEXT_2 = dftables$(EXEEXT)
@WITH_JIT_TRUE@am__EXEEXT_3 = pcre_jit_test$(EXEEXT)
@WITH_PCRE_CPP_TRUE@am__EXEEXT_4 = pcrecpp_unittest$(EXEEXT) \
@WITH_PCRE_CPP_TRUE@	pcre_scanner_unittest$(EXEEXT) \
@WITH_PCRE_CPP_TRUE@	pcre_stringpiece_unittest$(EXEEXT)
PROGRAMS = $(bin_PROGRAMS) $(noinst_PROGRAMS)
am__dftables_SOURCES_DIST = dftables.c
@WITH_REBUILD_CHARTABLES_TRUE@am_dftables_OBJECTS =  \
@WITH_REBUILD_CHARTABLES_TRUE@	dftables.$(OBJEXT)
dftables_OBJECTS = $(am_dftables_OBJECTS)
dftables_LDADD = $(LDADD)
am__pcre_jit_test_SOURCES_DIST = pcre_jit_test.c
@WITH_JIT_TRUE@am_pcre_jit_test_OBJECTS =  \
@WITH_JIT_TRUE@	pcre_jit_test-pcre_jit_test.$(OBJEXT)
pcre_jit_test_OBJECTS = $(am_pcre_jit_test_OBJECTS)
am__DEPENDENCIES_1 =
@WITH_GCOV_TRUE@@WITH_JIT_TRUE@am__DEPENDENCIES_2 =  \
@WITH_GCOV_TRUE@@WITH_JIT_TRUE@	$(am__DEPENDENCIES_1)
@WITH_JIT_TRUE@pcre_jit_test_DEPENDENCIES = $(am__append_15) \
@WITH_JIT_TRUE@	$(am__append_16) $(am__append_17) \
@WITH_JIT_TRUE@	$(am__DEPENDENCIES_2)
pcre_jit_test_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(pcre_jit_test_CFLAGS) \
	$(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
am__pcre_scanner_unittest_SOURCES_DIST = pcre_scanner_unittest.cc
@WITH_PCRE_CPP_TRUE@am_pcre_scanner_unittest_OBJECTS = pcre_scanner_unittest-pcre_scanner_unittest.$(OBJEXT)
pcre_scanner_unittest_OBJECTS = $(am_pcre_scanner_unittest_OBJECTS)
@WITH_GCOV_TRUE@@WITH_PCRE_CPP_TRUE@am__DEPENDENCIES_3 =  \
@WITH_GCOV_TRUE@@WITH_PCRE_CPP_TRUE@	$(am__DEPENDENCIES_1)
@WITH_PCRE_CPP_TRUE@pcre_scanner_unittest_DEPENDENCIES =  \
@WITH_PCRE_CPP_TRUE@	libpcrecpp.la $(am__DEPENDENCIES_3)
pcre_scanner_unittest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(pcre_scanner_unittest_CXXFLAGS) $(CXXFLAGS) $(AM_LDFLAGS) \
	$(LDFLAGS) -o $@
am__pcre_stringpiece_unittest_SOURCES_DIST =  \
	pcre_stringpiece_unittest.cc
@WITH_PCRE_CPP_TRUE@am_pcre_stringpiece_unittest_OBJECTS = pcre_stringpiece_unittest-pcre_stringpiece_unittest.$(OBJEXT)
pcre_stringpiece_unittest_OBJECTS =  \
	$(am_pcre_stringpiece_unittest_OBJECTS)
@WITH_PCRE_CPP_TRUE@pcre_stringpiece_unittest_DEPENDENCIES =  \
@WITH_PCRE_CPP_TRUE@	libpcrecpp.la $(am__DEPENDENCIES_3)
pcre_stringpiece_unittest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(pcre_stringpiece_unittest_CXXFLAGS) $(CXXFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
am__pcrecpp_unittest_SOURCES_DIST = pcrecpp_unittest.cc
@WITH_PCRE_CPP_TRUE@am_pcrecpp_unittest_OBJECTS = pcrecpp_unittest-pcrecpp_unittest.$(OBJEXT)
pcrecpp_unittest_OBJECTS = $(am_pcrecpp_unittest_OBJECTS)
@WITH_PCRE_CPP_TRUE@pcrecpp_unittest_DEPENDENCIES = libpcrecpp.la \
@WITH_PCRE_CPP_TRUE@	$(am__DEPENDENCIES_3)
pcrecpp_unittest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(pcrecpp_unittest_CXXFLAGS) $(CXXFLAGS) $(AM_LDFLAGS) \
	$(LDFLAGS) -o $@
am__pcregrep_SOURCES_DIST = pcregrep.c
@WITH_PCRE8_TRUE@am_pcregrep_OBJECTS = pcregrep-pcregrep.$(OBJEXT)
pcregrep_OBJECTS = $(am_pcregrep_OBJECTS)
@WITH_GCOV_TRUE@@WITH_PCRE8_TRUE@am__DEPENDENCIES_4 =  \
@WITH_GCOV_TRUE@@WITH_PCRE8_TRUE@	$(am__DEPENDENCIES_1)
@WITH_PCRE8_TRUE@pcregrep_DEPENDENCIES = $(am__DEPENDENCIES_1) \
@WITH_PCRE8_TRUE@	$(am__DEPENDENCIES_1) libpcre.la \
@WITH_PCRE8_TRUE@	libpcreposix.la $(am__DEPENDENCIES_4)
pcregrep_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(pcregrep_CFLAGS) \
	$(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
am__pcretest_SOURCES_DIST = pcretest.c pcre_printint.c \
	pcre16_printint.c pcre32_printint.c
@WITH_PCRE8_TRUE@am__objects_1 = pcretest-pcre_printint.$(OBJEXT)
@WITH_PCRE16_TRUE@am__objects_2 = pcretest-pcre16_printint.$(OBJEXT)
@WITH_PCRE32_TRUE@am__objects_3 = pcretest-pcre32_printint.$(OBJEXT)
am_pcretest_OBJECTS = pcretest-pcretest.$(OBJEXT) $(am__objects_1) \
	$(am__objects_2) $(am__objects_3)
pcretest_OBJECTS = $(am_pcretest_OBJECTS)
@WITH_GCOV_TRUE@am__DEPENDENCIES_5 = $(am__DEPENDENCIES_1)
pcretest_DEPENDENCIES = $(am__DEPENDENCIES_1) $(am__append_30) \
	$(am__append_32) $(am__append_34) $(am__DEPENDENCIES_5)
pcretest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(pcretest_CFLAGS) \
	$(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
am__dist_noinst_SCRIPTS_DIST = RunTest RunGrepTest
SCRIPTS = $(bin_SCRIPTS) $(dist_noinst_SCRIPTS)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 =
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 =
DEFAULT_INCLUDES = -I.@am__isrc@
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__depfiles_maybe = depfiles
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 =
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 =
CXXCOMPILE = $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS)
LTCXXCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CXXFLAGS) $(CXXFLAGS)
AM_V_CXX = $(am__v_CXX_@AM_V@)
am__v_CXX_ = $(am__v_CXX_@AM_DEFAULT_V@)
am__v_CXX_0 = @echo "  CXX     " $@;
am__v_CXX_1 =
CXXLD = $(CXX)
CXXLINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CXXLD = $(am__v_CXXLD_@AM_V@)
am__v_CXXLD_ = $(am__v_CXXLD_@AM_DEFAULT_V@)
am__v_CXXLD_0 = @echo "  CXXLD   " $@;
am__v_CXXLD_1 =
SOURCES = $(libpcre_la_SOURCES) $(nodist_libpcre_la_SOURCES) \
	$(libpcre16_la_SOURCES) $(nodist_libpcre16_la_SOURCES) \
	$(libpcre32_la_SOURCES) $(nodist_libpcre32_la_SOURCES) \
	$(libpcrecpp_la_SOURCES) $(libpcreposix_la_SOURCES) \
	$(dftables_SOURCES) $(pcre_jit_test_SOURCES) \
	$(pcre_scanner_unittest_SOURCES) \
	$(pcre_stringpiece_unittest_SOURCES) \
	$(pcrecpp_unittest_SOURCES) $(pcregrep_SOURCES) \
	$(pcretest_SOURCES)
DIST_SOURCES = $(am__libpcre_la_SOURCES_DIST) \
	$(am__libpcre16_la_SOURCES_DIST) \
	$(am__libpcre32_la_SOURCES_DIST) \
	$(am__libpcrecpp_la_SOURCES_DIST) \
	$(am__libpcreposix_la_SOURCES_DIST) \
	$(am__dftables_SOURCES_DIST) $(am__pcre_jit_test_SOURCES_DIST) \
	$(am__pcre_scanner_unittest_SOURCES_DIST) \
	$(am__pcre_stringpiece_unittest_SOURCES_DIST) \
	$(am__pcrecpp_unittest_SOURCES_DIST) \
	$(am__pcregrep_SOURCES_DIST) $(am__pcretest_SOURCES_DIST)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
man1dir = $(mandir)/man1
man3dir = $(mandir)/man3
NROFF = nroff
MANS = $(dist_man_MANS) $(man_MANS)
DATA = $(dist_doc_DATA) $(dist_html_DATA) $(dist_noinst_DATA) \
	$(html_DATA) $(pkgconfig_DATA)
am__include_HEADERS_DIST = pcreposix.h pcrecpp.h pcre_scanner.h
HEADERS = $(include_HEADERS) $(nodist_include_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) \
	$(LISP)config.h.in
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
CSCOPE = cscope
AM_RECURSIVE_TARGETS = cscope check recheck
am__tty_colors_dummy = \
  mgn= red= grn= lgn= blu= brg= std=; \
  am__color_tests=no
am__tty_colors = { \
  $(am__tty_colors_dummy); \
  if test "X$(AM_COLOR_TESTS)" = Xno; then \
    am__color_tests=no; \
  elif test "X$(AM_COLOR_TESTS)" = Xalways; then \
    am__color_tests=yes; \
  elif test "X$$TERM" != Xdumb && { test -t 1; } 2>/dev/null; then \
    am__color_tests=yes; \
  fi; \
  if test $$am__color_tests = yes; then \
    red='[0;31m'; \
    grn='[0;32m'; \
    lgn='[1;32m'; \
    blu='[1;34m'; \
    mgn='[0;35m'; \
    brg='[1m'; \
    std='[m'; \
  fi; \
}
am__recheck_rx = ^[ 	]*:recheck:[ 	]*
am__global_test_result_rx = ^[ 	]*:global-test-result:[ 	]*
am__copy_in_global_log_rx = ^[ 	]*:copy-in-global-log:[ 	]*
# A command that, given a newline-separated list of test names on the
# standard input, print the name of the tests that are to be re-run
# upon "make recheck".
am__list_recheck_tests = $(AWK) '{ \
  recheck = 1; \
  while ((rc = (getline line < ($$0 ".trs"))) != 0) \
    { \
      if (rc < 0) \
        { \
          if ((getline line2 < ($$0 ".log")) < 0) \
	    recheck = 0; \
          break; \
        } \
      else if (line ~ /$(am__recheck_rx)[nN][Oo]/) \
        { \
          recheck = 0; \
          break; \
        } \
      else if (line ~ /$(am__recheck_rx)[yY][eE][sS]/) \
        { \
          break; \
        } \
    }; \
  if (recheck) \
    print $$0; \
  close ($$0 ".trs"); \
  close ($$0 ".log"); \
}'
# A command that, given a newline-separated list of test names on the
# standard input, create the global log from their .trs and .log files.
am__create_global_log = $(AWK) ' \
function fatal(msg) \
{ \
  print "fatal: making $@: " msg | "cat >&2"; \
  exit 1; \
} \
function rst_section(header) \
{ \
  print header; \
  len = length(header); \
  for (i = 1; i <= len; i = i + 1) \
    printf "="; \
  printf "\n\n"; \
} \
{ \
  copy_in_global_log = 1; \
  global_test_result = "RUN"; \
  while ((rc = (getline line < ($$0 ".trs"))) != 0) \
    { \
      if (rc < 0) \
         fatal("failed to read from " $$0 ".trs"); \
      if (line ~ /$(am__global_test_result_rx)/) \
        { \
          sub("$(am__global_test_result_rx)", "", line); \
          sub("[ 	]*$$", "", line); \
          global_test_result = line; \
        } \
      else if (line ~ /$(am__copy_in_global_log_rx)[nN][oO]/) \
        copy_in_global_log = 0; \
    }; \
  if (copy_in_global_log) \
    { \
      rst_section(global_test_result ": " $$0); \
      while ((rc = (getline line < ($$0 ".log"))) != 0) \
      { \
        if (rc < 0) \
          fatal("failed to read from " $$0 ".log"); \
        print line; \
      }; \
      printf "\n"; \
    }; \
  close ($$0 ".trs"); \
  close ($$0 ".log"); \
}'
# Restructured Text title.
am__rst_title = { sed 's/.*/   &   /;h;s/./=/g;p;x;s/ *$$//;p;g' && echo; }
# Solaris 10 'make', and several other traditional 'make' implementations,
# pass "-e" to $(SHELL), and POSIX 2008 even requires this.  Work around it
# by disabling -e (using the XSI extension "set +e") if it's set.
am__sh_e_setup = case $$- in *e*) set +e;; esac
# Default flags passed to test drivers.
am__common_driver_flags = \
  --color-tests "$$am__color_tests" \
  --enable-hard-errors "$$am__enable_hard_errors" \
  --expect-failure "$$am__expect_failure"
# To be inserted before the command running the test.  Creates the
# directory for the log if needed.  Stores in $dir the directory
# containing $f, in $tst the test, in $log the log.  Executes the
# developer- defined test setup AM_TESTS_ENVIRONMENT (if any), and
# passes TESTS_ENVIRONMENT.  Set up options for the wrapper that
# will run the test scripts (or their associated LOG_COMPILER, if
# thy have one).
am__check_pre = \
$(am__sh_e_setup);					\
$(am__vpath_adj_setup) $(am__vpath_adj)			\
$(am__tty_colors);					\
srcdir=$(srcdir); export srcdir;			\
case "$@" in						\
  */*) am__odir=`echo "./$@" | sed 's|/[^/]*$$||'`;;	\
    *) am__odir=.;; 					\
esac;							\
test "x$$am__odir" = x"." || test -d "$$am__odir" 	\
  || $(MKDIR_P) "$$am__odir" || exit $$?;		\
if test -f "./$$f"; then dir=./;			\
elif test -f "$$f"; then dir=;				\
else dir="$(srcdir)/"; fi;				\
tst=$$dir$$f; log='$@'; 				\
if test -n '$(DISABLE_HARD_ERRORS)'; then		\
  am__enable_hard_errors=no; 				\
else							\
  am__enable_hard_errors=yes; 				\
fi; 							\
case " $(XFAIL_TESTS) " in				\
  *[\ \	]$$f[\ \	]* | *[\ \	]$$dir$$f[\ \	]*) \
    am__expect_failure=yes;;				\
  *)							\
    am__expect_failure=no;;				\
esac; 							\
$(AM_TESTS_ENVIRONMENT) $(TESTS_ENVIRONMENT)
# A shell command to get the names of the tests scripts with any registered
# extension removed (i.e., equivalently, the names of the test logs, with
# the '.log' extension removed).  The result is saved in the shell variable
# '$bases'.  This honors runtime overriding of TESTS and TEST_LOGS.  Sadly,
# we cannot use something simpler, involving e.g., "$(TEST_LOGS:.log=)",
# since that might cause problem with VPATH rewrites for suffix-less tests.
# See also 'test-harness-vpath-rewrite.sh' and 'test-trs-basic.sh'.
am__set_TESTS_bases = \
  bases='$(TEST_LOGS)'; \
  bases=`for i in $$bases; do echo $$i; done | sed 's/\.log$$//'`; \
  bases=`echo $$bases`
RECHECK_LOGS = $(TEST_LOGS)
TEST_SUITE_LOG = test-suite.log
TEST_EXTENSIONS = @EXEEXT@ .test
LOG_DRIVER = $(SHELL) $(top_srcdir)/test-driver
LOG_COMPILE = $(LOG_COMPILER) $(AM_LOG_FLAGS) $(LOG_FLAGS)
am__set_b = \
  case '$@' in \
    */*) \
      case '$*' in \
        */*) b='$*';; \
          *) b=`echo '$@' | sed 's/\.log$$//'`; \
       esac;; \
    *) \
      b='$*';; \
  esac
am__test_logs1 = $(TESTS:=.log)
am__test_logs2 = $(am__test_logs1:@EXEEXT@.log=.log)
TEST_LOGS = $(am__test_logs2:.test.log=.log)
TEST_LOG_DRIVER = $(SHELL) $(top_srcdir)/test-driver
TEST_LOG_COMPILE = $(TEST_LOG_COMPILER) $(AM_TEST_LOG_FLAGS) \
	$(TEST_LOG_FLAGS)
am__DIST_COMMON = $(dist_man_MANS) $(srcdir)/Makefile.in \
	$(srcdir)/config.h.in $(srcdir)/libpcre.pc.in \
	$(srcdir)/libpcre16.pc.in $(srcdir)/libpcre32.pc.in \
	$(srcdir)/libpcrecpp.pc.in $(srcdir)/libpcreposix.pc.in \
	$(srcdir)/pcre-config.in $(srcdir)/pcre.h.in \
	$(srcdir)/pcre_stringpiece.h.in $(srcdir)/pcrecpparg.h.in \
	AUTHORS COPYING ChangeLog INSTALL NEWS README ar-lib compile \
	config.guess config.sub depcomp install-sh ltmain.sh missing \
	test-driver
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
distdir = $(PACKAGE)-$(VERSION)
top_distdir = $(distdir)
am__remove_distdir = \
  if test -d "$(distdir)"; then \
    find "$(distdir)" -type d ! -perm -200 -exec chmod u+w {} ';' \
      && rm -rf "$(distdir)" \
      || { sleep 5 && rm -rf "$(distdir)"; }; \
  else :; fi
am__post_remove_distdir = $(am__remove_distdir)
DIST_ARCHIVES = $(distdir).tar.gz $(distdir).tar.bz2 $(distdir).zip
GZIP_ENV = --best
DIST_TARGETS = dist-bzip2 dist-gzip dist-zip
distuninstallcheck_listfiles = find . -type f -print
am__distuninstallcheck_listfiles = $(distuninstallcheck_listfiles) \
  | sed 's|^\./|$(prefix)/|' | grep -v '$(infodir)/dir$$'
distcleancheck_listfiles = find . -type f -print
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AS = @AS@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DISTCHECK_CONFIGURE_FLAGS = @DISTCHECK_CONFIGURE_FLAGS@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
EXTRA_LIBPCRE16_LDFLAGS = @EXTRA_LIBPCRE16_LDFLAGS@
EXTRA_LIBPCRE32_LDFLAGS = @EXTRA_LIBPCRE32_LDFLAGS@
EXTRA_LIBPCRECPP_LDFLAGS = @EXTRA_LIBPCRECPP_LDFLAGS@
EXTRA_LIBPCREPOSIX_LDFLAGS = @EXTRA_LIBPCREPOSIX_LDFLAGS@
EXTRA_LIBPCRE_LDFLAGS = @EXTRA_LIBPCRE_LDFLAGS@
FGREP = @FGREP@
GCOV_CFLAGS = @GCOV_CFLAGS@
GCOV_CXXFLAGS = @GCOV_CXXFLAGS@
GCOV_LIBS = @GCOV_LIBS@
GENHTML = @GENHTML@
GREP = @GREP@
HAVE_VISIBILITY = @HAVE_VISIBILITY@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LCOV = @LCOV@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBBZ2 = @LIBBZ2@
LIBOBJS = @LIBOBJS@
LIBREADLINE = @LIBREADLINE@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIBZ = @LIBZ@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PCRE_DATE = @PCRE_DATE@
PCRE_MAJOR = @PCRE_MAJOR@
PCRE_MINOR = @PCRE_MINOR@
PCRE_PRERELEASE = @PCRE_PRERELEASE@
PCRE_STATIC_CFLAG = @PCRE_STATIC_CFLAG@
PKG_CONFIG = @PKG_CONFIG@
PKG_CONFIG_LIBDIR = @PKG_CONFIG_LIBDIR@
PKG_CONFIG_PATH = @PKG_CONFIG_PATH@
PTHREAD_CC = @PTHREAD_CC@
PTHREAD_CFLAGS = @PTHREAD_CFLAGS@
PTHREAD_LIBS = @PTHREAD_LIBS@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SHTOOL = @SHTOOL@
STRIP = @STRIP@
VALGRIND_CFLAGS = @VALGRIND_CFLAGS@
VALGRIND_LIBS = @VALGRIND_LIBS@
VERSION = @VERSION@
VISIBILITY_CFLAGS = @VISIBILITY_CFLAGS@
VISIBILITY_CXXFLAGS = @VISIBILITY_CXXFLAGS@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
ax_pthread_config = @ax_pthread_config@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
enable_cpp = @enable_cpp@
enable_pcre16 = @enable_pcre16@
enable_pcre32 = @enable_pcre32@
enable_pcre8 = @enable_pcre8@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pcre_have_bits_type_traits = @pcre_have_bits_type_traits@
pcre_have_long_long = @pcre_have_long_long@
pcre_have_type_traits = @pcre_have_type_traits@
pcre_have_ulong_long = @pcre_have_ulong_long@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
ACLOCAL_AMFLAGS = -I m4
dist_doc_DATA = \
  doc/pcre.txt \
  doc/pcre-config.txt \
  doc/pcregrep.txt \
  doc/pcretest.txt \
  AUTHORS \
  COPYING \
  ChangeLog \
  LICENCE \
  NEWS \
  README


# Note that pcrecpp.html is not in this list; it is listed separately below.
dist_html_DATA = \
  doc/html/NON-AUTOTOOLS-BUILD.txt \
  doc/html/README.txt \
  doc/html/index.html \
  doc/html/pcre-config.html \
  doc/html/pcre.html \
  doc/html/pcre16.html \
  doc/html/pcre32.html \
  doc/html/pcre_assign_jit_stack.html \
  doc/html/pcre_compile.html \
  doc/html/pcre_compile2.html \
  doc/html/pcre_config.html \
  doc/html/pcre_copy_named_substring.html \
  doc/html/pcre_copy_substring.html \
  doc/html/pcre_dfa_exec.html \
  doc/html/pcre_exec.html \
  doc/html/pcre_free_study.html \
  doc/html/pcre_free_substring.html \
  doc/html/pcre_free_substring_list.html \
  doc/html/pcre_fullinfo.html \
  doc/html/pcre_get_named_substring.html \
  doc/html/pcre_get_stringnumber.html \
  doc/html/pcre_get_stringtable_entries.html \
  doc/html/pcre_get_substring.html \
  doc/html/pcre_get_substring_list.html \
  doc/html/pcre_jit_exec.html \
  doc/html/pcre_jit_stack_alloc.html \
  doc/html/pcre_jit_stack_free.html \
  doc/html/pcre_maketables.html \
  doc/html/pcre_pattern_to_host_byte_order.html \
  doc/html/pcre_refcount.html \
  doc/html/pcre_study.html \
  doc/html/pcre_utf16_to_host_byte_order.html \
  doc/html/pcre_utf32_to_host_byte_order.html \
  doc/html/pcre_version.html \
  doc/html/pcreapi.html \
  doc/html/pcrebuild.html \
  doc/html/pcrecallout.html \
  doc/html/pcrecompat.html \
  doc/html/pcredemo.html \
  doc/html/pcregrep.html \
  doc/html/pcrejit.html \
  doc/html/pcrelimits.html \
  doc/html/pcrematching.html \
  doc/html/pcrepartial.html \
  doc/html/pcrepattern.html \
  doc/html/pcreperform.html \
  doc/html/pcreposix.html \
  doc/html/pcreprecompile.html \
  doc/html/pcresample.html \
  doc/html/pcrestack.html \
  doc/html/pcresyntax.html \
  doc/html/pcretest.html \
  doc/html/pcreunicode.html

pcrecpp_html = doc/html/pcrecpp.html
dist_noinst_DATA = $(pcrecpp_html)
@WITH_PCRE_CPP_TRUE@html_DATA = $(pcrecpp_html)

# The Libtool libraries to install.  We'll add to this later.
lib_LTLIBRARIES = $(am__append_4) $(am__append_5) $(am__append_6) \
	$(am__append_20) $(am__append_22)
check_SCRIPTS =
dist_noinst_SCRIPTS = RunTest $(am__append_39)

# Additional files to delete on 'make clean' and 'make maintainer-clean'.
CLEANFILES = pcre_chartables.c testsavedregex teststderr testtemp* \
	testtry testNinput testtrygrep teststderrgrep testNinputgrep
MAINTAINERCLEANFILES = pcre.h.generic config.h.generic

# Additional files to bundle with the distribution, over and above what
# the Autotools include by default.

# These files contain additional m4 macros that are used by autoconf.

# These files contain maintenance information

# These files are used in the preparation of a release

# These files are to do with building for Virtual Pascal

# These files are usable versions of pcre.h and config.h that are distributed
# for the benefit of people who are building PCRE manually, without the
# Autotools support.

# The pcre_chartables.c.dist file is the default version of pcre_chartables.c,
# used unless --enable-rebuild-chartables is specified.

# The JIT compiler lives in a separate directory, but its files are #included
# when pcre_jit_compile.c is processed, so they must be distributed.

# PCRE demonstration program. No longer built automatcally. The point is that
# the users should build it themselves. So just distribute the source.
# noinst_PROGRAMS += pcredemo
# pcredemo_SOURCES = pcredemo.c
# pcredemo_LDADD = libpcre.la
EXTRA_DIST = m4/ax_pthread.m4 m4/pcre_visibility.m4 doc/perltest.txt \
	NON-UNIX-USE NON-AUTOTOOLS-BUILD HACKING PrepareRelease \
	CheckMan CleanTxt Detrail 132html doc/index.html.src \
	makevp.bat makevp_c.txt makevp_l.txt pcregexp.pas \
	pcre.h.generic config.h.generic pcre_chartables.c.dist \
	sljit/sljitConfig.h sljit/sljitConfigInternal.h \
	sljit/sljitExecAllocator.c sljit/sljitLir.c sljit/sljitLir.h \
	sljit/sljitNativeARM_32.c sljit/sljitNativeARM_64.c \
	sljit/sljitNativeARM_T2_32.c sljit/sljitNativeMIPS_32.c \
	sljit/sljitNativeMIPS_64.c sljit/sljitNativeMIPS_common.c \
	sljit/sljitNativePPC_32.c sljit/sljitNativePPC_64.c \
	sljit/sljitNativePPC_common.c sljit/sljitNativeSPARC_32.c \
	sljit/sljitNativeSPARC_common.c sljit/sljitNativeTILEGX_64.c \
	sljit/sljitNativeTILEGX-encoder.c sljit/sljitNativeX86_32.c \
	sljit/sljitNativeX86_64.c sljit/sljitNativeX86_common.c \
	sljit/sljitUtils.c RunTest.bat testdata/grepbinary \
	testdata/grepfilelist testdata/grepinput testdata/grepinput3 \
	testdata/grepinput8 testdata/grepinputv testdata/grepinputx \
	testdata/greplist testdata/grepoutput testdata/grepoutput8 \
	testdata/grepoutputN testdata/greppatN4 testdata/saved16 \
	testdata/saved16BE-1 testdata/saved16BE-2 testdata/saved16LE-1 \
	testdata/saved16LE-2 testdata/saved32 testdata/saved32BE-1 \
	testdata/saved32BE-2 testdata/saved32LE-1 testdata/saved32LE-2 \
	testdata/saved8 testdata/testinput1 testdata/testinput2 \
	testdata/testinput3 testdata/testinput4 testdata/testinput5 \
	testdata/testinput6 testdata/testinput7 testdata/testinput8 \
	testdata/testinput9 testdata/testinput10 testdata/testinput11 \
	testdata/testinput12 testdata/testinput13 testdata/testinput14 \
	testdata/testinput15 testdata/testinput16 testdata/testinput17 \
	testdata/testinput18 testdata/testinput19 testdata/testinput20 \
	testdata/testinput21 testdata/testinput22 testdata/testinput23 \
	testdata/testinput24 testdata/testinput25 testdata/testinput26 \
	testdata/testinputEBC testdata/testoutput1 \
	testdata/testoutput2 testdata/testoutput3 \
	testdata/testoutput3A testdata/testoutput3B \
	testdata/testoutput4 testdata/testoutput5 testdata/testoutput6 \
	testdata/testoutput7 testdata/testoutput8 testdata/testoutput9 \
	testdata/testoutput10 testdata/testoutput11-8 \
	testdata/testoutput11-16 testdata/testoutput11-32 \
	testdata/testoutput12 testdata/testoutput13 \
	testdata/testoutput14 testdata/testoutput15 \
	testdata/testoutput16 testdata/testoutput17 \
	testdata/testoutput18-16 testdata/testoutput18-32 \
	testdata/testoutput19 testdata/testoutput20 \
	testdata/testoutput21-16 testdata/testoutput21-32 \
	testdata/testoutput22-16 testdata/testoutput22-32 \
	testdata/testoutput23 testdata/testoutput24 \
	testdata/testoutput25 testdata/testoutput26 \
	testdata/testoutputEBC testdata/wintestinput3 \
	testdata/wintestoutput3 perltest.pl pcredemo.c $(pcrecpp_man) \
	cmake/COPYING-CMAKE-SCRIPTS \
	cmake/FindPackageHandleStandardArgs.cmake \
	cmake/FindReadline.cmake cmake/FindEditline.cmake \
	CMakeLists.txt config-cmake.h.in

# These are the header files we'll install. We do not distribute pcre.h because
# it is generated from pcre.h.in.
nodist_include_HEADERS = pcre.h $(am__append_1)
include_HEADERS = pcreposix.h $(am__append_2)
bin_SCRIPTS = pcre-config
@WITH_REBUILD_CHARTABLES_TRUE@dftables_SOURCES = dftables.c
BUILT_SOURCES = pcre_chartables.c
@WITH_PCRE8_TRUE@libpcre_la_SOURCES = \
@WITH_PCRE8_TRUE@  pcre_byte_order.c \
@WITH_PCRE8_TRUE@  pcre_compile.c \
@WITH_PCRE8_TRUE@  pcre_config.c \
@WITH_PCRE8_TRUE@  pcre_dfa_exec.c \
@WITH_PCRE8_TRUE@  pcre_exec.c \
@WITH_PCRE8_TRUE@  pcre_fullinfo.c \
@WITH_PCRE8_TRUE@  pcre_get.c \
@WITH_PCRE8_TRUE@  pcre_globals.c \
@WITH_PCRE8_TRUE@  pcre_internal.h \
@WITH_PCRE8_TRUE@  pcre_jit_compile.c \
@WITH_PCRE8_TRUE@  pcre_maketables.c \
@WITH_PCRE8_TRUE@  pcre_newline.c \
@WITH_PCRE8_TRUE@  pcre_ord2utf8.c \
@WITH_PCRE8_TRUE@  pcre_refcount.c \
@WITH_PCRE8_TRUE@  pcre_string_utils.c \
@WITH_PCRE8_TRUE@  pcre_study.c \
@WITH_PCRE8_TRUE@  pcre_tables.c \
@WITH_PCRE8_TRUE@  pcre_ucd.c \
@WITH_PCRE8_TRUE@  pcre_valid_utf8.c \
@WITH_PCRE8_TRUE@  pcre_version.c \
@WITH_PCRE8_TRUE@  pcre_xclass.c \
@WITH_PCRE8_TRUE@  ucp.h

@WITH_PCRE8_TRUE@libpcre_la_CFLAGS = $(VISIBILITY_CFLAGS) $(AM_CFLAGS) \
@WITH_PCRE8_TRUE@	$(am__append_7) $(am__append_10)
@WITH_PCRE8_TRUE@libpcre_la_LIBADD =
@WITH_PCRE8_TRUE@nodist_libpcre_la_SOURCES = \
@WITH_PCRE8_TRUE@  pcre_chartables.c

@WITH_PCRE16_TRUE@libpcre16_la_SOURCES = \
@WITH_PCRE16_TRUE@  pcre16_byte_order.c \
@WITH_PCRE16_TRUE@  pcre16_chartables.c \
@WITH_PCRE16_TRUE@  pcre16_compile.c \
@WITH_PCRE16_TRUE@  pcre16_config.c \
@WITH_PCRE16_TRUE@  pcre16_dfa_exec.c \
@WITH_PCRE16_TRUE@  pcre16_exec.c \
@WITH_PCRE16_TRUE@  pcre16_fullinfo.c \
@WITH_PCRE16_TRUE@  pcre16_get.c \
@WITH_PCRE16_TRUE@  pcre16_globals.c \
@WITH_PCRE16_TRUE@  pcre16_jit_compile.c \
@WITH_PCRE16_TRUE@  pcre16_maketables.c \
@WITH_PCRE16_TRUE@  pcre16_newline.c \
@WITH_PCRE16_TRUE@  pcre16_ord2utf16.c \
@WITH_PCRE16_TRUE@  pcre16_refcount.c \
@WITH_PCRE16_TRUE@  pcre16_string_utils.c \
@WITH_PCRE16_TRUE@  pcre16_study.c \
@WITH_PCRE16_TRUE@  pcre16_tables.c \
@WITH_PCRE16_TRUE@  pcre16_ucd.c \
@WITH_PCRE16_TRUE@  pcre16_utf16_utils.c \
@WITH_PCRE16_TRUE@  pcre16_valid_utf16.c \
@WITH_PCRE16_TRUE@  pcre16_version.c \
@WITH_PCRE16_TRUE@  pcre16_xclass.c

@WITH_PCRE16_TRUE@libpcre16_la_CFLAGS = $(VISIBILITY_CFLAGS) \
@WITH_PCRE16_TRUE@	$(AM_CFLAGS) $(am__append_8) \
@WITH_PCRE16_TRUE@	$(am__append_11)
@WITH_PCRE16_TRUE@libpcre16_la_LIBADD =
@WITH_PCRE16_TRUE@nodist_libpcre16_la_SOURCES = \
@WITH_PCRE16_TRUE@  pcre_chartables.c

@WITH_PCRE32_TRUE@libpcre32_la_SOURCES = \
@WITH_PCRE32_TRUE@  pcre32_byte_order.c \
@WITH_PCRE32_TRUE@  pcre32_chartables.c \
@WITH_PCRE32_TRUE@  pcre32_compile.c \
@WITH_PCRE32_TRUE@  pcre32_config.c \
@WITH_PCRE32_TRUE@  pcre32_dfa_exec.c \
@WITH_PCRE32_TRUE@  pcre32_exec.c \
@WITH_PCRE32_TRUE@  pcre32_fullinfo.c \
@WITH_PCRE32_TRUE@  pcre32_get.c \
@WITH_PCRE32_TRUE@  pcre32_globals.c \
@WITH_PCRE32_TRUE@  pcre32_jit_compile.c \
@WITH_PCRE32_TRUE@  pcre32_maketables.c \
@WITH_PCRE32_TRUE@  pcre32_newline.c \
@WITH_PCRE32_TRUE@  pcre32_ord2utf32.c \
@WITH_PCRE32_TRUE@  pcre32_refcount.c \
@WITH_PCRE32_TRUE@  pcre32_string_utils.c \
@WITH_PCRE32_TRUE@  pcre32_study.c \
@WITH_PCRE32_TRUE@  pcre32_tables.c \
@WITH_PCRE32_TRUE@  pcre32_ucd.c \
@WITH_PCRE32_TRUE@  pcre32_utf32_utils.c \
@WITH_PCRE32_TRUE@  pcre32_valid_utf32.c \
@WITH_PCRE32_TRUE@  pcre32_version.c \
@WITH_PCRE32_TRUE@  pcre32_xclass.c

@WITH_PCRE32_TRUE@libpcre32_la_CFLAGS = $(VISIBILITY_CFLAGS) \
@WITH_PCRE32_TRUE@	$(AM_CFLAGS) $(am__append_9) \
@WITH_PCRE32_TRUE@	$(am__append_12)
@WITH_PCRE32_TRUE@libpcre32_la_LIBADD =
@WITH_PCRE32_TRUE@nodist_libpcre32_la_SOURCES = \
@WITH_PCRE32_TRUE@  pcre_chartables.c

@WITH_PCRE8_TRUE@libpcre_la_LDFLAGS = $(EXTRA_LIBPCRE_LDFLAGS)
@WITH_PCRE16_TRUE@libpcre16_la_LDFLAGS = $(EXTRA_LIBPCRE16_LDFLAGS)
@WITH_PCRE32_TRUE@libpcre32_la_LDFLAGS = $(EXTRA_LIBPCRE32_LDFLAGS)
@WITH_JIT_TRUE@pcre_jit_test_SOURCES = pcre_jit_test.c
@WITH_JIT_TRUE@pcre_jit_test_CFLAGS = $(AM_CFLAGS) $(am__append_18)
@WITH_JIT_TRUE@pcre_jit_test_LDADD = $(am__append_15) $(am__append_16) \
@WITH_JIT_TRUE@	$(am__append_17) $(am__append_19)
@WITH_PCRE8_TRUE@libpcreposix_la_SOURCES = \
@WITH_PCRE8_TRUE@  pcreposix.c

@WITH_PCRE8_TRUE@libpcreposix_la_CFLAGS = $(VISIBILITY_CFLAGS) \
@WITH_PCRE8_TRUE@	$(AM_CFLAGS) $(am__append_21)
@WITH_PCRE8_TRUE@libpcreposix_la_LDFLAGS = $(EXTRA_LIBPCREPOSIX_LDFLAGS)
@WITH_PCRE8_TRUE@libpcreposix_la_LIBADD = libpcre.la
@WITH_PCRE_CPP_TRUE@libpcrecpp_la_SOURCES = \
@WITH_PCRE_CPP_TRUE@  pcrecpp_internal.h \
@WITH_PCRE_CPP_TRUE@  pcrecpp.cc \
@WITH_PCRE_CPP_TRUE@  pcre_scanner.cc \
@WITH_PCRE_CPP_TRUE@  pcre_stringpiece.cc

@WITH_PCRE_CPP_TRUE@libpcrecpp_la_CXXFLAGS = $(VISIBILITY_CXXFLAGS) \
@WITH_PCRE_CPP_TRUE@	$(AM_CXXFLAGS) $(am__append_25)
@WITH_PCRE_CPP_TRUE@libpcrecpp_la_LDFLAGS = $(EXTRA_LIBPCRECPP_LDFLAGS)
@WITH_PCRE_CPP_TRUE@libpcrecpp_la_LIBADD = libpcre.la
@WITH_PCRE_CPP_TRUE@pcrecpp_unittest_SOURCES = pcrecpp_unittest.cc
@WITH_PCRE_CPP_TRUE@pcrecpp_unittest_CXXFLAGS = $(AM_CXXFLAGS)
@WITH_PCRE_CPP_TRUE@pcrecpp_unittest_LDADD = libpcrecpp.la \
@WITH_PCRE_CPP_TRUE@	$(am__append_26)
@WITH_PCRE_CPP_TRUE@pcre_scanner_unittest_SOURCES = pcre_scanner_unittest.cc
@WITH_PCRE_CPP_TRUE@pcre_scanner_unittest_CXXFLAGS = $(AM_CXXFLAGS)
@WITH_PCRE_CPP_TRUE@pcre_scanner_unittest_LDADD = libpcrecpp.la \
@WITH_PCRE_CPP_TRUE@	$(am__append_27)
@WITH_PCRE_CPP_TRUE@pcre_stringpiece_unittest_SOURCES = pcre_stringpiece_unittest.cc
@WITH_PCRE_CPP_TRUE@pcre_stringpiece_unittest_CXXFLAGS = $(AM_CXXFLAGS)
@WITH_PCRE_CPP_TRUE@pcre_stringpiece_unittest_LDADD = libpcrecpp.la \
@WITH_PCRE_CPP_TRUE@	$(am__append_28)
pcretest_SOURCES = pcretest.c $(am__append_29) $(am__append_31) \
	$(am__append_33)
pcretest_CFLAGS = $(AM_CFLAGS) $(am__append_35) $(am__append_36)
pcretest_LDADD = $(LIBREADLINE) $(am__append_30) $(am__append_32) \
	$(am__append_34) $(am__append_37)
@WITH_PCRE8_TRUE@pcregrep_SOURCES = pcregrep.c
@WITH_PCRE8_TRUE@pcregrep_CFLAGS = $(AM_CFLAGS) $(am__append_41)
@WITH_PCRE8_TRUE@pcregrep_LDADD = $(LIBZ) $(LIBBZ2) libpcre.la \
@WITH_PCRE8_TRUE@	libpcreposix.la $(am__append_42)

# A PCRE user submitted the following addition, saying that it "will allow
# anyone using the 'mingw32' compiler to simply type 'make pcre.dll' and get a
# nice DLL for Windows use". (It is used by the pcre.dll target.)
DLL_OBJS = pcre_byte_order.o pcre_compile.o pcre_config.o \
	pcre_dfa_exec.o pcre_exec.o pcre_fullinfo.o pcre_get.o \
	pcre_globals.o pcre_jit_compile.o pcre_maketables.o \
	pcre_newline.o pcre_ord2utf8.o pcre_refcount.o \
	pcre_study.o pcre_tables.o pcre_ucd.o \
	pcre_valid_utf8.o pcre_version.o pcre_chartables.o \
	pcre_xclass.o


# We have .pc files for pkg-config users.
pkgconfigdir = $(libdir)/pkgconfig
pkgconfig_DATA = libpcre.pc libpcreposix.pc $(am__append_43) \
	$(am__append_44) $(am__append_45)

# Note that pcrecpp.3 is not in this list, but is included separately below.
dist_man_MANS = \
  doc/pcre-config.1 \
  doc/pcre.3 \
  doc/pcre16.3 \
  doc/pcre32.3 \
  doc/pcre_assign_jit_stack.3 \
  doc/pcre_compile.3 \
  doc/pcre_compile2.3 \
  doc/pcre_config.3 \
  doc/pcre_copy_named_substring.3 \
  doc/pcre_copy_substring.3 \
  doc/pcre_dfa_exec.3 \
  doc/pcre_exec.3 \
  doc/pcre_free_study.3 \
  doc/pcre_free_substring.3 \
  doc/pcre_free_substring_list.3 \
  doc/pcre_fullinfo.3 \
  doc/pcre_get_named_substring.3 \
  doc/pcre_get_stringnumber.3 \
  doc/pcre_get_stringtable_entries.3 \
  doc/pcre_get_substring.3 \
  doc/pcre_get_substring_list.3 \
  doc/pcre_jit_exec.3 \
  doc/pcre_jit_stack_alloc.3 \
  doc/pcre_jit_stack_free.3 \
  doc/pcre_maketables.3 \
  doc/pcre_pattern_to_host_byte_order.3 \
  doc/pcre_refcount.3 \
  doc/pcre_study.3 \
  doc/pcre_utf16_to_host_byte_order.3 \
  doc/pcre_utf32_to_host_byte_order.3 \
  doc/pcre_version.3 \
  doc/pcreapi.3 \
  doc/pcrebuild.3 \
  doc/pcrecallout.3 \
  doc/pcrecompat.3 \
  doc/pcredemo.3 \
  doc/pcregrep.1 \
  doc/pcrejit.3 \
  doc/pcrelimits.3 \
  doc/pcrematching.3 \
  doc/pcrepartial.3 \
  doc/pcrepattern.3 \
  doc/pcreperform.3 \
  doc/pcreposix.3 \
  doc/pcreprecompile.3 \
  doc/pcresample.3 \
  doc/pcrestack.3 \
  doc/pcresyntax.3 \
  doc/pcretest.1 \
  doc/pcreunicode.3

pcrecpp_man = doc/pcrecpp.3
@WITH_PCRE_CPP_TRUE@man_MANS = $(pcrecpp_man)

# gcov/lcov code coverage reporting

# Coverage reporting targets:
#
# coverage: Create a coverage report from 'make check'
# coverage-baseline: Capture baseline coverage information
# coverage-reset: This zeros the coverage counters only
# coverage-report: This creates the coverage report only
# coverage-clean-report: This removes the generated coverage report
#   without cleaning the coverage data itself
# coverage-clean-data: This removes the captured coverage data without
#   removing the coverage files created at compile time (*.gcno)
# coverage-clean: This cleans all coverage data including the generated
#   coverage report.
@WITH_GCOV_TRUE@COVERAGE_TEST_NAME = $(PACKAGE)
@WITH_GCOV_TRUE@COVERAGE_NAME = $(PACKAGE)-$(VERSION)
@WITH_GCOV_TRUE@COVERAGE_OUTPUT_FILE = $(COVERAGE_NAME)-coverage.info
@WITH_GCOV_TRUE@COVERAGE_OUTPUT_DIR = $(COVERAGE_NAME)-coverage
@WITH_GCOV_TRUE@COVERAGE_LCOV_EXTRA_FLAGS =
@WITH_GCOV_TRUE@COVERAGE_GENHTML_EXTRA_FLAGS =
@WITH_GCOV_TRUE@coverage_quiet = $(coverage_quiet_$(V))
@WITH_GCOV_TRUE@coverage_quiet_ = $(coverage_quiet_$(AM_DEFAULT_VERBOSITY))
@WITH_GCOV_TRUE@coverage_quiet_0 = --quiet
all: $(BUILT_SOURCES) config.h
	$(MAKE) $(AM_MAKEFLAGS) all-am

.SUFFIXES:
.SUFFIXES: .c .cc .lo .log .o .obj .test .test$(EXEEXT) .trs
am--refresh: Makefile
	@:
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      echo ' cd $(srcdir) && $(AUTOMAKE) --gnu'; \
	      $(am__cd) $(srcdir) && $(AUTOMAKE) --gnu \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    echo ' $(SHELL) ./config.status'; \
	    $(SHELL) ./config.status;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	$(SHELL) ./config.status --recheck

$(top_srcdir)/configure:  $(am__configure_deps)
	$(am__cd) $(srcdir) && $(AUTOCONF)
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	$(am__cd) $(srcdir) && $(ACLOCAL) $(ACLOCAL_AMFLAGS)
$(am__aclocal_m4_deps):

config.h: stamp-h1
	@test -f $@ || rm -f stamp-h1
	@test -f $@ || $(MAKE) $(AM_MAKEFLAGS) stamp-h1

stamp-h1: $(srcdir)/config.h.in $(top_builddir)/config.status
	@rm -f stamp-h1
	cd $(top_builddir) && $(SHELL) ./config.status config.h
$(srcdir)/config.h.in:  $(am__configure_deps)
	($(am__cd) $(top_srcdir) && $(AUTOHEADER))
	rm -f stamp-h1
	touch $@

distclean-hdr:
	-rm -f config.h stamp-h1
libpcre.pc: $(top_builddir)/config.status $(srcdir)/libpcre.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
libpcre16.pc: $(top_builddir)/config.status $(srcdir)/libpcre16.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
libpcre32.pc: $(top_builddir)/config.status $(srcdir)/libpcre32.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
libpcreposix.pc: $(top_builddir)/config.status $(srcdir)/libpcreposix.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
libpcrecpp.pc: $(top_builddir)/config.status $(srcdir)/libpcrecpp.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
pcre-config: $(top_builddir)/config.status $(srcdir)/pcre-config.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
pcre.h: $(top_builddir)/config.status $(srcdir)/pcre.h.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
pcre_stringpiece.h: $(top_builddir)/config.status $(srcdir)/pcre_stringpiece.h.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
pcrecpparg.h: $(top_builddir)/config.status $(srcdir)/pcrecpparg.h.in
	cd $(top_builddir) && $(SHELL) ./config.status $@

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

libpcre.la: $(libpcre_la_OBJECTS) $(libpcre_la_DEPENDENCIES) $(EXTRA_libpcre_la_DEPENDENCIES)
	$(AM_V_CCLD)$(libpcre_la_LINK) $(am_libpcre_la_rpath) $(libpcre_la_OBJECTS) $(libpcre_la_LIBADD) $(LIBS)

libpcre16.la: $(libpcre16_la_OBJECTS) $(libpcre16_la_DEPENDENCIES) $(EXTRA_libpcre16_la_DEPENDENCIES)
	$(AM_V_CCLD)$(libpcre16_la_LINK) $(am_libpcre16_la_rpath) $(libpcre16_la_OBJECTS) $(libpcre16_la_LIBADD) $(LIBS)

libpcre32.la: $(libpcre32_la_OBJECTS) $(libpcre32_la_DEPENDENCIES) $(EXTRA_libpcre32_la_DEPENDENCIES)
	$(AM_V_CCLD)$(libpcre32_la_LINK) $(am_libpcre32_la_rpath) $(libpcre32_la_OBJECTS) $(libpcre32_la_LIBADD) $(LIBS)

libpcrecpp.la: $(libpcrecpp_la_OBJECTS) $(libpcrecpp_la_DEPENDENCIES) $(EXTRA_libpcrecpp_la_DEPENDENCIES)
	$(AM_V_CXXLD)$(libpcrecpp_la_LINK) $(am_libpcrecpp_la_rpath) $(libpcrecpp_la_OBJECTS) $(libpcrecpp_la_LIBADD) $(LIBS)

libpcreposix.la: $(libpcreposix_la_OBJECTS) $(libpcreposix_la_DEPENDENCIES) $(EXTRA_libpcreposix_la_DEPENDENCIES)
	$(AM_V_CCLD)$(libpcreposix_la_LINK) $(am_libpcreposix_la_rpath) $(libpcreposix_la_OBJECTS) $(libpcreposix_la_LIBADD) $(LIBS)
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	@list='$(bin_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

dftables$(EXEEXT): $(dftables_OBJECTS) $(dftables_DEPENDENCIES) $(EXTRA_dftables_DEPENDENCIES)
	@rm -f dftables$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(dftables_OBJECTS) $(dftables_LDADD) $(LIBS)

pcre_jit_test$(EXEEXT): $(pcre_jit_test_OBJECTS) $(pcre_jit_test_DEPENDENCIES) $(EXTRA_pcre_jit_test_DEPENDENCIES)
	@rm -f pcre_jit_test$(EXEEXT)
	$(AM_V_CCLD)$(pcre_jit_test_LINK) $(pcre_jit_test_OBJECTS) $(pcre_jit_test_LDADD) $(LIBS)

pcre_scanner_unittest$(EXEEXT): $(pcre_scanner_unittest_OBJECTS) $(pcre_scanner_unittest_DEPENDENCIES) $(EXTRA_pcre_scanner_unittest_DEPENDENCIES)
	@rm -f pcre_scanner_unittest$(EXEEXT)
	$(AM_V_CXXLD)$(pcre_scanner_unittest_LINK) $(pcre_scanner_unittest_OBJECTS) $(pcre_scanner_unittest_LDADD) $(LIBS)

pcre_stringpiece_unittest$(EXEEXT): $(pcre_stringpiece_unittest_OBJECTS) $(pcre_stringpiece_unittest_DEPENDENCIES) $(EXTRA_pcre_stringpiece_unittest_DEPENDENCIES)
	@rm -f pcre_stringpiece_unittest$(EXEEXT)
	$(AM_V_CXXLD)$(pcre_stringpiece_unittest_LINK) $(pcre_stringpiece_unittest_OBJECTS) $(pcre_stringpiece_unittest_LDADD) $(LIBS)

pcrecpp_unittest$(EXEEXT): $(pcrecpp_unittest_OBJECTS) $(pcrecpp_unittest_DEPENDENCIES) $(EXTRA_pcrecpp_unittest_DEPENDENCIES)
	@rm -f pcrecpp_unittest$(EXEEXT)
	$(AM_V_CXXLD)$(pcrecpp_unittest_LINK) $(pcrecpp_unittest_OBJECTS) $(pcrecpp_unittest_LDADD) $(LIBS)

pcregrep$(EXEEXT): $(pcregrep_OBJECTS) $(pcregrep_DEPENDENCIES) $(EXTRA_pcregrep_DEPENDENCIES)
	@rm -f pcregrep$(EXEEXT)
	$(AM_V_CCLD)$(pcregrep_LINK) $(pcregrep_OBJECTS) $(pcregrep_LDADD) $(LIBS)

pcretest$(EXEEXT): $(pcretest_OBJECTS) $(pcretest_DEPENDENCIES) $(EXTRA_pcretest_DEPENDENCIES)
	@rm -f pcretest$(EXEEXT)
	$(AM_V_CCLD)$(pcretest_LINK) $(pcretest_OBJECTS) $(pcretest_LDADD) $(LIBS)
install-binSCRIPTS: $(bin_SCRIPTS)
	@$(NORMAL_INSTALL)
	@list='$(bin_SCRIPTS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  if test -f "$$d$$p"; then echo "$$d$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n' \
	    -e 'h;s|.*|.|' \
	    -e 'p;x;s,.*/,,;$(transform)' | sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1; } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) { files[d] = files[d] " " $$1; \
	      if (++n[d] == $(am__install_max)) { \
		print "f", d, files[d]; n[d] = 0; files[d] = "" } } \
	    else { print "f", d "/" $$4, $$1 } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	     if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	     test -z "$$files" || { \
	       echo " $(INSTALL_SCRIPT) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	       $(INSTALL_SCRIPT) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	     } \
	; done

uninstall-binSCRIPTS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_SCRIPTS)'; test -n "$(bindir)" || exit 0; \
	files=`for p in $$list; do echo "$$p"; done | \
	       sed -e 's,.*/,,;$(transform)'`; \
	dir='$(DESTDIR)$(bindir)'; $(am__uninstall_files_from_dir)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/dftables.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_byte_order.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_chartables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_compile.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_config.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_dfa_exec.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_exec.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_fullinfo.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_get.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_globals.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_jit_compile.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_maketables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_newline.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_ord2utf16.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_refcount.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_string_utils.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_study.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_tables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_ucd.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_utf16_utils.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_valid_utf16.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_version.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre16_xclass.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre16_la-pcre_chartables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_byte_order.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_chartables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_compile.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_config.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_dfa_exec.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_exec.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_fullinfo.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_get.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_globals.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_jit_compile.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_maketables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_newline.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_ord2utf32.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_refcount.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_string_utils.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_study.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_tables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_ucd.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_utf32_utils.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_valid_utf32.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_version.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre32_xclass.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre32_la-pcre_chartables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_byte_order.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_chartables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_compile.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_config.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_dfa_exec.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_exec.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_fullinfo.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_get.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_globals.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_jit_compile.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_maketables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_newline.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_ord2utf8.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_refcount.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_string_utils.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_study.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_tables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_ucd.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_valid_utf8.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_version.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcre_la-pcre_xclass.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcrecpp_la-pcre_scanner.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcrecpp_la-pcre_stringpiece.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcrecpp_la-pcrecpp.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libpcreposix_la-pcreposix.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pcre_jit_test-pcre_jit_test.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pcre_scanner_unittest-pcre_scanner_unittest.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pcre_stringpiece_unittest-pcre_stringpiece_unittest.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pcrecpp_unittest-pcrecpp_unittest.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pcregrep-pcregrep.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pcretest-pcre16_printint.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pcretest-pcre32_printint.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pcretest-pcre_printint.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pcretest-pcretest.Po@am__quote@

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LTCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

libpcre_la-pcre_byte_order.lo: pcre_byte_order.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_byte_order.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_byte_order.Tpo -c -o libpcre_la-pcre_byte_order.lo `test -f 'pcre_byte_order.c' || echo '$(srcdir)/'`pcre_byte_order.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_byte_order.Tpo $(DEPDIR)/libpcre_la-pcre_byte_order.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_byte_order.c' object='libpcre_la-pcre_byte_order.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_byte_order.lo `test -f 'pcre_byte_order.c' || echo '$(srcdir)/'`pcre_byte_order.c

libpcre_la-pcre_compile.lo: pcre_compile.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_compile.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_compile.Tpo -c -o libpcre_la-pcre_compile.lo `test -f 'pcre_compile.c' || echo '$(srcdir)/'`pcre_compile.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_compile.Tpo $(DEPDIR)/libpcre_la-pcre_compile.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_compile.c' object='libpcre_la-pcre_compile.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_compile.lo `test -f 'pcre_compile.c' || echo '$(srcdir)/'`pcre_compile.c

libpcre_la-pcre_config.lo: pcre_config.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_config.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_config.Tpo -c -o libpcre_la-pcre_config.lo `test -f 'pcre_config.c' || echo '$(srcdir)/'`pcre_config.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_config.Tpo $(DEPDIR)/libpcre_la-pcre_config.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_config.c' object='libpcre_la-pcre_config.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_config.lo `test -f 'pcre_config.c' || echo '$(srcdir)/'`pcre_config.c

libpcre_la-pcre_dfa_exec.lo: pcre_dfa_exec.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_dfa_exec.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_dfa_exec.Tpo -c -o libpcre_la-pcre_dfa_exec.lo `test -f 'pcre_dfa_exec.c' || echo '$(srcdir)/'`pcre_dfa_exec.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_dfa_exec.Tpo $(DEPDIR)/libpcre_la-pcre_dfa_exec.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_dfa_exec.c' object='libpcre_la-pcre_dfa_exec.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_dfa_exec.lo `test -f 'pcre_dfa_exec.c' || echo '$(srcdir)/'`pcre_dfa_exec.c

libpcre_la-pcre_exec.lo: pcre_exec.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_exec.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_exec.Tpo -c -o libpcre_la-pcre_exec.lo `test -f 'pcre_exec.c' || echo '$(srcdir)/'`pcre_exec.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_exec.Tpo $(DEPDIR)/libpcre_la-pcre_exec.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_exec.c' object='libpcre_la-pcre_exec.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_exec.lo `test -f 'pcre_exec.c' || echo '$(srcdir)/'`pcre_exec.c

libpcre_la-pcre_fullinfo.lo: pcre_fullinfo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_fullinfo.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_fullinfo.Tpo -c -o libpcre_la-pcre_fullinfo.lo `test -f 'pcre_fullinfo.c' || echo '$(srcdir)/'`pcre_fullinfo.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_fullinfo.Tpo $(DEPDIR)/libpcre_la-pcre_fullinfo.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_fullinfo.c' object='libpcre_la-pcre_fullinfo.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_fullinfo.lo `test -f 'pcre_fullinfo.c' || echo '$(srcdir)/'`pcre_fullinfo.c

libpcre_la-pcre_get.lo: pcre_get.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_get.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_get.Tpo -c -o libpcre_la-pcre_get.lo `test -f 'pcre_get.c' || echo '$(srcdir)/'`pcre_get.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_get.Tpo $(DEPDIR)/libpcre_la-pcre_get.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_get.c' object='libpcre_la-pcre_get.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_get.lo `test -f 'pcre_get.c' || echo '$(srcdir)/'`pcre_get.c

libpcre_la-pcre_globals.lo: pcre_globals.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_globals.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_globals.Tpo -c -o libpcre_la-pcre_globals.lo `test -f 'pcre_globals.c' || echo '$(srcdir)/'`pcre_globals.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_globals.Tpo $(DEPDIR)/libpcre_la-pcre_globals.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_globals.c' object='libpcre_la-pcre_globals.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_globals.lo `test -f 'pcre_globals.c' || echo '$(srcdir)/'`pcre_globals.c

libpcre_la-pcre_jit_compile.lo: pcre_jit_compile.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_jit_compile.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_jit_compile.Tpo -c -o libpcre_la-pcre_jit_compile.lo `test -f 'pcre_jit_compile.c' || echo '$(srcdir)/'`pcre_jit_compile.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_jit_compile.Tpo $(DEPDIR)/libpcre_la-pcre_jit_compile.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_jit_compile.c' object='libpcre_la-pcre_jit_compile.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_jit_compile.lo `test -f 'pcre_jit_compile.c' || echo '$(srcdir)/'`pcre_jit_compile.c

libpcre_la-pcre_maketables.lo: pcre_maketables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_maketables.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_maketables.Tpo -c -o libpcre_la-pcre_maketables.lo `test -f 'pcre_maketables.c' || echo '$(srcdir)/'`pcre_maketables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_maketables.Tpo $(DEPDIR)/libpcre_la-pcre_maketables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_maketables.c' object='libpcre_la-pcre_maketables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_maketables.lo `test -f 'pcre_maketables.c' || echo '$(srcdir)/'`pcre_maketables.c

libpcre_la-pcre_newline.lo: pcre_newline.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_newline.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_newline.Tpo -c -o libpcre_la-pcre_newline.lo `test -f 'pcre_newline.c' || echo '$(srcdir)/'`pcre_newline.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_newline.Tpo $(DEPDIR)/libpcre_la-pcre_newline.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_newline.c' object='libpcre_la-pcre_newline.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_newline.lo `test -f 'pcre_newline.c' || echo '$(srcdir)/'`pcre_newline.c

libpcre_la-pcre_ord2utf8.lo: pcre_ord2utf8.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_ord2utf8.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_ord2utf8.Tpo -c -o libpcre_la-pcre_ord2utf8.lo `test -f 'pcre_ord2utf8.c' || echo '$(srcdir)/'`pcre_ord2utf8.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_ord2utf8.Tpo $(DEPDIR)/libpcre_la-pcre_ord2utf8.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_ord2utf8.c' object='libpcre_la-pcre_ord2utf8.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_ord2utf8.lo `test -f 'pcre_ord2utf8.c' || echo '$(srcdir)/'`pcre_ord2utf8.c

libpcre_la-pcre_refcount.lo: pcre_refcount.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_refcount.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_refcount.Tpo -c -o libpcre_la-pcre_refcount.lo `test -f 'pcre_refcount.c' || echo '$(srcdir)/'`pcre_refcount.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_refcount.Tpo $(DEPDIR)/libpcre_la-pcre_refcount.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_refcount.c' object='libpcre_la-pcre_refcount.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_refcount.lo `test -f 'pcre_refcount.c' || echo '$(srcdir)/'`pcre_refcount.c

libpcre_la-pcre_string_utils.lo: pcre_string_utils.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_string_utils.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_string_utils.Tpo -c -o libpcre_la-pcre_string_utils.lo `test -f 'pcre_string_utils.c' || echo '$(srcdir)/'`pcre_string_utils.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_string_utils.Tpo $(DEPDIR)/libpcre_la-pcre_string_utils.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_string_utils.c' object='libpcre_la-pcre_string_utils.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_string_utils.lo `test -f 'pcre_string_utils.c' || echo '$(srcdir)/'`pcre_string_utils.c

libpcre_la-pcre_study.lo: pcre_study.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_study.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_study.Tpo -c -o libpcre_la-pcre_study.lo `test -f 'pcre_study.c' || echo '$(srcdir)/'`pcre_study.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_study.Tpo $(DEPDIR)/libpcre_la-pcre_study.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_study.c' object='libpcre_la-pcre_study.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_study.lo `test -f 'pcre_study.c' || echo '$(srcdir)/'`pcre_study.c

libpcre_la-pcre_tables.lo: pcre_tables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_tables.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_tables.Tpo -c -o libpcre_la-pcre_tables.lo `test -f 'pcre_tables.c' || echo '$(srcdir)/'`pcre_tables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_tables.Tpo $(DEPDIR)/libpcre_la-pcre_tables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_tables.c' object='libpcre_la-pcre_tables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_tables.lo `test -f 'pcre_tables.c' || echo '$(srcdir)/'`pcre_tables.c

libpcre_la-pcre_ucd.lo: pcre_ucd.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_ucd.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_ucd.Tpo -c -o libpcre_la-pcre_ucd.lo `test -f 'pcre_ucd.c' || echo '$(srcdir)/'`pcre_ucd.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_ucd.Tpo $(DEPDIR)/libpcre_la-pcre_ucd.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_ucd.c' object='libpcre_la-pcre_ucd.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_ucd.lo `test -f 'pcre_ucd.c' || echo '$(srcdir)/'`pcre_ucd.c

libpcre_la-pcre_valid_utf8.lo: pcre_valid_utf8.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_valid_utf8.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_valid_utf8.Tpo -c -o libpcre_la-pcre_valid_utf8.lo `test -f 'pcre_valid_utf8.c' || echo '$(srcdir)/'`pcre_valid_utf8.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_valid_utf8.Tpo $(DEPDIR)/libpcre_la-pcre_valid_utf8.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_valid_utf8.c' object='libpcre_la-pcre_valid_utf8.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_valid_utf8.lo `test -f 'pcre_valid_utf8.c' || echo '$(srcdir)/'`pcre_valid_utf8.c

libpcre_la-pcre_version.lo: pcre_version.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_version.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_version.Tpo -c -o libpcre_la-pcre_version.lo `test -f 'pcre_version.c' || echo '$(srcdir)/'`pcre_version.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_version.Tpo $(DEPDIR)/libpcre_la-pcre_version.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_version.c' object='libpcre_la-pcre_version.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_version.lo `test -f 'pcre_version.c' || echo '$(srcdir)/'`pcre_version.c

libpcre_la-pcre_xclass.lo: pcre_xclass.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_xclass.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_xclass.Tpo -c -o libpcre_la-pcre_xclass.lo `test -f 'pcre_xclass.c' || echo '$(srcdir)/'`pcre_xclass.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_xclass.Tpo $(DEPDIR)/libpcre_la-pcre_xclass.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_xclass.c' object='libpcre_la-pcre_xclass.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_xclass.lo `test -f 'pcre_xclass.c' || echo '$(srcdir)/'`pcre_xclass.c

libpcre_la-pcre_chartables.lo: pcre_chartables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -MT libpcre_la-pcre_chartables.lo -MD -MP -MF $(DEPDIR)/libpcre_la-pcre_chartables.Tpo -c -o libpcre_la-pcre_chartables.lo `test -f 'pcre_chartables.c' || echo '$(srcdir)/'`pcre_chartables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre_la-pcre_chartables.Tpo $(DEPDIR)/libpcre_la-pcre_chartables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_chartables.c' object='libpcre_la-pcre_chartables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre_la_CFLAGS) $(CFLAGS) -c -o libpcre_la-pcre_chartables.lo `test -f 'pcre_chartables.c' || echo '$(srcdir)/'`pcre_chartables.c

libpcre16_la-pcre16_byte_order.lo: pcre16_byte_order.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_byte_order.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_byte_order.Tpo -c -o libpcre16_la-pcre16_byte_order.lo `test -f 'pcre16_byte_order.c' || echo '$(srcdir)/'`pcre16_byte_order.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_byte_order.Tpo $(DEPDIR)/libpcre16_la-pcre16_byte_order.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_byte_order.c' object='libpcre16_la-pcre16_byte_order.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_byte_order.lo `test -f 'pcre16_byte_order.c' || echo '$(srcdir)/'`pcre16_byte_order.c

libpcre16_la-pcre16_chartables.lo: pcre16_chartables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_chartables.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_chartables.Tpo -c -o libpcre16_la-pcre16_chartables.lo `test -f 'pcre16_chartables.c' || echo '$(srcdir)/'`pcre16_chartables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_chartables.Tpo $(DEPDIR)/libpcre16_la-pcre16_chartables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_chartables.c' object='libpcre16_la-pcre16_chartables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_chartables.lo `test -f 'pcre16_chartables.c' || echo '$(srcdir)/'`pcre16_chartables.c

libpcre16_la-pcre16_compile.lo: pcre16_compile.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_compile.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_compile.Tpo -c -o libpcre16_la-pcre16_compile.lo `test -f 'pcre16_compile.c' || echo '$(srcdir)/'`pcre16_compile.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_compile.Tpo $(DEPDIR)/libpcre16_la-pcre16_compile.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_compile.c' object='libpcre16_la-pcre16_compile.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_compile.lo `test -f 'pcre16_compile.c' || echo '$(srcdir)/'`pcre16_compile.c

libpcre16_la-pcre16_config.lo: pcre16_config.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_config.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_config.Tpo -c -o libpcre16_la-pcre16_config.lo `test -f 'pcre16_config.c' || echo '$(srcdir)/'`pcre16_config.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_config.Tpo $(DEPDIR)/libpcre16_la-pcre16_config.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_config.c' object='libpcre16_la-pcre16_config.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_config.lo `test -f 'pcre16_config.c' || echo '$(srcdir)/'`pcre16_config.c

libpcre16_la-pcre16_dfa_exec.lo: pcre16_dfa_exec.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_dfa_exec.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_dfa_exec.Tpo -c -o libpcre16_la-pcre16_dfa_exec.lo `test -f 'pcre16_dfa_exec.c' || echo '$(srcdir)/'`pcre16_dfa_exec.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_dfa_exec.Tpo $(DEPDIR)/libpcre16_la-pcre16_dfa_exec.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_dfa_exec.c' object='libpcre16_la-pcre16_dfa_exec.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_dfa_exec.lo `test -f 'pcre16_dfa_exec.c' || echo '$(srcdir)/'`pcre16_dfa_exec.c

libpcre16_la-pcre16_exec.lo: pcre16_exec.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_exec.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_exec.Tpo -c -o libpcre16_la-pcre16_exec.lo `test -f 'pcre16_exec.c' || echo '$(srcdir)/'`pcre16_exec.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_exec.Tpo $(DEPDIR)/libpcre16_la-pcre16_exec.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_exec.c' object='libpcre16_la-pcre16_exec.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_exec.lo `test -f 'pcre16_exec.c' || echo '$(srcdir)/'`pcre16_exec.c

libpcre16_la-pcre16_fullinfo.lo: pcre16_fullinfo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_fullinfo.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_fullinfo.Tpo -c -o libpcre16_la-pcre16_fullinfo.lo `test -f 'pcre16_fullinfo.c' || echo '$(srcdir)/'`pcre16_fullinfo.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_fullinfo.Tpo $(DEPDIR)/libpcre16_la-pcre16_fullinfo.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_fullinfo.c' object='libpcre16_la-pcre16_fullinfo.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_fullinfo.lo `test -f 'pcre16_fullinfo.c' || echo '$(srcdir)/'`pcre16_fullinfo.c

libpcre16_la-pcre16_get.lo: pcre16_get.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_get.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_get.Tpo -c -o libpcre16_la-pcre16_get.lo `test -f 'pcre16_get.c' || echo '$(srcdir)/'`pcre16_get.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_get.Tpo $(DEPDIR)/libpcre16_la-pcre16_get.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_get.c' object='libpcre16_la-pcre16_get.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_get.lo `test -f 'pcre16_get.c' || echo '$(srcdir)/'`pcre16_get.c

libpcre16_la-pcre16_globals.lo: pcre16_globals.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_globals.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_globals.Tpo -c -o libpcre16_la-pcre16_globals.lo `test -f 'pcre16_globals.c' || echo '$(srcdir)/'`pcre16_globals.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_globals.Tpo $(DEPDIR)/libpcre16_la-pcre16_globals.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_globals.c' object='libpcre16_la-pcre16_globals.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_globals.lo `test -f 'pcre16_globals.c' || echo '$(srcdir)/'`pcre16_globals.c

libpcre16_la-pcre16_jit_compile.lo: pcre16_jit_compile.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_jit_compile.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_jit_compile.Tpo -c -o libpcre16_la-pcre16_jit_compile.lo `test -f 'pcre16_jit_compile.c' || echo '$(srcdir)/'`pcre16_jit_compile.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_jit_compile.Tpo $(DEPDIR)/libpcre16_la-pcre16_jit_compile.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_jit_compile.c' object='libpcre16_la-pcre16_jit_compile.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_jit_compile.lo `test -f 'pcre16_jit_compile.c' || echo '$(srcdir)/'`pcre16_jit_compile.c

libpcre16_la-pcre16_maketables.lo: pcre16_maketables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_maketables.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_maketables.Tpo -c -o libpcre16_la-pcre16_maketables.lo `test -f 'pcre16_maketables.c' || echo '$(srcdir)/'`pcre16_maketables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_maketables.Tpo $(DEPDIR)/libpcre16_la-pcre16_maketables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_maketables.c' object='libpcre16_la-pcre16_maketables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_maketables.lo `test -f 'pcre16_maketables.c' || echo '$(srcdir)/'`pcre16_maketables.c

libpcre16_la-pcre16_newline.lo: pcre16_newline.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_newline.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_newline.Tpo -c -o libpcre16_la-pcre16_newline.lo `test -f 'pcre16_newline.c' || echo '$(srcdir)/'`pcre16_newline.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_newline.Tpo $(DEPDIR)/libpcre16_la-pcre16_newline.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_newline.c' object='libpcre16_la-pcre16_newline.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_newline.lo `test -f 'pcre16_newline.c' || echo '$(srcdir)/'`pcre16_newline.c

libpcre16_la-pcre16_ord2utf16.lo: pcre16_ord2utf16.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_ord2utf16.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_ord2utf16.Tpo -c -o libpcre16_la-pcre16_ord2utf16.lo `test -f 'pcre16_ord2utf16.c' || echo '$(srcdir)/'`pcre16_ord2utf16.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_ord2utf16.Tpo $(DEPDIR)/libpcre16_la-pcre16_ord2utf16.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_ord2utf16.c' object='libpcre16_la-pcre16_ord2utf16.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_ord2utf16.lo `test -f 'pcre16_ord2utf16.c' || echo '$(srcdir)/'`pcre16_ord2utf16.c

libpcre16_la-pcre16_refcount.lo: pcre16_refcount.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_refcount.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_refcount.Tpo -c -o libpcre16_la-pcre16_refcount.lo `test -f 'pcre16_refcount.c' || echo '$(srcdir)/'`pcre16_refcount.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_refcount.Tpo $(DEPDIR)/libpcre16_la-pcre16_refcount.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_refcount.c' object='libpcre16_la-pcre16_refcount.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_refcount.lo `test -f 'pcre16_refcount.c' || echo '$(srcdir)/'`pcre16_refcount.c

libpcre16_la-pcre16_string_utils.lo: pcre16_string_utils.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_string_utils.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_string_utils.Tpo -c -o libpcre16_la-pcre16_string_utils.lo `test -f 'pcre16_string_utils.c' || echo '$(srcdir)/'`pcre16_string_utils.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_string_utils.Tpo $(DEPDIR)/libpcre16_la-pcre16_string_utils.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_string_utils.c' object='libpcre16_la-pcre16_string_utils.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_string_utils.lo `test -f 'pcre16_string_utils.c' || echo '$(srcdir)/'`pcre16_string_utils.c

libpcre16_la-pcre16_study.lo: pcre16_study.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_study.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_study.Tpo -c -o libpcre16_la-pcre16_study.lo `test -f 'pcre16_study.c' || echo '$(srcdir)/'`pcre16_study.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_study.Tpo $(DEPDIR)/libpcre16_la-pcre16_study.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_study.c' object='libpcre16_la-pcre16_study.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_study.lo `test -f 'pcre16_study.c' || echo '$(srcdir)/'`pcre16_study.c

libpcre16_la-pcre16_tables.lo: pcre16_tables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_tables.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_tables.Tpo -c -o libpcre16_la-pcre16_tables.lo `test -f 'pcre16_tables.c' || echo '$(srcdir)/'`pcre16_tables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_tables.Tpo $(DEPDIR)/libpcre16_la-pcre16_tables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_tables.c' object='libpcre16_la-pcre16_tables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_tables.lo `test -f 'pcre16_tables.c' || echo '$(srcdir)/'`pcre16_tables.c

libpcre16_la-pcre16_ucd.lo: pcre16_ucd.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_ucd.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_ucd.Tpo -c -o libpcre16_la-pcre16_ucd.lo `test -f 'pcre16_ucd.c' || echo '$(srcdir)/'`pcre16_ucd.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_ucd.Tpo $(DEPDIR)/libpcre16_la-pcre16_ucd.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_ucd.c' object='libpcre16_la-pcre16_ucd.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_ucd.lo `test -f 'pcre16_ucd.c' || echo '$(srcdir)/'`pcre16_ucd.c

libpcre16_la-pcre16_utf16_utils.lo: pcre16_utf16_utils.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_utf16_utils.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_utf16_utils.Tpo -c -o libpcre16_la-pcre16_utf16_utils.lo `test -f 'pcre16_utf16_utils.c' || echo '$(srcdir)/'`pcre16_utf16_utils.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_utf16_utils.Tpo $(DEPDIR)/libpcre16_la-pcre16_utf16_utils.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_utf16_utils.c' object='libpcre16_la-pcre16_utf16_utils.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_utf16_utils.lo `test -f 'pcre16_utf16_utils.c' || echo '$(srcdir)/'`pcre16_utf16_utils.c

libpcre16_la-pcre16_valid_utf16.lo: pcre16_valid_utf16.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_valid_utf16.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_valid_utf16.Tpo -c -o libpcre16_la-pcre16_valid_utf16.lo `test -f 'pcre16_valid_utf16.c' || echo '$(srcdir)/'`pcre16_valid_utf16.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_valid_utf16.Tpo $(DEPDIR)/libpcre16_la-pcre16_valid_utf16.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_valid_utf16.c' object='libpcre16_la-pcre16_valid_utf16.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_valid_utf16.lo `test -f 'pcre16_valid_utf16.c' || echo '$(srcdir)/'`pcre16_valid_utf16.c

libpcre16_la-pcre16_version.lo: pcre16_version.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_version.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_version.Tpo -c -o libpcre16_la-pcre16_version.lo `test -f 'pcre16_version.c' || echo '$(srcdir)/'`pcre16_version.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_version.Tpo $(DEPDIR)/libpcre16_la-pcre16_version.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_version.c' object='libpcre16_la-pcre16_version.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_version.lo `test -f 'pcre16_version.c' || echo '$(srcdir)/'`pcre16_version.c

libpcre16_la-pcre16_xclass.lo: pcre16_xclass.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre16_xclass.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre16_xclass.Tpo -c -o libpcre16_la-pcre16_xclass.lo `test -f 'pcre16_xclass.c' || echo '$(srcdir)/'`pcre16_xclass.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre16_xclass.Tpo $(DEPDIR)/libpcre16_la-pcre16_xclass.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_xclass.c' object='libpcre16_la-pcre16_xclass.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre16_xclass.lo `test -f 'pcre16_xclass.c' || echo '$(srcdir)/'`pcre16_xclass.c

libpcre16_la-pcre_chartables.lo: pcre_chartables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -MT libpcre16_la-pcre_chartables.lo -MD -MP -MF $(DEPDIR)/libpcre16_la-pcre_chartables.Tpo -c -o libpcre16_la-pcre_chartables.lo `test -f 'pcre_chartables.c' || echo '$(srcdir)/'`pcre_chartables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre16_la-pcre_chartables.Tpo $(DEPDIR)/libpcre16_la-pcre_chartables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_chartables.c' object='libpcre16_la-pcre_chartables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre16_la_CFLAGS) $(CFLAGS) -c -o libpcre16_la-pcre_chartables.lo `test -f 'pcre_chartables.c' || echo '$(srcdir)/'`pcre_chartables.c

libpcre32_la-pcre32_byte_order.lo: pcre32_byte_order.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_byte_order.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_byte_order.Tpo -c -o libpcre32_la-pcre32_byte_order.lo `test -f 'pcre32_byte_order.c' || echo '$(srcdir)/'`pcre32_byte_order.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_byte_order.Tpo $(DEPDIR)/libpcre32_la-pcre32_byte_order.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_byte_order.c' object='libpcre32_la-pcre32_byte_order.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_byte_order.lo `test -f 'pcre32_byte_order.c' || echo '$(srcdir)/'`pcre32_byte_order.c

libpcre32_la-pcre32_chartables.lo: pcre32_chartables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_chartables.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_chartables.Tpo -c -o libpcre32_la-pcre32_chartables.lo `test -f 'pcre32_chartables.c' || echo '$(srcdir)/'`pcre32_chartables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_chartables.Tpo $(DEPDIR)/libpcre32_la-pcre32_chartables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_chartables.c' object='libpcre32_la-pcre32_chartables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_chartables.lo `test -f 'pcre32_chartables.c' || echo '$(srcdir)/'`pcre32_chartables.c

libpcre32_la-pcre32_compile.lo: pcre32_compile.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_compile.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_compile.Tpo -c -o libpcre32_la-pcre32_compile.lo `test -f 'pcre32_compile.c' || echo '$(srcdir)/'`pcre32_compile.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_compile.Tpo $(DEPDIR)/libpcre32_la-pcre32_compile.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_compile.c' object='libpcre32_la-pcre32_compile.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_compile.lo `test -f 'pcre32_compile.c' || echo '$(srcdir)/'`pcre32_compile.c

libpcre32_la-pcre32_config.lo: pcre32_config.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_config.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_config.Tpo -c -o libpcre32_la-pcre32_config.lo `test -f 'pcre32_config.c' || echo '$(srcdir)/'`pcre32_config.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_config.Tpo $(DEPDIR)/libpcre32_la-pcre32_config.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_config.c' object='libpcre32_la-pcre32_config.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_config.lo `test -f 'pcre32_config.c' || echo '$(srcdir)/'`pcre32_config.c

libpcre32_la-pcre32_dfa_exec.lo: pcre32_dfa_exec.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_dfa_exec.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_dfa_exec.Tpo -c -o libpcre32_la-pcre32_dfa_exec.lo `test -f 'pcre32_dfa_exec.c' || echo '$(srcdir)/'`pcre32_dfa_exec.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_dfa_exec.Tpo $(DEPDIR)/libpcre32_la-pcre32_dfa_exec.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_dfa_exec.c' object='libpcre32_la-pcre32_dfa_exec.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_dfa_exec.lo `test -f 'pcre32_dfa_exec.c' || echo '$(srcdir)/'`pcre32_dfa_exec.c

libpcre32_la-pcre32_exec.lo: pcre32_exec.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_exec.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_exec.Tpo -c -o libpcre32_la-pcre32_exec.lo `test -f 'pcre32_exec.c' || echo '$(srcdir)/'`pcre32_exec.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_exec.Tpo $(DEPDIR)/libpcre32_la-pcre32_exec.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_exec.c' object='libpcre32_la-pcre32_exec.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_exec.lo `test -f 'pcre32_exec.c' || echo '$(srcdir)/'`pcre32_exec.c

libpcre32_la-pcre32_fullinfo.lo: pcre32_fullinfo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_fullinfo.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_fullinfo.Tpo -c -o libpcre32_la-pcre32_fullinfo.lo `test -f 'pcre32_fullinfo.c' || echo '$(srcdir)/'`pcre32_fullinfo.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_fullinfo.Tpo $(DEPDIR)/libpcre32_la-pcre32_fullinfo.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_fullinfo.c' object='libpcre32_la-pcre32_fullinfo.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_fullinfo.lo `test -f 'pcre32_fullinfo.c' || echo '$(srcdir)/'`pcre32_fullinfo.c

libpcre32_la-pcre32_get.lo: pcre32_get.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_get.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_get.Tpo -c -o libpcre32_la-pcre32_get.lo `test -f 'pcre32_get.c' || echo '$(srcdir)/'`pcre32_get.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_get.Tpo $(DEPDIR)/libpcre32_la-pcre32_get.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_get.c' object='libpcre32_la-pcre32_get.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_get.lo `test -f 'pcre32_get.c' || echo '$(srcdir)/'`pcre32_get.c

libpcre32_la-pcre32_globals.lo: pcre32_globals.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_globals.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_globals.Tpo -c -o libpcre32_la-pcre32_globals.lo `test -f 'pcre32_globals.c' || echo '$(srcdir)/'`pcre32_globals.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_globals.Tpo $(DEPDIR)/libpcre32_la-pcre32_globals.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_globals.c' object='libpcre32_la-pcre32_globals.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_globals.lo `test -f 'pcre32_globals.c' || echo '$(srcdir)/'`pcre32_globals.c

libpcre32_la-pcre32_jit_compile.lo: pcre32_jit_compile.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_jit_compile.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_jit_compile.Tpo -c -o libpcre32_la-pcre32_jit_compile.lo `test -f 'pcre32_jit_compile.c' || echo '$(srcdir)/'`pcre32_jit_compile.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_jit_compile.Tpo $(DEPDIR)/libpcre32_la-pcre32_jit_compile.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_jit_compile.c' object='libpcre32_la-pcre32_jit_compile.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_jit_compile.lo `test -f 'pcre32_jit_compile.c' || echo '$(srcdir)/'`pcre32_jit_compile.c

libpcre32_la-pcre32_maketables.lo: pcre32_maketables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_maketables.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_maketables.Tpo -c -o libpcre32_la-pcre32_maketables.lo `test -f 'pcre32_maketables.c' || echo '$(srcdir)/'`pcre32_maketables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_maketables.Tpo $(DEPDIR)/libpcre32_la-pcre32_maketables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_maketables.c' object='libpcre32_la-pcre32_maketables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_maketables.lo `test -f 'pcre32_maketables.c' || echo '$(srcdir)/'`pcre32_maketables.c

libpcre32_la-pcre32_newline.lo: pcre32_newline.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_newline.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_newline.Tpo -c -o libpcre32_la-pcre32_newline.lo `test -f 'pcre32_newline.c' || echo '$(srcdir)/'`pcre32_newline.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_newline.Tpo $(DEPDIR)/libpcre32_la-pcre32_newline.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_newline.c' object='libpcre32_la-pcre32_newline.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_newline.lo `test -f 'pcre32_newline.c' || echo '$(srcdir)/'`pcre32_newline.c

libpcre32_la-pcre32_ord2utf32.lo: pcre32_ord2utf32.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_ord2utf32.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_ord2utf32.Tpo -c -o libpcre32_la-pcre32_ord2utf32.lo `test -f 'pcre32_ord2utf32.c' || echo '$(srcdir)/'`pcre32_ord2utf32.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_ord2utf32.Tpo $(DEPDIR)/libpcre32_la-pcre32_ord2utf32.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_ord2utf32.c' object='libpcre32_la-pcre32_ord2utf32.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_ord2utf32.lo `test -f 'pcre32_ord2utf32.c' || echo '$(srcdir)/'`pcre32_ord2utf32.c

libpcre32_la-pcre32_refcount.lo: pcre32_refcount.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_refcount.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_refcount.Tpo -c -o libpcre32_la-pcre32_refcount.lo `test -f 'pcre32_refcount.c' || echo '$(srcdir)/'`pcre32_refcount.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_refcount.Tpo $(DEPDIR)/libpcre32_la-pcre32_refcount.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_refcount.c' object='libpcre32_la-pcre32_refcount.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_refcount.lo `test -f 'pcre32_refcount.c' || echo '$(srcdir)/'`pcre32_refcount.c

libpcre32_la-pcre32_string_utils.lo: pcre32_string_utils.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_string_utils.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_string_utils.Tpo -c -o libpcre32_la-pcre32_string_utils.lo `test -f 'pcre32_string_utils.c' || echo '$(srcdir)/'`pcre32_string_utils.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_string_utils.Tpo $(DEPDIR)/libpcre32_la-pcre32_string_utils.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_string_utils.c' object='libpcre32_la-pcre32_string_utils.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_string_utils.lo `test -f 'pcre32_string_utils.c' || echo '$(srcdir)/'`pcre32_string_utils.c

libpcre32_la-pcre32_study.lo: pcre32_study.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_study.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_study.Tpo -c -o libpcre32_la-pcre32_study.lo `test -f 'pcre32_study.c' || echo '$(srcdir)/'`pcre32_study.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_study.Tpo $(DEPDIR)/libpcre32_la-pcre32_study.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_study.c' object='libpcre32_la-pcre32_study.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_study.lo `test -f 'pcre32_study.c' || echo '$(srcdir)/'`pcre32_study.c

libpcre32_la-pcre32_tables.lo: pcre32_tables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_tables.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_tables.Tpo -c -o libpcre32_la-pcre32_tables.lo `test -f 'pcre32_tables.c' || echo '$(srcdir)/'`pcre32_tables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_tables.Tpo $(DEPDIR)/libpcre32_la-pcre32_tables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_tables.c' object='libpcre32_la-pcre32_tables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_tables.lo `test -f 'pcre32_tables.c' || echo '$(srcdir)/'`pcre32_tables.c

libpcre32_la-pcre32_ucd.lo: pcre32_ucd.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_ucd.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_ucd.Tpo -c -o libpcre32_la-pcre32_ucd.lo `test -f 'pcre32_ucd.c' || echo '$(srcdir)/'`pcre32_ucd.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_ucd.Tpo $(DEPDIR)/libpcre32_la-pcre32_ucd.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_ucd.c' object='libpcre32_la-pcre32_ucd.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_ucd.lo `test -f 'pcre32_ucd.c' || echo '$(srcdir)/'`pcre32_ucd.c

libpcre32_la-pcre32_utf32_utils.lo: pcre32_utf32_utils.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_utf32_utils.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_utf32_utils.Tpo -c -o libpcre32_la-pcre32_utf32_utils.lo `test -f 'pcre32_utf32_utils.c' || echo '$(srcdir)/'`pcre32_utf32_utils.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_utf32_utils.Tpo $(DEPDIR)/libpcre32_la-pcre32_utf32_utils.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_utf32_utils.c' object='libpcre32_la-pcre32_utf32_utils.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_utf32_utils.lo `test -f 'pcre32_utf32_utils.c' || echo '$(srcdir)/'`pcre32_utf32_utils.c

libpcre32_la-pcre32_valid_utf32.lo: pcre32_valid_utf32.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_valid_utf32.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_valid_utf32.Tpo -c -o libpcre32_la-pcre32_valid_utf32.lo `test -f 'pcre32_valid_utf32.c' || echo '$(srcdir)/'`pcre32_valid_utf32.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_valid_utf32.Tpo $(DEPDIR)/libpcre32_la-pcre32_valid_utf32.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_valid_utf32.c' object='libpcre32_la-pcre32_valid_utf32.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_valid_utf32.lo `test -f 'pcre32_valid_utf32.c' || echo '$(srcdir)/'`pcre32_valid_utf32.c

libpcre32_la-pcre32_version.lo: pcre32_version.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_version.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_version.Tpo -c -o libpcre32_la-pcre32_version.lo `test -f 'pcre32_version.c' || echo '$(srcdir)/'`pcre32_version.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_version.Tpo $(DEPDIR)/libpcre32_la-pcre32_version.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_version.c' object='libpcre32_la-pcre32_version.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_version.lo `test -f 'pcre32_version.c' || echo '$(srcdir)/'`pcre32_version.c

libpcre32_la-pcre32_xclass.lo: pcre32_xclass.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre32_xclass.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre32_xclass.Tpo -c -o libpcre32_la-pcre32_xclass.lo `test -f 'pcre32_xclass.c' || echo '$(srcdir)/'`pcre32_xclass.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre32_xclass.Tpo $(DEPDIR)/libpcre32_la-pcre32_xclass.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_xclass.c' object='libpcre32_la-pcre32_xclass.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre32_xclass.lo `test -f 'pcre32_xclass.c' || echo '$(srcdir)/'`pcre32_xclass.c

libpcre32_la-pcre_chartables.lo: pcre_chartables.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -MT libpcre32_la-pcre_chartables.lo -MD -MP -MF $(DEPDIR)/libpcre32_la-pcre_chartables.Tpo -c -o libpcre32_la-pcre_chartables.lo `test -f 'pcre_chartables.c' || echo '$(srcdir)/'`pcre_chartables.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcre32_la-pcre_chartables.Tpo $(DEPDIR)/libpcre32_la-pcre_chartables.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_chartables.c' object='libpcre32_la-pcre_chartables.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcre32_la_CFLAGS) $(CFLAGS) -c -o libpcre32_la-pcre_chartables.lo `test -f 'pcre_chartables.c' || echo '$(srcdir)/'`pcre_chartables.c

libpcreposix_la-pcreposix.lo: pcreposix.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcreposix_la_CFLAGS) $(CFLAGS) -MT libpcreposix_la-pcreposix.lo -MD -MP -MF $(DEPDIR)/libpcreposix_la-pcreposix.Tpo -c -o libpcreposix_la-pcreposix.lo `test -f 'pcreposix.c' || echo '$(srcdir)/'`pcreposix.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcreposix_la-pcreposix.Tpo $(DEPDIR)/libpcreposix_la-pcreposix.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcreposix.c' object='libpcreposix_la-pcreposix.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcreposix_la_CFLAGS) $(CFLAGS) -c -o libpcreposix_la-pcreposix.lo `test -f 'pcreposix.c' || echo '$(srcdir)/'`pcreposix.c

pcre_jit_test-pcre_jit_test.o: pcre_jit_test.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_jit_test_CFLAGS) $(CFLAGS) -MT pcre_jit_test-pcre_jit_test.o -MD -MP -MF $(DEPDIR)/pcre_jit_test-pcre_jit_test.Tpo -c -o pcre_jit_test-pcre_jit_test.o `test -f 'pcre_jit_test.c' || echo '$(srcdir)/'`pcre_jit_test.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcre_jit_test-pcre_jit_test.Tpo $(DEPDIR)/pcre_jit_test-pcre_jit_test.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_jit_test.c' object='pcre_jit_test-pcre_jit_test.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_jit_test_CFLAGS) $(CFLAGS) -c -o pcre_jit_test-pcre_jit_test.o `test -f 'pcre_jit_test.c' || echo '$(srcdir)/'`pcre_jit_test.c

pcre_jit_test-pcre_jit_test.obj: pcre_jit_test.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_jit_test_CFLAGS) $(CFLAGS) -MT pcre_jit_test-pcre_jit_test.obj -MD -MP -MF $(DEPDIR)/pcre_jit_test-pcre_jit_test.Tpo -c -o pcre_jit_test-pcre_jit_test.obj `if test -f 'pcre_jit_test.c'; then $(CYGPATH_W) 'pcre_jit_test.c'; else $(CYGPATH_W) '$(srcdir)/pcre_jit_test.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcre_jit_test-pcre_jit_test.Tpo $(DEPDIR)/pcre_jit_test-pcre_jit_test.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_jit_test.c' object='pcre_jit_test-pcre_jit_test.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_jit_test_CFLAGS) $(CFLAGS) -c -o pcre_jit_test-pcre_jit_test.obj `if test -f 'pcre_jit_test.c'; then $(CYGPATH_W) 'pcre_jit_test.c'; else $(CYGPATH_W) '$(srcdir)/pcre_jit_test.c'; fi`

pcregrep-pcregrep.o: pcregrep.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcregrep_CFLAGS) $(CFLAGS) -MT pcregrep-pcregrep.o -MD -MP -MF $(DEPDIR)/pcregrep-pcregrep.Tpo -c -o pcregrep-pcregrep.o `test -f 'pcregrep.c' || echo '$(srcdir)/'`pcregrep.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcregrep-pcregrep.Tpo $(DEPDIR)/pcregrep-pcregrep.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcregrep.c' object='pcregrep-pcregrep.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcregrep_CFLAGS) $(CFLAGS) -c -o pcregrep-pcregrep.o `test -f 'pcregrep.c' || echo '$(srcdir)/'`pcregrep.c

pcregrep-pcregrep.obj: pcregrep.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcregrep_CFLAGS) $(CFLAGS) -MT pcregrep-pcregrep.obj -MD -MP -MF $(DEPDIR)/pcregrep-pcregrep.Tpo -c -o pcregrep-pcregrep.obj `if test -f 'pcregrep.c'; then $(CYGPATH_W) 'pcregrep.c'; else $(CYGPATH_W) '$(srcdir)/pcregrep.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcregrep-pcregrep.Tpo $(DEPDIR)/pcregrep-pcregrep.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcregrep.c' object='pcregrep-pcregrep.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcregrep_CFLAGS) $(CFLAGS) -c -o pcregrep-pcregrep.obj `if test -f 'pcregrep.c'; then $(CYGPATH_W) 'pcregrep.c'; else $(CYGPATH_W) '$(srcdir)/pcregrep.c'; fi`

pcretest-pcretest.o: pcretest.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -MT pcretest-pcretest.o -MD -MP -MF $(DEPDIR)/pcretest-pcretest.Tpo -c -o pcretest-pcretest.o `test -f 'pcretest.c' || echo '$(srcdir)/'`pcretest.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcretest-pcretest.Tpo $(DEPDIR)/pcretest-pcretest.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcretest.c' object='pcretest-pcretest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -c -o pcretest-pcretest.o `test -f 'pcretest.c' || echo '$(srcdir)/'`pcretest.c

pcretest-pcretest.obj: pcretest.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -MT pcretest-pcretest.obj -MD -MP -MF $(DEPDIR)/pcretest-pcretest.Tpo -c -o pcretest-pcretest.obj `if test -f 'pcretest.c'; then $(CYGPATH_W) 'pcretest.c'; else $(CYGPATH_W) '$(srcdir)/pcretest.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcretest-pcretest.Tpo $(DEPDIR)/pcretest-pcretest.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcretest.c' object='pcretest-pcretest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -c -o pcretest-pcretest.obj `if test -f 'pcretest.c'; then $(CYGPATH_W) 'pcretest.c'; else $(CYGPATH_W) '$(srcdir)/pcretest.c'; fi`

pcretest-pcre_printint.o: pcre_printint.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -MT pcretest-pcre_printint.o -MD -MP -MF $(DEPDIR)/pcretest-pcre_printint.Tpo -c -o pcretest-pcre_printint.o `test -f 'pcre_printint.c' || echo '$(srcdir)/'`pcre_printint.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcretest-pcre_printint.Tpo $(DEPDIR)/pcretest-pcre_printint.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_printint.c' object='pcretest-pcre_printint.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -c -o pcretest-pcre_printint.o `test -f 'pcre_printint.c' || echo '$(srcdir)/'`pcre_printint.c

pcretest-pcre_printint.obj: pcre_printint.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -MT pcretest-pcre_printint.obj -MD -MP -MF $(DEPDIR)/pcretest-pcre_printint.Tpo -c -o pcretest-pcre_printint.obj `if test -f 'pcre_printint.c'; then $(CYGPATH_W) 'pcre_printint.c'; else $(CYGPATH_W) '$(srcdir)/pcre_printint.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcretest-pcre_printint.Tpo $(DEPDIR)/pcretest-pcre_printint.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre_printint.c' object='pcretest-pcre_printint.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -c -o pcretest-pcre_printint.obj `if test -f 'pcre_printint.c'; then $(CYGPATH_W) 'pcre_printint.c'; else $(CYGPATH_W) '$(srcdir)/pcre_printint.c'; fi`

pcretest-pcre16_printint.o: pcre16_printint.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -MT pcretest-pcre16_printint.o -MD -MP -MF $(DEPDIR)/pcretest-pcre16_printint.Tpo -c -o pcretest-pcre16_printint.o `test -f 'pcre16_printint.c' || echo '$(srcdir)/'`pcre16_printint.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcretest-pcre16_printint.Tpo $(DEPDIR)/pcretest-pcre16_printint.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_printint.c' object='pcretest-pcre16_printint.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -c -o pcretest-pcre16_printint.o `test -f 'pcre16_printint.c' || echo '$(srcdir)/'`pcre16_printint.c

pcretest-pcre16_printint.obj: pcre16_printint.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -MT pcretest-pcre16_printint.obj -MD -MP -MF $(DEPDIR)/pcretest-pcre16_printint.Tpo -c -o pcretest-pcre16_printint.obj `if test -f 'pcre16_printint.c'; then $(CYGPATH_W) 'pcre16_printint.c'; else $(CYGPATH_W) '$(srcdir)/pcre16_printint.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcretest-pcre16_printint.Tpo $(DEPDIR)/pcretest-pcre16_printint.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre16_printint.c' object='pcretest-pcre16_printint.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -c -o pcretest-pcre16_printint.obj `if test -f 'pcre16_printint.c'; then $(CYGPATH_W) 'pcre16_printint.c'; else $(CYGPATH_W) '$(srcdir)/pcre16_printint.c'; fi`

pcretest-pcre32_printint.o: pcre32_printint.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -MT pcretest-pcre32_printint.o -MD -MP -MF $(DEPDIR)/pcretest-pcre32_printint.Tpo -c -o pcretest-pcre32_printint.o `test -f 'pcre32_printint.c' || echo '$(srcdir)/'`pcre32_printint.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcretest-pcre32_printint.Tpo $(DEPDIR)/pcretest-pcre32_printint.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_printint.c' object='pcretest-pcre32_printint.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -c -o pcretest-pcre32_printint.o `test -f 'pcre32_printint.c' || echo '$(srcdir)/'`pcre32_printint.c

pcretest-pcre32_printint.obj: pcre32_printint.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -MT pcretest-pcre32_printint.obj -MD -MP -MF $(DEPDIR)/pcretest-pcre32_printint.Tpo -c -o pcretest-pcre32_printint.obj `if test -f 'pcre32_printint.c'; then $(CYGPATH_W) 'pcre32_printint.c'; else $(CYGPATH_W) '$(srcdir)/pcre32_printint.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcretest-pcre32_printint.Tpo $(DEPDIR)/pcretest-pcre32_printint.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pcre32_printint.c' object='pcretest-pcre32_printint.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcretest_CFLAGS) $(CFLAGS) -c -o pcretest-pcre32_printint.obj `if test -f 'pcre32_printint.c'; then $(CYGPATH_W) 'pcre32_printint.c'; else $(CYGPATH_W) '$(srcdir)/pcre32_printint.c'; fi`

.cc.o:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXXCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ $<

.cc.obj:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXXCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cc.lo:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LTCXXCOMPILE) -c -o $@ $<

libpcrecpp_la-pcrecpp.lo: pcrecpp.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcrecpp_la_CXXFLAGS) $(CXXFLAGS) -MT libpcrecpp_la-pcrecpp.lo -MD -MP -MF $(DEPDIR)/libpcrecpp_la-pcrecpp.Tpo -c -o libpcrecpp_la-pcrecpp.lo `test -f 'pcrecpp.cc' || echo '$(srcdir)/'`pcrecpp.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcrecpp_la-pcrecpp.Tpo $(DEPDIR)/libpcrecpp_la-pcrecpp.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='pcrecpp.cc' object='libpcrecpp_la-pcrecpp.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcrecpp_la_CXXFLAGS) $(CXXFLAGS) -c -o libpcrecpp_la-pcrecpp.lo `test -f 'pcrecpp.cc' || echo '$(srcdir)/'`pcrecpp.cc

libpcrecpp_la-pcre_scanner.lo: pcre_scanner.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcrecpp_la_CXXFLAGS) $(CXXFLAGS) -MT libpcrecpp_la-pcre_scanner.lo -MD -MP -MF $(DEPDIR)/libpcrecpp_la-pcre_scanner.Tpo -c -o libpcrecpp_la-pcre_scanner.lo `test -f 'pcre_scanner.cc' || echo '$(srcdir)/'`pcre_scanner.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcrecpp_la-pcre_scanner.Tpo $(DEPDIR)/libpcrecpp_la-pcre_scanner.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='pcre_scanner.cc' object='libpcrecpp_la-pcre_scanner.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcrecpp_la_CXXFLAGS) $(CXXFLAGS) -c -o libpcrecpp_la-pcre_scanner.lo `test -f 'pcre_scanner.cc' || echo '$(srcdir)/'`pcre_scanner.cc

libpcrecpp_la-pcre_stringpiece.lo: pcre_stringpiece.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcrecpp_la_CXXFLAGS) $(CXXFLAGS) -MT libpcrecpp_la-pcre_stringpiece.lo -MD -MP -MF $(DEPDIR)/libpcrecpp_la-pcre_stringpiece.Tpo -c -o libpcrecpp_la-pcre_stringpiece.lo `test -f 'pcre_stringpiece.cc' || echo '$(srcdir)/'`pcre_stringpiece.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libpcrecpp_la-pcre_stringpiece.Tpo $(DEPDIR)/libpcrecpp_la-pcre_stringpiece.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='pcre_stringpiece.cc' object='libpcrecpp_la-pcre_stringpiece.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpcrecpp_la_CXXFLAGS) $(CXXFLAGS) -c -o libpcrecpp_la-pcre_stringpiece.lo `test -f 'pcre_stringpiece.cc' || echo '$(srcdir)/'`pcre_stringpiece.cc

pcre_scanner_unittest-pcre_scanner_unittest.o: pcre_scanner_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_scanner_unittest_CXXFLAGS) $(CXXFLAGS) -MT pcre_scanner_unittest-pcre_scanner_unittest.o -MD -MP -MF $(DEPDIR)/pcre_scanner_unittest-pcre_scanner_unittest.Tpo -c -o pcre_scanner_unittest-pcre_scanner_unittest.o `test -f 'pcre_scanner_unittest.cc' || echo '$(srcdir)/'`pcre_scanner_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcre_scanner_unittest-pcre_scanner_unittest.Tpo $(DEPDIR)/pcre_scanner_unittest-pcre_scanner_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='pcre_scanner_unittest.cc' object='pcre_scanner_unittest-pcre_scanner_unittest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_scanner_unittest_CXXFLAGS) $(CXXFLAGS) -c -o pcre_scanner_unittest-pcre_scanner_unittest.o `test -f 'pcre_scanner_unittest.cc' || echo '$(srcdir)/'`pcre_scanner_unittest.cc

pcre_scanner_unittest-pcre_scanner_unittest.obj: pcre_scanner_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_scanner_unittest_CXXFLAGS) $(CXXFLAGS) -MT pcre_scanner_unittest-pcre_scanner_unittest.obj -MD -MP -MF $(DEPDIR)/pcre_scanner_unittest-pcre_scanner_unittest.Tpo -c -o pcre_scanner_unittest-pcre_scanner_unittest.obj `if test -f 'pcre_scanner_unittest.cc'; then $(CYGPATH_W) 'pcre_scanner_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/pcre_scanner_unittest.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcre_scanner_unittest-pcre_scanner_unittest.Tpo $(DEPDIR)/pcre_scanner_unittest-pcre_scanner_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='pcre_scanner_unittest.cc' object='pcre_scanner_unittest-pcre_scanner_unittest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_scanner_unittest_CXXFLAGS) $(CXXFLAGS) -c -o pcre_scanner_unittest-pcre_scanner_unittest.obj `if test -f 'pcre_scanner_unittest.cc'; then $(CYGPATH_W) 'pcre_scanner_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/pcre_scanner_unittest.cc'; fi`

pcre_stringpiece_unittest-pcre_stringpiece_unittest.o: pcre_stringpiece_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_stringpiece_unittest_CXXFLAGS) $(CXXFLAGS) -MT pcre_stringpiece_unittest-pcre_stringpiece_unittest.o -MD -MP -MF $(DEPDIR)/pcre_stringpiece_unittest-pcre_stringpiece_unittest.Tpo -c -o pcre_stringpiece_unittest-pcre_stringpiece_unittest.o `test -f 'pcre_stringpiece_unittest.cc' || echo '$(srcdir)/'`pcre_stringpiece_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcre_stringpiece_unittest-pcre_stringpiece_unittest.Tpo $(DEPDIR)/pcre_stringpiece_unittest-pcre_stringpiece_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='pcre_stringpiece_unittest.cc' object='pcre_stringpiece_unittest-pcre_stringpiece_unittest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_stringpiece_unittest_CXXFLAGS) $(CXXFLAGS) -c -o pcre_stringpiece_unittest-pcre_stringpiece_unittest.o `test -f 'pcre_stringpiece_unittest.cc' || echo '$(srcdir)/'`pcre_stringpiece_unittest.cc

pcre_stringpiece_unittest-pcre_stringpiece_unittest.obj: pcre_stringpiece_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_stringpiece_unittest_CXXFLAGS) $(CXXFLAGS) -MT pcre_stringpiece_unittest-pcre_stringpiece_unittest.obj -MD -MP -MF $(DEPDIR)/pcre_stringpiece_unittest-pcre_stringpiece_unittest.Tpo -c -o pcre_stringpiece_unittest-pcre_stringpiece_unittest.obj `if test -f 'pcre_stringpiece_unittest.cc'; then $(CYGPATH_W) 'pcre_stringpiece_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/pcre_stringpiece_unittest.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcre_stringpiece_unittest-pcre_stringpiece_unittest.Tpo $(DEPDIR)/pcre_stringpiece_unittest-pcre_stringpiece_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='pcre_stringpiece_unittest.cc' object='pcre_stringpiece_unittest-pcre_stringpiece_unittest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcre_stringpiece_unittest_CXXFLAGS) $(CXXFLAGS) -c -o pcre_stringpiece_unittest-pcre_stringpiece_unittest.obj `if test -f 'pcre_stringpiece_unittest.cc'; then $(CYGPATH_W) 'pcre_stringpiece_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/pcre_stringpiece_unittest.cc'; fi`

pcrecpp_unittest-pcrecpp_unittest.o: pcrecpp_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcrecpp_unittest_CXXFLAGS) $(CXXFLAGS) -MT pcrecpp_unittest-pcrecpp_unittest.o -MD -MP -MF $(DEPDIR)/pcrecpp_unittest-pcrecpp_unittest.Tpo -c -o pcrecpp_unittest-pcrecpp_unittest.o `test -f 'pcrecpp_unittest.cc' || echo '$(srcdir)/'`pcrecpp_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcrecpp_unittest-pcrecpp_unittest.Tpo $(DEPDIR)/pcrecpp_unittest-pcrecpp_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='pcrecpp_unittest.cc' object='pcrecpp_unittest-pcrecpp_unittest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcrecpp_unittest_CXXFLAGS) $(CXXFLAGS) -c -o pcrecpp_unittest-pcrecpp_unittest.o `test -f 'pcrecpp_unittest.cc' || echo '$(srcdir)/'`pcrecpp_unittest.cc

pcrecpp_unittest-pcrecpp_unittest.obj: pcrecpp_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcrecpp_unittest_CXXFLAGS) $(CXXFLAGS) -MT pcrecpp_unittest-pcrecpp_unittest.obj -MD -MP -MF $(DEPDIR)/pcrecpp_unittest-pcrecpp_unittest.Tpo -c -o pcrecpp_unittest-pcrecpp_unittest.obj `if test -f 'pcrecpp_unittest.cc'; then $(CYGPATH_W) 'pcrecpp_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/pcrecpp_unittest.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pcrecpp_unittest-pcrecpp_unittest.Tpo $(DEPDIR)/pcrecpp_unittest-pcrecpp_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='pcrecpp_unittest.cc' object='pcrecpp_unittest-pcrecpp_unittest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(pcrecpp_unittest_CXXFLAGS) $(CXXFLAGS) -c -o pcrecpp_unittest-pcrecpp_unittest.obj `if test -f 'pcrecpp_unittest.cc'; then $(CYGPATH_W) 'pcrecpp_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/pcrecpp_unittest.cc'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

distclean-libtool:
	-rm -f libtool config.lt
install-man1: $(dist_man_MANS) $(man_MANS)
	@$(NORMAL_INSTALL)
	@list1=''; \
	list2='$(dist_man_MANS) $(man_MANS)'; \
	test -n "$(man1dir)" \
	  && test -n "`echo $$list1$$list2`" \
	  || exit 0; \
	echo " $(MKDIR_P) '$(DESTDIR)$(man1dir)'"; \
	$(MKDIR_P) "$(DESTDIR)$(man1dir)" || exit 1; \
	{ for i in $$list1; do echo "$$i"; done;  \
	if test -n "$$list2"; then \
	  for i in $$list2; do echo "$$i"; done \
	    | sed -n '/\.1[a-z]*$$/p'; \
	fi; \
	} | while read p; do \
	  if test -f $$p; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; echo "$$p"; \
	done | \
	sed -e 'n;s,.*/,,;p;h;s,.*\.,,;s,^[^1][0-9a-z]*$$,1,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,' | \
	sed 'N;N;s,\n, ,g' | { \
	list=; while read file base inst; do \
	  if test "$$base" = "$$inst"; then list="$$list $$file"; else \
	    echo " $(INSTALL_DATA) '$$file' '$(DESTDIR)$(man1dir)/$$inst'"; \
	    $(INSTALL_DATA) "$$file" "$(DESTDIR)$(man1dir)/$$inst" || exit $$?; \
	  fi; \
	done; \
	for i in $$list; do echo "$$i"; done | $(am__base_list) | \
	while read files; do \
	  test -z "$$files" || { \
	    echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(man1dir)'"; \
	    $(INSTALL_DATA) $$files "$(DESTDIR)$(man1dir)" || exit $$?; }; \
	done; }

uninstall-man1:
	@$(NORMAL_UNINSTALL)
	@list=''; test -n "$(man1dir)" || exit 0; \
	files=`{ for i in $$list; do echo "$$i"; done; \
	l2='$(dist_man_MANS) $(man_MANS)'; for i in $$l2; do echo "$$i"; done | \
	  sed -n '/\.1[a-z]*$$/p'; \
	} | sed -e 's,.*/,,;h;s,.*\.,,;s,^[^1][0-9a-z]*$$,1,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,'`; \
	dir='$(DESTDIR)$(man1dir)'; $(am__uninstall_files_from_dir)
install-man3: $(dist_man_MANS) $(man_MANS)
	@$(NORMAL_INSTALL)
	@list1=''; \
	list2='$(dist_man_MANS) $(man_MANS)'; \
	test -n "$(man3dir)" \
	  && test -n "`echo $$list1$$list2`" \
	  || exit 0; \
	echo " $(MKDIR_P) '$(DESTDIR)$(man3dir)'"; \
	$(MKDIR_P) "$(DESTDIR)$(man3dir)" || exit 1; \
	{ for i in $$list1; do echo "$$i"; done;  \
	if test -n "$$list2"; then \
	  for i in $$list2; do echo "$$i"; done \
	    | sed -n '/\.3[a-z]*$$/p'; \
	fi; \
	} | while read p; do \
	  if test -f $$p; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; echo "$$p"; \
	done | \
	sed -e 'n;s,.*/,,;p;h;s,.*\.,,;s,^[^3][0-9a-z]*$$,3,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,' | \
	sed 'N;N;s,\n, ,g' | { \
	list=; while read file base inst; do \
	  if test "$$base" = "$$inst"; then list="$$list $$file"; else \
	    echo " $(INSTALL_DATA) '$$file' '$(DESTDIR)$(man3dir)/$$inst'"; \
	    $(INSTALL_DATA) "$$file" "$(DESTDIR)$(man3dir)/$$inst" || exit $$?; \
	  fi; \
	done; \
	for i in $$list; do echo "$$i"; done | $(am__base_list) | \
	while read files; do \
	  test -z "$$files" || { \
	    echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(man3dir)'"; \
	    $(INSTALL_DATA) $$files "$(DESTDIR)$(man3dir)" || exit $$?; }; \
	done; }

uninstall-man3:
	@$(NORMAL_UNINSTALL)
	@list=''; test -n "$(man3dir)" || exit 0; \
	files=`{ for i in $$list; do echo "$$i"; done; \
	l2='$(dist_man_MANS) $(man_MANS)'; for i in $$l2; do echo "$$i"; done | \
	  sed -n '/\.3[a-z]*$$/p'; \
	} | sed -e 's,.*/,,;h;s,.*\.,,;s,^[^3][0-9a-z]*$$,3,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,'`; \
	dir='$(DESTDIR)$(man3dir)'; $(am__uninstall_files_from_dir)
install-dist_docDATA: $(dist_doc_DATA)
	@$(NORMAL_INSTALL)
	@list='$(dist_doc_DATA)'; test -n "$(docdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(docdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(docdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(docdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(docdir)" || exit $$?; \
	done

uninstall-dist_docDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(dist_doc_DATA)'; test -n "$(docdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(docdir)'; $(am__uninstall_files_from_dir)
install-dist_htmlDATA: $(dist_html_DATA)
	@$(NORMAL_INSTALL)
	@list='$(dist_html_DATA)'; test -n "$(htmldir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(htmldir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(htmldir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(htmldir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(htmldir)" || exit $$?; \
	done

uninstall-dist_htmlDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(dist_html_DATA)'; test -n "$(htmldir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(htmldir)'; $(am__uninstall_files_from_dir)
install-htmlDATA: $(html_DATA)
	@$(NORMAL_INSTALL)
	@list='$(html_DATA)'; test -n "$(htmldir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(htmldir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(htmldir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(htmldir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(htmldir)" || exit $$?; \
	done

uninstall-htmlDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(html_DATA)'; test -n "$(htmldir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(htmldir)'; $(am__uninstall_files_from_dir)
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgconfigdir)" || exit $$?; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgconfigdir)'; $(am__uninstall_files_from_dir)
install-includeHEADERS: $(include_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(include_HEADERS)'; test -n "$(includedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(includedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(includedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(includedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(includedir)" || exit $$?; \
	done

uninstall-includeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(include_HEADERS)'; test -n "$(includedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(includedir)'; $(am__uninstall_files_from_dir)
install-nodist_includeHEADERS: $(nodist_include_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(nodist_include_HEADERS)'; test -n "$(includedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(includedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(includedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(includedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(includedir)" || exit $$?; \
	done

uninstall-nodist_includeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(nodist_include_HEADERS)'; test -n "$(includedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(includedir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscope: cscope.files
	test ! -s cscope.files \
	  || $(CSCOPE) -b -q $(AM_CSCOPEFLAGS) $(CSCOPEFLAGS) -i cscope.files $(CSCOPE_ARGS)
clean-cscope:
	-rm -f cscope.files
cscope.files: clean-cscope cscopelist
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
	-rm -f cscope.out cscope.in.out cscope.po.out cscope.files

# Recover from deleted '.trs' file; this should ensure that
# "rm -f foo.log; make foo.trs" re-run 'foo.test', and re-create
# both 'foo.log' and 'foo.trs'.  Break the recipe in two subshells
# to avoid problems with "make -n".
.log.trs:
	rm -f $< $@
	$(MAKE) $(AM_MAKEFLAGS) $<

# Leading 'am--fnord' is there to ensure the list of targets does not
# expand to empty, as could happen e.g. with make check TESTS=''.
am--fnord $(TEST_LOGS) $(TEST_LOGS:.log=.trs): $(am__force_recheck)
am--force-recheck:
	@:

$(TEST_SUITE_LOG): $(TEST_LOGS)
	@$(am__set_TESTS_bases); \
	am__f_ok () { test -f "$$1" && test -r "$$1"; }; \
	redo_bases=`for i in $$bases; do \
	              am__f_ok $$i.trs && am__f_ok $$i.log || echo $$i; \
	            done`; \
	if test -n "$$redo_bases"; then \
	  redo_logs=`for i in $$redo_bases; do echo $$i.log; done`; \
	  redo_results=`for i in $$redo_bases; do echo $$i.trs; done`; \
	  if $(am__make_dryrun); then :; else \
	    rm -f $$redo_logs && rm -f $$redo_results || exit 1; \
	  fi; \
	fi; \
	if test -n "$$am__remaking_logs"; then \
	  echo "fatal: making $(TEST_SUITE_LOG): possible infinite" \
	       "recursion detected" >&2; \
	elif test -n "$$redo_logs"; then \
	  am__remaking_logs=yes $(MAKE) $(AM_MAKEFLAGS) $$redo_logs; \
	fi; \
	if $(am__make_dryrun); then :; else \
	  st=0;  \
	  errmsg="fatal: making $(TEST_SUITE_LOG): failed to create"; \
	  for i in $$redo_bases; do \
	    test -f $$i.trs && test -r $$i.trs \
	      || { echo "$$errmsg $$i.trs" >&2; st=1; }; \
	    test -f $$i.log && test -r $$i.log \
	      || { echo "$$errmsg $$i.log" >&2; st=1; }; \
	  done; \
	  test $$st -eq 0 || exit 1; \
	fi
	@$(am__sh_e_setup); $(am__tty_colors); $(am__set_TESTS_bases); \
	ws='[ 	]'; \
	results=`for b in $$bases; do echo $$b.trs; done`; \
	test -n "$$results" || results=/dev/null; \
	all=`  grep "^$$ws*:test-result:"           $$results | wc -l`; \
	pass=` grep "^$$ws*:test-result:$$ws*PASS"  $$results | wc -l`; \
	fail=` grep "^$$ws*:test-result:$$ws*FAIL"  $$results | wc -l`; \
	skip=` grep "^$$ws*:test-result:$$ws*SKIP"  $$results | wc -l`; \
	xfail=`grep "^$$ws*:test-result:$$ws*XFAIL" $$results | wc -l`; \
	xpass=`grep "^$$ws*:test-result:$$ws*XPASS" $$results | wc -l`; \
	error=`grep "^$$ws*:test-result:$$ws*ERROR" $$results | wc -l`; \
	if test `expr $$fail + $$xpass + $$error` -eq 0; then \
	  success=true; \
	else \
	  success=false; \
	fi; \
	br='==================='; br=$$br$$br$$br$$br; \
	result_count () \
	{ \
	    if test x"$$1" = x"--maybe-color"; then \
	      maybe_colorize=yes; \
	    elif test x"$$1" = x"--no-color"; then \
	      maybe_colorize=no; \
	    else \
	      echo "$@: invalid 'result_count' usage" >&2; exit 4; \
	    fi; \
	    shift; \
	    desc=$$1 count=$$2; \
	    if test $$maybe_colorize = yes && test $$count -gt 0; then \
	      color_start=$$3 color_end=$$std; \
	    else \
	      color_start= color_end=; \
	    fi; \
	    echo "$${color_start}# $$desc $$count$${color_end}"; \
	}; \
	create_testsuite_report () \
	{ \
	  result_count $$1 "TOTAL:" $$all   "$$brg"; \
	  result_count $$1 "PASS: " $$pass  "$$grn"; \
	  result_count $$1 "SKIP: " $$skip  "$$blu"; \
	  result_count $$1 "XFAIL:" $$xfail "$$lgn"; \
	  result_count $$1 "FAIL: " $$fail  "$$red"; \
	  result_count $$1 "XPASS:" $$xpass "$$red"; \
	  result_count $$1 "ERROR:" $$error "$$mgn"; \
	}; \
	{								\
	  echo "$(PACKAGE_STRING): $(subdir)/$(TEST_SUITE_LOG)" |	\
	    $(am__rst_title);						\
	  create_testsuite_report --no-color;				\
	  echo;								\
	  echo ".. contents:: :depth: 2";				\
	  echo;								\
	  for b in $$bases; do echo $$b; done				\
	    | $(am__create_global_log);					\
	} >$(TEST_SUITE_LOG).tmp || exit 1;				\
	mv $(TEST_SUITE_LOG).tmp $(TEST_SUITE_LOG);			\
	if $$success; then						\
	  col="$$grn";							\
	 else								\
	  col="$$red";							\
	  test x"$$VERBOSE" = x || cat $(TEST_SUITE_LOG);		\
	fi;								\
	echo "$${col}$$br$${std}"; 					\
	echo "$${col}Testsuite summary for $(PACKAGE_STRING)$${std}";	\
	echo "$${col}$$br$${std}"; 					\
	create_testsuite_report --maybe-color;				\
	echo "$$col$$br$$std";						\
	if $$success; then :; else					\
	  echo "$${col}See $(subdir)/$(TEST_SUITE_LOG)$${std}";		\
	  if test -n "$(PACKAGE_BUGREPORT)"; then			\
	    echo "$${col}Please report to $(PACKAGE_BUGREPORT)$${std}";	\
	  fi;								\
	  echo "$$col$$br$$std";					\
	fi;								\
	$$success || exit 1

check-TESTS:
	@list='$(RECHECK_LOGS)';           test -z "$$list" || rm -f $$list
	@list='$(RECHECK_LOGS:.log=.trs)'; test -z "$$list" || rm -f $$list
	@test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)
	@set +e; $(am__set_TESTS_bases); \
	log_list=`for i in $$bases; do echo $$i.log; done`; \
	trs_list=`for i in $$bases; do echo $$i.trs; done`; \
	log_list=`echo $$log_list`; trs_list=`echo $$trs_list`; \
	$(MAKE) $(AM_MAKEFLAGS) $(TEST_SUITE_LOG) TEST_LOGS="$$log_list"; \
	exit $$?;
recheck: all $(check_SCRIPTS)
	@test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)
	@set +e; $(am__set_TESTS_bases); \
	bases=`for i in $$bases; do echo $$i; done \
	         | $(am__list_recheck_tests)` || exit 1; \
	log_list=`for i in $$bases; do echo $$i.log; done`; \
	log_list=`echo $$log_list`; \
	$(MAKE) $(AM_MAKEFLAGS) $(TEST_SUITE_LOG) \
	        am__force_recheck=am--force-recheck \
	        TEST_LOGS="$$log_list"; \
	exit $$?
pcre_jit_test.log: pcre_jit_test$(EXEEXT)
	@p='pcre_jit_test$(EXEEXT)'; \
	b='pcre_jit_test'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
pcrecpp_unittest.log: pcrecpp_unittest$(EXEEXT)
	@p='pcrecpp_unittest$(EXEEXT)'; \
	b='pcrecpp_unittest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
pcre_scanner_unittest.log: pcre_scanner_unittest$(EXEEXT)
	@p='pcre_scanner_unittest$(EXEEXT)'; \
	b='pcre_scanner_unittest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
pcre_stringpiece_unittest.log: pcre_stringpiece_unittest$(EXEEXT)
	@p='pcre_stringpiece_unittest$(EXEEXT)'; \
	b='pcre_stringpiece_unittest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
RunTest.log: RunTest
	@p='RunTest'; \
	b='RunTest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
RunGrepTest.log: RunGrepTest
	@p='RunGrepTest'; \
	b='RunGrepTest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
.test.log:
	@p='$<'; \
	$(am__set_b); \
	$(am__check_pre) $(TEST_LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_TEST_LOG_DRIVER_FLAGS) $(TEST_LOG_DRIVER_FLAGS) -- $(TEST_LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
@am__EXEEXT_TRUE@.test$(EXEEXT).log:
@am__EXEEXT_TRUE@	@p='$<'; \
@am__EXEEXT_TRUE@	$(am__set_b); \
@am__EXEEXT_TRUE@	$(am__check_pre) $(TEST_LOG_DRIVER) --test-name "$$f" \
@am__EXEEXT_TRUE@	--log-file $$b.log --trs-file $$b.trs \
@am__EXEEXT_TRUE@	$(am__common_driver_flags) $(AM_TEST_LOG_DRIVER_FLAGS) $(TEST_LOG_DRIVER_FLAGS) -- $(TEST_LOG_COMPILE) \
@am__EXEEXT_TRUE@	"$$tst" $(AM_TESTS_FD_REDIRECT)

distdir: $(DISTFILES)
	$(am__remove_distdir)
	test -d "$(distdir)" || mkdir "$(distdir)"
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	-test -n "$(am__skip_mode_fix)" \
	|| find "$(distdir)" -type d ! -perm -755 \
		-exec chmod u+rwx,go+rx {} \; -o \
	  ! -type d ! -perm -444 -links 1 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -400 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -444 -exec $(install_sh) -c -m a+r {} {} \; \
	|| chmod -R a+r "$(distdir)"
dist-gzip: distdir
	tardir=$(distdir) && $(am__tar) | GZIP=$(GZIP_ENV) gzip -c >$(distdir).tar.gz
	$(am__post_remove_distdir)
dist-bzip2: distdir
	tardir=$(distdir) && $(am__tar) | BZIP2=$${BZIP2--9} bzip2 -c >$(distdir).tar.bz2
	$(am__post_remove_distdir)

dist-lzip: distdir
	tardir=$(distdir) && $(am__tar) | lzip -c $${LZIP_OPT--9} >$(distdir).tar.lz
	$(am__post_remove_distdir)

dist-xz: distdir
	tardir=$(distdir) && $(am__tar) | XZ_OPT=$${XZ_OPT--e} xz -c >$(distdir).tar.xz
	$(am__post_remove_distdir)

dist-tarZ: distdir
	@echo WARNING: "Support for distribution archives compressed with" \
		       "legacy program 'compress' is deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	tardir=$(distdir) && $(am__tar) | compress -c >$(distdir).tar.Z
	$(am__post_remove_distdir)

dist-shar: distdir
	@echo WARNING: "Support for shar distribution archives is" \
	               "deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	shar $(distdir) | GZIP=$(GZIP_ENV) gzip -c >$(distdir).shar.gz
	$(am__post_remove_distdir)
dist-zip: distdir
	-rm -f $(distdir).zip
	zip -rq $(distdir).zip $(distdir)
	$(am__post_remove_distdir)

dist dist-all:
	$(MAKE) $(AM_MAKEFLAGS) $(DIST_TARGETS) am__post_remove_distdir='@:'
	$(am__post_remove_distdir)

# This target untars the dist file and tries a VPATH configuration.  Then
# it guarantees that the distribution is self-contained by making another
# tarfile.
distcheck: dist
	case '$(DIST_ARCHIVES)' in \
	*.tar.gz*) \
	  GZIP=$(GZIP_ENV) gzip -dc $(distdir).tar.gz | $(am__untar) ;;\
	*.tar.bz2*) \
	  bzip2 -dc $(distdir).tar.bz2 | $(am__untar) ;;\
	*.tar.lz*) \
	  lzip -dc $(distdir).tar.lz | $(am__untar) ;;\
	*.tar.xz*) \
	  xz -dc $(distdir).tar.xz | $(am__untar) ;;\
	*.tar.Z*) \
	  uncompress -c $(distdir).tar.Z | $(am__untar) ;;\
	*.shar.gz*) \
	  GZIP=$(GZIP_ENV) gzip -dc $(distdir).shar.gz | unshar ;;\
	*.zip*) \
	  unzip $(distdir).zip ;;\
	esac
	chmod -R a-w $(distdir)
	chmod u+w $(distdir)
	mkdir $(distdir)/_build $(distdir)/_build/sub $(distdir)/_inst
	chmod a-w $(distdir)
	test -d $(distdir)/_build || exit 0; \
	dc_install_base=`$(am__cd) $(distdir)/_inst && pwd | sed -e 's,^[^:\\/]:[\\/],/,'` \
	  && dc_destdir="$${TMPDIR-/tmp}/am-dc-$$$$/" \
	  && am__cwd=`pwd` \
	  && $(am__cd) $(distdir)/_build/sub \
	  && ../../configure \
	    $(AM_DISTCHECK_CONFIGURE_FLAGS) \
	    $(DISTCHECK_CONFIGURE_FLAGS) \
	    --srcdir=../.. --prefix="$$dc_install_base" \
	  && $(MAKE) $(AM_MAKEFLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) dvi \
	  && $(MAKE) $(AM_MAKEFLAGS) check \
	  && $(MAKE) $(AM_MAKEFLAGS) install \
	  && $(MAKE) $(AM_MAKEFLAGS) installcheck \
	  && $(MAKE) $(AM_MAKEFLAGS) uninstall \
	  && $(MAKE) $(AM_MAKEFLAGS) distuninstallcheck_dir="$$dc_install_base" \
	        distuninstallcheck \
	  && chmod -R a-w "$$dc_install_base" \
	  && ({ \
	       (cd ../.. && umask 077 && mkdir "$$dc_destdir") \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" install \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" uninstall \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" \
	            distuninstallcheck_dir="$$dc_destdir" distuninstallcheck; \
	      } || { rm -rf "$$dc_destdir"; exit 1; }) \
	  && rm -rf "$$dc_destdir" \
	  && $(MAKE) $(AM_MAKEFLAGS) dist \
	  && rm -rf $(DIST_ARCHIVES) \
	  && $(MAKE) $(AM_MAKEFLAGS) distcleancheck \
	  && cd "$$am__cwd" \
	  || exit 1
	$(am__post_remove_distdir)
	@(echo "$(distdir) archives ready for distribution: "; \
	  list='$(DIST_ARCHIVES)'; for i in $$list; do echo $$i; done) | \
	  sed -e 1h -e 1s/./=/g -e 1p -e 1x -e '$$p' -e '$$x'
distuninstallcheck:
	@test -n '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: trying to run $@ with an empty' \
	       '$$(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	$(am__cd) '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: cannot chdir into $(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	test `$(am__distuninstallcheck_listfiles) | wc -l` -eq 0 \
	   || { echo "ERROR: files left after uninstall:" ; \
	        if test -n "$(DESTDIR)"; then \
	          echo "  (check DESTDIR support)"; \
	        fi ; \
	        $(distuninstallcheck_listfiles) ; \
	        exit 1; } >&2
distcleancheck: distclean
	@if test '$(srcdir)' = . ; then \
	  echo "ERROR: distcleancheck can only run from a VPATH build" ; \
	  exit 1 ; \
	fi
	@test `$(distcleancheck_listfiles) | wc -l` -eq 0 \
	  || { echo "ERROR: files left in build directory after distclean:" ; \
	       $(distcleancheck_listfiles) ; \
	       exit 1; } >&2
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) $(check_SCRIPTS)
	$(MAKE) $(AM_MAKEFLAGS) check-TESTS
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-am
all-am: Makefile $(LTLIBRARIES) $(PROGRAMS) $(SCRIPTS) $(MANS) $(DATA) \
		$(HEADERS) config.h
install-binPROGRAMS: install-libLTLIBRARIES

installdirs:
	for dir in "$(DESTDIR)$(libdir)" "$(DESTDIR)$(bindir)" "$(DESTDIR)$(bindir)" "$(DESTDIR)$(man1dir)" "$(DESTDIR)$(man3dir)" "$(DESTDIR)$(docdir)" "$(DESTDIR)$(htmldir)" "$(DESTDIR)$(htmldir)" "$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(includedir)" "$(DESTDIR)$(includedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:
	-test -z "$(TEST_LOGS)" || rm -f $(TEST_LOGS)
	-test -z "$(TEST_LOGS:.log=.trs)" || rm -f $(TEST_LOGS:.log=.trs)
	-test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
	-test -z "$(MAINTAINERCLEANFILES)" || rm -f $(MAINTAINERCLEANFILES)
@WITH_GCOV_FALSE@distclean-local:
@WITH_GCOV_FALSE@clean-local:
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic clean-libLTLIBRARIES \
	clean-libtool clean-local clean-noinstPROGRAMS mostlyclean-am

distclean: distclean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-hdr distclean-libtool distclean-local distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-dist_docDATA install-dist_htmlDATA \
	install-htmlDATA install-includeHEADERS install-man \
	install-nodist_includeHEADERS install-pkgconfigDATA
	@$(NORMAL_INSTALL)
	$(MAKE) $(AM_MAKEFLAGS) install-data-hook
install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS install-binSCRIPTS \
	install-libLTLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man: install-man1 install-man3

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf $(top_srcdir)/autom4te.cache
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS uninstall-binSCRIPTS \
	uninstall-dist_docDATA uninstall-dist_htmlDATA \
	uninstall-htmlDATA uninstall-includeHEADERS \
	uninstall-libLTLIBRARIES uninstall-man \
	uninstall-nodist_includeHEADERS uninstall-pkgconfigDATA

uninstall-man: uninstall-man1 uninstall-man3

.MAKE: all check check-am install install-am install-data-am \
	install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--refresh check check-TESTS \
	check-am clean clean-binPROGRAMS clean-cscope clean-generic \
	clean-libLTLIBRARIES clean-libtool clean-local \
	clean-noinstPROGRAMS cscope cscopelist-am ctags ctags-am dist \
	dist-all dist-bzip2 dist-gzip dist-lzip dist-shar dist-tarZ \
	dist-xz dist-zip distcheck distclean distclean-compile \
	distclean-generic distclean-hdr distclean-libtool \
	distclean-local distclean-tags distcleancheck distdir \
	distuninstallcheck dvi dvi-am html html-am info info-am \
	install install-am install-binPROGRAMS install-binSCRIPTS \
	install-data install-data-am install-data-hook \
	install-dist_docDATA install-dist_htmlDATA install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-htmlDATA install-includeHEADERS \
	install-info install-info-am install-libLTLIBRARIES \
	install-man install-man1 install-man3 \
	install-nodist_includeHEADERS install-pdf install-pdf-am \
	install-pkgconfigDATA install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	recheck tags tags-am uninstall uninstall-am \
	uninstall-binPROGRAMS uninstall-binSCRIPTS \
	uninstall-dist_docDATA uninstall-dist_htmlDATA \
	uninstall-htmlDATA uninstall-includeHEADERS \
	uninstall-libLTLIBRARIES uninstall-man uninstall-man1 \
	uninstall-man3 uninstall-nodist_includeHEADERS \
	uninstall-pkgconfigDATA

.PRECIOUS: Makefile


# The only difference between pcre.h.in and pcre.h is the setting of the PCRE
# version number. Therefore, we can create the generic version just by copying.
pcre.h.generic: pcre.h.in configure.ac
	rm -f $@
	cp -p pcre.h $@

# It is more complicated for config.h.generic. We need the version that results
# from a default configuration so as to get all the default values for PCRE
# configuration macros such as MATCH_LIMIT and NEWLINE. We can get this by
# doing a configure in a temporary directory. However, some trickery is needed,
# because the source directory may already be configured. If you just try
# running configure in a new directory, it complains. For this reason, we move
# config.status out of the way while doing the default configuration. The
# resulting config.h is munged by perl to put #ifdefs round any #defines for
# macros with values, and to #undef all boolean macros such as HAVE_xxx and
# SUPPORT_xxx. We also get rid of any gcc-specific visibility settings. Make
# sure that PCRE_EXP_DEFN is unset (in case it has visibility settings).
config.h.generic: configure.ac
	rm -rf $@ _generic
	mkdir _generic
	cs=$(srcdir)/config.status; test ! -f $$cs || mv -f $$cs $$cs.aside
	cd _generic && $(abs_top_srcdir)/configure || :
	cs=$(srcdir)/config.status; test ! -f $$cs.aside || mv -f $$cs.aside $$cs
	test -f _generic/config.h
	perl -n \
	  -e 'BEGIN{$$blank=0;}' \
	  -e 'if(/PCRE_EXP_DEFN/){print"/* #undef PCRE_EXP_DEFN */\n";$$blank=0;next;}' \
	  -e 'if(/to make a symbol visible/){next;}' \
	  -e 'if(/__attribute__ \(\(visibility/){next;}' \
	  -e 'if(/LT_OBJDIR/){print"/* This is ignored unless you are using libtool. */\n";}' \
	  -e 'if(/^#define\s((?:HAVE|SUPPORT|STDC)_\w+)/){print"/* #undef $$1 */\n";$$blank=0;next;}' \
	  -e 'if(/^#define\s(?!PACKAGE|VERSION)(\w+)/){print"#ifndef $$1\n$$_#endif\n";$$blank=0;next;}' \
	  -e 'if(/^\s*$$/){print unless $$blank; $$blank=1;} else{print;$$blank=0;}' \
	  _generic/config.h >$@
	rm -rf _generic

@WITH_REBUILD_CHARTABLES_TRUE@pcre_chartables.c: dftables$(EXEEXT)
@WITH_REBUILD_CHARTABLES_TRUE@	./dftables$(EXEEXT) $@

@WITH_REBUILD_CHARTABLES_FALSE@pcre_chartables.c: $(srcdir)/pcre_chartables.c.dist
@WITH_REBUILD_CHARTABLES_FALSE@	rm -f $@
@WITH_REBUILD_CHARTABLES_FALSE@	$(LN_S) $(srcdir)/pcre_chartables.c.dist $@

# A compatibility line, the old build system worked with 'make test'
test: check ;

# A PCRE user submitted the following addition, saying that it "will allow
# anyone using the 'mingw32' compiler to simply type 'make pcre.dll' and get a
# nice DLL for Windows use".
pcre.dll: $(DLL_OBJS)
	$(CC) -shared -o pcre.dll -Wl,"--strip-all" -Wl,"--export-all-symbols" $(DLL_OBJS)

# Arrange for the per-function man pages to have 16- and 32-bit names as well.
install-data-hook:
	ln -sf pcre_assign_jit_stack.3		 $(DESTDIR)$(man3dir)/pcre16_assign_jit_stack.3
	ln -sf pcre_compile.3			 $(DESTDIR)$(man3dir)/pcre16_compile.3
	ln -sf pcre_compile2.3			 $(DESTDIR)$(man3dir)/pcre16_compile2.3
	ln -sf pcre_config.3			 $(DESTDIR)$(man3dir)/pcre16_config.3
	ln -sf pcre_copy_named_substring.3	 $(DESTDIR)$(man3dir)/pcre16_copy_named_substring.3
	ln -sf pcre_copy_substring.3		 $(DESTDIR)$(man3dir)/pcre16_copy_substring.3
	ln -sf pcre_dfa_exec.3			 $(DESTDIR)$(man3dir)/pcre16_dfa_exec.3
	ln -sf pcre_exec.3			 $(DESTDIR)$(man3dir)/pcre16_exec.3
	ln -sf pcre_free_study.3		 $(DESTDIR)$(man3dir)/pcre16_free_study.3
	ln -sf pcre_free_substring.3		 $(DESTDIR)$(man3dir)/pcre16_free_substring.3
	ln -sf pcre_free_substring_list.3	 $(DESTDIR)$(man3dir)/pcre16_free_substring_list.3
	ln -sf pcre_fullinfo.3			 $(DESTDIR)$(man3dir)/pcre16_fullinfo.3
	ln -sf pcre_get_named_substring.3	 $(DESTDIR)$(man3dir)/pcre16_get_named_substring.3
	ln -sf pcre_get_stringnumber.3		 $(DESTDIR)$(man3dir)/pcre16_get_stringnumber.3
	ln -sf pcre_get_stringtable_entries.3	 $(DESTDIR)$(man3dir)/pcre16_get_stringtable_entries.3
	ln -sf pcre_get_substring.3		 $(DESTDIR)$(man3dir)/pcre16_get_substring.3
	ln -sf pcre_get_substring_list.3	 $(DESTDIR)$(man3dir)/pcre16_get_substring_list.3
	ln -sf pcre_jit_exec.3			 $(DESTDIR)$(man3dir)/pcre16_jit_exec.3
	ln -sf pcre_jit_stack_alloc.3		 $(DESTDIR)$(man3dir)/pcre16_jit_stack_alloc.3
	ln -sf pcre_jit_stack_free.3		 $(DESTDIR)$(man3dir)/pcre16_jit_stack_free.3
	ln -sf pcre_maketables.3		 $(DESTDIR)$(man3dir)/pcre16_maketables.3
	ln -sf pcre_pattern_to_host_byte_order.3 $(DESTDIR)$(man3dir)/pcre16_pattern_to_host_byte_order.3
	ln -sf pcre_refcount.3			 $(DESTDIR)$(man3dir)/pcre16_refcount.3
	ln -sf pcre_study.3			 $(DESTDIR)$(man3dir)/pcre16_study.3
	ln -sf pcre_utf16_to_host_byte_order.3	 $(DESTDIR)$(man3dir)/pcre16_utf16_to_host_byte_order.3
	ln -sf pcre_version.3			 $(DESTDIR)$(man3dir)/pcre16_version.3
	ln -sf pcre_assign_jit_stack.3		 $(DESTDIR)$(man3dir)/pcre32_assign_jit_stack.3
	ln -sf pcre_compile.3			 $(DESTDIR)$(man3dir)/pcre32_compile.3
	ln -sf pcre_compile2.3			 $(DESTDIR)$(man3dir)/pcre32_compile2.3
	ln -sf pcre_config.3			 $(DESTDIR)$(man3dir)/pcre32_config.3
	ln -sf pcre_copy_named_substring.3	 $(DESTDIR)$(man3dir)/pcre32_copy_named_substring.3
	ln -sf pcre_copy_substring.3		 $(DESTDIR)$(man3dir)/pcre32_copy_substring.3
	ln -sf pcre_dfa_exec.3			 $(DESTDIR)$(man3dir)/pcre32_dfa_exec.3
	ln -sf pcre_exec.3			 $(DESTDIR)$(man3dir)/pcre32_exec.3
	ln -sf pcre_free_study.3		 $(DESTDIR)$(man3dir)/pcre32_free_study.3
	ln -sf pcre_free_substring.3		 $(DESTDIR)$(man3dir)/pcre32_free_substring.3
	ln -sf pcre_free_substring_list.3	 $(DESTDIR)$(man3dir)/pcre32_free_substring_list.3
	ln -sf pcre_fullinfo.3			 $(DESTDIR)$(man3dir)/pcre32_fullinfo.3
	ln -sf pcre_get_named_substring.3	 $(DESTDIR)$(man3dir)/pcre32_get_named_substring.3
	ln -sf pcre_get_stringnumber.3		 $(DESTDIR)$(man3dir)/pcre32_get_stringnumber.3
	ln -sf pcre_get_stringtable_entries.3	 $(DESTDIR)$(man3dir)/pcre32_get_stringtable_entries.3
	ln -sf pcre_get_substring.3		 $(DESTDIR)$(man3dir)/pcre32_get_substring.3
	ln -sf pcre_get_substring_list.3	 $(DESTDIR)$(man3dir)/pcre32_get_substring_list.3
	ln -sf pcre_jit_exec.3			 $(DESTDIR)$(man3dir)/pcre32_jit_exec.3
	ln -sf pcre_jit_stack_alloc.3		 $(DESTDIR)$(man3dir)/pcre32_jit_stack_alloc.3
	ln -sf pcre_jit_stack_free.3		 $(DESTDIR)$(man3dir)/pcre32_jit_stack_free.3
	ln -sf pcre_maketables.3		 $(DESTDIR)$(man3dir)/pcre32_maketables.3
	ln -sf pcre_pattern_to_host_byte_order.3 $(DESTDIR)$(man3dir)/pcre32_pattern_to_host_byte_order.3
	ln -sf pcre_refcount.3			 $(DESTDIR)$(man3dir)/pcre32_refcount.3
	ln -sf pcre_study.3			 $(DESTDIR)$(man3dir)/pcre32_study.3
	ln -sf pcre_utf32_to_host_byte_order.3	 $(DESTDIR)$(man3dir)/pcre32_utf32_to_host_byte_order.3
	ln -sf pcre_version.3			 $(DESTDIR)$(man3dir)/pcre32_version.3

@WITH_GCOV_TRUE@coverage-check: all
@WITH_GCOV_TRUE@	-$(MAKE) $(AM_MAKEFLAGS) -k check

@WITH_GCOV_TRUE@coverage-baseline:
@WITH_GCOV_TRUE@	$(LCOV) $(coverage_quiet) \
@WITH_GCOV_TRUE@		--directory $(top_builddir) \
@WITH_GCOV_TRUE@		--output-file "$(COVERAGE_OUTPUT_FILE)" \
@WITH_GCOV_TRUE@		--capture \
@WITH_GCOV_TRUE@		--initial

@WITH_GCOV_TRUE@coverage-report:
@WITH_GCOV_TRUE@	$(LCOV) $(coverage_quiet) \
@WITH_GCOV_TRUE@		--directory $(top_builddir) \
@WITH_GCOV_TRUE@		--capture \
@WITH_GCOV_TRUE@		--output-file "$(COVERAGE_OUTPUT_FILE).tmp" \
@WITH_GCOV_TRUE@		--test-name "$(COVERAGE_TEST_NAME)" \
@WITH_GCOV_TRUE@		--no-checksum \
@WITH_GCOV_TRUE@		--compat-libtool \
@WITH_GCOV_TRUE@		$(COVERAGE_LCOV_EXTRA_FLAGS)
@WITH_GCOV_TRUE@	$(LCOV) $(coverage_quiet) \
@WITH_GCOV_TRUE@		--directory $(top_builddir) \
@WITH_GCOV_TRUE@		--output-file "$(COVERAGE_OUTPUT_FILE)" \
@WITH_GCOV_TRUE@		--remove "$(COVERAGE_OUTPUT_FILE).tmp" \
@WITH_GCOV_TRUE@		"/tmp/*" \
@WITH_GCOV_TRUE@		"/usr/include/*" \
@WITH_GCOV_TRUE@		"$(includedir)/*"
@WITH_GCOV_TRUE@	-@rm -f "$(COVERAGE_OUTPUT_FILE).tmp"
@WITH_GCOV_TRUE@	LANG=C $(GENHTML) $(coverage_quiet) \
@WITH_GCOV_TRUE@		--prefix $(top_builddir) \
@WITH_GCOV_TRUE@		--output-directory "$(COVERAGE_OUTPUT_DIR)" \
@WITH_GCOV_TRUE@		--title "$(PACKAGE) $(VERSION) Code Coverage Report" \
@WITH_GCOV_TRUE@		--show-details "$(COVERAGE_OUTPUT_FILE)" \
@WITH_GCOV_TRUE@		--legend \
@WITH_GCOV_TRUE@		$(COVERAGE_GENHTML_EXTRA_FLAGS)
@WITH_GCOV_TRUE@	@echo "Code coverage report written to file://$(abs_builddir)/$(COVERAGE_OUTPUT_DIR)/index.html"

@WITH_GCOV_TRUE@coverage-reset:
@WITH_GCOV_TRUE@	-$(LCOV) $(coverage_quiet) --zerocounters --directory $(top_builddir)

@WITH_GCOV_TRUE@coverage-clean-report:
@WITH_GCOV_TRUE@	-rm -f "$(COVERAGE_OUTPUT_FILE)" "$(COVERAGE_OUTPUT_FILE).tmp"
@WITH_GCOV_TRUE@	-rm -rf "$(COVERAGE_OUTPUT_DIR)"

@WITH_GCOV_TRUE@coverage-clean-data:
@WITH_GCOV_TRUE@	-find $(top_builddir) -name "*.gcda" -delete

@WITH_GCOV_TRUE@coverage-clean: coverage-reset coverage-clean-report coverage-clean-data
@WITH_GCOV_TRUE@	-find $(top_builddir) -name "*.gcno" -delete

@WITH_GCOV_TRUE@coverage-distclean: coverage-clean

@WITH_GCOV_TRUE@coverage: coverage-reset coverage-baseline coverage-check coverage-report
@WITH_GCOV_TRUE@clean-local: coverage-clean
@WITH_GCOV_TRUE@distclean-local: coverage-distclean

@WITH_GCOV_TRUE@.PHONY: coverage coverage-baseline coverage-check coverage-report coverage-reset coverage-clean-report coverage-clean-data coverage-clean coverage-distclean

@WITH_GCOV_FALSE@coverage:
@WITH_GCOV_FALSE@	@echo "Configuring with --enable-coverage required to generate code coverage report."

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
