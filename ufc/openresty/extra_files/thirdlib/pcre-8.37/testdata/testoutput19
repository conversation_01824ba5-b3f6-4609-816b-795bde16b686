/-- This set of tests is for Unicode property support, relevant only to the
    16- and 32-bit library. --/
    
/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8iDZ
------------------------------------------------------------------
        Bra
     /i A\x{391}\x{10427}\x{ff3a}\x{1fb0}
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: caseless utf
First char = 'A' (caseless)
Need char = \x{1fb0} (caseless)

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8DZ
------------------------------------------------------------------
        Bra
        A\x{391}\x{10427}\x{ff3a}\x{1fb0}
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
First char = 'A'
Need char = \x{1fb0}

/AB\x{1fb0}/8DZ
------------------------------------------------------------------
        Bra
        AB\x{1fb0}
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
First char = 'A'
Need char = \x{1fb0}

/AB\x{1fb0}/8DZi
------------------------------------------------------------------
        Bra
     /i AB\x{1fb0}
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: caseless utf
First char = 'A' (caseless)
Need char = \x{1fb0} (caseless)

/\x{401}\x{420}\x{421}\x{422}\x{423}\x{424}\x{425}\x{426}\x{427}\x{428}\x{429}\x{42a}\x{42b}\x{42c}\x{42d}\x{42e}\x{42f}/8iSI
Capturing subpattern count = 0
Options: caseless utf
First char = \x{401} (caseless)
Need char = \x{42f} (caseless)
Subject length lower bound = 17
No starting char list
    \x{401}\x{420}\x{421}\x{422}\x{423}\x{424}\x{425}\x{426}\x{427}\x{428}\x{429}\x{42a}\x{42b}\x{42c}\x{42d}\x{42e}\x{42f}
 0: \x{401}\x{420}\x{421}\x{422}\x{423}\x{424}\x{425}\x{426}\x{427}\x{428}\x{429}\x{42a}\x{42b}\x{42c}\x{42d}\x{42e}\x{42f}
    \x{451}\x{440}\x{441}\x{442}\x{443}\x{444}\x{445}\x{446}\x{447}\x{448}\x{449}\x{44a}\x{44b}\x{44c}\x{44d}\x{44e}\x{44f}
 0: \x{451}\x{440}\x{441}\x{442}\x{443}\x{444}\x{445}\x{446}\x{447}\x{448}\x{449}\x{44a}\x{44b}\x{44c}\x{44d}\x{44e}\x{44f}

/[ⱥ]/8iBZ
------------------------------------------------------------------
        Bra
     /i \x{2c65}
        Ket
        End
------------------------------------------------------------------

/[^ⱥ]/8iBZ
------------------------------------------------------------------
        Bra
     /i [^\x{2c65}]
        Ket
        End
------------------------------------------------------------------

/[[:blank:]]/WBZ
------------------------------------------------------------------
        Bra
        [\x09 \xa0\x{1680}\x{180e}\x{2000}-\x{200a}\x{202f}\x{205f}\x{3000}]
        Ket
        End
------------------------------------------------------------------

/\x{212a}+/i8SI
Capturing subpattern count = 0
Options: caseless utf
No first char
No need char
Subject length lower bound = 1
Starting chars: K k \xff 
    KKkk\x{212a}
 0: KKkk\x{212a}

/s+/i8SI
Capturing subpattern count = 0
Options: caseless utf
No first char
No need char
Subject length lower bound = 1
Starting chars: S s \xff 
    SSss\x{17f}
 0: SSss\x{17f}

/-- End of testinput19 --/ 
