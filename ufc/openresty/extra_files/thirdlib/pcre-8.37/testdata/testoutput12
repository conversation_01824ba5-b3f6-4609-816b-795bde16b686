/-- This test is run only when JIT support is available. It checks for a
successful and an unsuccessful JIT compile and save and restore behaviour,
and a couple of things that are different with JIT. --/

/abc/S+I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'
Subject length lower bound = 3
No starting char list
JIT study was successful

/(?(?C1)(?=a)a)/S+I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
Study returned NULL
JIT study was not successful

/(?(?C1)(?=a)a)/S!+I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
Subject length lower bound = -1
No starting char list
JIT study was not successful

/abc/S+I>testsavedregex
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'
Subject length lower bound = 3
No starting char list
JIT study was successful
Compiled pattern written to testsavedregex
Study data written to testsavedregex

<testsavedregex
Compiled pattern loaded from testsavedregex
Study data loaded from testsavedregex
    abc
 0: abc

/a*/SI
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
Study returned NULL

/(?(R)a*(?1)|((?R))b)/S+
    aaaabcde
Error -27 (JIT stack limit reached)
    
/-- Test various compile modes --/ 
    
/abcd/S++
    abcd
 0: abcd (JIT)
    xyz  
No match (JIT)

/abcd/S+
    abcd
 0: abcd (JIT)
    ab\P
Partial match: ab (JIT)
    ab\P\P
Partial match: ab (JIT)
    xyz
No match (JIT)

/abcd/S++
    abcd
 0: abcd (JIT)
    ab\P
Partial match: ab (JIT)
    ab\P\P
Partial match: ab (JIT)
    xyz
No match (JIT)

/abcd/S++1
    abcd
 0: abcd (JIT)
    ab\P
Partial match: ab
    ab\P\P
Partial match: ab
    xyz
No match (JIT)
    xyz\P
No match

/abcd/S++2
    abcd
 0: abcd
    ab\P
Partial match: ab (JIT)
    ab\P\P
Partial match: ab
    xyz
No match

/abcd/S++3
    abcd
 0: abcd (JIT)
    ab\P
Partial match: ab (JIT)
    ab\P\P
Partial match: ab
    xyz
No match (JIT)

/abcd/S++4
    abcd
 0: abcd
    ab\P
Partial match: ab
    ab\P\P
Partial match: ab (JIT)
    xyz
No match

/abcd/S++5
    abcd
 0: abcd (JIT)
    ab\P
Partial match: ab
    ab\P\P
Partial match: ab (JIT)
    xyz
No match (JIT)

/abcd/S++6
    abcd
 0: abcd
    ab\P
Partial match: ab (JIT)
    ab\P\P
Partial match: ab (JIT)
    xyz
No match

/abcd/S++7
    abcd
 0: abcd (JIT)
    ab\P
Partial match: ab (JIT)
    ab\P\P
Partial match: ab (JIT)
    xyz
No match (JIT)
    
/abcd/S++2I 
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'd'
Subject length lower bound = 4
No starting char list
JIT study was successful

/(*NO_START_OPT)a(*:m)b/KS++
    a
No match, mark = m (JIT)

/^12345678abcd/mS++
    12345678abcd
 0: 12345678abcd (JIT)

/-- Test pattern compilation --/ 

/(?:a|b|c|d|e)(?R)/S++

/(?:a|b|c|d|e)(?R)(?R)/S++

/(a(?:a|b|c|d|e)b){8,16}/S++

/-- End of testinput12 --/
