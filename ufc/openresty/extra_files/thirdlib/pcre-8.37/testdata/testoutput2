/-- This set of tests is not Perl-compatible. It checks on special features
    of PCRE's API, error diagnostics, and the compiled code of some patterns.
    It also checks the non-Perl syntax the PCRE supports (Python, .NET, 
    Oniguruma). Finally, there are some tests where PCRE and Perl differ, 
    either because PCRE can't be compatible, or there is a possible Perl 
    bug.
    
    NOTE: This is a non-UTF set of tests. When UTF support is needed, use
    test 5, and if Unicode Property Support is needed, use test 7. --/
    
< forbid 8W 
  
/(a)b|/I
Capturing subpattern count = 1
May match empty string
No options
No first char
No need char

/abc/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'
    abc
 0: abc
    defabc
 0: abc
    \Aabc
 0: abc
    *** Failers
No match
    \Adefabc
No match
    ABC
No match

/^abc/I
Capturing subpattern count = 0
Options: anchored
No first char
No need char
    abc
 0: abc
    \Aabc
 0: abc
    *** Failers
No match
    defabc
No match
    \Adefabc
No match

/a+bc/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'

/a*bc/I
Capturing subpattern count = 0
No options
No first char
Need char = 'c'

/a{3}bc/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'

/(abc|a+z)/I
Capturing subpattern count = 1
No options
First char = 'a'
No need char

/^abc$/I
Capturing subpattern count = 0
Options: anchored
No first char
No need char
    abc
 0: abc
    *** Failers
No match
    def\nabc
No match

/ab\idef/X
Failed: unrecognized character follows \ at offset 3

/(?X)ab\idef/X
Failed: unrecognized character follows \ at offset 7

/x{5,4}/
Failed: numbers out of order in {} quantifier at offset 5

/z{65536}/
Failed: number too big in {} quantifier at offset 7

/[abcd/
Failed: missing terminating ] for character class at offset 5

/(?X)[\B]/
Failed: invalid escape sequence in character class at offset 6

/(?X)[\R]/
Failed: invalid escape sequence in character class at offset 6

/(?X)[\X]/
Failed: invalid escape sequence in character class at offset 6

/[\B]/BZ
------------------------------------------------------------------
        Bra
        B
        Ket
        End
------------------------------------------------------------------

/[\R]/BZ
------------------------------------------------------------------
        Bra
        R
        Ket
        End
------------------------------------------------------------------

/[\X]/BZ
------------------------------------------------------------------
        Bra
        X
        Ket
        End
------------------------------------------------------------------

/[z-a]/
Failed: range out of order in character class at offset 3

/^*/
Failed: nothing to repeat at offset 1

/(abc/
Failed: missing ) at offset 4

/(?# abc/
Failed: missing ) after comment at offset 7

/(?z)abc/
Failed: unrecognized character after (? or (?- at offset 2

/.*b/I
Capturing subpattern count = 0
No options
First char at start or follows newline
Need char = 'b'

/.*?b/I
Capturing subpattern count = 0
No options
First char at start or follows newline
Need char = 'b'

/cat|dog|elephant/I
Capturing subpattern count = 0
No options
No first char
No need char
    this sentence eventually mentions a cat
 0: cat
    this sentences rambles on and on for a while and then reaches elephant
 0: elephant

/cat|dog|elephant/IS
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 3
Starting chars: c d e 
    this sentence eventually mentions a cat
 0: cat
    this sentences rambles on and on for a while and then reaches elephant
 0: elephant

/cat|dog|elephant/IiS
Capturing subpattern count = 0
Options: caseless
No first char
No need char
Subject length lower bound = 3
Starting chars: C D E c d e 
    this sentence eventually mentions a CAT cat
 0: CAT
    this sentences rambles on and on for a while to elephant ElePhant
 0: elephant

/a|[bcd]/IS
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b c d 

/(a|[^\dZ])/IS
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: \x00 \x01 \x02 \x03 \x04 \x05 \x06 \x07 \x08 \x09 \x0a 
  \x0b \x0c \x0d \x0e \x0f \x10 \x11 \x12 \x13 \x14 \x15 \x16 \x17 \x18 \x19 
  \x1a \x1b \x1c \x1d \x1e \x1f \x20 ! " # $ % & ' ( ) * + , - . / : ; < = > 
  ? @ A B C D E F G H I J K L M N O P Q R S T U V W X Y [ \ ] ^ _ ` a b c d 
  e f g h i j k l m n o p q r s t u v w x y z { | } ~ \x7f \x80 \x81 \x82 \x83 
  \x84 \x85 \x86 \x87 \x88 \x89 \x8a \x8b \x8c \x8d \x8e \x8f \x90 \x91 \x92 
  \x93 \x94 \x95 \x96 \x97 \x98 \x99 \x9a \x9b \x9c \x9d \x9e \x9f \xa0 \xa1 
  \xa2 \xa3 \xa4 \xa5 \xa6 \xa7 \xa8 \xa9 \xaa \xab \xac \xad \xae \xaf \xb0 
  \xb1 \xb2 \xb3 \xb4 \xb5 \xb6 \xb7 \xb8 \xb9 \xba \xbb \xbc \xbd \xbe \xbf 
  \xc0 \xc1 \xc2 \xc3 \xc4 \xc5 \xc6 \xc7 \xc8 \xc9 \xca \xcb \xcc \xcd \xce 
  \xcf \xd0 \xd1 \xd2 \xd3 \xd4 \xd5 \xd6 \xd7 \xd8 \xd9 \xda \xdb \xdc \xdd 
  \xde \xdf \xe0 \xe1 \xe2 \xe3 \xe4 \xe5 \xe6 \xe7 \xe8 \xe9 \xea \xeb \xec 
  \xed \xee \xef \xf0 \xf1 \xf2 \xf3 \xf4 \xf5 \xf6 \xf7 \xf8 \xf9 \xfa \xfb 
  \xfc \xfd \xfe \xff 

/(a|b)*[\s]/IS
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: \x09 \x0a \x0b \x0c \x0d \x20 a b 

/(ab\2)/
Failed: reference to non-existent subpattern at offset 6

/{4,5}abc/
Failed: nothing to repeat at offset 4

/(a)(b)(c)\2/I
Capturing subpattern count = 3
Max back reference = 2
No options
First char = 'a'
Need char = 'c'
    abcb
 0: abcb
 1: a
 2: b
 3: c
    \O0abcb
Matched, but too many substrings
    \O3abcb
Matched, but too many substrings
 0: abcb
    \O6abcb
Matched, but too many substrings
 0: abcb
 1: a
    \O9abcb
Matched, but too many substrings
 0: abcb
 1: a
 2: b
    \O12abcb
 0: abcb
 1: a
 2: b
 3: c

/(a)bc|(a)(b)\2/I
Capturing subpattern count = 3
Max back reference = 2
No options
First char = 'a'
No need char
    abc
 0: abc
 1: a
    \O0abc
Matched, but too many substrings
    \O3abc
Matched, but too many substrings
 0: abc
    \O6abc
 0: abc
 1: a
    aba
 0: aba
 1: <unset>
 2: a
 3: b
    \O0aba
Matched, but too many substrings
    \O3aba
Matched, but too many substrings
 0: aba
    \O6aba
Matched, but too many substrings
 0: aba
 1: <unset>
    \O9aba
Matched, but too many substrings
 0: aba
 1: <unset>
 2: a
    \O12aba
 0: aba
 1: <unset>
 2: a
 3: b

/abc$/IE
Capturing subpattern count = 0
Options: dollar_endonly
First char = 'a'
Need char = 'c'
    abc
 0: abc
    *** Failers
No match
    abc\n
No match
    abc\ndef
No match

/(a)(b)(c)(d)(e)\6/
Failed: reference to non-existent subpattern at offset 17

/the quick brown fox/I
Capturing subpattern count = 0
No options
First char = 't'
Need char = 'x'
    the quick brown fox
 0: the quick brown fox
    this is a line with the quick brown fox
 0: the quick brown fox

/the quick brown fox/IA
Capturing subpattern count = 0
Options: anchored
No first char
No need char
    the quick brown fox
 0: the quick brown fox
    *** Failers
No match
    this is a line with the quick brown fox
No match

/ab(?z)cd/
Failed: unrecognized character after (? or (?- at offset 4

/^abc|def/I
Capturing subpattern count = 0
No options
No first char
No need char
    abcdef
 0: abc
    abcdef\B
 0: def

/.*((abc)$|(def))/I
Capturing subpattern count = 3
No options
First char at start or follows newline
No need char
    defabc
 0: defabc
 1: abc
 2: abc
    \Zdefabc
 0: def
 1: def
 2: <unset>
 3: def

/)/
Failed: unmatched parentheses at offset 0

/a[]b/
Failed: missing terminating ] for character class at offset 4

/[^aeiou ]{3,}/I
Capturing subpattern count = 0
No options
No first char
No need char
    co-processors, and for
 0: -pr

/<.*>/I
Capturing subpattern count = 0
No options
First char = '<'
Need char = '>'
    abc<def>ghi<klm>nop
 0: <def>ghi<klm>

/<.*?>/I
Capturing subpattern count = 0
No options
First char = '<'
Need char = '>'
    abc<def>ghi<klm>nop
 0: <def>

/<.*>/IU
Capturing subpattern count = 0
Options: ungreedy
First char = '<'
Need char = '>'
    abc<def>ghi<klm>nop
 0: <def>

/(?U)<.*>/I
Capturing subpattern count = 0
Options: ungreedy
First char = '<'
Need char = '>'
    abc<def>ghi<klm>nop
 0: <def>

/<.*?>/IU
Capturing subpattern count = 0
Options: ungreedy
First char = '<'
Need char = '>'
    abc<def>ghi<klm>nop
 0: <def>ghi<klm>

/={3,}/IU
Capturing subpattern count = 0
Options: ungreedy
First char = '='
Need char = '='
    abc========def
 0: ===

/(?U)={3,}?/I
Capturing subpattern count = 0
Options: ungreedy
First char = '='
Need char = '='
    abc========def
 0: ========

/(?<!bar|cattle)foo/I
Capturing subpattern count = 0
Max lookbehind = 6
No options
First char = 'f'
Need char = 'o'
    foo
 0: foo
    catfoo
 0: foo
    *** Failers
No match
    the barfoo
No match
    and cattlefoo
No match

/(?<=a+)b/
Failed: lookbehind assertion is not fixed length at offset 6

/(?<=aaa|b{0,3})b/
Failed: lookbehind assertion is not fixed length at offset 14

/(?<!(foo)a\1)bar/
Failed: lookbehind assertion is not fixed length at offset 12

/(?i)abc/I
Capturing subpattern count = 0
Options: caseless
First char = 'a' (caseless)
Need char = 'c' (caseless)

/(a|(?m)a)/I
Capturing subpattern count = 1
No options
First char = 'a'
No need char

/(?i)^1234/I
Capturing subpattern count = 0
Options: anchored caseless
No first char
No need char

/(^b|(?i)^d)/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char

/(?s).*/I
Capturing subpattern count = 0
May match empty string
Options: anchored dotall
No first char
No need char

/[abcd]/IS
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b c d 

/(?i)[abcd]/IS
Capturing subpattern count = 0
Options: caseless
No first char
No need char
Subject length lower bound = 1
Starting chars: A B C D a b c d 

/(?m)[xy]|(b|c)/IS
Capturing subpattern count = 1
Options: multiline
No first char
No need char
Subject length lower bound = 1
Starting chars: b c x y 

/(^a|^b)/Im
Capturing subpattern count = 1
Options: multiline
First char at start or follows newline
No need char

/(?i)(^a|^b)/Im
Capturing subpattern count = 1
Options: caseless multiline
First char at start or follows newline
No need char

/(a)(?(1)a|b|c)/
Failed: conditional group contains more than two branches at offset 13

/(?(?=a)a|b|c)/
Failed: conditional group contains more than two branches at offset 12

/(?(1a)/
Failed: malformed number or name after (?( at offset 4

/(?(1a))/
Failed: malformed number or name after (?( at offset 4

/(?(?i))/
Failed: assertion expected after (?( at offset 3

/(?(abc))/
Failed: reference to non-existent subpattern at offset 7

/(?(?<ab))/
Failed: assertion expected after (?( at offset 3

/((?s)blah)\s+\1/I
Capturing subpattern count = 1
Max back reference = 1
No options
First char = 'b'
Need char = 'h'

/((?i)blah)\s+\1/I
Capturing subpattern count = 1
Max back reference = 1
No options
First char = 'b' (caseless)
Need char = 'h' (caseless)

/((?i)b)/IDZS
------------------------------------------------------------------
        Bra
        CBra 1
     /i b
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
No options
First char = 'b' (caseless)
No need char
Subject length lower bound = 1
No starting char list

/(a*b|(?i:c*(?-i)d))/IS
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: C a b c d 

/a$/I
Capturing subpattern count = 0
No options
First char = 'a'
No need char
    a
 0: a
    a\n
 0: a
    *** Failers
No match
    \Za
No match
    \Za\n
No match

/a$/Im
Capturing subpattern count = 0
Options: multiline
First char = 'a'
No need char
    a
 0: a
    a\n
 0: a
    \Za\n
 0: a
    *** Failers
No match
    \Za
No match

/\Aabc/Im
Capturing subpattern count = 0
Max lookbehind = 1
Options: anchored multiline
No first char
No need char

/^abc/Im
Capturing subpattern count = 0
Options: multiline
First char at start or follows newline
Need char = 'c'

/^((a+)(?U)([ab]+)(?-U)([bc]+)(\w*))/I
Capturing subpattern count = 5
Options: anchored
No first char
No need char
  aaaaabbbbbcccccdef
 0: aaaaabbbbbcccccdef
 1: aaaaabbbbbcccccdef
 2: aaaaa
 3: b
 4: bbbbccccc
 5: def

/(?<=foo)[ab]/IS
Capturing subpattern count = 0
Max lookbehind = 3
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b 

/(?<!foo)(alpha|omega)/IS
Capturing subpattern count = 1
Max lookbehind = 3
No options
No first char
Need char = 'a'
Subject length lower bound = 5
Starting chars: a o 

/(?!alphabet)[ab]/IS
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b 

/(?<=foo\n)^bar/Im
Capturing subpattern count = 0
Max lookbehind = 4
Contains explicit CR or LF match
Options: multiline
No first char
Need char = 'r'
    foo\nbarbar
 0: bar
    ***Failers
No match
    rhubarb
No match
    barbell
No match
    abc\nbarton
No match

/^(?<=foo\n)bar/Im
Capturing subpattern count = 0
Max lookbehind = 4
Contains explicit CR or LF match
Options: multiline
First char at start or follows newline
Need char = 'r'
    foo\nbarbar
 0: bar
    ***Failers
No match
    rhubarb
No match
    barbell
No match
    abc\nbarton
No match

/(?>^abc)/Im
Capturing subpattern count = 0
Options: multiline
First char at start or follows newline
Need char = 'c'
    abc
 0: abc
    def\nabc
 0: abc
    *** Failers
No match
    defabc
No match

/(?<=ab(c+)d)ef/
Failed: lookbehind assertion is not fixed length at offset 11

/(?<=ab(?<=c+)d)ef/
Failed: lookbehind assertion is not fixed length at offset 12

/(?<=ab(c|de)f)g/
Failed: lookbehind assertion is not fixed length at offset 13

/The next three are in testinput2 because they have variable length branches/

/(?<=bullock|donkey)-cart/I
Capturing subpattern count = 0
Max lookbehind = 7
No options
First char = '-'
Need char = 't'
    the bullock-cart
 0: -cart
    a donkey-cart race
 0: -cart
    *** Failers
No match
    cart
No match
    horse-and-cart
No match

/(?<=ab(?i)x|y|z)/I
Capturing subpattern count = 0
Max lookbehind = 3
May match empty string
No options
No first char
No need char

/(?>.*)(?<=(abcd)|(xyz))/I
Capturing subpattern count = 2
Max lookbehind = 4
May match empty string
No options
No first char
No need char
    alphabetabcd
 0: alphabetabcd
 1: abcd
    endingxyz
 0: endingxyz
 1: <unset>
 2: xyz

/(?<=ab(?i)x(?-i)y|(?i)z|b)ZZ/I
Capturing subpattern count = 0
Max lookbehind = 4
No options
First char = 'Z'
Need char = 'Z'
    abxyZZ
 0: ZZ
    abXyZZ
 0: ZZ
    ZZZ
 0: ZZ
    zZZ
 0: ZZ
    bZZ
 0: ZZ
    BZZ
 0: ZZ
    *** Failers
No match
    ZZ
No match
    abXYZZ
No match
    zzz
No match
    bzz
No match

/(?<!(foo)a)bar/I
Capturing subpattern count = 1
Max lookbehind = 4
No options
First char = 'b'
Need char = 'r'
    bar
 0: bar
    foobbar
 0: bar
    *** Failers
No match
    fooabar
No match

/This one is here because Perl behaves differently; see also the following/I
Capturing subpattern count = 0
No options
First char = 'T'
Need char = 'g'

/^(a\1?){4}$/I
Capturing subpattern count = 1
Max back reference = 1
Options: anchored
No first char
No need char
    aaaa
No match
    aaaaaa
No match
    
/Perl does not fail these two for the final subjects. Neither did PCRE until/
/release 8.01. The problem is in backtracking into a subpattern that contains/
No match
/a recursive reference to itself. PCRE has now made these into atomic patterns./
No match

/^(xa|=?\1a){2}$/
    xa=xaa
 0: xa=xaa
 1: =xaa
    ** Failers
No match
    xa=xaaa
No match

/^(xa|=?\1a)+$/
    xa=xaa
 0: xa=xaa
 1: =xaa
    ** Failers
No match
    xa=xaaa
No match

/These are syntax tests from Perl 5.005/I
Capturing subpattern count = 0
No options
First char = 'T'
Need char = '5'

/a[b-a]/
Failed: range out of order in character class at offset 4

/a[]b/
Failed: missing terminating ] for character class at offset 4

/a[/
Failed: missing terminating ] for character class at offset 2

/*a/
Failed: nothing to repeat at offset 0

/(*)b/
Failed: nothing to repeat at offset 1

/abc)/
Failed: unmatched parentheses at offset 3

/(abc/
Failed: missing ) at offset 4

/a**/
Failed: nothing to repeat at offset 2

/)(/
Failed: unmatched parentheses at offset 0

/\1/
Failed: reference to non-existent subpattern at offset 2

/\2/
Failed: reference to non-existent subpattern at offset 2

/(a)|\2/
Failed: reference to non-existent subpattern at offset 6

/a[b-a]/Ii
Failed: range out of order in character class at offset 4

/a[]b/Ii
Failed: missing terminating ] for character class at offset 4

/a[/Ii
Failed: missing terminating ] for character class at offset 2

/*a/Ii
Failed: nothing to repeat at offset 0

/(*)b/Ii
Failed: nothing to repeat at offset 1

/abc)/Ii
Failed: unmatched parentheses at offset 3

/(abc/Ii
Failed: missing ) at offset 4

/a**/Ii
Failed: nothing to repeat at offset 2

/)(/Ii
Failed: unmatched parentheses at offset 0

/:(?:/
Failed: missing ) at offset 4

/(?<%)b/
Failed: unrecognized character after (?< at offset 3

/a(?{)b/
Failed: unrecognized character after (? or (?- at offset 3

/a(?{{})b/
Failed: unrecognized character after (? or (?- at offset 3

/a(?{}})b/
Failed: unrecognized character after (? or (?- at offset 3

/a(?{"{"})b/
Failed: unrecognized character after (? or (?- at offset 3

/a(?{"{"}})b/
Failed: unrecognized character after (? or (?- at offset 3

/(?(1?)a|b)/
Failed: malformed number or name after (?( at offset 4

/[a[:xyz:/
Failed: missing terminating ] for character class at offset 8

/(?<=x+)y/
Failed: lookbehind assertion is not fixed length at offset 6

/a{37,17}/
Failed: numbers out of order in {} quantifier at offset 7

/abc/\
Failed: \ at end of pattern at offset 4

/abc/\i
Failed: \ at end of pattern at offset 4

/(a)bc(d)/I
Capturing subpattern count = 2
No options
First char = 'a'
Need char = 'd'
    abcd
 0: abcd
 1: a
 2: d
    abcd\C2
 0: abcd
 1: a
 2: d
 2C d (1)
    abcd\C5
 0: abcd
 1: a
 2: d
copy substring 5 failed -7

/(.{20})/I
Capturing subpattern count = 1
No options
No first char
No need char
    abcdefghijklmnopqrstuvwxyz
 0: abcdefghijklmnopqrst
 1: abcdefghijklmnopqrst
    abcdefghijklmnopqrstuvwxyz\C1
 0: abcdefghijklmnopqrst
 1: abcdefghijklmnopqrst
 1C abcdefghijklmnopqrst (20)
    abcdefghijklmnopqrstuvwxyz\G1
 0: abcdefghijklmnopqrst
 1: abcdefghijklmnopqrst
 1G abcdefghijklmnopqrst (20)

/(.{15})/I
Capturing subpattern count = 1
No options
No first char
No need char
    abcdefghijklmnopqrstuvwxyz
 0: abcdefghijklmno
 1: abcdefghijklmno
    abcdefghijklmnopqrstuvwxyz\C1\G1
 0: abcdefghijklmno
 1: abcdefghijklmno
 1C abcdefghijklmno (15)
 1G abcdefghijklmno (15)

/(.{16})/I
Capturing subpattern count = 1
No options
No first char
No need char
    abcdefghijklmnopqrstuvwxyz
 0: abcdefghijklmnop
 1: abcdefghijklmnop
    abcdefghijklmnopqrstuvwxyz\C1\G1\L
 0: abcdefghijklmnop
 1: abcdefghijklmnop
 1C abcdefghijklmnop (16)
 1G abcdefghijklmnop (16)
 0L abcdefghijklmnop
 1L abcdefghijklmnop

/^(a|(bc))de(f)/I
Capturing subpattern count = 3
Options: anchored
No first char
No need char
    adef\G1\G2\G3\G4\L
 0: adef
 1: a
 2: <unset>
 3: f
 1G a (1)
 2G  (0)
 3G f (1)
get substring 4 failed -7
 0L adef
 1L a
 2L 
 3L f
    bcdef\G1\G2\G3\G4\L
 0: bcdef
 1: bc
 2: bc
 3: f
 1G bc (2)
 2G bc (2)
 3G f (1)
get substring 4 failed -7
 0L bcdef
 1L bc
 2L bc
 3L f
    adefghijk\C0
 0: adef
 1: a
 2: <unset>
 3: f
 0C adef (4)

/^abc\00def/I
Capturing subpattern count = 0
Options: anchored
No first char
No need char
    abc\00def\L\C0
 0: abc\x00def
 0C abc\x00def (7)
 0L abc

/word ((?:[a-zA-Z0-9]+ )((?:[a-zA-Z0-9]+ )((?:[a-zA-Z0-9]+ )((?:[a-zA-Z0-9]+
)((?:[a-zA-Z0-9]+ )((?:[a-zA-Z0-9]+ )((?:[a-zA-Z0-9]+ )((?:[a-zA-Z0-9]+
)?)?)?)?)?)?)?)?)?otherword/I
Capturing subpattern count = 8
Contains explicit CR or LF match
No options
First char = 'w'
Need char = 'd'

/.*X/IDZ
------------------------------------------------------------------
        Bra
        Any*
        X
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char at start or follows newline
Need char = 'X'

/.*X/IDZs
------------------------------------------------------------------
        Bra
        AllAny*
        X
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored dotall
No first char
Need char = 'X'

/(.*X|^B)/IDZ
------------------------------------------------------------------
        Bra
        CBra 1
        Any*
        X
        Alt
        ^
        B
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
No options
First char at start or follows newline
No need char

/(.*X|^B)/IDZs
------------------------------------------------------------------
        Bra
        CBra 1
        AllAny*
        X
        Alt
        ^
        B
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Options: anchored dotall
No first char
No need char

/(?s)(.*X|^B)/IDZ
------------------------------------------------------------------
        Bra
        CBra 1
        AllAny*
        X
        Alt
        ^
        B
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Options: anchored dotall
No first char
No need char

/(?s:.*X|^B)/IDZ
------------------------------------------------------------------
        Bra
        Bra
        AllAny*
        X
        Alt
        ^
        B
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/\Biss\B/I+
Capturing subpattern count = 0
Max lookbehind = 1
No options
First char = 'i'
Need char = 's'
    Mississippi
 0: iss
 0+ issippi

/iss/IG+
Capturing subpattern count = 0
No options
First char = 'i'
Need char = 's'
    Mississippi
 0: iss
 0+ issippi
 0: iss
 0+ ippi

/\Biss\B/IG+
Capturing subpattern count = 0
Max lookbehind = 1
No options
First char = 'i'
Need char = 's'
    Mississippi
 0: iss
 0+ issippi

/\Biss\B/Ig+
Capturing subpattern count = 0
Max lookbehind = 1
No options
First char = 'i'
Need char = 's'
    Mississippi
 0: iss
 0+ issippi
 0: iss
 0+ ippi
    *** Failers
No match
    Mississippi\A
No match

/(?<=[Ms])iss/Ig+
Capturing subpattern count = 0
Max lookbehind = 1
No options
First char = 'i'
Need char = 's'
    Mississippi
 0: iss
 0+ issippi
 0: iss
 0+ ippi

/(?<=[Ms])iss/IG+
Capturing subpattern count = 0
Max lookbehind = 1
No options
First char = 'i'
Need char = 's'
    Mississippi
 0: iss
 0+ issippi

/^iss/Ig+
Capturing subpattern count = 0
Options: anchored
No first char
No need char
    ississippi
 0: iss
 0+ issippi

/.*iss/Ig+
Capturing subpattern count = 0
No options
First char at start or follows newline
Need char = 's'
    abciss\nxyzisspqr
 0: abciss
 0+ \x0axyzisspqr
 0: xyziss
 0+ pqr

/.i./I+g
Capturing subpattern count = 0
No options
No first char
Need char = 'i'
    Mississippi
 0: Mis
 0+ sissippi
 0: sis
 0+ sippi
 0: sip
 0+ pi
    Mississippi\A
 0: Mis
 0+ sissippi
 0: sis
 0+ sippi
 0: sip
 0+ pi
    Missouri river
 0: Mis
 0+ souri river
 0: ri 
 0+ river
 0: riv
 0+ er
    Missouri river\A
 0: Mis
 0+ souri river

/^.is/I+g
Capturing subpattern count = 0
Options: anchored
No first char
No need char
    Mississippi
 0: Mis
 0+ sissippi

/^ab\n/Ig+
Capturing subpattern count = 0
Contains explicit CR or LF match
Options: anchored
No first char
No need char
    ab\nab\ncd
 0: ab\x0a
 0+ ab\x0acd

/^ab\n/Img+
Capturing subpattern count = 0
Contains explicit CR or LF match
Options: multiline
First char at start or follows newline
Need char = \x0a
    ab\nab\ncd
 0: ab\x0a
 0+ ab\x0acd
 0: ab\x0a
 0+ cd

/abc/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'

/abc|bac/I
Capturing subpattern count = 0
No options
No first char
Need char = 'c'

/(abc|bac)/I
Capturing subpattern count = 1
No options
No first char
Need char = 'c'

/(abc|(c|dc))/I
Capturing subpattern count = 2
No options
No first char
Need char = 'c'

/(abc|(d|de)c)/I
Capturing subpattern count = 2
No options
No first char
Need char = 'c'

/a*/I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char

/a+/I
Capturing subpattern count = 0
No options
First char = 'a'
No need char

/(baa|a+)/I
Capturing subpattern count = 1
No options
No first char
Need char = 'a'

/a{0,3}/I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char

/baa{3,}/I
Capturing subpattern count = 0
No options
First char = 'b'
Need char = 'a'

/"([^\\"]+|\\.)*"/I
Capturing subpattern count = 1
No options
First char = '"'
Need char = '"'

/(abc|ab[cd])/I
Capturing subpattern count = 1
No options
First char = 'a'
No need char

/(a|.)/I
Capturing subpattern count = 1
No options
No first char
No need char

/a|ba|\w/I
Capturing subpattern count = 0
No options
No first char
No need char

/abc(?=pqr)/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'r'

/...(?<=abc)/I
Capturing subpattern count = 0
Max lookbehind = 3
No options
No first char
No need char

/abc(?!pqr)/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'

/ab./I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'

/ab[xyz]/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'

/abc*/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'

/ab.c*/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'

/a.c*/I
Capturing subpattern count = 0
No options
First char = 'a'
No need char

/.c*/I
Capturing subpattern count = 0
No options
No first char
No need char

/ac*/I
Capturing subpattern count = 0
No options
First char = 'a'
No need char

/(a.c*|b.c*)/I
Capturing subpattern count = 1
No options
No first char
No need char

/a.c*|aba/I
Capturing subpattern count = 0
No options
First char = 'a'
No need char

/.+a/I
Capturing subpattern count = 0
No options
No first char
Need char = 'a'

/(?=abcda)a.*/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'a'

/(?=a)a.*/I
Capturing subpattern count = 0
No options
First char = 'a'
No need char

/a(b)*/I
Capturing subpattern count = 1
No options
First char = 'a'
No need char

/a\d*/I
Capturing subpattern count = 0
No options
First char = 'a'
No need char

/ab\d*/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'

/a(\d)*/I
Capturing subpattern count = 1
No options
First char = 'a'
No need char

/abcde{0,0}/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'd'

/ab\d+/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'

/a(?(1)b)(.)/I
Capturing subpattern count = 1
Max back reference = 1
No options
First char = 'a'
No need char

/a(?(1)bag|big)(.)/I
Capturing subpattern count = 1
Max back reference = 1
No options
First char = 'a'
Need char = 'g'

/a(?(1)bag|big)*(.)/I
Capturing subpattern count = 1
Max back reference = 1
No options
First char = 'a'
No need char

/a(?(1)bag|big)+(.)/I
Capturing subpattern count = 1
Max back reference = 1
No options
First char = 'a'
Need char = 'g'

/a(?(1)b..|b..)(.)/I
Capturing subpattern count = 1
Max back reference = 1
No options
First char = 'a'
Need char = 'b'

/ab\d{0}e/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'e'

/a?b?/I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
    a
 0: a
    b
 0: b
    ab
 0: ab
    \
 0: 
    *** Failers
 0: 
    \N
No match

/|-/I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
    abcd
 0: 
    -abc
 0: 
    \Nab-c
 0: -
    *** Failers
 0: 
    \Nabc
No match

/^.?abcd/IS
Capturing subpattern count = 0
Options: anchored
No first char
Need char = 'd'
Subject length lower bound = 4
No starting char list

/\(             # ( at start
  (?:           # Non-capturing bracket
  (?>[^()]+)    # Either a sequence of non-brackets (no backtracking)
  |             # Or
  (?R)          # Recurse - i.e. nested bracketed string
  )*            # Zero or more contents
  \)            # Closing )
  /Ix
Capturing subpattern count = 0
Options: extended
First char = '('
Need char = ')'
    (abcd)
 0: (abcd)
    (abcd)xyz
 0: (abcd)
    xyz(abcd)
 0: (abcd)
    (ab(xy)cd)pqr
 0: (ab(xy)cd)
    (ab(xycd)pqr
 0: (xycd)
    () abc ()
 0: ()
    12(abcde(fsh)xyz(foo(bar))lmno)89
 0: (abcde(fsh)xyz(foo(bar))lmno)
    *** Failers
No match
    abcd
No match
    abcd)
No match
    (abcd
No match

/\(  ( (?>[^()]+) | (?R) )* \) /Ixg
Capturing subpattern count = 1
Options: extended
First char = '('
Need char = ')'
    (ab(xy)cd)pqr
 0: (ab(xy)cd)
 1: cd
    1(abcd)(x(y)z)pqr
 0: (abcd)
 1: abcd
 0: (x(y)z)
 1: z

/\(  (?: (?>[^()]+) | (?R) ) \) /Ix
Capturing subpattern count = 0
Options: extended
First char = '('
Need char = ')'
    (abcd)
 0: (abcd)
    (ab(xy)cd)
 0: (xy)
    (a(b(c)d)e)
 0: (c)
    ((ab))
 0: ((ab))
    *** Failers
No match
    ()
No match

/\(  (?: (?>[^()]+) | (?R) )? \) /Ix
Capturing subpattern count = 0
Options: extended
First char = '('
Need char = ')'
    ()
 0: ()
    12(abcde(fsh)xyz(foo(bar))lmno)89
 0: (fsh)

/\(  ( (?>[^()]+) | (?R) )* \) /Ix
Capturing subpattern count = 1
Options: extended
First char = '('
Need char = ')'
    (ab(xy)cd)
 0: (ab(xy)cd)
 1: cd

/\( ( ( (?>[^()]+) | (?R) )* ) \) /Ix
Capturing subpattern count = 2
Options: extended
First char = '('
Need char = ')'
    (ab(xy)cd)
 0: (ab(xy)cd)
 1: ab(xy)cd
 2: cd

/\( (123)? ( ( (?>[^()]+) | (?R) )* ) \) /Ix
Capturing subpattern count = 3
Options: extended
First char = '('
Need char = ')'
    (ab(xy)cd)
 0: (ab(xy)cd)
 1: <unset>
 2: ab(xy)cd
 3: cd
    (123ab(xy)cd)
 0: (123ab(xy)cd)
 1: 123
 2: ab(xy)cd
 3: cd

/\( ( (123)? ( (?>[^()]+) | (?R) )* ) \) /Ix
Capturing subpattern count = 3
Options: extended
First char = '('
Need char = ')'
    (ab(xy)cd)
 0: (ab(xy)cd)
 1: ab(xy)cd
 2: <unset>
 3: cd
    (123ab(xy)cd)
 0: (123ab(xy)cd)
 1: 123ab(xy)cd
 2: 123
 3: cd

/\( (((((((((( ( (?>[^()]+) | (?R) )* )))))))))) \) /Ix
Capturing subpattern count = 11
Options: extended
First char = '('
Need char = ')'
    (ab(xy)cd)
 0: (ab(xy)cd)
 1: ab(xy)cd
 2: ab(xy)cd
 3: ab(xy)cd
 4: ab(xy)cd
 5: ab(xy)cd
 6: ab(xy)cd
 7: ab(xy)cd
 8: ab(xy)cd
 9: ab(xy)cd
10: ab(xy)cd
11: cd

/\( ( ( (?>[^()<>]+) | ((?>[^()]+)) | (?R) )* ) \) /Ix
Capturing subpattern count = 3
Options: extended
First char = '('
Need char = ')'
    (abcd(xyz<p>qrs)123)
 0: (abcd(xyz<p>qrs)123)
 1: abcd(xyz<p>qrs)123
 2: 123

/\( ( ( (?>[^()]+) | ((?R)) )* ) \) /Ix
Capturing subpattern count = 3
Options: extended
First char = '('
Need char = ')'
    (ab(cd)ef)
 0: (ab(cd)ef)
 1: ab(cd)ef
 2: ef
 3: (cd)
    (ab(cd(ef)gh)ij)
 0: (ab(cd(ef)gh)ij)
 1: ab(cd(ef)gh)ij
 2: ij
 3: (cd(ef)gh)

/^[[:alnum:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [0-9A-Za-z]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:^alnum:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [\x00-/:-@[-`{-\xff] (neg)
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:alpha:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [A-Za-z]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:^alpha:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [\x00-@[-`{-\xff] (neg)
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/[_[:alpha:]]/IS
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: A B C D E F G H I J K L M N O P Q R S T U V W X Y Z 
  _ a b c d e f g h i j k l m n o p q r s t u v w x y z 

/^[[:ascii:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [\x00-\x7f]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:^ascii:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [\x80-\xff] (neg)
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:blank:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [\x09 ]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:^blank:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [\x00-\x08\x0a-\x1f!-\xff] (neg)
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/[\n\x0b\x0c\x0d[:blank:]]/IS
Capturing subpattern count = 0
Contains explicit CR or LF match
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: \x09 \x0a \x0b \x0c \x0d \x20 

/^[[:cntrl:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [\x00-\x1f\x7f]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:digit:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [0-9]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:graph:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [!-~]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:lower:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [a-z]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:print:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [ -~]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:punct:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [!-/:-@[-`{-~]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:space:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [\x09-\x0d ]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:upper:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [A-Z]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:xdigit:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [0-9A-Fa-f]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:word:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [0-9A-Z_a-z]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:^cntrl:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [ -~\x80-\xff] (neg)
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[12[:^digit:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [\x00-/12:-\xff] (neg)
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/^[[:^blank:]]/DZ
------------------------------------------------------------------
        Bra
        ^
        [\x00-\x08\x0a-\x1f!-\xff] (neg)
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/[01[:alpha:]%]/DZ
------------------------------------------------------------------
        Bra
        [%01A-Za-z]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/[[.ch.]]/I
Failed: POSIX collating elements are not supported at offset 1

/[[=ch=]]/I
Failed: POSIX collating elements are not supported at offset 1

/[[:rhubarb:]]/I
Failed: unknown POSIX class name at offset 3

/[[:upper:]]/Ii
Capturing subpattern count = 0
Options: caseless
No first char
No need char
    A
 0: A
    a
 0: a

/[[:lower:]]/Ii
Capturing subpattern count = 0
Options: caseless
No first char
No need char
    A
 0: A
    a
 0: a

/((?-i)[[:lower:]])[[:lower:]]/Ii
Capturing subpattern count = 1
Options: caseless
No first char
No need char
    ab
 0: ab
 1: a
    aB
 0: aB
 1: a
    *** Failers
 0: ai
 1: a
    Ab
No match
    AB
No match

/[\200-\110]/I
Failed: range out of order in character class at offset 9

/^(?(0)f|b)oo/I
Failed: invalid condition (?(0) at offset 6

/This one's here because of the large output vector needed/I
Capturing subpattern count = 0
No options
First char = 'T'
Need char = 'd'

/(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\d+(?:\s|$))(\w+)\s+(\270)/I
Capturing subpattern count = 271
Max back reference = 270
No options
No first char
No need char
    \O900 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49 50 51 52 53 54 55 56 57 58 59 60 61 62 63 64 65 66 67 68 69 70 71 72 73 74 75 76 77 78 79 80 81 82 83 84 85 86 87 88 89 90 91 92 93 94 95 96 97 98 99 100 101 102 103 104 105 106 107 108 109 110 111 112 113 114 115 116 117 118 119 120 121 122 123 124 125 126 127 128 129 130 131 132 133 134 135 136 137 138 139 140 141 142 143 144 145 146 147 148 149 150 151 152 153 154 155 156 157 158 159 160 161 162 163 164 165 166 167 168 169 170 171 172 173 174 175 176 177 178 179 180 181 182 183 184 185 186 187 188 189 190 191 192 193 194 195 196 197 198 199 200 201 202 203 204 205 206 207 208 209 210 211 212 213 214 215 216 217 218 219 220 221 222 223 224 225 226 227 228 229 230 231 232 233 234 235 236 237 238 239 240 241 242 243 244 245 246 247 248 249 250 251 252 253 254 255 256 257 258 259 260 261 262 263 264 265 266 267 268 269 ABC ABC
 0: 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49 50 51 52 53 54 55 56 57 58 59 60 61 62 63 64 65 66 67 68 69 70 71 72 73 74 75 76 77 78 79 80 81 82 83 84 85 86 87 88 89 90 91 92 93 94 95 96 97 98 99 100 101 102 103 104 105 106 107 108 109 110 111 112 113 114 115 116 117 118 119 120 121 122 123 124 125 126 127 128 129 130 131 132 133 134 135 136 137 138 139 140 141 142 143 144 145 146 147 148 149 150 151 152 153 154 155 156 157 158 159 160 161 162 163 164 165 166 167 168 169 170 171 172 173 174 175 176 177 178 179 180 181 182 183 184 185 186 187 188 189 190 191 192 193 194 195 196 197 198 199 200 201 202 203 204 205 206 207 208 209 210 211 212 213 214 215 216 217 218 219 220 221 222 223 224 225 226 227 228 229 230 231 232 233 234 235 236 237 238 239 240 241 242 243 244 245 246 247 248 249 250 251 252 253 254 255 256 257 258 259 260 261 262 263 264 265 266 267 268 269 ABC ABC
 1: 1 
 2: 2 
 3: 3 
 4: 4 
 5: 5 
 6: 6 
 7: 7 
 8: 8 
 9: 9 
10: 10 
11: 11 
12: 12 
13: 13 
14: 14 
15: 15 
16: 16 
17: 17 
18: 18 
19: 19 
20: 20 
21: 21 
22: 22 
23: 23 
24: 24 
25: 25 
26: 26 
27: 27 
28: 28 
29: 29 
30: 30 
31: 31 
32: 32 
33: 33 
34: 34 
35: 35 
36: 36 
37: 37 
38: 38 
39: 39 
40: 40 
41: 41 
42: 42 
43: 43 
44: 44 
45: 45 
46: 46 
47: 47 
48: 48 
49: 49 
50: 50 
51: 51 
52: 52 
53: 53 
54: 54 
55: 55 
56: 56 
57: 57 
58: 58 
59: 59 
60: 60 
61: 61 
62: 62 
63: 63 
64: 64 
65: 65 
66: 66 
67: 67 
68: 68 
69: 69 
70: 70 
71: 71 
72: 72 
73: 73 
74: 74 
75: 75 
76: 76 
77: 77 
78: 78 
79: 79 
80: 80 
81: 81 
82: 82 
83: 83 
84: 84 
85: 85 
86: 86 
87: 87 
88: 88 
89: 89 
90: 90 
91: 91 
92: 92 
93: 93 
94: 94 
95: 95 
96: 96 
97: 97 
98: 98 
99: 99 
100: 100 
101: 101 
102: 102 
103: 103 
104: 104 
105: 105 
106: 106 
107: 107 
108: 108 
109: 109 
110: 110 
111: 111 
112: 112 
113: 113 
114: 114 
115: 115 
116: 116 
117: 117 
118: 118 
119: 119 
120: 120 
121: 121 
122: 122 
123: 123 
124: 124 
125: 125 
126: 126 
127: 127 
128: 128 
129: 129 
130: 130 
131: 131 
132: 132 
133: 133 
134: 134 
135: 135 
136: 136 
137: 137 
138: 138 
139: 139 
140: 140 
141: 141 
142: 142 
143: 143 
144: 144 
145: 145 
146: 146 
147: 147 
148: 148 
149: 149 
150: 150 
151: 151 
152: 152 
153: 153 
154: 154 
155: 155 
156: 156 
157: 157 
158: 158 
159: 159 
160: 160 
161: 161 
162: 162 
163: 163 
164: 164 
165: 165 
166: 166 
167: 167 
168: 168 
169: 169 
170: 170 
171: 171 
172: 172 
173: 173 
174: 174 
175: 175 
176: 176 
177: 177 
178: 178 
179: 179 
180: 180 
181: 181 
182: 182 
183: 183 
184: 184 
185: 185 
186: 186 
187: 187 
188: 188 
189: 189 
190: 190 
191: 191 
192: 192 
193: 193 
194: 194 
195: 195 
196: 196 
197: 197 
198: 198 
199: 199 
200: 200 
201: 201 
202: 202 
203: 203 
204: 204 
205: 205 
206: 206 
207: 207 
208: 208 
209: 209 
210: 210 
211: 211 
212: 212 
213: 213 
214: 214 
215: 215 
216: 216 
217: 217 
218: 218 
219: 219 
220: 220 
221: 221 
222: 222 
223: 223 
224: 224 
225: 225 
226: 226 
227: 227 
228: 228 
229: 229 
230: 230 
231: 231 
232: 232 
233: 233 
234: 234 
235: 235 
236: 236 
237: 237 
238: 238 
239: 239 
240: 240 
241: 241 
242: 242 
243: 243 
244: 244 
245: 245 
246: 246 
247: 247 
248: 248 
249: 249 
250: 250 
251: 251 
252: 252 
253: 253 
254: 254 
255: 255 
256: 256 
257: 257 
258: 258 
259: 259 
260: 260 
261: 261 
262: 262 
263: 263 
264: 264 
265: 265 
266: 266 
267: 267 
268: 268 
269: 269 
270: ABC
271: ABC

/This one's here because Perl does this differently and PCRE can't at present/I
Capturing subpattern count = 0
No options
First char = 'T'
Need char = 't'

/(main(O)?)+/I
Capturing subpattern count = 2
No options
First char = 'm'
Need char = 'n'
    mainmain
 0: mainmain
 1: main
    mainOmain
 0: mainOmain
 1: main
 2: O

/These are all cases where Perl does it differently (nested captures)/I
Capturing subpattern count = 1
No options
First char = 'T'
Need char = 's'

/^(a(b)?)+$/I
Capturing subpattern count = 2
Options: anchored
No first char
No need char
    aba
 0: aba
 1: a
 2: b

/^(aa(bb)?)+$/I
Capturing subpattern count = 2
Options: anchored
No first char
No need char
    aabbaa
 0: aabbaa
 1: aa
 2: bb

/^(aa|aa(bb))+$/I
Capturing subpattern count = 2
Options: anchored
No first char
No need char
    aabbaa
 0: aabbaa
 1: aa
 2: bb

/^(aa(bb)??)+$/I
Capturing subpattern count = 2
Options: anchored
No first char
No need char
    aabbaa
 0: aabbaa
 1: aa
 2: bb

/^(?:aa(bb)?)+$/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char
    aabbaa
 0: aabbaa
 1: bb

/^(aa(b(b))?)+$/I
Capturing subpattern count = 3
Options: anchored
No first char
No need char
    aabbaa
 0: aabbaa
 1: aa
 2: bb
 3: b

/^(?:aa(b(b))?)+$/I
Capturing subpattern count = 2
Options: anchored
No first char
No need char
    aabbaa
 0: aabbaa
 1: bb
 2: b

/^(?:aa(b(?:b))?)+$/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char
    aabbaa
 0: aabbaa
 1: bb

/^(?:aa(bb(?:b))?)+$/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char
    aabbbaa
 0: aabbbaa
 1: bbb

/^(?:aa(b(?:bb))?)+$/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char
    aabbbaa
 0: aabbbaa
 1: bbb

/^(?:aa(?:b(b))?)+$/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char
    aabbaa
 0: aabbaa
 1: b

/^(?:aa(?:b(bb))?)+$/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char
    aabbbaa
 0: aabbbaa
 1: bb

/^(aa(b(bb))?)+$/I
Capturing subpattern count = 3
Options: anchored
No first char
No need char
    aabbbaa
 0: aabbbaa
 1: aa
 2: bbb
 3: bb

/^(aa(bb(bb))?)+$/I
Capturing subpattern count = 3
Options: anchored
No first char
No need char
    aabbbbaa
 0: aabbbbaa
 1: aa
 2: bbbb
 3: bb

/--------------------------------------------------------------------/I
Capturing subpattern count = 0
No options
First char = '-'
Need char = '-'

/#/IxDZ
------------------------------------------------------------------
        Bra
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
May match empty string
Options: extended
No first char
No need char

/a#/IxDZ
------------------------------------------------------------------
        Bra
        a
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: extended
First char = 'a'
No need char

/[\s]/DZ
------------------------------------------------------------------
        Bra
        [\x09-\x0d ]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/[\S]/DZ
------------------------------------------------------------------
        Bra
        [\x00-\x08\x0e-\x1f!-\xff] (neg)
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/a(?i)b/DZ
------------------------------------------------------------------
        Bra
        a
     /i b
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b' (caseless)
    ab
 0: ab
    aB
 0: aB
    *** Failers
No match
    AB
No match

/(a(?i)b)/DZ
------------------------------------------------------------------
        Bra
        CBra 1
        a
     /i b
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'b' (caseless)
    ab
 0: ab
 1: ab
    aB
 0: aB
 1: aB
    *** Failers
No match
    AB
No match

/   (?i)abc/IxDZ
------------------------------------------------------------------
        Bra
     /i abc
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: caseless extended
First char = 'a' (caseless)
Need char = 'c' (caseless)

/#this is a comment
  (?i)abc/IxDZ
------------------------------------------------------------------
        Bra
     /i abc
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: caseless extended
First char = 'a' (caseless)
Need char = 'c' (caseless)

/123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890/DZ
------------------------------------------------------------------
        Bra
        123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = '1'
Need char = '0'

/\Q123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890/DZ
------------------------------------------------------------------
        Bra
        123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = '1'
Need char = '0'

/\Q\E/DZ
------------------------------------------------------------------
        Bra
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
    \
 0: 

/\Q\Ex/DZ
------------------------------------------------------------------
        Bra
        x
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = 'x'
No need char

/ \Q\E/DZ
------------------------------------------------------------------
        Bra
         
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = ' '
No need char

/a\Q\E/DZ
------------------------------------------------------------------
        Bra
        a
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = 'a'
No need char
  abc
 0: a
  bca
 0: a
  bac
 0: a

/a\Q\Eb/DZ
------------------------------------------------------------------
        Bra
        ab
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'
  abc
 0: ab

/\Q\Eabc/DZ
------------------------------------------------------------------
        Bra
        abc
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'

/x*+\w/DZ
------------------------------------------------------------------
        Bra
        x*+
        \w
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char
    *** Failers
 0: F
    xxxxx
No match

/x?+/DZ
------------------------------------------------------------------
        Bra
        x?+
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char

/x++/DZ
------------------------------------------------------------------
        Bra
        x++
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = 'x'
No need char

/x{1,3}+/BZO
------------------------------------------------------------------
        Bra
        x
        x{0,2}+
        Ket
        End
------------------------------------------------------------------

/x{1,3}+/BZOi
------------------------------------------------------------------
        Bra
     /i x
     /i x{0,2}+
        Ket
        End
------------------------------------------------------------------

/[^x]{1,3}+/BZO
------------------------------------------------------------------
        Bra
        [^x]
        [^x]{0,2}+
        Ket
        End
------------------------------------------------------------------

/[^x]{1,3}+/BZOi
------------------------------------------------------------------
        Bra
     /i [^x]
     /i [^x]{0,2}+
        Ket
        End
------------------------------------------------------------------

/(x)*+/DZ
------------------------------------------------------------------
        Bra
        Braposzero
        CBraPos 1
        x
        KetRpos
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
May match empty string
No options
No first char
No need char

/^(\w++|\s++)*$/I
Capturing subpattern count = 1
May match empty string
Options: anchored
No first char
No need char
    now is the time for all good men to come to the aid of the party
 0: now is the time for all good men to come to the aid of the party
 1: party
    *** Failers
No match
    this is not a line with only words and spaces!
No match

/(\d++)(\w)/I
Capturing subpattern count = 2
No options
No first char
No need char
    12345a
 0: 12345a
 1: 12345
 2: a
    *** Failers
No match
    12345+
No match

/a++b/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'
    aaab
 0: aaab

/(a++b)/I
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'b'
    aaab
 0: aaab
 1: aaab

/(a++)b/I
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'b'
    aaab
 0: aaab
 1: aaa

/([^()]++|\([^()]*\))+/I
Capturing subpattern count = 1
No options
No first char
No need char
    ((abc(ade)ufh()()x
 0: abc(ade)ufh()()x
 1: x

/\(([^()]++|\([^()]+\))+\)/I
Capturing subpattern count = 1
No options
First char = '('
Need char = ')'
    (abc)
 0: (abc)
 1: abc
    (abc(def)xyz)
 0: (abc(def)xyz)
 1: xyz
    *** Failers
No match
    ((()aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
No match

/(abc){1,3}+/DZ
------------------------------------------------------------------
        Bra
        Once
        CBra 1
        abc
        Ket
        Brazero
        Bra
        CBra 1
        abc
        Ket
        Brazero
        CBra 1
        abc
        Ket
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'c'

/a+?+/I
Failed: nothing to repeat at offset 3

/a{2,3}?+b/I
Failed: nothing to repeat at offset 7

/(?U)a+?+/I
Failed: nothing to repeat at offset 7

/a{2,3}?+b/IU
Failed: nothing to repeat at offset 7

/x(?U)a++b/DZ
------------------------------------------------------------------
        Bra
        x
        a++
        b
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = 'x'
Need char = 'b'
    xaaaab
 0: xaaaab

/(?U)xa++b/DZ
------------------------------------------------------------------
        Bra
        x
        a++
        b
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: ungreedy
First char = 'x'
Need char = 'b'
    xaaaab
 0: xaaaab

/^((a+)(?U)([ab]+)(?-U)([bc]+)(\w*))/DZ
------------------------------------------------------------------
        Bra
        ^
        CBra 1
        CBra 2
        a+
        Ket
        CBra 3
        [ab]+?
        Ket
        CBra 4
        [bc]+
        Ket
        CBra 5
        \w*+
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 5
Options: anchored
No first char
No need char

/^x(?U)a+b/DZ
------------------------------------------------------------------
        Bra
        ^
        x
        a++
        b
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
Need char = 'b'

/^x(?U)(a+)b/DZ
------------------------------------------------------------------
        Bra
        ^
        x
        CBra 1
        a+?
        Ket
        b
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Options: anchored
No first char
Need char = 'b'

/[.x.]/I
Failed: POSIX collating elements are not supported at offset 0

/[=x=]/I
Failed: POSIX collating elements are not supported at offset 0

/[:x:]/I
Failed: POSIX named classes are supported only within a class at offset 0

/\l/I
Failed: PCRE does not support \L, \l, \N{name}, \U, or \u at offset 1

/\L/I
Failed: PCRE does not support \L, \l, \N{name}, \U, or \u at offset 1

/\N{name}/I
Failed: PCRE does not support \L, \l, \N{name}, \U, or \u at offset 1

/\u/I
Failed: PCRE does not support \L, \l, \N{name}, \U, or \u at offset 1

/\U/I
Failed: PCRE does not support \L, \l, \N{name}, \U, or \u at offset 1

/a{1,3}b/U
    ab
 0: ab

/[/I
Failed: missing terminating ] for character class at offset 1

/[a-/I
Failed: missing terminating ] for character class at offset 3

/[[:space:]/I
Failed: missing terminating ] for character class at offset 10

/[\s]/IDZ
------------------------------------------------------------------
        Bra
        [\x09-\x0d ]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/[[:space:]]/IDZ
------------------------------------------------------------------
        Bra
        [\x09-\x0d ]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/[[:space:]abcde]/IDZ
------------------------------------------------------------------
        Bra
        [\x09-\x0d a-e]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/< (?: (?(R) \d++  | [^<>]*+) | (?R)) * >/Ix
Capturing subpattern count = 0
Options: extended
First char = '<'
Need char = '>'
    <>
 0: <>
    <abcd>
 0: <abcd>
    <abc <123> hij>
 0: <abc <123> hij>
    <abc <def> hij>
 0: <def>
    <abc<>def>
 0: <abc<>def>
    <abc<>
 0: <>
    *** Failers
No match
    <abc
No match

|8J\$WE\<\.rX\+ix\[d1b\!H\#\?vV0vrK\:ZH1\=2M\>iV\;\?aPhFB\<\*vW\@QW\@sO9\}cfZA\-i\'w\%hKd6gt1UJP\,15_\#QY\$M\^Mss_U\/\]\&LK9\[5vQub\^w\[KDD\<EjmhUZ\?\.akp2dF\>qmj\;2\}YWFdYx\.Ap\]hjCPTP\(n28k\+3\;o\&WXqs\/gOXdr\$\:r\'do0\;b4c\(f_Gr\=\"\\4\)\[01T7ajQJvL\$W\~mL_sS\/4h\:x\*\[ZN\=KLs\&L5zX\/\/\>it\,o\:aU\(\;Z\>pW\&T7oP\'2K\^E\:x9\'c\[\%z\-\,64JQ5AeH_G\#KijUKghQw\^\\vea3a\?kka_G\$8\#\`\*kynsxzBLru\'\]k_\[7FrVx\}\^\=\$blx\>s\-N\%j\;D\*aZDnsw\:YKZ\%Q\.Kne9\#hP\?\+b3\(SOvL\,\^\;\&u5\@\?5C5Bhb\=m\-vEh_L15Jl\]U\)0RP6\{q\%L\^_z5E\'Dw6X\b|IDZ
------------------------------------------------------------------
        Bra
        8J$WE<.rX+ix[d1b!H#?vV0vrK:ZH1=2M>iV;?aPhFB<*vW@QW@sO9}cfZA-i'w%hKd6gt1UJP,15_#QY$M^Mss_U/]&LK9[5vQub^w[KDD<EjmhUZ?.akp2dF>qmj;2}YWFdYx.Ap]hjCPTP(n28k+3;o&WXqs/gOXdr$:r'do0;b4c(f_Gr="\4)[01T7ajQJvL$W~mL_sS/4h:x*[ZN=KLs&L5zX//>it,o:aU(;Z>pW&T7oP'2K^E:x9'c[%z-,64JQ5AeH_G#KijUKghQw^\vea3a?kka_G$8#`*kynsxzBLru']k_[7FrVx}^=$blx>s-N%j;D*aZDnsw:YKZ%Q.Kne9#hP?+b3(SOvL,^;&u5@?5C5Bhb=m-vEh_L15Jl]U)0RP6{q%L^_z5E'Dw6X
        \b
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Max lookbehind = 1
No options
First char = '8'
Need char = 'X'

|\$\<\.X\+ix\[d1b\!H\#\?vV0vrK\:ZH1\=2M\>iV\;\?aPhFB\<\*vW\@QW\@sO9\}cfZA\-i\'w\%hKd6gt1UJP\,15_\#QY\$M\^Mss_U\/\]\&LK9\[5vQub\^w\[KDD\<EjmhUZ\?\.akp2dF\>qmj\;2\}YWFdYx\.Ap\]hjCPTP\(n28k\+3\;o\&WXqs\/gOXdr\$\:r\'do0\;b4c\(f_Gr\=\"\\4\)\[01T7ajQJvL\$W\~mL_sS\/4h\:x\*\[ZN\=KLs\&L5zX\/\/\>it\,o\:aU\(\;Z\>pW\&T7oP\'2K\^E\:x9\'c\[\%z\-\,64JQ5AeH_G\#KijUKghQw\^\\vea3a\?kka_G\$8\#\`\*kynsxzBLru\'\]k_\[7FrVx\}\^\=\$blx\>s\-N\%j\;D\*aZDnsw\:YKZ\%Q\.Kne9\#hP\?\+b3\(SOvL\,\^\;\&u5\@\?5C5Bhb\=m\-vEh_L15Jl\]U\)0RP6\{q\%L\^_z5E\'Dw6X\b|IDZ
------------------------------------------------------------------
        Bra
        $<.X+ix[d1b!H#?vV0vrK:ZH1=2M>iV;?aPhFB<*vW@QW@sO9}cfZA-i'w%hKd6gt1UJP,15_#QY$M^Mss_U/]&LK9[5vQub^w[KDD<EjmhUZ?.akp2dF>qmj;2}YWFdYx.Ap]hjCPTP(n28k+3;o&WXqs/gOXdr$:r'do0;b4c(f_Gr="\4)[01T7ajQJvL$W~mL_sS/4h:x*[ZN=KLs&L5zX//>it,o:aU(;Z>pW&T7oP'2K^E:x9'c[%z-,64JQ5AeH_G#KijUKghQw^\vea3a?kka_G$8#`*kynsxzBLru']k_[7FrVx}^=$blx>s-N%j;D*aZDnsw:YKZ%Q.Kne9#hP?+b3(SOvL,^;&u5@?5C5Bhb=m-vEh_L15Jl]U)0RP6{q%L^_z5E'Dw6X
        \b
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Max lookbehind = 1
No options
First char = '$'
Need char = 'X'

/(.*)\d+\1/I
Capturing subpattern count = 1
Max back reference = 1
No options
No first char
No need char

/(.*)\d+/I
Capturing subpattern count = 1
No options
First char at start or follows newline
No need char

/(.*)\d+\1/Is
Capturing subpattern count = 1
Max back reference = 1
Options: dotall
No first char
No need char

/(.*)\d+/Is
Capturing subpattern count = 1
Options: anchored dotall
No first char
No need char

/(.*(xyz))\d+\2/I
Capturing subpattern count = 2
Max back reference = 2
No options
First char at start or follows newline
Need char = 'z'

/((.*))\d+\1/I
Capturing subpattern count = 2
Max back reference = 1
No options
No first char
No need char
    abc123bc
 0: bc123bc
 1: bc
 2: bc

/a[b]/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'

/(?=a).*/I
Capturing subpattern count = 0
May match empty string
No options
First char = 'a'
No need char

/(?=abc).xyz/IiI
Capturing subpattern count = 0
Options: caseless
First char = 'a' (caseless)
Need char = 'z' (caseless)

/(?=abc)(?i).xyz/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'z' (caseless)

/(?=a)(?=b)/I
Capturing subpattern count = 0
May match empty string
No options
First char = 'a'
No need char

/(?=.)a/I
Capturing subpattern count = 0
No options
First char = 'a'
No need char

/((?=abcda)a)/I
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'a'

/((?=abcda)ab)/I
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'b'

/()a/I
Capturing subpattern count = 1
No options
No first char
Need char = 'a'

/(?(1)ab|ac)(.)/I
Capturing subpattern count = 1
Max back reference = 1
No options
First char = 'a'
No need char

/(?(1)abz|acz)(.)/I
Capturing subpattern count = 1
Max back reference = 1
No options
First char = 'a'
Need char = 'z'

/(?(1)abz)(.)/I
Capturing subpattern count = 1
Max back reference = 1
No options
No first char
No need char

/(?(1)abz)(1)23/I
Capturing subpattern count = 1
Max back reference = 1
No options
No first char
Need char = '3'

/(a)+/I
Capturing subpattern count = 1
No options
First char = 'a'
No need char

/(a){2,3}/I
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'a'

/(a)*/I
Capturing subpattern count = 1
May match empty string
No options
No first char
No need char

/[a]/I
Capturing subpattern count = 0
No options
First char = 'a'
No need char

/[ab]/I
Capturing subpattern count = 0
No options
No first char
No need char

/[ab]/IS
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b 

/[^a]/I
Capturing subpattern count = 0
No options
No first char
No need char

/\d456/I
Capturing subpattern count = 0
No options
No first char
Need char = '6'

/\d456/IS
Capturing subpattern count = 0
No options
No first char
Need char = '6'
Subject length lower bound = 4
Starting chars: 0 1 2 3 4 5 6 7 8 9 

/a^b/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'

/^a/Im
Capturing subpattern count = 0
Options: multiline
First char at start or follows newline
Need char = 'a'
  abcde
 0: a
  xy\nabc
 0: a
  *** Failers
No match
  xyabc
No match

/c|abc/I
Capturing subpattern count = 0
No options
No first char
Need char = 'c'

/(?i)[ab]/IS
Capturing subpattern count = 0
Options: caseless
No first char
No need char
Subject length lower bound = 1
Starting chars: A B a b 

/[ab](?i)cd/IS
Capturing subpattern count = 0
No options
No first char
Need char = 'd' (caseless)
Subject length lower bound = 3
Starting chars: a b 

/abc(?C)def/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'f'
    abcdef
--->abcdef
  0 ^  ^       d
 0: abcdef
    1234abcdef
--->1234abcdef
  0     ^  ^       d
 0: abcdef
    *** Failers
No match
    abcxyz
No match
    abcxyzf
--->abcxyzf
  0 ^  ^        d
No match

/abc(?C)de(?C1)f/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'f'
    123abcdef
--->123abcdef
  0    ^  ^       d
  1    ^    ^     f
 0: abcdef

/(?C1)\dabc(?C2)def/IS
Capturing subpattern count = 0
No options
No first char
Need char = 'f'
Subject length lower bound = 7
Starting chars: 0 1 2 3 4 5 6 7 8 9 
    1234abcdef
--->1234abcdef
  1 ^              \d
  1  ^             \d
  1   ^            \d
  1    ^           \d
  2    ^   ^       d
 0: 4abcdef
    *** Failers
No match
    abcdef
No match

/(?C1)\dabc(?C2)def/ISS
Capturing subpattern count = 0
No options
No first char
Need char = 'f'
    1234abcdef
--->1234abcdef
  1 ^              \d
  1  ^             \d
  1   ^            \d
  1    ^           \d
  2    ^   ^       d
 0: 4abcdef
    *** Failers
No match
    abcdef
--->abcdef
  1 ^          \d
  1  ^         \d
  1   ^        \d
  1    ^       \d
  1     ^      \d
  1      ^     \d
No match

/(?C255)ab/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'b'

/(?C256)ab/I
Failed: number after (?C is > 255 at offset 6

/(?Cab)xx/I
Failed: closing ) for (?C expected at offset 3

/(?C12vr)x/I
Failed: closing ) for (?C expected at offset 5

/abc(?C)def/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'f'
    *** Failers
No match
    \x83\x0\x61bcdef
--->\x83\x00abcdef
  0         ^  ^       d
 0: abcdef

/(abc)(?C)de(?C1)f/I
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'f'
    123abcdef
--->123abcdef
  0    ^  ^       d
  1    ^    ^     f
 0: abcdef
 1: abc
    123abcdef\C+
Callout 0: last capture = 1
 0: <unset>
 1: abc
--->123abcdef
       ^  ^       d
Callout 1: last capture = 1
 0: <unset>
 1: abc
--->123abcdef
       ^    ^     f
 0: abcdef
 1: abc
    123abcdef\C-
 0: abcdef
 1: abc
    *** Failers
No match
    123abcdef\C!1
--->123abcdef
  0    ^  ^       d
  1    ^    ^     f
No match

/(?C0)(abc(?C1))*/I
Capturing subpattern count = 1
May match empty string
No options
No first char
No need char
    abcabcabc
--->abcabcabc
  0 ^             (abc(?C1))*
  1 ^  ^          )
  1 ^     ^       )
  1 ^        ^    )
 0: abcabcabc
 1: abc
    abcabc\C!1!3
--->abcabc
  0 ^          (abc(?C1))*
  1 ^  ^       )
  1 ^     ^    )
 0: abcabc
 1: abc
    *** Failers
--->*** Failers
  0 ^               (abc(?C1))*
 0: 
    abcabcabc\C!1!3
--->abcabcabc
  0 ^             (abc(?C1))*
  1 ^  ^          )
  1 ^     ^       )
  1 ^        ^    )
 0: abcabc
 1: abc

/(\d{3}(?C))*/I
Capturing subpattern count = 1
May match empty string
No options
No first char
No need char
    123\C+
Callout 0: last capture = -1
 0: <unset>
--->123
    ^  ^    )
 0: 123
 1: 123
    123456\C+
Callout 0: last capture = -1
 0: <unset>
--->123456
    ^  ^       )
Callout 0: last capture = 1
 0: <unset>
 1: 123
--->123456
    ^     ^    )
 0: 123456
 1: 456
    123456789\C+
Callout 0: last capture = -1
 0: <unset>
--->123456789
    ^  ^          )
Callout 0: last capture = 1
 0: <unset>
 1: 123
--->123456789
    ^     ^       )
Callout 0: last capture = 1
 0: <unset>
 1: 456
--->123456789
    ^        ^    )
 0: 123456789
 1: 789

/((xyz)(?C)p|(?C1)xyzabc)/I
Capturing subpattern count = 2
No options
First char = 'x'
No need char
    xyzabc\C+
Callout 0: last capture = 2
 0: <unset>
 1: <unset>
 2: xyz
--->xyzabc
    ^  ^       p
Callout 1: last capture = -1
 0: <unset>
--->xyzabc
    ^          x
 0: xyzabc
 1: xyzabc

/(X)((xyz)(?C)p|(?C1)xyzabc)/I
Capturing subpattern count = 3
No options
First char = 'X'
Need char = 'x'
    Xxyzabc\C+
Callout 0: last capture = 3
 0: <unset>
 1: X
 2: <unset>
 3: xyz
--->Xxyzabc
    ^   ^       p
Callout 1: last capture = 1
 0: <unset>
 1: X
--->Xxyzabc
    ^^          x
 0: Xxyzabc
 1: X
 2: xyzabc

/(?=(abc))(?C)abcdef/I
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'f'
    abcdef\C+
Callout 0: last capture = 1
 0: <unset>
 1: abc
--->abcdef
    ^          a
 0: abcdef
 1: abc

/(?!(abc)(?C1)d)(?C2)abcxyz/I
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'z'
    abcxyz\C+
Callout 1: last capture = 1
 0: <unset>
 1: abc
--->abcxyz
    ^  ^       d
Callout 2: last capture = -1
 0: <unset>
--->abcxyz
    ^          a
 0: abcxyz

/(?<=(abc)(?C))xyz/I
Capturing subpattern count = 1
Max lookbehind = 3
No options
First char = 'x'
Need char = 'z'
   abcxyz\C+
Callout 0: last capture = 1
 0: <unset>
 1: abc
--->abcxyz
       ^       )
 0: xyz
 1: abc

/a(b+)(c*)(?C1)/I
Capturing subpattern count = 2
No options
First char = 'a'
Need char = 'b'
    abbbbbccc\C*1
--->abbbbbccc
  1 ^        ^    
Callout data = 1
No match

/a(b+?)(c*?)(?C1)/I
Capturing subpattern count = 2
No options
First char = 'a'
Need char = 'b'
    abbbbbccc\C*1
--->abbbbbccc
  1 ^ ^           
Callout data = 1
  1 ^  ^          
Callout data = 1
  1 ^   ^         
Callout data = 1
  1 ^    ^        
Callout data = 1
  1 ^     ^       
Callout data = 1
  1 ^      ^      
Callout data = 1
  1 ^       ^     
Callout data = 1
  1 ^        ^    
Callout data = 1
No match

/(?C)abc/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'

/(?C)^abc/I
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/(?C)a|b/IS
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b 

/(?R)/I
Failed: recursive call could loop indefinitely at offset 3

/(a|(?R))/I
Failed: recursive call could loop indefinitely at offset 6

/(ab|(bc|(de|(?R))))/I
Failed: recursive call could loop indefinitely at offset 15

/x(ab|(bc|(de|(?R))))/I
Capturing subpattern count = 3
No options
First char = 'x'
No need char
    xab
 0: xab
 1: ab
    xbc
 0: xbc
 1: bc
 2: bc
    xde
 0: xde
 1: de
 2: de
 3: de
    xxab
 0: xxab
 1: xab
 2: xab
 3: xab
    xxxab
 0: xxxab
 1: xxab
 2: xxab
 3: xxab
    *** Failers
No match
    xyab
No match

/(ab|(bc|(de|(?1))))/I
Failed: recursive call could loop indefinitely at offset 15

/x(ab|(bc|(de|(?1)x)x)x)/I
Failed: recursive call could loop indefinitely at offset 16

/^([^()]|\((?1)*\))*$/I
Capturing subpattern count = 1
May match empty string
Options: anchored
No first char
No need char
    abc
 0: abc
 1: c
    a(b)c
 0: a(b)c
 1: c
    a(b(c))d
 0: a(b(c))d
 1: d
    *** Failers)
No match
    a(b(c)d
No match

/^>abc>([^()]|\((?1)*\))*<xyz<$/I
Capturing subpattern count = 1
Options: anchored
No first char
Need char = '<'
   >abc>123<xyz<
 0: >abc>123<xyz<
 1: 3
   >abc>1(2)3<xyz<
 0: >abc>1(2)3<xyz<
 1: 3
   >abc>(1(2)3)<xyz<
 0: >abc>(1(2)3)<xyz<
 1: (1(2)3)

/(a(?1)b)/DZ
------------------------------------------------------------------
        Bra
        CBra 1
        a
        Recurse
        b
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'b'

/(a(?1)+b)/DZ
------------------------------------------------------------------
        Bra
        CBra 1
        a
        Once
        Recurse
        KetRmax
        b
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'b'

/^(\d+|\((?1)([+*-])(?1)\)|-(?1))$/I
Capturing subpattern count = 2
Options: anchored
No first char
No need char
    12
 0: 12
 1: 12
    (((2+2)*-3)-7)
 0: (((2+2)*-3)-7)
 1: (((2+2)*-3)-7)
 2: -
    -12
 0: -12
 1: -12
    *** Failers
No match
    ((2+2)*-3)-7)
No match

/^(x(y|(?1){2})z)/I
Capturing subpattern count = 2
Options: anchored
No first char
No need char
    xyz
 0: xyz
 1: xyz
 2: y
    xxyzxyzz
 0: xxyzxyzz
 1: xxyzxyzz
 2: xyzxyz
    *** Failers
No match
    xxyzz
No match
    xxyzxyzxyzz
No match

/((< (?: (?(R) \d++  | [^<>]*+) | (?2)) * >))/Ix
Capturing subpattern count = 2
Options: extended
First char = '<'
Need char = '>'
    <>
 0: <>
 1: <>
 2: <>
    <abcd>
 0: <abcd>
 1: <abcd>
 2: <abcd>
    <abc <123> hij>
 0: <abc <123> hij>
 1: <abc <123> hij>
 2: <abc <123> hij>
    <abc <def> hij>
 0: <def>
 1: <def>
 2: <def>
    <abc<>def>
 0: <abc<>def>
 1: <abc<>def>
 2: <abc<>def>
    <abc<>
 0: <>
 1: <>
 2: <>
    *** Failers
No match
    <abc
No match

/(?1)/I
Failed: reference to non-existent subpattern at offset 3

/((?2)(abc)/I
Failed: missing ) at offset 10

/^(abc)def(?1)/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char
    abcdefabc
 0: abcdefabc
 1: abc

/^(a|b|c)=(?1)+/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char
    a=a
 0: a=a
 1: a
    a=b
 0: a=b
 1: a
    a=bc
 0: a=bc
 1: a

/^(a|b|c)=((?1))+/I
Capturing subpattern count = 2
Options: anchored
No first char
No need char
    a=a
 0: a=a
 1: a
 2: a
    a=b
 0: a=b
 1: a
 2: b
    a=bc
 0: a=bc
 1: a
 2: c

/a(?P<name1>b|c)d(?P<longername2>e)/DZ
------------------------------------------------------------------
        Bra
        a
        CBra 1
        b
        Alt
        c
        Ket
        d
        CBra 2
        e
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 2
Named capturing subpatterns:
  longername2   2
  name1         1
No options
First char = 'a'
Need char = 'e'
    abde
 0: abde
 1: b
 2: e
    acde
 0: acde
 1: c
 2: e

/(?:a(?P<c>c(?P<d>d)))(?P<a>a)/DZ
------------------------------------------------------------------
        Bra
        Bra
        a
        CBra 1
        c
        CBra 2
        d
        Ket
        Ket
        Ket
        CBra 3
        a
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 3
Named capturing subpatterns:
  a   3
  c   1
  d   2
No options
First char = 'a'
Need char = 'a'

/(?P<a>a)...(?P=a)bbb(?P>a)d/DZ
------------------------------------------------------------------
        Bra
        CBra 1
        a
        Ket
        Any
        Any
        Any
        \1
        bbb
        Recurse
        d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Max back reference = 1
Named capturing subpatterns:
  a   1
No options
First char = 'a'
Need char = 'd'

/^\W*(?:(?P<one>(?P<two>.)\W*(?P>one)\W*(?P=two)|)|(?P<three>(?P<four>.)\W*(?P>three)\W*(?P=four)|\W*.\W*))\W*$/Ii
Capturing subpattern count = 4
Max back reference = 4
Named capturing subpatterns:
  four    4
  one     1
  three   3
  two     2
May match empty string
Options: anchored caseless
No first char
No need char
    1221
 0: 1221
 1: 1221
 2: 1
    Satan, oscillate my metallic sonatas!
 0: Satan, oscillate my metallic sonatas!
 1: <unset>
 2: <unset>
 3: Satan, oscillate my metallic sonatas
 4: S
    A man, a plan, a canal: Panama!
 0: A man, a plan, a canal: Panama!
 1: <unset>
 2: <unset>
 3: A man, a plan, a canal: Panama
 4: A
    Able was I ere I saw Elba.
 0: Able was I ere I saw Elba.
 1: <unset>
 2: <unset>
 3: Able was I ere I saw Elba
 4: A
    *** Failers
No match
    The quick brown fox
No match

/((?(R)a|b))\1(?1)?/I
Capturing subpattern count = 1
Max back reference = 1
No options
No first char
No need char
  bb
 0: bb
 1: b
  bbaa
 0: bba
 1: b

/(.*)a/Is
Capturing subpattern count = 1
Options: anchored dotall
No first char
Need char = 'a'

/(.*)a\1/Is
Capturing subpattern count = 1
Max back reference = 1
Options: dotall
No first char
Need char = 'a'

/(.*)a(b)\2/Is
Capturing subpattern count = 2
Max back reference = 2
Options: anchored dotall
No first char
Need char = 'b'

/((.*)a|(.*)b)z/Is
Capturing subpattern count = 3
Options: anchored dotall
No first char
Need char = 'z'

/((.*)a|(.*)b)z\1/Is
Capturing subpattern count = 3
Max back reference = 1
Options: dotall
No first char
Need char = 'z'

/((.*)a|(.*)b)z\2/Is
Capturing subpattern count = 3
Max back reference = 2
Options: dotall
No first char
Need char = 'z'

/((.*)a|(.*)b)z\3/Is
Capturing subpattern count = 3
Max back reference = 3
Options: dotall
No first char
Need char = 'z'

/((.*)a|^(.*)b)z\3/Is
Capturing subpattern count = 3
Max back reference = 3
Options: anchored dotall
No first char
Need char = 'z'

/(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)a/Is
Capturing subpattern count = 31
May match empty string
Options: anchored dotall
No first char
No need char

/(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)a\31/Is
Capturing subpattern count = 31
Max back reference = 31
May match empty string
Options: dotall
No first char
No need char

/(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)|(.*)a\32/Is
Capturing subpattern count = 32
Max back reference = 32
May match empty string
Options: dotall
No first char
No need char

/(a)(bc)/INDZ
------------------------------------------------------------------
        Bra
        Bra
        a
        Ket
        Bra
        bc
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: no_auto_capture
First char = 'a'
Need char = 'c'
  abc
 0: abc

/(?P<one>a)(bc)/INDZ
------------------------------------------------------------------
        Bra
        CBra 1
        a
        Ket
        Bra
        bc
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Named capturing subpatterns:
  one   1
Options: no_auto_capture
First char = 'a'
Need char = 'c'
  abc
 0: abc
 1: a

/(a)(?P<named>bc)/INDZ
------------------------------------------------------------------
        Bra
        Bra
        a
        Ket
        CBra 1
        bc
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Named capturing subpatterns:
  named   1
Options: no_auto_capture
First char = 'a'
Need char = 'c'

/(a+)*zz/I
Capturing subpattern count = 1
No options
No first char
Need char = 'z'
  aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaazzbbbbbb\M
Minimum match() limit = 8
Minimum match() recursion limit = 6
 0: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaazz
 1: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
  aaaaaaaaaaaaaz\M
Minimum match() limit = 32768
Minimum match() recursion limit = 29
No match

/(aaa(?C1)bbb|ab)/I
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'b'
   aaabbb
--->aaabbb
  1 ^  ^       b
 0: aaabbb
 1: aaabbb
   aaabbb\C*0
--->aaabbb
  1 ^  ^       b
 0: aaabbb
 1: aaabbb
   aaabbb\C*1
--->aaabbb
  1 ^  ^       b
Callout data = 1
 0: ab
 1: ab
   aaabbb\C*-1
--->aaabbb
  1 ^  ^       b
Callout data = -1
No match

/ab(?P<one>cd)ef(?P<two>gh)/I
Capturing subpattern count = 2
Named capturing subpatterns:
  one   1
  two   2
No options
First char = 'a'
Need char = 'h'
    abcdefgh
 0: abcdefgh
 1: cd
 2: gh
    abcdefgh\C1\Gtwo
 0: abcdefgh
 1: cd
 2: gh
 1C cd (2)
  G gh (2) two
    abcdefgh\Cone\Ctwo
 0: abcdefgh
 1: cd
 2: gh
  C cd (2) one
  C gh (2) two
    abcdefgh\Cthree
no parentheses with name "three"
 0: abcdefgh
 1: cd
 2: gh
copy substring three failed -7

/(?P<Tes>)(?P<Test>)/DZ
------------------------------------------------------------------
        Bra
        CBra 1
        Ket
        CBra 2
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 2
Named capturing subpatterns:
  Tes    1
  Test   2
May match empty string
No options
No first char
No need char

/(?P<Test>)(?P<Tes>)/DZ
------------------------------------------------------------------
        Bra
        CBra 1
        Ket
        CBra 2
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 2
Named capturing subpatterns:
  Tes    2
  Test   1
May match empty string
No options
No first char
No need char

/(?P<Z>zz)(?P<A>aa)/I
Capturing subpattern count = 2
Named capturing subpatterns:
  A   2
  Z   1
No options
First char = 'z'
Need char = 'a'
    zzaa\CZ
 0: zzaa
 1: zz
 2: aa
  C zz (2) Z
    zzaa\CA
 0: zzaa
 1: zz
 2: aa
  C aa (2) A

/(?P<x>eks)(?P<x>eccs)/I
Failed: two named subpatterns have the same name at offset 15

/(?P<abc>abc(?P<def>def)(?P<abc>xyz))/I
Failed: two named subpatterns have the same name at offset 30

"\[((?P<elem>\d+)(,(?P>elem))*)\]"I
Capturing subpattern count = 3
Named capturing subpatterns:
  elem   2
No options
First char = '['
Need char = ']'
    [10,20,30,5,5,4,4,2,43,23,4234]
 0: [10,20,30,5,5,4,4,2,43,23,4234]
 1: 10,20,30,5,5,4,4,2,43,23,4234
 2: 10
 3: ,4234
    *** Failers
No match
    []
No match

"\[((?P<elem>\d+)(,(?P>elem))*)?\]"I
Capturing subpattern count = 3
Named capturing subpatterns:
  elem   2
No options
First char = '['
Need char = ']'
    [10,20,30,5,5,4,4,2,43,23,4234]
 0: [10,20,30,5,5,4,4,2,43,23,4234]
 1: 10,20,30,5,5,4,4,2,43,23,4234
 2: 10
 3: ,4234
    []
 0: []

/(a(b(?2)c))?/DZ
------------------------------------------------------------------
        Bra
        Brazero
        CBra 1
        a
        CBra 2
        b
        Recurse
        c
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 2
May match empty string
No options
No first char
No need char

/(a(b(?2)c))*/DZ
------------------------------------------------------------------
        Bra
        Brazero
        CBra 1
        a
        CBra 2
        b
        Recurse
        c
        Ket
        KetRmax
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 2
May match empty string
No options
No first char
No need char

/(a(b(?2)c)){0,2}/DZ
------------------------------------------------------------------
        Bra
        Brazero
        Bra
        CBra 1
        a
        CBra 2
        b
        Recurse
        c
        Ket
        Ket
        Brazero
        CBra 1
        a
        CBra 2
        b
        Recurse
        c
        Ket
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 2
May match empty string
No options
No first char
No need char

/[ab]{1}+/DZ
------------------------------------------------------------------
        Bra
        [ab]{1,1}+
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/((w\/|-|with)*(free|immediate)*.*?shipping\s*[!.-]*)/Ii
Capturing subpattern count = 3
Options: caseless
No first char
Need char = 'g' (caseless)
     Baby Bjorn Active Carrier - With free SHIPPING!!
 0: Baby Bjorn Active Carrier - With free SHIPPING!!
 1: Baby Bjorn Active Carrier - With free SHIPPING!!

/((w\/|-|with)*(free|immediate)*.*?shipping\s*[!.-]*)/IiS
Capturing subpattern count = 3
Options: caseless
No first char
Need char = 'g' (caseless)
Subject length lower bound = 8
No starting char list
     Baby Bjorn Active Carrier - With free SHIPPING!!
 0: Baby Bjorn Active Carrier - With free SHIPPING!!
 1: Baby Bjorn Active Carrier - With free SHIPPING!!

/a*.*b/ISDZ
------------------------------------------------------------------
        Bra
        a*
        Any*
        b
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
Need char = 'b'
Subject length lower bound = 1
No starting char list

/(a|b)*.?c/ISDZ
------------------------------------------------------------------
        Bra
        Brazero
        CBra 1
        a
        Alt
        b
        KetRmax
        Any?
        c
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
No options
No first char
Need char = 'c'
Subject length lower bound = 1
No starting char list

/abc(?C255)de(?C)f/DZ
------------------------------------------------------------------
        Bra
        abc
        Callout 255 10 1
        de
        Callout 0 16 1
        f
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'f'

/abcde/ICDZ
------------------------------------------------------------------
        Bra
        Callout 255 0 1
        a
        Callout 255 1 1
        b
        Callout 255 2 1
        c
        Callout 255 3 1
        d
        Callout 255 4 1
        e
        Callout 255 5 0
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options:
First char = 'a'
Need char = 'e'
  abcde
--->abcde
 +0 ^         a
 +1 ^^        b
 +2 ^ ^       c
 +3 ^  ^      d
 +4 ^   ^     e
 +5 ^    ^    
 0: abcde
  abcdfe
--->abcdfe
 +0 ^          a
 +1 ^^         b
 +2 ^ ^        c
 +3 ^  ^       d
 +4 ^   ^      e
No match

/a*b/ICDZS
------------------------------------------------------------------
        Bra
        Callout 255 0 2
        a*+
        Callout 255 2 1
        b
        Callout 255 3 0
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options:
No first char
Need char = 'b'
Subject length lower bound = 1
Starting chars: a b 
  ab
--->ab
 +0 ^      a*
 +2 ^^     b
 +3 ^ ^    
 0: ab
  aaaab
--->aaaab
 +0 ^         a*
 +2 ^   ^     b
 +3 ^    ^    
 0: aaaab
  aaaacb
--->aaaacb
 +0 ^          a*
 +2 ^   ^      b
 +0  ^         a*
 +2  ^  ^      b
 +0   ^        a*
 +2   ^ ^      b
 +0    ^       a*
 +2    ^^      b
 +0      ^     a*
 +2      ^     b
 +3      ^^    
 0: b

/a*b/ICDZSS
------------------------------------------------------------------
        Bra
        Callout 255 0 2
        a*+
        Callout 255 2 1
        b
        Callout 255 3 0
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options:
No first char
Need char = 'b'
  ab
--->ab
 +0 ^      a*
 +2 ^^     b
 +3 ^ ^    
 0: ab
  aaaab
--->aaaab
 +0 ^         a*
 +2 ^   ^     b
 +3 ^    ^    
 0: aaaab
  aaaacb
--->aaaacb
 +0 ^          a*
 +2 ^   ^      b
 +0  ^         a*
 +2  ^  ^      b
 +0   ^        a*
 +2   ^ ^      b
 +0    ^       a*
 +2    ^^      b
 +0     ^      a*
 +2     ^      b
 +0      ^     a*
 +2      ^     b
 +3      ^^    
 0: b

/a+b/ICDZ
------------------------------------------------------------------
        Bra
        Callout 255 0 2
        a++
        Callout 255 2 1
        b
        Callout 255 3 0
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options:
First char = 'a'
Need char = 'b'
  ab
--->ab
 +0 ^      a+
 +2 ^^     b
 +3 ^ ^    
 0: ab
  aaaab
--->aaaab
 +0 ^         a+
 +2 ^   ^     b
 +3 ^    ^    
 0: aaaab
  aaaacb
--->aaaacb
 +0 ^          a+
 +2 ^   ^      b
 +0  ^         a+
 +2  ^  ^      b
 +0   ^        a+
 +2   ^ ^      b
 +0    ^       a+
 +2    ^^      b
No match

/(abc|def)x/ICDZS
------------------------------------------------------------------
        Bra
        Callout 255 0 9
        CBra 1
        Callout 255 1 1
        a
        Callout 255 2 1
        b
        Callout 255 3 1
        c
        Callout 255 4 0
        Alt
        Callout 255 5 1
        d
        Callout 255 6 1
        e
        Callout 255 7 1
        f
        Callout 255 8 0
        Ket
        Callout 255 9 1
        x
        Callout 255 10 0
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Options:
No first char
Need char = 'x'
Subject length lower bound = 4
Starting chars: a d 
  abcx
--->abcx
 +0 ^        (abc|def)
 +1 ^        a
 +2 ^^       b
 +3 ^ ^      c
 +4 ^  ^     |
 +9 ^  ^     x
+10 ^   ^    
 0: abcx
 1: abc
  defx
--->defx
 +0 ^        (abc|def)
 +1 ^        a
 +5 ^        d
 +6 ^^       e
 +7 ^ ^      f
 +8 ^  ^     )
 +9 ^  ^     x
+10 ^   ^    
 0: defx
 1: def
  ** Failers 
No match
  abcdefzx
--->abcdefzx
 +0 ^            (abc|def)
 +1 ^            a
 +2 ^^           b
 +3 ^ ^          c
 +4 ^  ^         |
 +9 ^  ^         x
 +5 ^            d
 +0    ^         (abc|def)
 +1    ^         a
 +5    ^         d
 +6    ^^        e
 +7    ^ ^       f
 +8    ^  ^      )
 +9    ^  ^      x
No match

/(abc|def)x/ICDZSS
------------------------------------------------------------------
        Bra
        Callout 255 0 9
        CBra 1
        Callout 255 1 1
        a
        Callout 255 2 1
        b
        Callout 255 3 1
        c
        Callout 255 4 0
        Alt
        Callout 255 5 1
        d
        Callout 255 6 1
        e
        Callout 255 7 1
        f
        Callout 255 8 0
        Ket
        Callout 255 9 1
        x
        Callout 255 10 0
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Options:
No first char
Need char = 'x'
  abcx
--->abcx
 +0 ^        (abc|def)
 +1 ^        a
 +2 ^^       b
 +3 ^ ^      c
 +4 ^  ^     |
 +9 ^  ^     x
+10 ^   ^    
 0: abcx
 1: abc
  defx
--->defx
 +0 ^        (abc|def)
 +1 ^        a
 +5 ^        d
 +6 ^^       e
 +7 ^ ^      f
 +8 ^  ^     )
 +9 ^  ^     x
+10 ^   ^    
 0: defx
 1: def
  ** Failers 
No match
  abcdefzx
--->abcdefzx
 +0 ^            (abc|def)
 +1 ^            a
 +2 ^^           b
 +3 ^ ^          c
 +4 ^  ^         |
 +9 ^  ^         x
 +5 ^            d
 +0  ^           (abc|def)
 +1  ^           a
 +5  ^           d
 +0   ^          (abc|def)
 +1   ^          a
 +5   ^          d
 +0    ^         (abc|def)
 +1    ^         a
 +5    ^         d
 +6    ^^        e
 +7    ^ ^       f
 +8    ^  ^      )
 +9    ^  ^      x
 +0     ^        (abc|def)
 +1     ^        a
 +5     ^        d
 +0      ^       (abc|def)
 +1      ^       a
 +5      ^       d
 +0       ^      (abc|def)
 +1       ^      a
 +5       ^      d
 +0        ^     (abc|def)
 +1        ^     a
 +5        ^     d
No match

/(ab|cd){3,4}/IC
Capturing subpattern count = 1
Options:
No first char
No need char
  ababab
--->ababab
 +0 ^          (ab|cd){3,4}
 +1 ^          a
 +2 ^^         b
 +3 ^ ^        |
 +1 ^ ^        a
 +2 ^  ^       b
 +3 ^   ^      |
 +1 ^   ^      a
 +2 ^    ^     b
 +3 ^     ^    |
 +1 ^     ^    a
 +4 ^     ^    c
+12 ^     ^    
 0: ababab
 1: ab
  abcdabcd
--->abcdabcd
 +0 ^            (ab|cd){3,4}
 +1 ^            a
 +2 ^^           b
 +3 ^ ^          |
 +1 ^ ^          a
 +4 ^ ^          c
 +5 ^  ^         d
 +6 ^   ^        )
 +1 ^   ^        a
 +2 ^    ^       b
 +3 ^     ^      |
 +1 ^     ^      a
 +4 ^     ^      c
 +5 ^      ^     d
 +6 ^       ^    )
+12 ^       ^    
 0: abcdabcd
 1: cd
  abcdcdcdcdcd
--->abcdcdcdcdcd
 +0 ^                (ab|cd){3,4}
 +1 ^                a
 +2 ^^               b
 +3 ^ ^              |
 +1 ^ ^              a
 +4 ^ ^              c
 +5 ^  ^             d
 +6 ^   ^            )
 +1 ^   ^            a
 +4 ^   ^            c
 +5 ^    ^           d
 +6 ^     ^          )
 +1 ^     ^          a
 +4 ^     ^          c
 +5 ^      ^         d
 +6 ^       ^        )
+12 ^       ^        
 0: abcdcdcd
 1: cd

/([ab]{,4}c|xy)/ICDZS
------------------------------------------------------------------
        Bra
        Callout 255 0 14
        CBra 1
        Callout 255 1 4
        [ab]
        Callout 255 5 1
        {
        Callout 255 6 1
        ,
        Callout 255 7 1
        4
        Callout 255 8 1
        }
        Callout 255 9 1
        c
        Callout 255 10 0
        Alt
        Callout 255 11 1
        x
        Callout 255 12 1
        y
        Callout 255 13 0
        Ket
        Callout 255 14 0
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Options:
No first char
No need char
Subject length lower bound = 2
Starting chars: a b x 
    Note: that { does NOT introduce a quantifier
--->Note: that { does NOT introduce a quantifier
 +0         ^                                        ([ab]{,4}c|xy)
 +1         ^                                        [ab]
 +5         ^^                                       {
+11         ^                                        x
 +0                                 ^                ([ab]{,4}c|xy)
 +1                                 ^                [ab]
 +5                                 ^^               {
+11                                 ^                x
 +0                                     ^            ([ab]{,4}c|xy)
 +1                                     ^            [ab]
 +5                                     ^^           {
+11                                     ^            x
No match

/([ab]{,4}c|xy)/ICDZSS
------------------------------------------------------------------
        Bra
        Callout 255 0 14
        CBra 1
        Callout 255 1 4
        [ab]
        Callout 255 5 1
        {
        Callout 255 6 1
        ,
        Callout 255 7 1
        4
        Callout 255 8 1
        }
        Callout 255 9 1
        c
        Callout 255 10 0
        Alt
        Callout 255 11 1
        x
        Callout 255 12 1
        y
        Callout 255 13 0
        Ket
        Callout 255 14 0
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Options:
No first char
No need char
    Note: that { does NOT introduce a quantifier
--->Note: that { does NOT introduce a quantifier
 +0 ^                                                ([ab]{,4}c|xy)
 +1 ^                                                [ab]
+11 ^                                                x
 +0  ^                                               ([ab]{,4}c|xy)
 +1  ^                                               [ab]
+11  ^                                               x
 +0   ^                                              ([ab]{,4}c|xy)
 +1   ^                                              [ab]
+11   ^                                              x
 +0    ^                                             ([ab]{,4}c|xy)
 +1    ^                                             [ab]
+11    ^                                             x
 +0     ^                                            ([ab]{,4}c|xy)
 +1     ^                                            [ab]
+11     ^                                            x
 +0      ^                                           ([ab]{,4}c|xy)
 +1      ^                                           [ab]
+11      ^                                           x
 +0       ^                                          ([ab]{,4}c|xy)
 +1       ^                                          [ab]
+11       ^                                          x
 +0        ^                                         ([ab]{,4}c|xy)
 +1        ^                                         [ab]
+11        ^                                         x
 +0         ^                                        ([ab]{,4}c|xy)
 +1         ^                                        [ab]
 +5         ^^                                       {
+11         ^                                        x
 +0          ^                                       ([ab]{,4}c|xy)
 +1          ^                                       [ab]
+11          ^                                       x
 +0           ^                                      ([ab]{,4}c|xy)
 +1           ^                                      [ab]
+11           ^                                      x
 +0            ^                                     ([ab]{,4}c|xy)
 +1            ^                                     [ab]
+11            ^                                     x
 +0             ^                                    ([ab]{,4}c|xy)
 +1             ^                                    [ab]
+11             ^                                    x
 +0              ^                                   ([ab]{,4}c|xy)
 +1              ^                                   [ab]
+11              ^                                   x
 +0               ^                                  ([ab]{,4}c|xy)
 +1               ^                                  [ab]
+11               ^                                  x
 +0                ^                                 ([ab]{,4}c|xy)
 +1                ^                                 [ab]
+11                ^                                 x
 +0                 ^                                ([ab]{,4}c|xy)
 +1                 ^                                [ab]
+11                 ^                                x
 +0                  ^                               ([ab]{,4}c|xy)
 +1                  ^                               [ab]
+11                  ^                               x
 +0                   ^                              ([ab]{,4}c|xy)
 +1                   ^                              [ab]
+11                   ^                              x
 +0                    ^                             ([ab]{,4}c|xy)
 +1                    ^                             [ab]
+11                    ^                             x
 +0                     ^                            ([ab]{,4}c|xy)
 +1                     ^                            [ab]
+11                     ^                            x
 +0                      ^                           ([ab]{,4}c|xy)
 +1                      ^                           [ab]
+11                      ^                           x
 +0                       ^                          ([ab]{,4}c|xy)
 +1                       ^                          [ab]
+11                       ^                          x
 +0                        ^                         ([ab]{,4}c|xy)
 +1                        ^                         [ab]
+11                        ^                         x
 +0                         ^                        ([ab]{,4}c|xy)
 +1                         ^                        [ab]
+11                         ^                        x
 +0                          ^                       ([ab]{,4}c|xy)
 +1                          ^                       [ab]
+11                          ^                       x
 +0                           ^                      ([ab]{,4}c|xy)
 +1                           ^                      [ab]
+11                           ^                      x
 +0                            ^                     ([ab]{,4}c|xy)
 +1                            ^                     [ab]
+11                            ^                     x
 +0                             ^                    ([ab]{,4}c|xy)
 +1                             ^                    [ab]
+11                             ^                    x
 +0                              ^                   ([ab]{,4}c|xy)
 +1                              ^                   [ab]
+11                              ^                   x
 +0                               ^                  ([ab]{,4}c|xy)
 +1                               ^                  [ab]
+11                               ^                  x
 +0                                ^                 ([ab]{,4}c|xy)
 +1                                ^                 [ab]
+11                                ^                 x
 +0                                 ^                ([ab]{,4}c|xy)
 +1                                 ^                [ab]
 +5                                 ^^               {
+11                                 ^                x
 +0                                  ^               ([ab]{,4}c|xy)
 +1                                  ^               [ab]
+11                                  ^               x
 +0                                   ^              ([ab]{,4}c|xy)
 +1                                   ^              [ab]
+11                                   ^              x
 +0                                    ^             ([ab]{,4}c|xy)
 +1                                    ^             [ab]
+11                                    ^             x
 +0                                     ^            ([ab]{,4}c|xy)
 +1                                     ^            [ab]
 +5                                     ^^           {
+11                                     ^            x
 +0                                      ^           ([ab]{,4}c|xy)
 +1                                      ^           [ab]
+11                                      ^           x
 +0                                       ^          ([ab]{,4}c|xy)
 +1                                       ^          [ab]
+11                                       ^          x
 +0                                        ^         ([ab]{,4}c|xy)
 +1                                        ^         [ab]
+11                                        ^         x
 +0                                         ^        ([ab]{,4}c|xy)
 +1                                         ^        [ab]
+11                                         ^        x
 +0                                          ^       ([ab]{,4}c|xy)
 +1                                          ^       [ab]
+11                                          ^       x
 +0                                           ^      ([ab]{,4}c|xy)
 +1                                           ^      [ab]
+11                                           ^      x
 +0                                            ^     ([ab]{,4}c|xy)
 +1                                            ^     [ab]
+11                                            ^     x
 +0                                             ^    ([ab]{,4}c|xy)
 +1                                             ^    [ab]
+11                                             ^    x
No match

/([ab]{1,4}c|xy){4,5}?123/ICDZ
------------------------------------------------------------------
        Bra
        Callout 255 0 21
        CBra 1
        Callout 255 1 9
        [ab]{1,4}+
        Callout 255 10 1
        c
        Callout 255 11 0
        Alt
        Callout 255 12 1
        x
        Callout 255 13 1
        y
        Callout 255 14 0
        Ket
        CBra 1
        Callout 255 1 9
        [ab]{1,4}+
        Callout 255 10 1
        c
        Callout 255 11 0
        Alt
        Callout 255 12 1
        x
        Callout 255 13 1
        y
        Callout 255 14 0
        Ket
        CBra 1
        Callout 255 1 9
        [ab]{1,4}+
        Callout 255 10 1
        c
        Callout 255 11 0
        Alt
        Callout 255 12 1
        x
        Callout 255 13 1
        y
        Callout 255 14 0
        Ket
        CBra 1
        Callout 255 1 9
        [ab]{1,4}+
        Callout 255 10 1
        c
        Callout 255 11 0
        Alt
        Callout 255 12 1
        x
        Callout 255 13 1
        y
        Callout 255 14 0
        Ket
        Braminzero
        CBra 1
        Callout 255 1 9
        [ab]{1,4}+
        Callout 255 10 1
        c
        Callout 255 11 0
        Alt
        Callout 255 12 1
        x
        Callout 255 13 1
        y
        Callout 255 14 0
        Ket
        Callout 255 21 1
        1
        Callout 255 22 1
        2
        Callout 255 23 1
        3
        Callout 255 24 0
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
Options:
No first char
Need char = '3'
    aacaacaacaacaac123
--->aacaacaacaacaac123
 +0 ^                      ([ab]{1,4}c|xy){4,5}?
 +1 ^                      [ab]{1,4}
+10 ^ ^                    c
+11 ^  ^                   |
 +1 ^  ^                   [ab]{1,4}
+10 ^    ^                 c
+11 ^     ^                |
 +1 ^     ^                [ab]{1,4}
+10 ^       ^              c
+11 ^        ^             |
 +1 ^        ^             [ab]{1,4}
+10 ^          ^           c
+11 ^           ^          |
+21 ^           ^          1
 +1 ^           ^          [ab]{1,4}
+10 ^             ^        c
+11 ^              ^       |
+21 ^              ^       1
+22 ^               ^      2
+23 ^                ^     3
+24 ^                 ^    
 0: aacaacaacaacaac123
 1: aac

/\b.*/I
Capturing subpattern count = 0
Max lookbehind = 1
May match empty string
No options
No first char
No need char
  ab cd\>1
 0:  cd

/\b.*/Is
Capturing subpattern count = 0
Max lookbehind = 1
May match empty string
Options: dotall
No first char
No need char
  ab cd\>1
 0:  cd

/(?!.bcd).*/I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
  Xbcd12345
 0: bcd12345

/abcde/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'e'
    ab\P
Partial match: ab
    abc\P
Partial match: abc
    abcd\P
Partial match: abcd
    abcde\P
 0: abcde
    the quick brown abc\P
Partial match: abc
    ** Failers\P
No match
    the quick brown abxyz fox\P
No match

"^(0?[1-9]|[12][0-9]|3[01])/(0?[1-9]|1[012])/(20)?\d\d$"I
Capturing subpattern count = 3
Options: anchored
No first char
Need char = '/'
    13/05/04\P
 0: 13/05/04
 1: 13
 2: 05
    13/5/2004\P
 0: 13/5/2004
 1: 13
 2: 5
 3: 20
    02/05/09\P
 0: 02/05/09
 1: 02
 2: 05
    1\P
Partial match: 1
    1/2\P
Partial match: 1/2
    1/2/0\P
Partial match: 1/2/0
    1/2/04\P
 0: 1/2/04
 1: 1
 2: 2
    0\P
Partial match: 0
    02/\P
Partial match: 02/
    02/0\P
Partial match: 02/0
    02/1\P
Partial match: 02/1
    ** Failers\P
No match
    \P
No match
    123\P
No match
    33/4/04\P
No match
    3/13/04\P
No match
    0/1/2003\P
No match
    0/\P
No match
    02/0/\P
No match
    02/13\P
No match

/0{0,2}ABC/I
Capturing subpattern count = 0
No options
No first char
Need char = 'C'

/\d{3,}ABC/I
Capturing subpattern count = 0
No options
No first char
Need char = 'C'

/\d*ABC/I
Capturing subpattern count = 0
No options
No first char
Need char = 'C'

/[abc]+DE/I
Capturing subpattern count = 0
No options
No first char
Need char = 'E'

/[abc]?123/I
Capturing subpattern count = 0
No options
No first char
Need char = '3'
    123\P
 0: 123
    a\P
Partial match: a
    b\P
Partial match: b
    c\P
Partial match: c
    c12\P
Partial match: c12
    c123\P
 0: c123

/^(?:\d){3,5}X/I
Capturing subpattern count = 0
Options: anchored
No first char
Need char = 'X'
    1\P
Partial match: 1
    123\P
Partial match: 123
    123X
 0: 123X
    1234\P
Partial match: 1234
    1234X
 0: 1234X
    12345\P
Partial match: 12345
    12345X
 0: 12345X
    *** Failers
No match
    1X
No match
    123456\P
No match

//KF>/dev/null
Compiled pattern written to /dev/null
Study data written to /dev/null

/abc/IS>testsavedregex
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'
Subject length lower bound = 3
No starting char list
Compiled pattern written to testsavedregex
Study data written to testsavedregex
<testsavedregex
Compiled pattern loaded from testsavedregex
Study data loaded from testsavedregex
    abc
 0: abc
    ** Failers
No match
    bca
No match

/abc/ISS>testsavedregex
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'
Compiled pattern written to testsavedregex
<testsavedregex
Compiled pattern loaded from testsavedregex
No study data
    abc
 0: abc
    ** Failers
No match
    bca
No match

/abc/IFS>testsavedregex
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'
Subject length lower bound = 3
No starting char list
Compiled pattern written to testsavedregex
Study data written to testsavedregex
<testsavedregex
Compiled pattern (byte-inverted) loaded from testsavedregex
Study data loaded from testsavedregex
    abc
 0: abc
    ** Failers
No match
    bca
No match

/abc/IFSS>testsavedregex
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'
Compiled pattern written to testsavedregex
<testsavedregex
Compiled pattern (byte-inverted) loaded from testsavedregex
No study data
    abc
 0: abc
    ** Failers
No match
    bca
No match

/(a|b)/IS>testsavedregex
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b 
Compiled pattern written to testsavedregex
Study data written to testsavedregex
<testsavedregex
Compiled pattern loaded from testsavedregex
Study data loaded from testsavedregex
    abc
 0: a
 1: a
    ** Failers
 0: a
 1: a
    def
No match

/(a|b)/ISS>testsavedregex
Capturing subpattern count = 1
No options
No first char
No need char
Compiled pattern written to testsavedregex
<testsavedregex
Compiled pattern loaded from testsavedregex
No study data
    abc
 0: a
 1: a
    ** Failers
 0: a
 1: a
    def
No match

/(a|b)/ISF>testsavedregex
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b 
Compiled pattern written to testsavedregex
Study data written to testsavedregex
<testsavedregex
Compiled pattern (byte-inverted) loaded from testsavedregex
Study data loaded from testsavedregex
    abc
 0: a
 1: a
    ** Failers
 0: a
 1: a
    def
No match

/(a|b)/ISSF>testsavedregex
Capturing subpattern count = 1
No options
No first char
No need char
Compiled pattern written to testsavedregex
<testsavedregex
Compiled pattern (byte-inverted) loaded from testsavedregex
No study data
    abc
 0: a
 1: a
    ** Failers
 0: a
 1: a
    def
No match

~<(\w+)/?>(.)*</(\1)>~smgI
Capturing subpattern count = 3
Max back reference = 1
Options: multiline dotall
First char = '<'
Need char = '>'
    \J1024<!DOCTYPE seite SYSTEM "http://www.lco.lineas.de/xmlCms.dtd">\n<seite>\n<dokumenteninformation>\n<seitentitel>Partner der LCO</seitentitel>\n<sprache>de</sprache>\n<seitenbeschreibung>Partner der LINEAS Consulting\nGmbH</seitenbeschreibung>\n<schluesselworte>LINEAS Consulting GmbH Hamburg\nPartnerfirmen</schluesselworte>\n<revisit>30 days</revisit>\n<robots>index,follow</robots>\n<menueinformation>\n<aktiv>ja</aktiv>\n<menueposition>3</menueposition>\n<menuetext>Partner</menuetext>\n</menueinformation>\n<lastedited>\n<autor>LCO</autor>\n<firma>LINEAS Consulting</firma>\n<datum>15.10.2003</datum>\n</lastedited>\n</dokumenteninformation>\n<inhalt>\n\n<absatzueberschrift>Die Partnerfirmen der LINEAS Consulting\nGmbH</absatzueberschrift>\n\n<absatz><link ziel="http://www.ca.com/" zielfenster="_blank">\n<bild name="logo_ca.gif" rahmen="no"/></link> <link\nziel="http://www.ey.com/" zielfenster="_blank"><bild\nname="logo_euy.gif" rahmen="no"/></link>\n</absatz>\n\n<absatz><link ziel="http://www.cisco.de/" zielfenster="_blank">\n<bild name="logo_cisco.gif" rahmen="ja"/></link></absatz>\n\n<absatz><link ziel="http://www.atelion.de/"\nzielfenster="_blank"><bild\nname="logo_atelion.gif" rahmen="no"/></link>\n</absatz>\n\n<absatz><link ziel="http://www.line-information.de/"\nzielfenster="_blank">\n<bild name="logo_line_information.gif" rahmen="no"/></link>\n</absatz>\n\n<absatz><bild name="logo_aw.gif" rahmen="no"/></absatz>\n\n<absatz><link ziel="http://www.incognis.de/"\nzielfenster="_blank"><bild\nname="logo_incognis.gif" rahmen="no"/></link></absatz>\n\n<absatz><link ziel="http://www.addcraft.com/"\nzielfenster="_blank"><bild\nname="logo_addcraft.gif" rahmen="no"/></link></absatz>\n\n<absatz><link ziel="http://www.comendo.com/"\nzielfenster="_blank"><bild\nname="logo_comendo.gif" rahmen="no"/></link></absatz>\n\n</inhalt>\n</seite>
 0: <seite>\x0a<dokumenteninformation>\x0a<seitentitel>Partner der LCO</seitentitel>\x0a<sprache>de</sprache>\x0a<seitenbeschreibung>Partner der LINEAS Consulting\x0aGmbH</seitenbeschreibung>\x0a<schluesselworte>LINEAS Consulting GmbH Hamburg\x0aPartnerfirmen</schluesselworte>\x0a<revisit>30 days</revisit>\x0a<robots>index,follow</robots>\x0a<menueinformation>\x0a<aktiv>ja</aktiv>\x0a<menueposition>3</menueposition>\x0a<menuetext>Partner</menuetext>\x0a</menueinformation>\x0a<lastedited>\x0a<autor>LCO</autor>\x0a<firma>LINEAS Consulting</firma>\x0a<datum>15.10.2003</datum>\x0a</lastedited>\x0a</dokumenteninformation>\x0a<inhalt>\x0a\x0a<absatzueberschrift>Die Partnerfirmen der LINEAS Consulting\x0aGmbH</absatzueberschrift>\x0a\x0a<absatz><link ziel="http://www.ca.com/" zielfenster="_blank">\x0a<bild name="logo_ca.gif" rahmen="no"/></link> <link\x0aziel="http://www.ey.com/" zielfenster="_blank"><bild\x0aname="logo_euy.gif" rahmen="no"/></link>\x0a</absatz>\x0a\x0a<absatz><link ziel="http://www.cisco.de/" zielfenster="_blank">\x0a<bild name="logo_cisco.gif" rahmen="ja"/></link></absatz>\x0a\x0a<absatz><link ziel="http://www.atelion.de/"\x0azielfenster="_blank"><bild\x0aname="logo_atelion.gif" rahmen="no"/></link>\x0a</absatz>\x0a\x0a<absatz><link ziel="http://www.line-information.de/"\x0azielfenster="_blank">\x0a<bild name="logo_line_information.gif" rahmen="no"/></link>\x0a</absatz>\x0a\x0a<absatz><bild name="logo_aw.gif" rahmen="no"/></absatz>\x0a\x0a<absatz><link ziel="http://www.incognis.de/"\x0azielfenster="_blank"><bild\x0aname="logo_incognis.gif" rahmen="no"/></link></absatz>\x0a\x0a<absatz><link ziel="http://www.addcraft.com/"\x0azielfenster="_blank"><bild\x0aname="logo_addcraft.gif" rahmen="no"/></link></absatz>\x0a\x0a<absatz><link ziel="http://www.comendo.com/"\x0azielfenster="_blank"><bild\x0aname="logo_comendo.gif" rahmen="no"/></link></absatz>\x0a\x0a</inhalt>\x0a</seite>
 1: seite
 2: \x0a
 3: seite

/^a/IF
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/line\nbreak/I
Capturing subpattern count = 0
Contains explicit CR or LF match
No options
First char = 'l'
Need char = 'k'
    this is a line\nbreak
 0: line\x0abreak
    line one\nthis is a line\nbreak in the second line
 0: line\x0abreak

/line\nbreak/If
Capturing subpattern count = 0
Contains explicit CR or LF match
Options: firstline
First char = 'l'
Need char = 'k'
    this is a line\nbreak
 0: line\x0abreak
    ** Failers
No match
    line one\nthis is a line\nbreak in the second line
No match

/line\nbreak/Imf
Capturing subpattern count = 0
Contains explicit CR or LF match
Options: multiline firstline
First char = 'l'
Need char = 'k'
    this is a line\nbreak
 0: line\x0abreak
    ** Failers
No match
    line one\nthis is a line\nbreak in the second line
No match

/(?i)(?-i)AbCd/I
Capturing subpattern count = 0
No options
First char = 'A'
Need char = 'd'
    AbCd
 0: AbCd
    ** Failers
No match
    abcd
No match

/a{11111111111111111111}/I
Failed: number too big in {} quantifier at offset 8

/(){64294967295}/I
Failed: number too big in {} quantifier at offset 9

/(){2,4294967295}/I
Failed: number too big in {} quantifier at offset 11

"(?i:a)(?i:b)(?i:c)(?i:d)(?i:e)(?i:f)(?i:g)(?i:h)(?i:i)(?i:j)(k)(?i:l)A\1B"I
Capturing subpattern count = 1
Max back reference = 1
No options
First char = 'a' (caseless)
Need char = 'B'
    abcdefghijklAkB
 0: abcdefghijklAkB
 1: k

"(?P<n0>a)(?P<n1>b)(?P<n2>c)(?P<n3>d)(?P<n4>e)(?P<n5>f)(?P<n6>g)(?P<n7>h)(?P<n8>i)(?P<n9>j)(?P<n10>k)(?P<n11>l)A\11B"I
Capturing subpattern count = 12
Max back reference = 11
Named capturing subpatterns:
  n0    1
  n1    2
  n10  11
  n11  12
  n2    3
  n3    4
  n4    5
  n5    6
  n6    7
  n7    8
  n8    9
  n9   10
No options
First char = 'a'
Need char = 'B'
    abcdefghijklAkB
 0: abcdefghijklAkB
 1: a
 2: b
 3: c
 4: d
 5: e
 6: f
 7: g
 8: h
 9: i
10: j
11: k
12: l

"(a)(b)(c)(d)(e)(f)(g)(h)(i)(j)(k)(l)A\11B"I
Capturing subpattern count = 12
Max back reference = 11
No options
First char = 'a'
Need char = 'B'
    abcdefghijklAkB
 0: abcdefghijklAkB
 1: a
 2: b
 3: c
 4: d
 5: e
 6: f
 7: g
 8: h
 9: i
10: j
11: k
12: l

"(?P<name0>a)(?P<name1>a)(?P<name2>a)(?P<name3>a)(?P<name4>a)(?P<name5>a)(?P<name6>a)(?P<name7>a)(?P<name8>a)(?P<name9>a)(?P<name10>a)(?P<name11>a)(?P<name12>a)(?P<name13>a)(?P<name14>a)(?P<name15>a)(?P<name16>a)(?P<name17>a)(?P<name18>a)(?P<name19>a)(?P<name20>a)(?P<name21>a)(?P<name22>a)(?P<name23>a)(?P<name24>a)(?P<name25>a)(?P<name26>a)(?P<name27>a)(?P<name28>a)(?P<name29>a)(?P<name30>a)(?P<name31>a)(?P<name32>a)(?P<name33>a)(?P<name34>a)(?P<name35>a)(?P<name36>a)(?P<name37>a)(?P<name38>a)(?P<name39>a)(?P<name40>a)(?P<name41>a)(?P<name42>a)(?P<name43>a)(?P<name44>a)(?P<name45>a)(?P<name46>a)(?P<name47>a)(?P<name48>a)(?P<name49>a)(?P<name50>a)(?P<name51>a)(?P<name52>a)(?P<name53>a)(?P<name54>a)(?P<name55>a)(?P<name56>a)(?P<name57>a)(?P<name58>a)(?P<name59>a)(?P<name60>a)(?P<name61>a)(?P<name62>a)(?P<name63>a)(?P<name64>a)(?P<name65>a)(?P<name66>a)(?P<name67>a)(?P<name68>a)(?P<name69>a)(?P<name70>a)(?P<name71>a)(?P<name72>a)(?P<name73>a)(?P<name74>a)(?P<name75>a)(?P<name76>a)(?P<name77>a)(?P<name78>a)(?P<name79>a)(?P<name80>a)(?P<name81>a)(?P<name82>a)(?P<name83>a)(?P<name84>a)(?P<name85>a)(?P<name86>a)(?P<name87>a)(?P<name88>a)(?P<name89>a)(?P<name90>a)(?P<name91>a)(?P<name92>a)(?P<name93>a)(?P<name94>a)(?P<name95>a)(?P<name96>a)(?P<name97>a)(?P<name98>a)(?P<name99>a)(?P<name100>a)"I
Capturing subpattern count = 101
Named capturing subpatterns:
  name0     1
  name1     2
  name10   11
  name100 101
  name11   12
  name12   13
  name13   14
  name14   15
  name15   16
  name16   17
  name17   18
  name18   19
  name19   20
  name2     3
  name20   21
  name21   22
  name22   23
  name23   24
  name24   25
  name25   26
  name26   27
  name27   28
  name28   29
  name29   30
  name3     4
  name30   31
  name31   32
  name32   33
  name33   34
  name34   35
  name35   36
  name36   37
  name37   38
  name38   39
  name39   40
  name4     5
  name40   41
  name41   42
  name42   43
  name43   44
  name44   45
  name45   46
  name46   47
  name47   48
  name48   49
  name49   50
  name5     6
  name50   51
  name51   52
  name52   53
  name53   54
  name54   55
  name55   56
  name56   57
  name57   58
  name58   59
  name59   60
  name6     7
  name60   61
  name61   62
  name62   63
  name63   64
  name64   65
  name65   66
  name66   67
  name67   68
  name68   69
  name69   70
  name7     8
  name70   71
  name71   72
  name72   73
  name73   74
  name74   75
  name75   76
  name76   77
  name77   78
  name78   79
  name79   80
  name8     9
  name80   81
  name81   82
  name82   83
  name83   84
  name84   85
  name85   86
  name86   87
  name87   88
  name88   89
  name89   90
  name9    10
  name90   91
  name91   92
  name92   93
  name93   94
  name94   95
  name95   96
  name96   97
  name97   98
  name98   99
  name99  100
No options
First char = 'a'
Need char = 'a'
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
Matched, but too many substrings
 0: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 1: a
 2: a
 3: a
 4: a
 5: a
 6: a
 7: a
 8: a
 9: a
10: a
11: a
12: a
13: a
14: a

"(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)(a)"I
Capturing subpattern count = 101
No options
First char = 'a'
Need char = 'a'
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
Matched, but too many substrings
 0: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 1: a
 2: a
 3: a
 4: a
 5: a
 6: a
 7: a
 8: a
 9: a
10: a
11: a
12: a
13: a
14: a

/[^()]*(?:\((?R)\)[^()]*)*/I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
    (this(and)that
 0: 
    (this(and)that)
 0: (this(and)that)
    (this(and)that)stuff
 0: (this(and)that)stuff

/[^()]*(?:\((?>(?R))\)[^()]*)*/I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
    (this(and)that
 0: 
    (this(and)that)
 0: (this(and)that)

/[^()]*(?:\((?R)\))*[^()]*/I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
    (this(and)that
 0: 
    (this(and)that)
 0: (this(and)that)

/(?:\((?R)\))*[^()]*/I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
    (this(and)that
 0: 
    (this(and)that)
 0: 
    ((this))
 0: ((this))

/(?:\((?R)\))|[^()]*/I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
    (this(and)that
 0: 
    (this(and)that)
 0: 
    (this)
 0: (this)
    ((this))
 0: ((this))

/\x{0000ff}/I
Capturing subpattern count = 0
No options
First char = \xff
No need char

/^((?P<A>a1)|(?P<A>a2)b)/I
Failed: two named subpatterns have the same name at offset 17

/^((?P<A>a1)|(?P<A>a2)b)/IJ
Capturing subpattern count = 3
Named capturing subpatterns:
  A   2
  A   3
Options: anchored dupnames
No first char
No need char
    a1b\CA
 0: a1
 1: a1
 2: a1
  C a1 (2) A
    a2b\CA
 0: a2b
 1: a2b
 2: <unset>
 3: a2
  C a2 (2) A
    ** Failers
No match
    a1b\CZ\CA
no parentheses with name "Z"
 0: a1
 1: a1
 2: a1
copy substring Z failed -7
  C a1 (2) A
    
/(?|(?<a>)(?<b>)(?<a>)|(?<a>)(?<b>)(?<a>))/IJ
Capturing subpattern count = 3
Named capturing subpatterns:
  a   1
  a   3
  b   2
May match empty string
Options: dupnames
No first char
No need char

/^(?P<A>a)(?P<A>b)/IJ
Capturing subpattern count = 2
Named capturing subpatterns:
  A   1
  A   2
Options: anchored dupnames
No first char
No need char
    ab\CA
 0: ab
 1: a
 2: b
  C a (1) A

/^(?P<A>a)(?P<A>b)|cd/IJ
Capturing subpattern count = 2
Named capturing subpatterns:
  A   1
  A   2
Options: dupnames
No first char
No need char
    ab\CA
 0: ab
 1: a
 2: b
  C a (1) A
    cd\CA
 0: cd
copy substring A failed -7

/^(?P<A>a)(?P<A>b)|cd(?P<A>ef)(?P<A>gh)/IJ
Capturing subpattern count = 4
Named capturing subpatterns:
  A   1
  A   2
  A   3
  A   4
Options: dupnames
No first char
No need char
    cdefgh\CA
 0: cdefgh
 1: <unset>
 2: <unset>
 3: ef
 4: gh
  C ef (2) A

/^((?P<A>a1)|(?P<A>a2)b)/IJ
Capturing subpattern count = 3
Named capturing subpatterns:
  A   2
  A   3
Options: anchored dupnames
No first char
No need char
    a1b\GA
 0: a1
 1: a1
 2: a1
  G a1 (2) A
    a2b\GA
 0: a2b
 1: a2b
 2: <unset>
 3: a2
  G a2 (2) A
    ** Failers
No match
    a1b\GZ\GA
no parentheses with name "Z"
 0: a1
 1: a1
 2: a1
get substring Z failed -7
  G a1 (2) A

/^(?P<A>a)(?P<A>b)/IJ
Capturing subpattern count = 2
Named capturing subpatterns:
  A   1
  A   2
Options: anchored dupnames
No first char
No need char
    ab\GA
 0: ab
 1: a
 2: b
  G a (1) A

/^(?P<A>a)(?P<A>b)|cd/IJ
Capturing subpattern count = 2
Named capturing subpatterns:
  A   1
  A   2
Options: dupnames
No first char
No need char
    ab\GA
 0: ab
 1: a
 2: b
  G a (1) A
    cd\GA
 0: cd
get substring A failed -7

/^(?P<A>a)(?P<A>b)|cd(?P<A>ef)(?P<A>gh)/IJ
Capturing subpattern count = 4
Named capturing subpatterns:
  A   1
  A   2
  A   3
  A   4
Options: dupnames
No first char
No need char
    cdefgh\GA
 0: cdefgh
 1: <unset>
 2: <unset>
 3: ef
 4: gh
  G ef (2) A

/(?J)^((?P<A>a1)|(?P<A>a2)b)/I
Capturing subpattern count = 3
Named capturing subpatterns:
  A   2
  A   3
Options: anchored dupnames
Duplicate name status changes
No first char
No need char
    a1b\CA
 0: a1
 1: a1
 2: a1
  C a1 (2) A
    a2b\CA
 0: a2b
 1: a2b
 2: <unset>
 3: a2
  C a2 (2) A

/^(?P<A>a) (?J:(?P<B>b)(?P<B>c)) (?P<A>d)/I
Failed: two named subpatterns have the same name at offset 37

/ In this next test, J is not set at the outer level; consequently it isn't
set in the pattern's options; consequently pcre_get_named_substring() produces
a random value. /Ix
Capturing subpattern count = 1
Options: extended
First char = 'I'
Need char = 'e'

/^(?P<A>a) (?J:(?P<B>b)(?P<B>c)) (?P<C>d)/I
Capturing subpattern count = 4
Named capturing subpatterns:
  A   1
  B   2
  B   3
  C   4
Options: anchored
Duplicate name status changes
No first char
No need char
    a bc d\CA\CB\CC
 0: a bc d
 1: a
 2: b
 3: c
 4: d
  C a (1) A
  C b (1) B
  C d (1) C

/^(?P<A>a)?(?(A)a|b)/I
Capturing subpattern count = 1
Max back reference = 1
Named capturing subpatterns:
  A   1
Options: anchored
No first char
No need char
    aabc
 0: aa
 1: a
    bc
 0: b
    ** Failers
No match
    abc
No match

/(?:(?(ZZ)a|b)(?P<ZZ>X))+/I
Capturing subpattern count = 1
Max back reference = 1
Named capturing subpatterns:
  ZZ   1
No options
No first char
Need char = 'X'
    bXaX
 0: bXaX
 1: X

/(?:(?(2y)a|b)(X))+/I
Failed: malformed number or name after (?( at offset 7

/(?:(?(ZA)a|b)(?P<ZZ>X))+/I
Failed: reference to non-existent subpattern at offset 9

/(?:(?(ZZ)a|b)(?(ZZ)a|b)(?P<ZZ>X))+/I
Capturing subpattern count = 1
Max back reference = 1
Named capturing subpatterns:
  ZZ   1
No options
No first char
Need char = 'X'
    bbXaaX
 0: bbXaaX
 1: X

/(?:(?(ZZ)a|\(b\))\\(?P<ZZ>X))+/I
Capturing subpattern count = 1
Max back reference = 1
Named capturing subpatterns:
  ZZ   1
No options
No first char
Need char = 'X'
    (b)\\Xa\\X
 0: (b)\Xa\X
 1: X

/(?P<ABC/I
Failed: syntax error in subpattern name (missing terminator) at offset 7

/(?:(?(A)(?P=A)a|b)(?P<A>X|Y))+/I
Capturing subpattern count = 1
Max back reference = 1
Named capturing subpatterns:
  A   1
No options
No first char
No need char
    bXXaYYaY
 0: bXXaYYaY
 1: Y
    bXYaXXaX
 0: bX
 1: X

/()()()()()()()()()(?:(?(A)(?P=A)a|b)(?P<A>X|Y))+/I
Capturing subpattern count = 10
Max back reference = 10
Named capturing subpatterns:
  A  10
No options
No first char
No need char
    bXXaYYaY
 0: bXXaYYaY
 1: 
 2: 
 3: 
 4: 
 5: 
 6: 
 7: 
 8: 
 9: 
10: Y

/\s*,\s*/IS
Capturing subpattern count = 0
No options
No first char
Need char = ','
Subject length lower bound = 1
Starting chars: \x09 \x0a \x0b \x0c \x0d \x20 , 
    \x0b,\x0b
 0: \x0b,\x0b
    \x0c,\x0d
 0: \x0c,\x0d

/^abc/Im
Capturing subpattern count = 0
Options: multiline
First char at start or follows newline
Need char = 'c'
    xyz\nabc
 0: abc
    xyz\nabc\<lf>
 0: abc
    xyz\r\nabc\<lf>
 0: abc
    xyz\rabc\<cr>
 0: abc
    xyz\r\nabc\<crlf>
 0: abc
    ** Failers
No match
    xyz\nabc\<cr>
No match
    xyz\r\nabc\<cr>
No match
    xyz\nabc\<crlf>
No match
    xyz\rabc\<crlf>
No match
    xyz\rabc\<lf>
No match

/abc$/Im<lf>
Capturing subpattern count = 0
Options: multiline
Forced newline sequence: LF
First char = 'a'
Need char = 'c'
    xyzabc
 0: abc
    xyzabc\n
 0: abc
    xyzabc\npqr
 0: abc
    xyzabc\r\<cr>
 0: abc
    xyzabc\rpqr\<cr>
 0: abc
    xyzabc\r\n\<crlf>
 0: abc
    xyzabc\r\npqr\<crlf>
 0: abc
    ** Failers
No match
    xyzabc\r
No match
    xyzabc\rpqr
No match
    xyzabc\r\n
No match
    xyzabc\r\npqr
No match

/^abc/Im<cr>
Capturing subpattern count = 0
Options: multiline
Forced newline sequence: CR
First char at start or follows newline
Need char = 'c'
    xyz\rabcdef
 0: abc
    xyz\nabcdef\<lf>
 0: abc
    ** Failers
No match
    xyz\nabcdef
No match

/^abc/Im<lf>
Capturing subpattern count = 0
Options: multiline
Forced newline sequence: LF
First char at start or follows newline
Need char = 'c'
    xyz\nabcdef
 0: abc
    xyz\rabcdef\<cr>
 0: abc
    ** Failers
No match
    xyz\rabcdef
No match

/^abc/Im<crlf>
Capturing subpattern count = 0
Options: multiline
Forced newline sequence: CRLF
First char at start or follows newline
Need char = 'c'
    xyz\r\nabcdef
 0: abc
    xyz\rabcdef\<cr>
 0: abc
    ** Failers
No match
    xyz\rabcdef
No match

/^abc/Im<bad>
Unknown modifier at: <bad>


/abc/I
Capturing subpattern count = 0
No options
First char = 'a'
Need char = 'c'
    xyz\rabc\<bad>
Unknown escape sequence at: <bad>
    abc
 0: abc

/.*/I<lf>
Capturing subpattern count = 0
May match empty string
Options:
Forced newline sequence: LF
First char at start or follows newline
No need char
    abc\ndef
 0: abc
    abc\rdef
 0: abc\x0ddef
    abc\r\ndef
 0: abc\x0d
    \<cr>abc\ndef
 0: abc\x0adef
    \<cr>abc\rdef
 0: abc
    \<cr>abc\r\ndef
 0: abc
    \<crlf>abc\ndef
 0: abc\x0adef
    \<crlf>abc\rdef
 0: abc\x0ddef
    \<crlf>abc\r\ndef
 0: abc

/\w+(.)(.)?def/Is
Capturing subpattern count = 2
Options: dotall
No first char
Need char = 'f'
    abc\ndef
 0: abc\x0adef
 1: \x0a
    abc\rdef
 0: abc\x0ddef
 1: \x0d
    abc\r\ndef
 0: abc\x0d\x0adef
 1: \x0d
 2: \x0a

+((?:\s|//.*\\n|/[*](?:\\n|.)*?[*]/)*)+I
Capturing subpattern count = 1
May match empty string
No options
No first char
No need char
   /* this is a C style comment */\M
Minimum match() limit = 120
Minimum match() recursion limit = 6
 0: /* this is a C style comment */
 1: /* this is a C style comment */

/(?P<B>25[0-5]|2[0-4]\d|[01]?\d?\d)(?:\.(?P>B)){3}/I
Capturing subpattern count = 1
Named capturing subpatterns:
  B   1
No options
No first char
Need char = '.'

/()()()()()()()()()()()()()()()()()()()()
 ()()()()()()()()()()()()()()()()()()()()
 ()()()()()()()()()()()()()()()()()()()()
 ()()()()()()()()()()()()()()()()()()()()
 ()()()()()()()()()()()()()()()()()()()()
 (.(.))/Ix
Capturing subpattern count = 102
Options: extended
No first char
No need char
    XY\O400
 0: XY
 1: 
 2: 
 3: 
 4: 
 5: 
 6: 
 7: 
 8: 
 9: 
10: 
11: 
12: 
13: 
14: 
15: 
16: 
17: 
18: 
19: 
20: 
21: 
22: 
23: 
24: 
25: 
26: 
27: 
28: 
29: 
30: 
31: 
32: 
33: 
34: 
35: 
36: 
37: 
38: 
39: 
40: 
41: 
42: 
43: 
44: 
45: 
46: 
47: 
48: 
49: 
50: 
51: 
52: 
53: 
54: 
55: 
56: 
57: 
58: 
59: 
60: 
61: 
62: 
63: 
64: 
65: 
66: 
67: 
68: 
69: 
70: 
71: 
72: 
73: 
74: 
75: 
76: 
77: 
78: 
79: 
80: 
81: 
82: 
83: 
84: 
85: 
86: 
87: 
88: 
89: 
90: 
91: 
92: 
93: 
94: 
95: 
96: 
97: 
98: 
99: 
100: 
101: XY
102: Y

/(a*b|(?i:c*(?-i)d))/IS
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: C a b c d 

/()[ab]xyz/IS
Capturing subpattern count = 1
No options
No first char
Need char = 'z'
Subject length lower bound = 4
Starting chars: a b 

/(|)[ab]xyz/IS
Capturing subpattern count = 1
No options
No first char
Need char = 'z'
Subject length lower bound = 4
Starting chars: a b 

/(|c)[ab]xyz/IS
Capturing subpattern count = 1
No options
No first char
Need char = 'z'
Subject length lower bound = 4
Starting chars: a b c 

/(|c?)[ab]xyz/IS
Capturing subpattern count = 1
No options
No first char
Need char = 'z'
Subject length lower bound = 4
Starting chars: a b c 

/(d?|c?)[ab]xyz/IS
Capturing subpattern count = 1
No options
No first char
Need char = 'z'
Subject length lower bound = 4
Starting chars: a b c d 

/(d?|c)[ab]xyz/IS
Capturing subpattern count = 1
No options
No first char
Need char = 'z'
Subject length lower bound = 4
Starting chars: a b c d 

/^a*b\d/DZ
------------------------------------------------------------------
        Bra
        ^
        a*+
        b
        \d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
Need char = 'b'

/^a*+b\d/DZ
------------------------------------------------------------------
        Bra
        ^
        a*+
        b
        \d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
Need char = 'b'

/^a*?b\d/DZ
------------------------------------------------------------------
        Bra
        ^
        a*+
        b
        \d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
Need char = 'b'

/^a+A\d/DZ
------------------------------------------------------------------
        Bra
        ^
        a++
        A
        \d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored
No first char
Need char = 'A'
    aaaA5
 0: aaaA5
    ** Failers
No match
    aaaa5
No match

/^a*A\d/IiDZ
------------------------------------------------------------------
        Bra
        ^
     /i a*
     /i A
        \d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: anchored caseless
No first char
Need char = 'A' (caseless)
    aaaA5
 0: aaaA5
    aaaa5
 0: aaaa5

/(a*|b*)[cd]/IS
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b c d 

/(a+|b*)[cd]/IS
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b c d 

/(a*|b+)[cd]/IS
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: a b c d 

/(a+|b+)[cd]/IS
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 2
Starting chars: a b 

/((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((
 ((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((
 (((
 a
 ))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))
 ))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))))
 )))
/Ix
Capturing subpattern count = 203
Options: extended
First char = 'a'
No need char
  large nest
Matched, but too many substrings
 0: a
 1: a
 2: a
 3: a
 4: a
 5: a
 6: a
 7: a
 8: a
 9: a
10: a
11: a
12: a
13: a
14: a

/a*\d/BZ
------------------------------------------------------------------
        Bra
        a*+
        \d
        Ket
        End
------------------------------------------------------------------

/a*\D/BZ
------------------------------------------------------------------
        Bra
        a*
        \D
        Ket
        End
------------------------------------------------------------------

/0*\d/BZ
------------------------------------------------------------------
        Bra
        0*
        \d
        Ket
        End
------------------------------------------------------------------

/0*\D/BZ
------------------------------------------------------------------
        Bra
        0*+
        \D
        Ket
        End
------------------------------------------------------------------

/a*\s/BZ
------------------------------------------------------------------
        Bra
        a*+
        \s
        Ket
        End
------------------------------------------------------------------

/a*\S/BZ
------------------------------------------------------------------
        Bra
        a*
        \S
        Ket
        End
------------------------------------------------------------------

/ *\s/BZ
------------------------------------------------------------------
        Bra
         *
        \s
        Ket
        End
------------------------------------------------------------------

/ *\S/BZ
------------------------------------------------------------------
        Bra
         *+
        \S
        Ket
        End
------------------------------------------------------------------

/a*\w/BZ
------------------------------------------------------------------
        Bra
        a*
        \w
        Ket
        End
------------------------------------------------------------------

/a*\W/BZ
------------------------------------------------------------------
        Bra
        a*+
        \W
        Ket
        End
------------------------------------------------------------------

/=*\w/BZ
------------------------------------------------------------------
        Bra
        =*+
        \w
        Ket
        End
------------------------------------------------------------------

/=*\W/BZ
------------------------------------------------------------------
        Bra
        =*
        \W
        Ket
        End
------------------------------------------------------------------

/\d*a/BZ
------------------------------------------------------------------
        Bra
        \d*+
        a
        Ket
        End
------------------------------------------------------------------

/\d*2/BZ
------------------------------------------------------------------
        Bra
        \d*
        2
        Ket
        End
------------------------------------------------------------------

/\d*\d/BZ
------------------------------------------------------------------
        Bra
        \d*
        \d
        Ket
        End
------------------------------------------------------------------

/\d*\D/BZ
------------------------------------------------------------------
        Bra
        \d*+
        \D
        Ket
        End
------------------------------------------------------------------

/\d*\s/BZ
------------------------------------------------------------------
        Bra
        \d*+
        \s
        Ket
        End
------------------------------------------------------------------

/\d*\S/BZ
------------------------------------------------------------------
        Bra
        \d*
        \S
        Ket
        End
------------------------------------------------------------------

/\d*\w/BZ
------------------------------------------------------------------
        Bra
        \d*
        \w
        Ket
        End
------------------------------------------------------------------

/\d*\W/BZ
------------------------------------------------------------------
        Bra
        \d*+
        \W
        Ket
        End
------------------------------------------------------------------

/\D*a/BZ
------------------------------------------------------------------
        Bra
        \D*
        a
        Ket
        End
------------------------------------------------------------------

/\D*2/BZ
------------------------------------------------------------------
        Bra
        \D*+
        2
        Ket
        End
------------------------------------------------------------------

/\D*\d/BZ
------------------------------------------------------------------
        Bra
        \D*+
        \d
        Ket
        End
------------------------------------------------------------------

/\D*\D/BZ
------------------------------------------------------------------
        Bra
        \D*
        \D
        Ket
        End
------------------------------------------------------------------

/\D*\s/BZ
------------------------------------------------------------------
        Bra
        \D*
        \s
        Ket
        End
------------------------------------------------------------------

/\D*\S/BZ
------------------------------------------------------------------
        Bra
        \D*
        \S
        Ket
        End
------------------------------------------------------------------

/\D*\w/BZ
------------------------------------------------------------------
        Bra
        \D*
        \w
        Ket
        End
------------------------------------------------------------------

/\D*\W/BZ
------------------------------------------------------------------
        Bra
        \D*
        \W
        Ket
        End
------------------------------------------------------------------

/\s*a/BZ
------------------------------------------------------------------
        Bra
        \s*+
        a
        Ket
        End
------------------------------------------------------------------

/\s*2/BZ
------------------------------------------------------------------
        Bra
        \s*+
        2
        Ket
        End
------------------------------------------------------------------

/\s*\d/BZ
------------------------------------------------------------------
        Bra
        \s*+
        \d
        Ket
        End
------------------------------------------------------------------

/\s*\D/BZ
------------------------------------------------------------------
        Bra
        \s*
        \D
        Ket
        End
------------------------------------------------------------------

/\s*\s/BZ
------------------------------------------------------------------
        Bra
        \s*
        \s
        Ket
        End
------------------------------------------------------------------

/\s*\S/BZ
------------------------------------------------------------------
        Bra
        \s*+
        \S
        Ket
        End
------------------------------------------------------------------

/\s*\w/BZ
------------------------------------------------------------------
        Bra
        \s*+
        \w
        Ket
        End
------------------------------------------------------------------

/\s*\W/BZ
------------------------------------------------------------------
        Bra
        \s*
        \W
        Ket
        End
------------------------------------------------------------------

/\S*a/BZ
------------------------------------------------------------------
        Bra
        \S*
        a
        Ket
        End
------------------------------------------------------------------

/\S*2/BZ
------------------------------------------------------------------
        Bra
        \S*
        2
        Ket
        End
------------------------------------------------------------------

/\S*\d/BZ
------------------------------------------------------------------
        Bra
        \S*
        \d
        Ket
        End
------------------------------------------------------------------

/\S*\D/BZ
------------------------------------------------------------------
        Bra
        \S*
        \D
        Ket
        End
------------------------------------------------------------------

/\S*\s/BZ
------------------------------------------------------------------
        Bra
        \S*+
        \s
        Ket
        End
------------------------------------------------------------------

/\S*\S/BZ
------------------------------------------------------------------
        Bra
        \S*
        \S
        Ket
        End
------------------------------------------------------------------

/\S*\w/BZ
------------------------------------------------------------------
        Bra
        \S*
        \w
        Ket
        End
------------------------------------------------------------------

/\S*\W/BZ
------------------------------------------------------------------
        Bra
        \S*
        \W
        Ket
        End
------------------------------------------------------------------

/\w*a/BZ
------------------------------------------------------------------
        Bra
        \w*
        a
        Ket
        End
------------------------------------------------------------------

/\w*2/BZ
------------------------------------------------------------------
        Bra
        \w*
        2
        Ket
        End
------------------------------------------------------------------

/\w*\d/BZ
------------------------------------------------------------------
        Bra
        \w*
        \d
        Ket
        End
------------------------------------------------------------------

/\w*\D/BZ
------------------------------------------------------------------
        Bra
        \w*
        \D
        Ket
        End
------------------------------------------------------------------

/\w*\s/BZ
------------------------------------------------------------------
        Bra
        \w*+
        \s
        Ket
        End
------------------------------------------------------------------

/\w*\S/BZ
------------------------------------------------------------------
        Bra
        \w*
        \S
        Ket
        End
------------------------------------------------------------------

/\w*\w/BZ
------------------------------------------------------------------
        Bra
        \w*
        \w
        Ket
        End
------------------------------------------------------------------

/\w*\W/BZ
------------------------------------------------------------------
        Bra
        \w*+
        \W
        Ket
        End
------------------------------------------------------------------

/\W*a/BZ
------------------------------------------------------------------
        Bra
        \W*+
        a
        Ket
        End
------------------------------------------------------------------

/\W*2/BZ
------------------------------------------------------------------
        Bra
        \W*+
        2
        Ket
        End
------------------------------------------------------------------

/\W*\d/BZ
------------------------------------------------------------------
        Bra
        \W*+
        \d
        Ket
        End
------------------------------------------------------------------

/\W*\D/BZ
------------------------------------------------------------------
        Bra
        \W*
        \D
        Ket
        End
------------------------------------------------------------------

/\W*\s/BZ
------------------------------------------------------------------
        Bra
        \W*
        \s
        Ket
        End
------------------------------------------------------------------

/\W*\S/BZ
------------------------------------------------------------------
        Bra
        \W*
        \S
        Ket
        End
------------------------------------------------------------------

/\W*\w/BZ
------------------------------------------------------------------
        Bra
        \W*+
        \w
        Ket
        End
------------------------------------------------------------------

/\W*\W/BZ
------------------------------------------------------------------
        Bra
        \W*
        \W
        Ket
        End
------------------------------------------------------------------

/[^a]+a/BZ
------------------------------------------------------------------
        Bra
        [^a]++
        a
        Ket
        End
------------------------------------------------------------------

/[^a]+a/BZi
------------------------------------------------------------------
        Bra
     /i [^a]++
     /i a
        Ket
        End
------------------------------------------------------------------

/[^a]+A/BZi
------------------------------------------------------------------
        Bra
     /i [^a]++
     /i A
        Ket
        End
------------------------------------------------------------------

/[^a]+b/BZ
------------------------------------------------------------------
        Bra
        [^a]+
        b
        Ket
        End
------------------------------------------------------------------

/[^a]+\d/BZ
------------------------------------------------------------------
        Bra
        [^a]+
        \d
        Ket
        End
------------------------------------------------------------------

/a*[^a]/BZ
------------------------------------------------------------------
        Bra
        a*+
        [^a]
        Ket
        End
------------------------------------------------------------------

/(?P<abc>x)(?P<xyz>y)/I
Capturing subpattern count = 2
Named capturing subpatterns:
  abc   1
  xyz   2
No options
First char = 'x'
Need char = 'y'
    xy\Cabc\Cxyz
 0: xy
 1: x
 2: y
  C x (1) abc
  C y (1) xyz

/(?<abc>x)(?'xyz'y)/I
Capturing subpattern count = 2
Named capturing subpatterns:
  abc   1
  xyz   2
No options
First char = 'x'
Need char = 'y'
    xy\Cabc\Cxyz
 0: xy
 1: x
 2: y
  C x (1) abc
  C y (1) xyz

/(?<abc'x)(?'xyz'y)/I
Failed: syntax error in subpattern name (missing terminator) at offset 6

/(?<abc>x)(?'xyz>y)/I
Failed: syntax error in subpattern name (missing terminator) at offset 15

/(?P'abc'x)(?P<xyz>y)/I
Failed: unrecognized character after (?P at offset 3

/^(?:(?(ZZ)a|b)(?<ZZ>X))+/
    bXaX
 0: bXaX
 1: X
    bXbX
 0: bX
 1: X
    ** Failers
No match
    aXaX
No match
    aXbX
No match

/^(?P>abc)(?<abcd>xxx)/
Failed: reference to non-existent subpattern at offset 8

/^(?P>abc)(?<abc>x|y)/
    xx
 0: xx
 1: x
    xy
 0: xy
 1: y
    yy
 0: yy
 1: y
    yx
 0: yx
 1: x

/^(?P>abc)(?P<abc>x|y)/
    xx
 0: xx
 1: x
    xy
 0: xy
 1: y
    yy
 0: yy
 1: y
    yx
 0: yx
 1: x

/^((?(abc)a|b)(?<abc>x|y))+/
    bxay
 0: bxay
 1: ay
 2: y
    bxby
 0: bx
 1: bx
 2: x
    ** Failers
No match
    axby
No match

/^(((?P=abc)|X)(?<abc>x|y))+/
    XxXxxx
 0: XxXxxx
 1: xx
 2: x
 3: x
    XxXyyx
 0: XxXyyx
 1: yx
 2: y
 3: x
    XxXyxx
 0: XxXy
 1: Xy
 2: X
 3: y
    ** Failers
No match
    x
No match

/^(?1)(abc)/
    abcabc
 0: abcabc
 1: abc

/^(?:(?:\1|X)(a|b))+/
    Xaaa
 0: Xaaa
 1: a
    Xaba
 0: Xa
 1: a

/^[\E\Qa\E-\Qz\E]+/BZ
------------------------------------------------------------------
        Bra
        ^
        [a-z]++
        Ket
        End
------------------------------------------------------------------

/^[a\Q]bc\E]/BZ
------------------------------------------------------------------
        Bra
        ^
        [\]a-c]
        Ket
        End
------------------------------------------------------------------

/^[a-\Q\E]/BZ
------------------------------------------------------------------
        Bra
        ^
        [\-a]
        Ket
        End
------------------------------------------------------------------

/^(?P>abc)[()](?<abc>)/BZ
------------------------------------------------------------------
        Bra
        ^
        Recurse
        [()]
        CBra 1
        Ket
        Ket
        End
------------------------------------------------------------------

/^((?(abc)y)[()](?P<abc>x))+/BZ
------------------------------------------------------------------
        Bra
        ^
        CBra 1
        Cond
      2 Cond ref
        y
        Ket
        [()]
        CBra 2
        x
        Ket
        KetRmax
        Ket
        End
------------------------------------------------------------------
    (xy)x
 0: (xy)x
 1: y)x
 2: x

/^(?P>abc)\Q()\E(?<abc>)/BZ
------------------------------------------------------------------
        Bra
        ^
        Recurse
        ()
        CBra 1
        Ket
        Ket
        End
------------------------------------------------------------------

/^(?P>abc)[a\Q(]\E(](?<abc>)/BZ
------------------------------------------------------------------
        Bra
        ^
        Recurse
        [(\]a]
        CBra 1
        Ket
        Ket
        End
------------------------------------------------------------------

/^(?P>abc) # this is (a comment)
  (?<abc>)/BZx
------------------------------------------------------------------
        Bra
        ^
        Recurse
        CBra 1
        Ket
        Ket
        End
------------------------------------------------------------------

/^\W*(?:(?<one>(?<two>.)\W*(?&one)\W*\k<two>|)|(?<three>(?<four>.)\W*(?&three)\W*\k'four'|\W*.\W*))\W*$/Ii
Capturing subpattern count = 4
Max back reference = 4
Named capturing subpatterns:
  four    4
  one     1
  three   3
  two     2
May match empty string
Options: anchored caseless
No first char
No need char
    1221
 0: 1221
 1: 1221
 2: 1
    Satan, oscillate my metallic sonatas!
 0: Satan, oscillate my metallic sonatas!
 1: <unset>
 2: <unset>
 3: Satan, oscillate my metallic sonatas
 4: S
    A man, a plan, a canal: Panama!
 0: A man, a plan, a canal: Panama!
 1: <unset>
 2: <unset>
 3: A man, a plan, a canal: Panama
 4: A
    Able was I ere I saw Elba.
 0: Able was I ere I saw Elba.
 1: <unset>
 2: <unset>
 3: Able was I ere I saw Elba
 4: A
    *** Failers
No match
    The quick brown fox
No match

/(?=(\w+))\1:/I
Capturing subpattern count = 1
Max back reference = 1
No options
No first char
Need char = ':'
    abcd:
 0: abcd:
 1: abcd

/(?=(?'abc'\w+))\k<abc>:/I
Capturing subpattern count = 1
Max back reference = 1
Named capturing subpatterns:
  abc   1
No options
No first char
Need char = ':'
    abcd:
 0: abcd:
 1: abcd

/(?'abc'a|b)(?<abc>d|e)\k<abc>{2}/J
    adaa
 0: adaa
 1: a
 2: d
    ** Failers
No match
    addd
No match
    adbb
No match

/(?'abc'a|b)(?<abc>d|e)(?&abc){2}/J
    bdaa
 0: bdaa
 1: b
 2: d
    bdab
 0: bdab
 1: b
 2: d
    ** Failers
No match
    bddd
No match

/(?(<bc))/
Failed: malformed number or name after (?( at offset 6

/(?(''))/
Failed: assertion expected after (?( at offset 4

/(?('R')stuff)/
Failed: reference to non-existent subpattern at offset 7

/((abc (?(R) (?(R1)1) (?(R2)2) X  |  (?1)  (?2)   (?R) ))) /x
    abcabc1Xabc2XabcXabcabc
 0: abcabc1Xabc2XabcX
 1: abcabc1Xabc2XabcX
 2: abcabc1Xabc2XabcX

/(?<A> (?'B' abc (?(R) (?(R&A)1) (?(R&B)2) X  |  (?1)  (?2)   (?R) ))) /x
    abcabc1Xabc2XabcXabcabc
 0: abcabc1Xabc2XabcX
 1: abcabc1Xabc2XabcX
 2: abcabc1Xabc2XabcX

/(?<A> (?'B' abc (?(R) (?(R&C)1) (?(R&B)2) X  |  (?1)  (?2)   (?R) ))) /x
Failed: reference to non-existent subpattern at offset 29

/^(?(DEFINE) abc | xyz ) /x
Failed: DEFINE group contains more than one branch at offset 22

/(?(DEFINE) abc) xyz/xI
Capturing subpattern count = 0
Options: extended
First char = 'x'
Need char = 'z'

/(a|)*\d/
  \O0aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
No match
  \O0aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa4
Matched, but too many substrings

/^a.b/<lf>
    a\rb
 0: a\x0db
    a\nb\<cr>
 0: a\x0ab
    a\x85b\<anycrlf> 
 0: a\x85b
    ** Failers
No match
    a\nb
No match
    a\nb\<any>
No match
    a\rb\<cr>
No match
    a\rb\<any>
No match
    a\x85b\<any> 
No match
    a\rb\<anycrlf>
No match

/^abc./mgx<any>
    abc1 \x0aabc2 \x0babc3xx \x0cabc4 \x0dabc5xx \x0d\x0aabc6 \x85abc7 JUNK
 0: abc1
 0: abc2
 0: abc3
 0: abc4
 0: abc5
 0: abc6
 0: abc7

/abc.$/mgx<any>
    abc1\x0a abc2\x0b abc3\x0c abc4\x0d abc5\x0d\x0a abc6\x85 abc7 abc9
 0: abc1
 0: abc2
 0: abc3
 0: abc4
 0: abc5
 0: abc6
 0: abc9

/a/<cr><any>

/a/<any><crlf>
Failed: inconsistent NEWLINE options at offset 0

/^a\Rb/<bsr_unicode>
    a\nb
 0: a\x0ab
    a\rb
 0: a\x0db
    a\r\nb
 0: a\x0d\x0ab
    a\x0bb
 0: a\x0bb
    a\x0cb
 0: a\x0cb
    a\x85b
 0: a\x85b
    ** Failers
No match
    a\n\rb
No match

/^a\R*b/<bsr_unicode>
    ab
 0: ab
    a\nb
 0: a\x0ab
    a\rb
 0: a\x0db
    a\r\nb
 0: a\x0d\x0ab
    a\x0bb
 0: a\x0bb
    a\x0cb
 0: a\x0cb
    a\x85b
 0: a\x85b
    a\n\rb
 0: a\x0a\x0db
    a\n\r\x85\x0cb
 0: a\x0a\x0d\x85\x0cb

/^a\R+b/<bsr_unicode>
    a\nb
 0: a\x0ab
    a\rb
 0: a\x0db
    a\r\nb
 0: a\x0d\x0ab
    a\x0bb
 0: a\x0bb
    a\x0cb
 0: a\x0cb
    a\x85b
 0: a\x85b
    a\n\rb
 0: a\x0a\x0db
    a\n\r\x85\x0cb
 0: a\x0a\x0d\x85\x0cb
    ** Failers
No match
    ab
No match

/^a\R{1,3}b/<bsr_unicode>
    a\nb
 0: a\x0ab
    a\n\rb
 0: a\x0a\x0db
    a\n\r\x85b
 0: a\x0a\x0d\x85b
    a\r\n\r\nb
 0: a\x0d\x0a\x0d\x0ab
    a\r\n\r\n\r\nb
 0: a\x0d\x0a\x0d\x0a\x0d\x0ab
    a\n\r\n\rb
 0: a\x0a\x0d\x0a\x0db
    a\n\n\r\nb
 0: a\x0a\x0a\x0d\x0ab
    ** Failers
No match
    a\n\n\n\rb
No match
    a\r
No match

/^a[\R]b/<bsr_unicode>
    aRb
 0: aRb
    ** Failers
No match
    a\nb
No match

/(?&abc)X(?<abc>P)/I
Capturing subpattern count = 1
Named capturing subpatterns:
  abc   1
No options
No first char
Need char = 'P'
    abcPXP123
 0: PXP
 1: P

/(?1)X(?<abc>P)/I
Capturing subpattern count = 1
Named capturing subpatterns:
  abc   1
No options
No first char
Need char = 'P'
    abcPXP123
 0: PXP
 1: P

/(?:a(?&abc)b)*(?<abc>x)/
    123axbaxbaxbx456
 0: axbaxbaxbx
 1: x
    123axbaxbaxb456
 0: x
 1: x

/(?:a(?&abc)b){1,5}(?<abc>x)/
    123axbaxbaxbx456
 0: axbaxbaxbx
 1: x

/(?:a(?&abc)b){2,5}(?<abc>x)/
    123axbaxbaxbx456
 0: axbaxbaxbx
 1: x

/(?:a(?&abc)b){2,}(?<abc>x)/
    123axbaxbaxbx456
 0: axbaxbaxbx
 1: x

/(abc)(?i:(?1))/
   defabcabcxyz
 0: abcabc
 1: abc
   DEFabcABCXYZ
No match

/(abc)(?:(?i)(?1))/
   defabcabcxyz
 0: abcabc
 1: abc
   DEFabcABCXYZ
No match

/^(a)\g-2/
Failed: reference to non-existent subpattern at offset 7

/^(a)\g/
Failed: a numbered reference must not be zero at offset 5

/^(a)\g{0}/
Failed: a numbered reference must not be zero at offset 8

/^(a)\g{3/
Failed: \g is not followed by a braced, angle-bracketed, or quoted name/number or by a plain number at offset 8

/^(a)\g{aa}/
Failed: reference to non-existent subpattern at offset 9

/^a.b/<lf>
    a\rb
 0: a\x0db
    *** Failers
No match
    a\nb
No match

/.+foo/
    afoo
 0: afoo
    ** Failers
No match
    \r\nfoo
No match
    \nfoo
No match

/.+foo/<crlf>
    afoo
 0: afoo
    \nfoo
 0: \x0afoo
    ** Failers
No match
    \r\nfoo
No match

/.+foo/<any>
    afoo
 0: afoo
    ** Failers
No match
    \nfoo
No match
    \r\nfoo
No match

/.+foo/s
    afoo
 0: afoo
    \r\nfoo
 0: \x0d\x0afoo
    \nfoo
 0: \x0afoo
    
/^$/mg<any>
    abc\r\rxyz
 0: 
    abc\n\rxyz  
 0: 
    ** Failers 
No match
    abc\r\nxyz
No match

/(?m)^$/<any>g+
    abc\r\n\r\n
 0: 
 0+ \x0d\x0a

/(?m)^$|^\r\n/<any>g+ 
    abc\r\n\r\n
 0: 
 0+ \x0d\x0a
 0: \x0d\x0a
 0+ 
    
/(?m)$/<any>g+ 
    abc\r\n\r\n
 0: 
 0+ \x0d\x0a\x0d\x0a
 0: 
 0+ \x0d\x0a
 0: 
 0+ 

/abc.$/mgx<anycrlf>
    abc1\x0a abc2\x0b abc3\x0c abc4\x0d abc5\x0d\x0a abc6\x85 abc9
 0: abc1
 0: abc4
 0: abc5
 0: abc9

/^X/m
    XABC
 0: X
    ** Failers 
No match
    XABC\B
No match

/(ab|c)(?-1)/BZ
------------------------------------------------------------------
        Bra
        CBra 1
        ab
        Alt
        c
        Ket
        Recurse
        Ket
        End
------------------------------------------------------------------
    abc
 0: abc
 1: ab

/xy(?+1)(abc)/BZ
------------------------------------------------------------------
        Bra
        xy
        Recurse
        CBra 1
        abc
        Ket
        Ket
        End
------------------------------------------------------------------
    xyabcabc
 0: xyabcabc
 1: abc
    ** Failers
No match
    xyabc  
No match
    
/x(?-0)y/
Failed: a numbered reference must not be zero at offset 5

/x(?-1)y/
Failed: reference to non-existent subpattern at offset 5

/x(?+0)y/ 
Failed: a numbered reference must not be zero at offset 5

/x(?+1)y/
Failed: reference to non-existent subpattern at offset 5

/^(abc)?(?(-1)X|Y)/BZ
------------------------------------------------------------------
        Bra
        ^
        Brazero
        CBra 1
        abc
        Ket
        Cond
      1 Cond ref
        X
        Alt
        Y
        Ket
        Ket
        End
------------------------------------------------------------------
    abcX
 0: abcX
 1: abc
    Y
 0: Y
    ** Failers
No match
    abcY   
No match
    
/^((?(+1)X|Y)(abc))+/BZ 
------------------------------------------------------------------
        Bra
        ^
        CBra 1
        Cond
      2 Cond ref
        X
        Alt
        Y
        Ket
        CBra 2
        abc
        Ket
        KetRmax
        Ket
        End
------------------------------------------------------------------
    YabcXabc
 0: YabcXabc
 1: Xabc
 2: abc
    YabcXabcXabc
 0: YabcXabcXabc
 1: Xabc
 2: abc
    ** Failers
No match
    XabcXabc  
No match

/(?(-1)a)/BZ
Failed: reference to non-existent subpattern at offset 6

/((?(-1)a))/BZ
------------------------------------------------------------------
        Bra
        CBra 1
        Cond
      1 Cond ref
        a
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------

/((?(-2)a))/BZ
Failed: reference to non-existent subpattern at offset 7

/^(?(+1)X|Y)(.)/BZ
------------------------------------------------------------------
        Bra
        ^
        Cond
      1 Cond ref
        X
        Alt
        Y
        Ket
        CBra 1
        Any
        Ket
        Ket
        End
------------------------------------------------------------------
    Y!
 0: Y!
 1: !

/(?<A>tom|bon)-\k{A}/
    tom-tom
 0: tom-tom
 1: tom
    bon-bon 
 0: bon-bon
 1: bon
    ** Failers
No match
    tom-bon  
No match

/\g{A/ 
Failed: syntax error in subpattern name (missing terminator) at offset 4

/(?|(abc)|(xyz))/BZ
------------------------------------------------------------------
        Bra
        Bra
        CBra 1
        abc
        Ket
        Alt
        CBra 1
        xyz
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------
   >abc<
 0: abc
 1: abc
   >xyz< 
 0: xyz
 1: xyz

/(x)(?|(abc)|(xyz))(x)/BZ
------------------------------------------------------------------
        Bra
        CBra 1
        x
        Ket
        Bra
        CBra 2
        abc
        Ket
        Alt
        CBra 2
        xyz
        Ket
        Ket
        CBra 3
        x
        Ket
        Ket
        End
------------------------------------------------------------------
    xabcx
 0: xabcx
 1: x
 2: abc
 3: x
    xxyzx 
 0: xxyzx
 1: x
 2: xyz
 3: x

/(x)(?|(abc)(pqr)|(xyz))(x)/BZ
------------------------------------------------------------------
        Bra
        CBra 1
        x
        Ket
        Bra
        CBra 2
        abc
        Ket
        CBra 3
        pqr
        Ket
        Alt
        CBra 2
        xyz
        Ket
        Ket
        CBra 4
        x
        Ket
        Ket
        End
------------------------------------------------------------------
    xabcpqrx
 0: xabcpqrx
 1: x
 2: abc
 3: pqr
 4: x
    xxyzx 
 0: xxyzx
 1: x
 2: xyz
 3: <unset>
 4: x

/\H++X/BZ
------------------------------------------------------------------
        Bra
        \H++
        X
        Ket
        End
------------------------------------------------------------------
    ** Failers
No match
    XXXX
No match
    
/\H+\hY/BZ
------------------------------------------------------------------
        Bra
        \H++
        \h
        Y
        Ket
        End
------------------------------------------------------------------
    XXXX Y 
 0: XXXX Y

/\H+ Y/BZ
------------------------------------------------------------------
        Bra
        \H++
         Y
        Ket
        End
------------------------------------------------------------------

/\h+A/BZ
------------------------------------------------------------------
        Bra
        \h++
        A
        Ket
        End
------------------------------------------------------------------

/\v*B/BZ
------------------------------------------------------------------
        Bra
        \v*+
        B
        Ket
        End
------------------------------------------------------------------

/\V+\x0a/BZ
------------------------------------------------------------------
        Bra
        \V++
        \x0a
        Ket
        End
------------------------------------------------------------------

/A+\h/BZ
------------------------------------------------------------------
        Bra
        A++
        \h
        Ket
        End
------------------------------------------------------------------

/ *\H/BZ
------------------------------------------------------------------
        Bra
         *+
        \H
        Ket
        End
------------------------------------------------------------------

/A*\v/BZ
------------------------------------------------------------------
        Bra
        A*+
        \v
        Ket
        End
------------------------------------------------------------------

/\x0b*\V/BZ
------------------------------------------------------------------
        Bra
        \x0b*+
        \V
        Ket
        End
------------------------------------------------------------------

/\d+\h/BZ
------------------------------------------------------------------
        Bra
        \d++
        \h
        Ket
        End
------------------------------------------------------------------

/\d*\v/BZ
------------------------------------------------------------------
        Bra
        \d*+
        \v
        Ket
        End
------------------------------------------------------------------

/S+\h\S+\v/BZ
------------------------------------------------------------------
        Bra
        S++
        \h
        \S++
        \v
        Ket
        End
------------------------------------------------------------------

/\w{3,}\h\w+\v/BZ
------------------------------------------------------------------
        Bra
        \w{3}
        \w*+
        \h
        \w++
        \v
        Ket
        End
------------------------------------------------------------------

/\h+\d\h+\w\h+\S\h+\H/BZ
------------------------------------------------------------------
        Bra
        \h++
        \d
        \h++
        \w
        \h++
        \S
        \h++
        \H
        Ket
        End
------------------------------------------------------------------

/\v+\d\v+\w\v+\S\v+\V/BZ
------------------------------------------------------------------
        Bra
        \v++
        \d
        \v++
        \w
        \v++
        \S
        \v++
        \V
        Ket
        End
------------------------------------------------------------------

/\H+\h\H+\d/BZ
------------------------------------------------------------------
        Bra
        \H++
        \h
        \H+
        \d
        Ket
        End
------------------------------------------------------------------

/\V+\v\V+\w/BZ
------------------------------------------------------------------
        Bra
        \V++
        \v
        \V+
        \w
        Ket
        End
------------------------------------------------------------------

/\( (?: [^()]* | (?R) )* \)/x
\J1024(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(00)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)
 0: (0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(0(00)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)0)

/[\E]AAA/
Failed: missing terminating ] for character class at offset 7

/[\Q\E]AAA/
Failed: missing terminating ] for character class at offset 9

/[^\E]AAA/
Failed: missing terminating ] for character class at offset 8

/[^\Q\E]AAA/
Failed: missing terminating ] for character class at offset 10

/[\E^]AAA/
Failed: missing terminating ] for character class at offset 8

/[\Q\E^]AAA/
Failed: missing terminating ] for character class at offset 10

/A(*PRUNE)B(*SKIP)C(*THEN)D(*COMMIT)E(*F)F(*FAIL)G(?!)H(*ACCEPT)I/BZ
------------------------------------------------------------------
        Bra
        A
        *PRUNE
        B
        *SKIP
        C
        *THEN
        D
        *COMMIT
        E
        *FAIL
        F
        *FAIL
        G
        *FAIL
        H
        *ACCEPT
        I
        Ket
        End
------------------------------------------------------------------

/^a+(*FAIL)/C
    aaaaaa
--->aaaaaa
 +0 ^          ^
 +1 ^          a+
 +3 ^     ^    (*FAIL)
 +3 ^    ^     (*FAIL)
 +3 ^   ^      (*FAIL)
 +3 ^  ^       (*FAIL)
 +3 ^ ^        (*FAIL)
 +3 ^^         (*FAIL)
No match
    
/a+b?c+(*FAIL)/C
    aaabccc
--->aaabccc
 +0 ^           a+
 +2 ^  ^        b?
 +4 ^   ^       c+
 +6 ^      ^    (*FAIL)
 +6 ^     ^     (*FAIL)
 +6 ^    ^      (*FAIL)
 +0  ^          a+
 +2  ^ ^        b?
 +4  ^  ^       c+
 +6  ^     ^    (*FAIL)
 +6  ^    ^     (*FAIL)
 +6  ^   ^      (*FAIL)
 +0   ^         a+
 +2   ^^        b?
 +4   ^ ^       c+
 +6   ^    ^    (*FAIL)
 +6   ^   ^     (*FAIL)
 +6   ^  ^      (*FAIL)
No match

/a+b?(*PRUNE)c+(*FAIL)/C
    aaabccc
--->aaabccc
 +0 ^           a+
 +2 ^  ^        b?
 +4 ^   ^       (*PRUNE)
+12 ^   ^       c+
+14 ^      ^    (*FAIL)
+14 ^     ^     (*FAIL)
+14 ^    ^      (*FAIL)
 +0  ^          a+
 +2  ^ ^        b?
 +4  ^  ^       (*PRUNE)
+12  ^  ^       c+
+14  ^     ^    (*FAIL)
+14  ^    ^     (*FAIL)
+14  ^   ^      (*FAIL)
 +0   ^         a+
 +2   ^^        b?
 +4   ^ ^       (*PRUNE)
+12   ^ ^       c+
+14   ^    ^    (*FAIL)
+14   ^   ^     (*FAIL)
+14   ^  ^      (*FAIL)
No match

/a+b?(*COMMIT)c+(*FAIL)/C
    aaabccc
--->aaabccc
 +0 ^           a+
 +2 ^  ^        b?
 +4 ^   ^       (*COMMIT)
+13 ^   ^       c+
+15 ^      ^    (*FAIL)
+15 ^     ^     (*FAIL)
+15 ^    ^      (*FAIL)
No match
    
/a+b?(*SKIP)c+(*FAIL)/C
    aaabcccaaabccc
--->aaabcccaaabccc
 +0 ^                  a+
 +2 ^  ^               b?
 +4 ^   ^              (*SKIP)
+11 ^   ^              c+
+13 ^      ^           (*FAIL)
+13 ^     ^            (*FAIL)
+13 ^    ^             (*FAIL)
 +0        ^           a+
 +2        ^  ^        b?
 +4        ^   ^       (*SKIP)
+11        ^   ^       c+
+13        ^      ^    (*FAIL)
+13        ^     ^     (*FAIL)
+13        ^    ^      (*FAIL)
No match

/a+b?(*THEN)c+(*FAIL)/C
    aaabccc
--->aaabccc
 +0 ^           a+
 +2 ^  ^        b?
 +4 ^   ^       (*THEN)
+11 ^   ^       c+
+13 ^      ^    (*FAIL)
+13 ^     ^     (*FAIL)
+13 ^    ^      (*FAIL)
 +0  ^          a+
 +2  ^ ^        b?
 +4  ^  ^       (*THEN)
+11  ^  ^       c+
+13  ^     ^    (*FAIL)
+13  ^    ^     (*FAIL)
+13  ^   ^      (*FAIL)
 +0   ^         a+
 +2   ^^        b?
 +4   ^ ^       (*THEN)
+11   ^ ^       c+
+13   ^    ^    (*FAIL)
+13   ^   ^     (*FAIL)
+13   ^  ^      (*FAIL)
No match
    
/a(*MARK)b/ 
Failed: (*MARK) must have an argument at offset 7

/(?i:A{1,}\6666666666)/
Failed: number is too big at offset 19

/\g6666666666/
Failed: number is too big at offset 11

/[\g6666666666]/BZ
------------------------------------------------------------------
        Bra
        [6g]
        Ket
        End
------------------------------------------------------------------

/(?1)\c[/
Failed: reference to non-existent subpattern at offset 3

/.+A/<crlf>
    \r\nA
No match
    
/\nA/<crlf>
    \r\nA 
 0: \x0aA

/[\r\n]A/<crlf>
    \r\nA 
 0: \x0aA

/(\r|\n)A/<crlf>
    \r\nA 
 0: \x0aA
 1: \x0a

/a(*CR)b/
Failed: (*VERB) not recognized or malformed at offset 5

/(*CR)a.b/
    a\nb
 0: a\x0ab
    ** Failers
No match
    a\rb  
No match

/(*CR)a.b/<lf>
    a\nb
 0: a\x0ab
    ** Failers
No match
    a\rb  
No match

/(*LF)a.b/<CRLF>
    a\rb
 0: a\x0db
    ** Failers
No match
    a\nb  
No match

/(*CRLF)a.b/
    a\rb
 0: a\x0db
    a\nb  
 0: a\x0ab
    ** Failers
No match
    a\r\nb  
No match

/(*ANYCRLF)a.b/<CR>
    ** Failers
No match
    a\rb
No match
    a\nb  
No match
    a\r\nb  
No match

/(*ANY)a.b/<cr>
    ** Failers
No match
    a\rb
No match
    a\nb  
No match
    a\r\nb  
No match
    a\x85b 
No match
    
/(*ANY).*/g
    abc\r\ndef
 0: abc
 0: 
 0: def
 0: 

/(*ANYCRLF).*/g
    abc\r\ndef
 0: abc
 0: 
 0: def
 0: 

/(*CRLF).*/g
    abc\r\ndef
 0: abc
 0: 
 0: def
 0: 

/a\Rb/I<bsr_anycrlf>
Capturing subpattern count = 0
Options: bsr_anycrlf
First char = 'a'
Need char = 'b'
    a\rb
 0: a\x0db
    a\nb
 0: a\x0ab
    a\r\nb
 0: a\x0d\x0ab
    ** Failers
No match
    a\x85b
No match
    a\x0bb     
No match

/a\Rb/I<bsr_unicode>
Capturing subpattern count = 0
Options: bsr_unicode
First char = 'a'
Need char = 'b'
    a\rb
 0: a\x0db
    a\nb
 0: a\x0ab
    a\r\nb
 0: a\x0d\x0ab
    a\x85b
 0: a\x85b
    a\x0bb     
 0: a\x0bb
    ** Failers 
No match
    a\x85b\<bsr_anycrlf>
No match
    a\x0bb\<bsr_anycrlf>
No match
    
/a\R?b/I<bsr_anycrlf>
Capturing subpattern count = 0
Options: bsr_anycrlf
First char = 'a'
Need char = 'b'
    a\rb
 0: a\x0db
    a\nb
 0: a\x0ab
    a\r\nb
 0: a\x0d\x0ab
    ** Failers
No match
    a\x85b
No match
    a\x0bb     
No match

/a\R?b/I<bsr_unicode>
Capturing subpattern count = 0
Options: bsr_unicode
First char = 'a'
Need char = 'b'
    a\rb
 0: a\x0db
    a\nb
 0: a\x0ab
    a\r\nb
 0: a\x0d\x0ab
    a\x85b
 0: a\x85b
    a\x0bb     
 0: a\x0bb
    ** Failers 
No match
    a\x85b\<bsr_anycrlf>
No match
    a\x0bb\<bsr_anycrlf>
No match
    
/a\R{2,4}b/I<bsr_anycrlf>
Capturing subpattern count = 0
Options: bsr_anycrlf
First char = 'a'
Need char = 'b'
    a\r\n\nb
 0: a\x0d\x0a\x0ab
    a\n\r\rb
 0: a\x0a\x0d\x0db
    a\r\n\r\n\r\n\r\nb
 0: a\x0d\x0a\x0d\x0a\x0d\x0a\x0d\x0ab
    ** Failers
No match
    a\x85\85b
No match
    a\x0b\0bb     
No match

/a\R{2,4}b/I<bsr_unicode>
Capturing subpattern count = 0
Options: bsr_unicode
First char = 'a'
Need char = 'b'
    a\r\rb
 0: a\x0d\x0db
    a\n\n\nb
 0: a\x0a\x0a\x0ab
    a\r\n\n\r\rb
 0: a\x0d\x0a\x0a\x0d\x0db
    a\x85\85b
No match
    a\x0b\0bb     
No match
    ** Failers 
No match
    a\r\r\r\r\rb 
No match
    a\x85\85b\<bsr_anycrlf>
No match
    a\x0b\0bb\<bsr_anycrlf>
No match
 
/(*BSR_ANYCRLF)a\Rb/I
Capturing subpattern count = 0
Options: bsr_anycrlf
First char = 'a'
Need char = 'b'
    a\nb
 0: a\x0ab
    a\rb 
 0: a\x0db

/(*BSR_UNICODE)a\Rb/I
Capturing subpattern count = 0
Options: bsr_unicode
First char = 'a'
Need char = 'b'
    a\x85b
 0: a\x85b

/(*BSR_ANYCRLF)(*CRLF)a\Rb/I
Capturing subpattern count = 0
Options: bsr_anycrlf
Forced newline sequence: CRLF
First char = 'a'
Need char = 'b'
    a\nb
 0: a\x0ab
    a\rb 
 0: a\x0db

/(*CRLF)(*BSR_UNICODE)a\Rb/I
Capturing subpattern count = 0
Options: bsr_unicode
Forced newline sequence: CRLF
First char = 'a'
Need char = 'b'
    a\x85b
 0: a\x85b

/(*CRLF)(*BSR_ANYCRLF)(*CR)ab/I
Capturing subpattern count = 0
Options: bsr_anycrlf
Forced newline sequence: CR
First char = 'a'
Need char = 'b'

/(?<a>)(?&)/
Failed: subpattern name expected at offset 9

/(?<abc>)(?&a)/
Failed: reference to non-existent subpattern at offset 12

/(?<a>)(?&aaaaaaaaaaaaaaaaaaaaaaa)/
Failed: reference to non-existent subpattern at offset 32

/(?+-a)/
Failed: digit expected after (?+ at offset 3

/(?-+a)/
Failed: unrecognized character after (? or (?- at offset 3

/(?(-1))/
Failed: reference to non-existent subpattern at offset 6

/(?(+10))/
Failed: reference to non-existent subpattern at offset 7

/(?(10))/
Failed: reference to non-existent subpattern at offset 6

/(?(+2))()()/

/(?(2))()()/

/\k''/
Failed: subpattern name expected at offset 3

/\k<>/
Failed: subpattern name expected at offset 3

/\k{}/
Failed: subpattern name expected at offset 3

/\k/
Failed: \k is not followed by a braced, angle-bracketed, or quoted name at offset 2

/\kabc/
Failed: \k is not followed by a braced, angle-bracketed, or quoted name at offset 5

/(?P=)/
Failed: subpattern name expected at offset 4

/(?P>)/
Failed: subpattern name expected at offset 4

/(?!\w)(?R)/
Failed: recursive call could loop indefinitely at offset 9

/(?=\w)(?R)/
Failed: recursive call could loop indefinitely at offset 9

/(?<!\w)(?R)/
Failed: recursive call could loop indefinitely at offset 10

/(?<=\w)(?R)/
Failed: recursive call could loop indefinitely at offset 10

/[[:foo:]]/
Failed: unknown POSIX class name at offset 3

/[[:1234:]]/
Failed: unknown POSIX class name at offset 3

/[[:f\oo:]]/
Failed: unknown POSIX class name at offset 3

/[[: :]]/
Failed: unknown POSIX class name at offset 3

/[[:...:]]/
Failed: unknown POSIX class name at offset 3

/[[:l\ower:]]/
Failed: unknown POSIX class name at offset 3

/[[:abc\:]]/
Failed: unknown POSIX class name at offset 3

/[abc[:x\]pqr:]]/
Failed: unknown POSIX class name at offset 6

/[[:a\dz:]]/
Failed: unknown POSIX class name at offset 3

/(^(a|b\g<-1'c))/
Failed: \g is not followed by a braced, angle-bracketed, or quoted name/number or by a plain number at offset 15

/^(?+1)(?<a>x|y){0}z/
    xzxx
 0: xz
    yzyy 
 0: yz
    ** Failers
No match
    xxz  
No match

/(\3)(\1)(a)/
    cat
No match

/(\3)(\1)(a)/<JS>
    cat
 0: a
 1: 
 2: 
 3: a
    
/TA]/
    The ACTA] comes 
 0: TA]

/TA]/<JS>
Failed: ] is an invalid data character in JavaScript compatibility mode at offset 2

/(?2)[]a()b](abc)/
Failed: reference to non-existent subpattern at offset 3

/(?2)[^]a()b](abc)/
Failed: reference to non-existent subpattern at offset 3

/(?1)[]a()b](abc)/
    abcbabc
 0: abcbabc
 1: abc
    ** Failers 
No match
    abcXabc
No match

/(?1)[^]a()b](abc)/
    abcXabc
 0: abcXabc
 1: abc
    ** Failers 
No match
    abcbabc
No match

/(?2)[]a()b](abc)(xyz)/
    xyzbabcxyz
 0: xyzbabcxyz
 1: abc
 2: xyz

/(?&N)[]a(?<N>)](?<M>abc)/
Failed: reference to non-existent subpattern at offset 4

/(?&N)[]a(?<N>)](abc)/
Failed: reference to non-existent subpattern at offset 4

/a[]b/
Failed: missing terminating ] for character class at offset 4

/a[^]b/
Failed: missing terminating ] for character class at offset 5

/a[]b/<JS>
    ** Failers
No match
    ab
No match

/a[]+b/<JS>
    ** Failers
No match
    ab 
No match

/a[]*+b/<JS>
    ** Failers
No match
    ab 
No match

/a[^]b/<JS>
    aXb
 0: aXb
    a\nb 
 0: a\x0ab
    ** Failers
No match
    ab  
No match
    
/a[^]+b/<JS> 
    aXb
 0: aXb
    a\nX\nXb 
 0: a\x0aX\x0aXb
    ** Failers
No match
    ab  
No match

/a(?!)b/BZ
------------------------------------------------------------------
        Bra
        a
        *FAIL
        b
        Ket
        End
------------------------------------------------------------------

/(?!)?a/BZ
------------------------------------------------------------------
        Bra
        Brazero
        Assert not
        Ket
        a
        Ket
        End
------------------------------------------------------------------
    ab
 0: a

/a(*FAIL)+b/
Failed: nothing to repeat at offset 8

/(abc|pqr|123){0}[xyz]/SI
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: x y z 

/(?(?=.*b)b|^)/CI
Capturing subpattern count = 0
May match empty string
Options:
No first char
No need char
   adc
--->adc
 +0 ^       (?(?=.*b)b|^)
 +2 ^       (?=.*b)
 +5 ^       .*
 +7 ^  ^    b
 +7 ^ ^     b
 +7 ^^      b
 +7 ^       b
+11 ^       ^
+12 ^       )
+13 ^       
 0: 
   abc 
--->abc
 +0 ^       (?(?=.*b)b|^)
 +2 ^       (?=.*b)
 +5 ^       .*
 +7 ^  ^    b
 +7 ^ ^     b
 +7 ^^      b
 +8 ^ ^     )
 +9 ^       b
 +0  ^      (?(?=.*b)b|^)
 +2  ^      (?=.*b)
 +5  ^      .*
 +7  ^ ^    b
 +7  ^^     b
 +7  ^      b
 +8  ^^     )
 +9  ^      b
+10  ^^     |
+13  ^^     
 0: b
   
/(?(?=b).*b|^d)/I
Capturing subpattern count = 0
No options
No first char
No need char

/(?(?=.*b).*b|^d)/I
Capturing subpattern count = 0
No options
First char at start or follows newline
No need char

/xyz/C
  xyz 
--->xyz
 +0 ^       x
 +1 ^^      y
 +2 ^ ^     z
 +3 ^  ^    
 0: xyz
  abcxyz 
--->abcxyz
 +0    ^       x
 +1    ^^      y
 +2    ^ ^     z
 +3    ^  ^    
 0: xyz
  abcxyz\Y
--->abcxyz
 +0 ^          x
 +0  ^         x
 +0   ^        x
 +0    ^       x
 +1    ^^      y
 +2    ^ ^     z
 +3    ^  ^    
 0: xyz
  ** Failers 
No match
  abc
No match
  abc\Y
--->abc
 +0 ^       x
 +0  ^      x
 +0   ^     x
 +0    ^    x
No match
  abcxypqr  
No match
  abcxypqr\Y  
--->abcxypqr
 +0 ^            x
 +0  ^           x
 +0   ^          x
 +0    ^         x
 +1    ^^        y
 +2    ^ ^       z
 +0     ^        x
 +0      ^       x
 +0       ^      x
 +0        ^     x
 +0         ^    x
No match
  
/(*NO_START_OPT)xyz/C
  abcxyz
--->abcxyz
+15 ^          x
+15  ^         x
+15   ^        x
+15    ^       x
+16    ^^      y
+17    ^ ^     z
+18    ^  ^    
 0: xyz
  
/(*NO_AUTO_POSSESS)a+b/BZ  
------------------------------------------------------------------
        Bra
        a+
        b
        Ket
        End
------------------------------------------------------------------

/xyz/CY
  abcxyz 
--->abcxyz
 +0 ^          x
 +0  ^         x
 +0   ^        x
 +0    ^       x
 +1    ^^      y
 +2    ^ ^     z
 +3    ^  ^    
 0: xyz

/^"((?(?=[a])[^"])|b)*"$/C
    "ab"
--->"ab"
 +0 ^        ^
 +1 ^        "
 +2 ^^       ((?(?=[a])[^"])|b)*
 +3 ^^       (?(?=[a])[^"])
 +5 ^^       (?=[a])
 +8 ^^       [a]
+11 ^ ^      )
+12 ^^       [^"]
+16 ^ ^      )
+17 ^ ^      |
 +3 ^ ^      (?(?=[a])[^"])
 +5 ^ ^      (?=[a])
 +8 ^ ^      [a]
+17 ^ ^      |
+21 ^ ^      "
+18 ^ ^      b
+19 ^  ^     )
 +3 ^  ^     (?(?=[a])[^"])
 +5 ^  ^     (?=[a])
 +8 ^  ^     [a]
+17 ^  ^     |
+21 ^  ^     "
+22 ^   ^    $
+23 ^   ^    
 0: "ab"
 1: 

/^"((?(?=[a])[^"])|b)*"$/
    "ab"
 0: "ab"
 1: 

/^X(?5)(a)(?|(b)|(q))(c)(d)Y/
Failed: reference to non-existent subpattern at offset 5

/^X(?&N)(a)(?|(b)|(q))(c)(d)(?<N>Y)/
    XYabcdY
 0: XYabcdY
 1: a
 2: b
 3: c
 4: d
 5: Y
 
/Xa{2,4}b/
    X\P
Partial match: X
    Xa\P
Partial match: Xa
    Xaa\P 
Partial match: Xaa
    Xaaa\P
Partial match: Xaaa
    Xaaaa\P 
Partial match: Xaaaa
    
/Xa{2,4}?b/
    X\P
Partial match: X
    Xa\P
Partial match: Xa
    Xaa\P 
Partial match: Xaa
    Xaaa\P
Partial match: Xaaa
    Xaaaa\P 
Partial match: Xaaaa
    
/Xa{2,4}+b/
    X\P
Partial match: X
    Xa\P
Partial match: Xa
    Xaa\P 
Partial match: Xaa
    Xaaa\P
Partial match: Xaaa
    Xaaaa\P 
Partial match: Xaaaa
    
/X\d{2,4}b/
    X\P
Partial match: X
    X3\P
Partial match: X3
    X33\P 
Partial match: X33
    X333\P
Partial match: X333
    X3333\P 
Partial match: X3333
    
/X\d{2,4}?b/
    X\P
Partial match: X
    X3\P
Partial match: X3
    X33\P 
Partial match: X33
    X333\P
Partial match: X333
    X3333\P 
Partial match: X3333
    
/X\d{2,4}+b/
    X\P
Partial match: X
    X3\P
Partial match: X3
    X33\P 
Partial match: X33
    X333\P
Partial match: X333
    X3333\P 
Partial match: X3333
    
/X\D{2,4}b/
    X\P
Partial match: X
    Xa\P
Partial match: Xa
    Xaa\P 
Partial match: Xaa
    Xaaa\P
Partial match: Xaaa
    Xaaaa\P 
Partial match: Xaaaa
    
/X\D{2,4}?b/
    X\P
Partial match: X
    Xa\P
Partial match: Xa
    Xaa\P 
Partial match: Xaa
    Xaaa\P
Partial match: Xaaa
    Xaaaa\P 
Partial match: Xaaaa
    
/X\D{2,4}+b/
    X\P
Partial match: X
    Xa\P
Partial match: Xa
    Xaa\P 
Partial match: Xaa
    Xaaa\P
Partial match: Xaaa
    Xaaaa\P 
Partial match: Xaaaa
    
/X[abc]{2,4}b/
    X\P
Partial match: X
    Xa\P
Partial match: Xa
    Xaa\P 
Partial match: Xaa
    Xaaa\P
Partial match: Xaaa
    Xaaaa\P 
Partial match: Xaaaa
    
/X[abc]{2,4}?b/
    X\P
Partial match: X
    Xa\P
Partial match: Xa
    Xaa\P 
Partial match: Xaa
    Xaaa\P
Partial match: Xaaa
    Xaaaa\P 
Partial match: Xaaaa
    
/X[abc]{2,4}+b/
    X\P
Partial match: X
    Xa\P
Partial match: Xa
    Xaa\P 
Partial match: Xaa
    Xaaa\P
Partial match: Xaaa
    Xaaaa\P 
Partial match: Xaaaa
    
/X[^a]{2,4}b/
    X\P
Partial match: X
    Xz\P
Partial match: Xz
    Xzz\P 
Partial match: Xzz
    Xzzz\P
Partial match: Xzzz
    Xzzzz\P 
Partial match: Xzzzz
    
/X[^a]{2,4}?b/
    X\P
Partial match: X
    Xz\P
Partial match: Xz
    Xzz\P 
Partial match: Xzz
    Xzzz\P
Partial match: Xzzz
    Xzzzz\P 
Partial match: Xzzzz
    
/X[^a]{2,4}+b/
    X\P
Partial match: X
    Xz\P
Partial match: Xz
    Xzz\P 
Partial match: Xzz
    Xzzz\P
Partial match: Xzzz
    Xzzzz\P 
Partial match: Xzzzz
    
/(Y)X\1{2,4}b/
    YX\P
Partial match: YX
    YXY\P
Partial match: YXY
    YXYY\P 
Partial match: YXYY
    YXYYY\P
Partial match: YXYYY
    YXYYYY\P 
Partial match: YXYYYY
    
/(Y)X\1{2,4}?b/
    YX\P
Partial match: YX
    YXY\P
Partial match: YXY
    YXYY\P 
Partial match: YXYY
    YXYYY\P
Partial match: YXYYY
    YXYYYY\P 
Partial match: YXYYYY
    
/(Y)X\1{2,4}+b/
    YX\P
Partial match: YX
    YXY\P
Partial match: YXY
    YXYY\P 
Partial match: YXYY
    YXYYY\P
Partial match: YXYYY
    YXYYYY\P 
Partial match: YXYYYY
    
/\++\KZ|\d+X|9+Y/
    ++++123999\P
Partial match: 123999
    ++++123999Y\P
 0: 999Y
    ++++Z1234\P 
 0: Z

/Z(*F)/
    Z\P
No match
    ZA\P 
No match
    
/Z(?!)/
    Z\P 
No match
    ZA\P 
No match

/dog(sbody)?/
    dogs\P
 0: dog
    dogs\P\P 
Partial match: dogs
    
/dog(sbody)??/
    dogs\P
 0: dog
    dogs\P\P 
 0: dog

/dog|dogsbody/
    dogs\P
 0: dog
    dogs\P\P 
 0: dog
 
/dogsbody|dog/
    dogs\P
 0: dog
    dogs\P\P 
Partial match: dogs

/\bthe cat\b/
    the cat\P
 0: the cat
    the cat\P\P
Partial match: the cat

/abc/
   abc\P
 0: abc
   abc\P\P
 0: abc
   
/abc\K123/
    xyzabc123pqr
 0: 123
    xyzabc12\P
Partial match: abc12
    xyzabc12\P\P
Partial match: abc12
    
/(?<=abc)123/
    xyzabc123pqr 
 0: 123
    xyzabc12\P
Partial match at offset 6: abc12
    xyzabc12\P\P
Partial match at offset 6: abc12

/\babc\b/
    +++abc+++
 0: abc
    +++ab\P
Partial match at offset 3: +ab
    +++ab\P\P  
Partial match at offset 3: +ab

/(?&word)(?&element)(?(DEFINE)(?<element><[^m][^>]>[^<])(?<word>\w*+))/BZ
------------------------------------------------------------------
        Bra
        Recurse
        Recurse
        Cond
        Cond def
        CBra 1
        <
        [^m]
        [^>]
        >
        [^<]
        Ket
        CBra 2
        \w*+
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------

/(?&word)(?&element)(?(DEFINE)(?<element><[^\d][^>]>[^<])(?<word>\w*+))/BZ
------------------------------------------------------------------
        Bra
        Recurse
        Recurse
        Cond
        Cond def
        CBra 1
        <
        [\x00-/:-\xff] (neg)
        [^>]
        >
        [^<]
        Ket
        CBra 2
        \w*+
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------

/(ab)(x(y)z(cd(*ACCEPT)))pq/BZ
------------------------------------------------------------------
        Bra
        CBra 1
        ab
        Ket
        CBra 2
        x
        CBra 3
        y
        Ket
        z
        CBra 4
        cd
        Close 4
        Close 2
        *ACCEPT
        Ket
        Ket
        pq
        Ket
        End
------------------------------------------------------------------

/abc\K/+
    abcdef
 0: 
 0+ def
    abcdef\N\N
 0: 
 0+ def
    xyzabcdef\N\N
 0: 
 0+ def
    ** Failers
No match
    abcdef\N 
No match
    xyzabcdef\N
No match
    
/^(?:(?=abc)|abc\K)/+
    abcdef
 0: 
 0+ abcdef
    abcdef\N\N 
 0: 
 0+ def
    ** Failers 
No match
    abcdef\N 
No match

/a?b?/+
    xyz
 0: 
 0+ xyz
    xyzabc
 0: 
 0+ xyzabc
    xyzabc\N
 0: ab
 0+ c
    xyzabc\N\N
 0: 
 0+ yzabc
    xyz\N\N    
 0: 
 0+ yz
    ** Failers 
 0: 
 0+ ** Failers
    xyz\N 
No match

/^a?b?/+
    xyz
 0: 
 0+ xyz
    xyzabc
 0: 
 0+ xyzabc
    ** Failers 
 0: 
 0+ ** Failers
    xyzabc\N
No match
    xyzabc\N\N
No match
    xyz\N\N    
No match
    xyz\N 
No match
    
/^(?<name>a|b\g<name>c)/
    aaaa
 0: a
 1: a
    bacxxx
 0: bac
 1: bac
    bbaccxxx 
 0: bbacc
 1: bbacc
    bbbacccxx
 0: bbbaccc
 1: bbbaccc

/^(?<name>a|b\g'name'c)/
    aaaa
 0: a
 1: a
    bacxxx
 0: bac
 1: bac
    bbaccxxx 
 0: bbacc
 1: bbacc
    bbbacccxx
 0: bbbaccc
 1: bbbaccc

/^(a|b\g<1>c)/
    aaaa
 0: a
 1: a
    bacxxx
 0: bac
 1: bac
    bbaccxxx 
 0: bbacc
 1: bbacc
    bbbacccxx
 0: bbbaccc
 1: bbbaccc

/^(a|b\g'1'c)/
    aaaa
 0: a
 1: a
    bacxxx
 0: bac
 1: bac
    bbaccxxx 
 0: bbacc
 1: bbacc
    bbbacccxx
 0: bbbaccc
 1: bbbaccc

/^(a|b\g'-1'c)/
    aaaa
 0: a
 1: a
    bacxxx
 0: bac
 1: bac
    bbaccxxx 
 0: bbacc
 1: bbacc
    bbbacccxx
 0: bbbaccc
 1: bbbaccc

/(^(a|b\g<-1>c))/
    aaaa
 0: a
 1: a
 2: a
    bacxxx
 0: bac
 1: bac
 2: bac
    bbaccxxx 
 0: bbacc
 1: bbacc
 2: bbacc
    bbbacccxx
 0: bbbaccc
 1: bbbaccc
 2: bbbaccc

/(?-i:\g<name>)(?i:(?<name>a))/
    XaaX
 0: aa
 1: a
    XAAX 
 0: AA
 1: A

/(?i:\g<name>)(?-i:(?<name>a))/
    XaaX
 0: aa
 1: a
    ** Failers 
No match
    XAAX 
No match

/(?-i:\g<+1>)(?i:(a))/
    XaaX
 0: aa
 1: a
    XAAX 
 0: AA
 1: A

/(?=(?<regex>(?#simplesyntax)\$(?<name>[a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*)(?:\[(?<index>[a-zA-Z0-9_\x{7f}-\x{ff}]+|\$\g<name>)\]|->\g<name>(\(.*?\))?)?|(?#simple syntax withbraces)\$\{(?:\g<name>(?<indices>\[(?:\g<index>|'(?:\\.|[^'\\])*'|"(?:\g<regex>|\\.|[^"\\])*")\])?|\g<complex>|\$\{\g<complex>\})\}|(?#complexsyntax)\{(?<complex>\$(?<segment>\g<name>(\g<indices>*|\(.*?\))?)(?:->\g<segment>)*|\$\g<complex>|\$\{\g<complex>\})\}))\{/

/(?<n>a|b|c)\g<n>*/
   abc
 0: abc
 1: a
   accccbbb 
 0: accccbbb
 1: a

/^X(?7)(a)(?|(b)|(q)(r)(s))(c)(d)(Y)/
    XYabcdY
 0: XYabcdY
 1: a
 2: b
 3: <unset>
 4: <unset>
 5: c
 6: d
 7: Y

/(?<=b(?1)|zzz)(a)/
    xbaax
 0: a
 1: a
    xzzzax 
 0: a
 1: a

/(a)(?<=b\1)/
Failed: lookbehind assertion is not fixed length at offset 10

/(a)(?<=b+(?1))/
Failed: lookbehind assertion is not fixed length at offset 13

/(a+)(?<=b(?1))/
Failed: lookbehind assertion is not fixed length at offset 14

/(a(?<=b(?1)))/
Failed: lookbehind assertion is not fixed length at offset 13

/(?<=b(?1))xyz/
Failed: reference to non-existent subpattern at offset 8

/(?<=b(?1))xyz(b+)pqrstuvew/
Failed: lookbehind assertion is not fixed length at offset 26

/(a|bc)\1/SI
Capturing subpattern count = 1
Max back reference = 1
No options
No first char
No need char
Subject length lower bound = 2
Starting chars: a b 

/(a|bc)\1{2,3}/SI
Capturing subpattern count = 1
Max back reference = 1
No options
No first char
No need char
Subject length lower bound = 3
Starting chars: a b 

/(a|bc)(?1)/SI
Capturing subpattern count = 1
No options
No first char
No need char
Subject length lower bound = 2
Starting chars: a b 

/(a|b\1)(a|b\1)/SI
Capturing subpattern count = 2
Max back reference = 1
No options
No first char
No need char
Subject length lower bound = 2
Starting chars: a b 

/(a|b\1){2}/SI
Capturing subpattern count = 1
Max back reference = 1
No options
No first char
No need char
Subject length lower bound = 2
Starting chars: a b 

/(a|bbbb\1)(a|bbbb\1)/SI
Capturing subpattern count = 2
Max back reference = 1
No options
No first char
No need char
Subject length lower bound = 2
Starting chars: a b 

/(a|bbbb\1){2}/SI
Capturing subpattern count = 1
Max back reference = 1
No options
No first char
No need char
Subject length lower bound = 2
Starting chars: a b 

/^From +([^ ]+) +[a-zA-Z][a-zA-Z][a-zA-Z] +[a-zA-Z][a-zA-Z][a-zA-Z] +[0-9]?[0-9] +[0-9][0-9]:[0-9][0-9]/SI
Capturing subpattern count = 1
Options: anchored
No first char
Need char = ':'
Subject length lower bound = 22
No starting char list

/<tr([\w\W\s\d][^<>]{0,})><TD([\w\W\s\d][^<>]{0,})>([\d]{0,}\.)(.*)((<BR>([\w\W\s\d][^<>]{0,})|[\s]{0,}))<\/a><\/TD><TD([\w\W\s\d][^<>]{0,})>([\w\W\s\d][^<>]{0,})<\/TD><TD([\w\W\s\d][^<>]{0,})>([\w\W\s\d][^<>]{0,})<\/TD><\/TR>/isIS
Capturing subpattern count = 11
Options: caseless dotall
First char = '<'
Need char = '>'
Subject length lower bound = 47
No starting char list

"(?>.*/)foo"SI
Capturing subpattern count = 0
No options
No first char
Need char = 'o'
Subject length lower bound = 4
No starting char list

/(?(?=[^a-z]+[a-z])  \d{2}-[a-z]{3}-\d{2}  |  \d{2}-\d{2}-\d{2} ) /xSI
Capturing subpattern count = 0
Options: extended
No first char
Need char = '-'
Subject length lower bound = 8
No starting char list

/(?:(?:(?:(?:(?:(?:(?:(?:(?:(a|b|c))))))))))/iSI
Capturing subpattern count = 1
Options: caseless
No first char
No need char
Subject length lower bound = 1
Starting chars: A B C a b c 

/(?:c|d)(?:)(?:aaaaaaaa(?:)(?:bbbbbbbb)(?:bbbbbbbb(?:))(?:bbbbbbbb(?:)(?:bbbbbbbb)))/SI
Capturing subpattern count = 0
No options
No first char
Need char = 'b'
Subject length lower bound = 41
Starting chars: c d 

/<a[\s]+href[\s]*=[\s]*          # find <a href=
 ([\"\'])?                       # find single or double quote
 (?(1) (.*?)\1 | ([^\s]+))       # if quote found, match up to next matching
                                 # quote, otherwise match up to next space
/isxSI
Capturing subpattern count = 3
Max back reference = 1
Options: caseless extended dotall
First char = '<'
Need char = '='
Subject length lower bound = 9
No starting char list

/^(?!:)                       # colon disallowed at start
  (?:                         # start of item
    (?: [0-9a-f]{1,4} |       # 1-4 hex digits or
    (?(1)0 | () ) )           # if null previously matched, fail; else null
    :                         # followed by colon
  ){1,7}                      # end item; 1-7 of them required               
  [0-9a-f]{1,4} $             # final hex number at end of string
  (?(1)|.)                    # check that there was an empty component
  /xiIS
Capturing subpattern count = 1
Max back reference = 1
Options: anchored caseless extended
No first char
Need char = ':'
Subject length lower bound = 2
No starting char list

/(?|(?<a>A)|(?<a>B))/I
Capturing subpattern count = 1
Named capturing subpatterns:
  a   1
No options
No first char
No need char
    AB\Ca
 0: A
 1: A
  C A (1) a
    BA\Ca
 0: B
 1: B
  C B (1) a

/(?|(?<a>A)|(?<b>B))/ 
Failed: different names for subpatterns of the same number are not allowed at offset 15

/(?:a(?<quote> (?<apostrophe>')|(?<realquote>")) |
    b(?<quote> (?<apostrophe>')|(?<realquote>")) ) 
    (?('quote')[a-z]+|[0-9]+)/JIx
Capturing subpattern count = 6
Max back reference = 1
Named capturing subpatterns:
  apostrophe   2
  apostrophe   5
  quote        1
  quote        4
  realquote    3
  realquote    6
Options: extended dupnames
No first char
No need char
    a"aaaaa
 0: a"aaaaa
 1: "
 2: <unset>
 3: "
    b"aaaaa 
 0: b"aaaaa
 1: <unset>
 2: <unset>
 3: <unset>
 4: "
 5: <unset>
 6: "
    ** Failers 
No match
    b"11111
No match
    a"11111 
No match
    
/^(?|(a)(b)(c)(?<D>d)|(?<D>e)) (?('D')X|Y)/JDZx
------------------------------------------------------------------
        Bra
        ^
        Bra
        CBra 1
        a
        Ket
        CBra 2
        b
        Ket
        CBra 3
        c
        Ket
        CBra 4
        d
        Ket
        Alt
        CBra 1
        e
        Ket
        Ket
        Cond
        Cond ref <D>2
        X
        Alt
        Y
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 4
Max back reference = 4
Named capturing subpatterns:
  D   4
  D   1
Options: anchored extended dupnames
No first char
No need char
    abcdX
 0: abcdX
 1: a
 2: b
 3: c
 4: d
    eX
 0: eX
 1: e
    ** Failers
No match
    abcdY
No match
    ey     
No match
    
/(?<A>a) (b)(c)  (?<A>d  (?(R&A)$ | (?4)) )/JDZx
------------------------------------------------------------------
        Bra
        CBra 1
        a
        Ket
        CBra 2
        b
        Ket
        CBra 3
        c
        Ket
        CBra 4
        d
        Cond
        Cond recurse <A>2
        $
        Alt
        Recurse
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 4
Max back reference = 1
Named capturing subpatterns:
  A   1
  A   4
Options: extended dupnames
First char = 'a'
Need char = 'd'
    abcdd
 0: abcdd
 1: a
 2: b
 3: c
 4: dd
    ** Failers
No match
    abcdde  
No match

/abcd*/
    xxxxabcd\P
 0: abcd
    xxxxabcd\P\P
Partial match: abcd

/abcd*/i
    xxxxabcd\P
 0: abcd
    xxxxabcd\P\P
Partial match: abcd
    XXXXABCD\P
 0: ABCD
    XXXXABCD\P\P
Partial match: ABCD

/abc\d*/
    xxxxabc1\P
 0: abc1
    xxxxabc1\P\P
Partial match: abc1

/(a)bc\1*/
    xxxxabca\P
 0: abca
 1: a
    xxxxabca\P\P
Partial match: abca

/abc[de]*/
    xxxxabcde\P
 0: abcde
    xxxxabcde\P\P
Partial match: abcde

/-- This is not in the Perl-compatible test because Perl seems currently to be
    broken and not behaving as specified in that it *does* bumpalong after
    hitting (*COMMIT). --/

/(?1)(A(*COMMIT)|B)D/
    ABD
 0: ABD
 1: B
    XABD
 0: ABD
 1: B
    BAD
 0: BAD
 1: A
    ABXABD  
 0: ABD
 1: B
    ** Failers 
No match
    ABX 
No match
    BAXBAD  
No match

/(\3)(\1)(a)/<JS>
    cat
 0: a
 1: 
 2: 
 3: a

/(\3)(\1)(a)/SI<JS>
Capturing subpattern count = 3
Max back reference = 3
Options:
No first char
Need char = 'a'
Subject length lower bound = 1
No starting char list
    cat
 0: a
 1: 
 2: 
 3: a

/(\3)(\1)(a)/SI
Capturing subpattern count = 3
Max back reference = 3
No options
No first char
Need char = 'a'
Subject length lower bound = 3
No starting char list
    cat
No match

/i(?(DEFINE)(?<s>a))/SI
Capturing subpattern count = 1
Named capturing subpatterns:
  s   1
No options
First char = 'i'
No need char
Subject length lower bound = 1
No starting char list
    i
 0: i
    
/()i(?(1)a)/SI 
Capturing subpattern count = 1
Max back reference = 1
No options
No first char
Need char = 'i'
Subject length lower bound = 1
Starting chars: i 
    ia
 0: ia
 1: 

/(?i)a(?-i)b|c/BZ
------------------------------------------------------------------
        Bra
     /i a
        b
        Alt
        c
        Ket
        End
------------------------------------------------------------------
    XabX
 0: ab
    XAbX
 0: Ab
    CcC 
 0: c
    ** Failers
No match
    XABX   
No match

/(?i)a(?s)b|c/BZ
------------------------------------------------------------------
        Bra
     /i ab
        Alt
     /i c
        Ket
        End
------------------------------------------------------------------

/(?i)a(?s-i)b|c/BZ
------------------------------------------------------------------
        Bra
     /i a
        b
        Alt
        c
        Ket
        End
------------------------------------------------------------------

/^(ab(c\1)d|x){2}$/BZ
------------------------------------------------------------------
        Bra
        ^
        Once
        CBra 1
        ab
        CBra 2
        c
        \1
        Ket
        d
        Alt
        x
        Ket
        Ket
        Once
        CBra 1
        ab
        CBra 2
        c
        \1
        Ket
        d
        Alt
        x
        Ket
        Ket
        $
        Ket
        End
------------------------------------------------------------------
    xabcxd
 0: xabcxd
 1: abcxd
 2: cx
    
/^(?&t)*+(?(DEFINE)(?<t>.))$/BZ
------------------------------------------------------------------
        Bra
        ^
        Braposzero
        SBraPos
        Recurse
        KetRpos
        Cond
        Cond def
        CBra 1
        Any
        Ket
        Ket
        $
        Ket
        End
------------------------------------------------------------------

/^(?&t)*(?(DEFINE)(?<t>.))$/BZ
------------------------------------------------------------------
        Bra
        ^
        Brazero
        Once
        Recurse
        KetRmax
        Cond
        Cond def
        CBra 1
        Any
        Ket
        Ket
        $
        Ket
        End
------------------------------------------------------------------

/ -- This one is here because Perl gives the match as "b" rather than "ab". I
     believe this to be a Perl bug. --/  
      
/(?>a\Kb)z|(ab)/
    ab 
 0: ab
 1: ab

/(?P<L1>(?P<L2>0|)|(?P>L2)(?P>L1))/
Failed: recursive call could loop indefinitely at offset 31

/abc(*MARK:)pqr/
Failed: (*MARK) must have an argument at offset 10

/abc(*:)pqr/
Failed: (*MARK) must have an argument at offset 6

/abc(*FAIL:123)xyz/
Failed: an argument is not allowed for (*ACCEPT), (*FAIL), or (*COMMIT) at offset 13

/--- This should, and does, fail. In Perl, it does not, which I think is a 
     bug because replacing the B in the pattern by (B|D) does make it fail. ---/

/A(*COMMIT)B/+K
    ACABX
No match

/--- These should be different, but in Perl they are not, which I think
     is a bug in Perl. ---/

/A(*THEN)B|A(*THEN)C/K
    AC
 0: AC

/A(*PRUNE)B|A(*PRUNE)C/K
    AC
No match
    
/--- Mark names can be duplicated. Perl doesn't give a mark for this one,
though PCRE does. ---/

/^A(*:A)B|^X(*:A)Y/K
    ** Failers
No match
    XAQQ
No match, mark = A
    
/--- COMMIT at the start of a pattern should be the same as an anchor. Perl 
optimizations defeat this. So does the PCRE optimization unless we disable it 
with \Y. ---/

/(*COMMIT)ABC/
    ABCDEFG
 0: ABC
    ** Failers
No match
    DEFGABC\Y  
No match
    
/^(ab (c+(*THEN)cd) | xyz)/x
    abcccd  
No match

/^(ab (c+(*PRUNE)cd) | xyz)/x
    abcccd  
No match

/^(ab (c+(*FAIL)cd) | xyz)/x
    abcccd  
No match
    
/--- Perl gets some of these wrong ---/ 

/(?>.(*ACCEPT))*?5/
    abcde
 0: a

/(.(*ACCEPT))*?5/
    abcde
 0: a
 1: a

/(.(*ACCEPT))5/
    abcde
 0: a
 1: a

/(.(*ACCEPT))*5/
    abcde
 0: a
 1: a

/A\NB./BZ
------------------------------------------------------------------
        Bra
        A
        Any
        B
        Any
        Ket
        End
------------------------------------------------------------------
    ACBD
 0: ACBD
    *** Failers
No match
    A\nB
No match
    ACB\n   
No match

/A\NB./sBZ
------------------------------------------------------------------
        Bra
        A
        Any
        B
        AllAny
        Ket
        End
------------------------------------------------------------------
    ACBD
 0: ACBD
    ACB\n 
 0: ACB\x0a
    *** Failers
No match
    A\nB  
No match
  
/A\NB/<crlf>
    A\nB
 0: A\x0aB
    A\rB
 0: A\x0dB
    ** Failers
No match
    A\r\nB    
No match

/\R+b/BZ
------------------------------------------------------------------
        Bra
        \R++
        b
        Ket
        End
------------------------------------------------------------------

/\R+\n/BZ
------------------------------------------------------------------
        Bra
        \R+
        \x0a
        Ket
        End
------------------------------------------------------------------

/\R+\d/BZ
------------------------------------------------------------------
        Bra
        \R++
        \d
        Ket
        End
------------------------------------------------------------------

/\d*\R/BZ
------------------------------------------------------------------
        Bra
        \d*+
        \R
        Ket
        End
------------------------------------------------------------------

/\s*\R/BZ
------------------------------------------------------------------
        Bra
        \s*
        \R
        Ket
        End
------------------------------------------------------------------
    \x20\x0a
 0:  \x0a
    \x20\x0d
 0:  \x0d
    \x20\x0d\x0a
 0:  \x0d\x0a

/\S*\R/BZ
------------------------------------------------------------------
        Bra
        \S*+
        \R
        Ket
        End
------------------------------------------------------------------
    a\x0a
 0: a\x0a

/X\h*\R/BZ
------------------------------------------------------------------
        Bra
        X
        \h*+
        \R
        Ket
        End
------------------------------------------------------------------
    X\x20\x0a
 0: X \x0a

/X\H*\R/BZ
------------------------------------------------------------------
        Bra
        X
        \H*
        \R
        Ket
        End
------------------------------------------------------------------
    X\x0d\x0a
 0: X\x0d\x0a

/X\H+\R/BZ
------------------------------------------------------------------
        Bra
        X
        \H+
        \R
        Ket
        End
------------------------------------------------------------------
    X\x0d\x0a
 0: X\x0d\x0a

/X\H++\R/BZ
------------------------------------------------------------------
        Bra
        X
        \H++
        \R
        Ket
        End
------------------------------------------------------------------
    X\x0d\x0a
No match

/(?<=abc)def/
    abc\P\P
Partial match at offset 3: abc

/abc$/
    abc
 0: abc
    abc\P
 0: abc
    abc\P\P
Partial match: abc

/abc$/m
    abc
 0: abc
    abc\n
 0: abc
    abc\P\P
Partial match: abc
    abc\n\P\P 
 0: abc
    abc\P
 0: abc
    abc\n\P
 0: abc

/abc\z/
    abc
 0: abc
    abc\P
 0: abc
    abc\P\P
Partial match: abc

/abc\Z/
    abc
 0: abc
    abc\P
 0: abc
    abc\P\P
Partial match: abc

/abc\b/
    abc
 0: abc
    abc\P
 0: abc
    abc\P\P
Partial match: abc

/abc\B/
    abc
No match
    abc\P
Partial match: abc
    abc\P\P
Partial match: abc

/.+/
    abc\>0
 0: abc
    abc\>1
 0: bc
    abc\>2
 0: c
    abc\>3
No match
    abc\>4
Error -24 (bad offset value)
    abc\>-4 
Error -24 (bad offset value)

/^\cģ/
Failed: \c must be followed by an ASCII character at offset 3

/(?P<abn>(?P=abn)xxx)/BZ
------------------------------------------------------------------
        Bra
        Once
        CBra 1
        \1
        xxx
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------

/(a\1z)/BZ
------------------------------------------------------------------
        Bra
        Once
        CBra 1
        a
        \1
        z
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------

/(?P<abn>(?P=abn)(?<badstufxxx)/BZ
Failed: syntax error in subpattern name (missing terminator) at offset 29

/(?P<abn>(?P=axn)xxx)/BZ
Failed: reference to non-existent subpattern at offset 15

/(?P<abn>(?P=axn)xxx)(?<axn>yy)/BZ
------------------------------------------------------------------
        Bra
        CBra 1
        \2
        xxx
        Ket
        CBra 2
        yy
        Ket
        Ket
        End
------------------------------------------------------------------

/-- These tests are here because Perl gets the first one wrong. --/

/(\R*)(.)/s
    \r\n
 0: \x0d
 1: 
 2: \x0d
    \r\r\n\n\r 
 0: \x0d\x0d\x0a\x0a\x0d
 1: \x0d\x0d\x0a\x0a
 2: \x0d
    \r\r\n\n\r\n 
 0: \x0d\x0d\x0a\x0a\x0d
 1: \x0d\x0d\x0a\x0a
 2: \x0d

/(\R)*(.)/s
    \r\n
 0: \x0d
 1: <unset>
 2: \x0d
    \r\r\n\n\r 
 0: \x0d\x0d\x0a\x0a\x0d
 1: \x0a
 2: \x0d
    \r\r\n\n\r\n 
 0: \x0d\x0d\x0a\x0a\x0d
 1: \x0a
 2: \x0d

/((?>\r\n|\n|\x0b|\f|\r|\x85)*)(.)/s
    \r\n
 0: \x0d
 1: 
 2: \x0d
    \r\r\n\n\r 
 0: \x0d\x0d\x0a\x0a\x0d
 1: \x0d\x0d\x0a\x0a
 2: \x0d
    \r\r\n\n\r\n 
 0: \x0d\x0d\x0a\x0a\x0d
 1: \x0d\x0d\x0a\x0a
 2: \x0d

/-- --/

/^abc$/BZ
------------------------------------------------------------------
        Bra
        ^
        abc
        $
        Ket
        End
------------------------------------------------------------------

/^abc$/BZm
------------------------------------------------------------------
        Bra
     /m ^
        abc
     /m $
        Ket
        End
------------------------------------------------------------------

/^(a)*+(\w)/S
    aaaaX
 0: aaaaX
 1: a
 2: X
    ** Failers 
No match
    aaaa
No match

/^(?:a)*+(\w)/S
    aaaaX
 0: aaaaX
 1: X
    ** Failers 
No match
    aaaa
No match

/(a)++1234/SDZ
------------------------------------------------------------------
        Bra
        CBraPos 1
        a
        KetRpos
        1234
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 1
No options
First char = 'a'
Need char = '4'
Subject length lower bound = 5
No starting char list

/([abc])++1234/SI
Capturing subpattern count = 1
No options
No first char
Need char = '4'
Subject length lower bound = 5
Starting chars: a b c 

/(?<=(abc)+)X/
Failed: lookbehind assertion is not fixed length at offset 10

/(^ab)/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char

/(^ab)++/I
Capturing subpattern count = 1
Options: anchored
No first char
No need char

/(^ab|^)+/I
Capturing subpattern count = 1
May match empty string
Options: anchored
No first char
No need char

/(^ab|^)++/I
Capturing subpattern count = 1
May match empty string
Options: anchored
No first char
No need char

/(?:^ab)/I
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/(?:^ab)++/I
Capturing subpattern count = 0
Options: anchored
No first char
No need char

/(?:^ab|^)+/I
Capturing subpattern count = 0
May match empty string
Options: anchored
No first char
No need char

/(?:^ab|^)++/I
Capturing subpattern count = 0
May match empty string
Options: anchored
No first char
No need char

/(.*ab)/I
Capturing subpattern count = 1
No options
First char at start or follows newline
Need char = 'b'

/(.*ab)++/I
Capturing subpattern count = 1
No options
First char at start or follows newline
Need char = 'b'

/(.*ab|.*)+/I
Capturing subpattern count = 1
May match empty string
No options
First char at start or follows newline
No need char

/(.*ab|.*)++/I
Capturing subpattern count = 1
May match empty string
No options
First char at start or follows newline
No need char

/(?:.*ab)/I
Capturing subpattern count = 0
No options
First char at start or follows newline
Need char = 'b'

/(?:.*ab)++/I
Capturing subpattern count = 0
No options
First char at start or follows newline
Need char = 'b'

/(?:.*ab|.*)+/I
Capturing subpattern count = 0
May match empty string
No options
First char at start or follows newline
No need char

/(?:.*ab|.*)++/I
Capturing subpattern count = 0
May match empty string
No options
First char at start or follows newline
No need char

/(?=a)[bcd]/I
Capturing subpattern count = 0
No options
First char = 'a'
No need char

/((?=a))[bcd]/I
Capturing subpattern count = 1
No options
First char = 'a'
No need char

/((?=a))+[bcd]/I
Capturing subpattern count = 1
No options
First char = 'a'
No need char

/((?=a))++[bcd]/I
Capturing subpattern count = 1
No options
First char = 'a'
No need char

/(?=a+)[bcd]/iI
Capturing subpattern count = 0
Options: caseless
First char = 'a' (caseless)
No need char

/(?=a+?)[bcd]/iI
Capturing subpattern count = 0
Options: caseless
First char = 'a' (caseless)
No need char

/(?=a++)[bcd]/iI
Capturing subpattern count = 0
Options: caseless
First char = 'a' (caseless)
No need char

/(?=a{3})[bcd]/iI
Capturing subpattern count = 0
Options: caseless
First char = 'a' (caseless)
Need char = 'a' (caseless)

/(abc)\1+/S

/-- Perl doesn't get these right IMO (the 3rd is PCRE-specific) --/

/(?1)(?:(b(*ACCEPT))){0}/
    b
 0: b

/(?1)(?:(b(*ACCEPT))){0}c/
    bc
 0: bc
    ** Failers 
No match
    b 
No match

/(?1)(?:((*ACCEPT))){0}c/
    c
 0: c
    c\N 
 0: c

/^.*?(?(?=a)a|b(*THEN)c)/
    ba
No match

/^.*?(?(?=a)a|bc)/
    ba
 0: ba

/^.*?(?(?=a)a(*THEN)b|c)/
    ac
No match

/^.*?(?(?=a)a(*THEN)b)c/
    ac
No match

/^.*?(a(*THEN)b)c/
    aabc
No match
    
/^.*? (?1) c (?(DEFINE)(a(*THEN)b))/x
    aabc
 0: aabc

/^.*?(a(*THEN)b|z)c/
    aabc
 0: aabc
 1: ab

/^.*?(z|a(*THEN)b)c/
    aabc
 0: aabc
 1: ab

/-- --/

/-- These studied versions are here because they are not Perl-compatible; the
    studying means the mark is not seen. --/

/(*MARK:A)(*SKIP:B)(C|X)/KS
    C
 0: C
 1: C
MK: A
    D
No match, mark = A
     
/(*:A)A+(*SKIP:A)(B|Z)/KS
    AAAC
No match, mark = A

/-- --/

"(?=a*(*ACCEPT)b)c"
    c
 0: c
    c\N 
 0: c
    
/(?1)c(?(DEFINE)((*ACCEPT)b))/
    c
 0: c
    c\N  
 0: c
    
/(?>(*ACCEPT)b)c/
    c
 0: 
    c\N  
No match

/(?:(?>(a)))+a%/++
    %aa%
 0: aa%
 0+ 
 1: a
 1+ a%

/(a)b|ac/++SS
    ac\O3
 0: ac
 0+ 
    
/(a)(b)x|abc/++
     abc\O6
 0: abc
 0+ 

/(a)bc|(a)(b)\2/
    \O3abc
Matched, but too many substrings
 0: abc
    \O4abc 
Matched, but too many substrings
 0: abc

/(?(DEFINE)(a(?2)|b)(b(?1)|a))(?:(?1)|(?2))/SI
Capturing subpattern count = 2
No options
No first char
No need char
Subject length lower bound = 1
No starting char list

/(a(?2)|b)(b(?1)|a)(?:(?1)|(?2))/SI
Capturing subpattern count = 2
No options
No first char
No need char
Subject length lower bound = 3
Starting chars: a b 

/(a(?2)|b)(b(?1)|a)(?1)(?2)/SI
Capturing subpattern count = 2
No options
No first char
No need char
Subject length lower bound = 4
Starting chars: a b 

/(abc)(?1)/SI
Capturing subpattern count = 1
No options
First char = 'a'
Need char = 'c'
Subject length lower bound = 6
No starting char list

/^(?>a)++/
    aa\M
Minimum match() limit = 5
Minimum match() recursion limit = 2
 0: aa
    aaaaaaaaa\M 
Minimum match() limit = 12
Minimum match() recursion limit = 2
 0: aaaaaaaaa
    
/(a)(?1)++/
    aa\M
Minimum match() limit = 7
Minimum match() recursion limit = 4
 0: aa
 1: a
    aaaaaaaaa\M  
Minimum match() limit = 21
Minimum match() recursion limit = 4
 0: aaaaaaaaa
 1: a

/(?:(foo)|(bar)|(baz))X/SS=
    bazfooX
 0: fooX
 1: foo
 2: <unset>
 3: <unset>
    foobazbarX
 0: barX
 1: <unset>
 2: bar
 3: <unset>
    barfooX
 0: fooX
 1: foo
 2: <unset>
 3: <unset>
    bazX
 0: bazX
 1: <unset>
 2: <unset>
 3: baz
    foobarbazX    
 0: bazX
 1: <unset>
 2: <unset>
 3: baz
    bazfooX\O0
Matched, but too many substrings
    bazfooX\O2
Matched, but too many substrings
 0: fooX
    bazfooX\O4
Matched, but too many substrings
 0: fooX
 1: <unset>
    bazfooX\O6
Matched, but too many substrings
 0: fooX
 1: foo
 2: <unset>
    bazfooX\O8
Matched, but too many substrings
 0: fooX
 1: foo
 2: <unset>
 3: <unset>
    bazfooX\O10
 0: fooX
 1: foo
 2: <unset>
 3: <unset>

/(?=abc){3}abc/BZ
------------------------------------------------------------------
        Bra
        Assert
        abc
        Ket
        abc
        Ket
        End
------------------------------------------------------------------

/(?=abc)+abc/BZ
------------------------------------------------------------------
        Bra
        Assert
        abc
        Ket
        abc
        Ket
        End
------------------------------------------------------------------

/(?=abc)++abc/BZ
------------------------------------------------------------------
        Bra
        Assert
        abc
        Ket
        abc
        Ket
        End
------------------------------------------------------------------

/(?=abc){0}xyz/BZ
------------------------------------------------------------------
        Bra
        Skip zero
        Assert
        abc
        Ket
        xyz
        Ket
        End
------------------------------------------------------------------

/(?=(a))?./BZ
------------------------------------------------------------------
        Bra
        Brazero
        Assert
        CBra 1
        a
        Ket
        Ket
        Any
        Ket
        End
------------------------------------------------------------------

/(?=(a))??./BZ
------------------------------------------------------------------
        Bra
        Braminzero
        Assert
        CBra 1
        a
        Ket
        Ket
        Any
        Ket
        End
------------------------------------------------------------------

/^(?=(a)){0}b(?1)/BZ
------------------------------------------------------------------
        Bra
        ^
        Skip zero
        Assert
        CBra 1
        a
        Ket
        Ket
        b
        Recurse
        Ket
        End
------------------------------------------------------------------

/(?(DEFINE)(a))?b(?1)/BZ
------------------------------------------------------------------
        Bra
        Cond
        Cond def
        CBra 1
        a
        Ket
        Ket
        b
        Recurse
        Ket
        End
------------------------------------------------------------------

/^(?=(?1))?[az]([abc])d/BZ
------------------------------------------------------------------
        Bra
        ^
        Brazero
        Assert
        Recurse
        Ket
        [az]
        CBra 1
        [a-c]
        Ket
        d
        Ket
        End
------------------------------------------------------------------

/^(?!a){0}\w+/BZ
------------------------------------------------------------------
        Bra
        ^
        Skip zero
        Assert not
        a
        Ket
        \w++
        Ket
        End
------------------------------------------------------------------

/(?<=(abc))?xyz/BZ
------------------------------------------------------------------
        Bra
        Brazero
        AssertB
        Reverse
        CBra 1
        abc
        Ket
        Ket
        xyz
        Ket
        End
------------------------------------------------------------------

/[:a[:abc]b:]/BZ
------------------------------------------------------------------
        Bra
        [:[a-c]
        b:]
        Ket
        End
------------------------------------------------------------------

/((?2))((?1))/SS
    abc
Error -26 (nested recursion at the same subject position)

/((?(R2)a+|(?1)b))/SS
    aaaabcde
Error -26 (nested recursion at the same subject position)

/(?(R)a*(?1)|((?R))b)/SS
    aaaabcde
Error -26 (nested recursion at the same subject position)

/(a+|(?R)b)/
Failed: recursive call could loop indefinitely at offset 7

/^(a(*:A)(d|e(*:B))z|aeq)/C
    adz
--->adz
 +0 ^       ^
 +1 ^       (a(*:A)(d|e(*:B))z|aeq)
 +2 ^       a
 +3 ^^      (*:A)
 +8 ^^      (d|e(*:B))
Latest Mark: A
 +9 ^^      d
+10 ^ ^     |
+18 ^ ^     z
+19 ^  ^    |
+24 ^  ^    
 0: adz
 1: adz
 2: d
    aez
--->aez
 +0 ^       ^
 +1 ^       (a(*:A)(d|e(*:B))z|aeq)
 +2 ^       a
 +3 ^^      (*:A)
 +8 ^^      (d|e(*:B))
Latest Mark: A
 +9 ^^      d
+11 ^^      e
+12 ^ ^     (*:B)
+17 ^ ^     )
Latest Mark: B
+18 ^ ^     z
+19 ^  ^    |
+24 ^  ^    
 0: aez
 1: aez
 2: e
    aeqwerty
--->aeqwerty
 +0 ^            ^
 +1 ^            (a(*:A)(d|e(*:B))z|aeq)
 +2 ^            a
 +3 ^^           (*:A)
 +8 ^^           (d|e(*:B))
Latest Mark: A
 +9 ^^           d
+11 ^^           e
+12 ^ ^          (*:B)
+17 ^ ^          )
Latest Mark: B
+18 ^ ^          z
+20 ^            a
+21 ^^           e
+22 ^ ^          q
+23 ^  ^         )
+24 ^  ^         
 0: aeq
 1: aeq

/.(*F)/
    \P\Pabc
No match

/\btype\b\W*?\btext\b\W*?\bjavascript\b/IS
Capturing subpattern count = 0
Max lookbehind = 1
No options
First char = 't'
Need char = 't'
Subject length lower bound = 18
No starting char list

/\btype\b\W*?\btext\b\W*?\bjavascript\b|\burl\b\W*?\bshell:|<input\b.*?\btype\b\W*?\bimage\b|\bonkeyup\b\W*?\=/IS
Capturing subpattern count = 0
Max lookbehind = 1
No options
No first char
No need char
Subject length lower bound = 8
Starting chars: < o t u 

/a(*SKIP)c|b(*ACCEPT)|/+S!I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
Subject length lower bound = -1
No starting char list
    a
 0: 
 0+ 

/a(*SKIP)c|b(*ACCEPT)cd(*ACCEPT)|x/SI
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = -1
Starting chars: a b x 
    ax
 0: x

'a*(*ACCEPT)b'+
    \N\N
No match
    abc\N\N
 0: a
 0+ bc
    bbb\N\N 
 0: 
 0+ bb

/(*ACCEPT)a/+I
Capturing subpattern count = 0
No options
No first char
No need char
    bax
 0: 
 0+ bax

/z(*ACCEPT)a/+I
Capturing subpattern count = 0
No options
First char = 'z'
No need char
    baxzbx
 0: z
 0+ bx

/a(?:.)*?a/ims                                                                  
    \Mabbbbbbbbbbbbbbbbbbbbba
Minimum match() limit = 65
Minimum match() recursion limit = 2
 0: abbbbbbbbbbbbbbbbbbbbba
    
/a(?:.(*THEN))*?a/ims
    \Mabbbbbbbbbbbbbbbbbbbbba
Minimum match() limit = 86
Minimum match() recursion limit = 45
 0: abbbbbbbbbbbbbbbbbbbbba

/a(?:.(*THEN:ABC))*?a/ims
    \Mabbbbbbbbbbbbbbbbbbbbba
Minimum match() limit = 86
Minimum match() recursion limit = 45
 0: abbbbbbbbbbbbbbbbbbbbba

/^(?>a+)(?>(z+))\w/BZ
------------------------------------------------------------------
        Bra
        ^
        Once_NC
        a++
        Ket
        Once
        CBra 1
        z++
        Ket
        Ket
        \w
        Ket
        End
------------------------------------------------------------------
    aaaazzzzb
 0: aaaazzzzb
 1: zzzz
    ** Failers
No match
    aazz  
No match

/(.)(\1|a(?2))/
    bab
 0: bab
 1: b
 2: ab
    
/\1|(.)(?R)\1/
    cbbbc
 0: cbbbc
 1: c
    
/(.)((?(1)c|a)|a(?2))/
    baa  
No match

/(?P<abn>(?P=abn)xxx)/BZ
------------------------------------------------------------------
        Bra
        Once
        CBra 1
        \1
        xxx
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------

/(a\1z)/BZ
------------------------------------------------------------------
        Bra
        Once
        CBra 1
        a
        \1
        z
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------

/^(?>a+)(?>b+)(?>c+)(?>d+)(?>e+)/
     \Maabbccddee
Minimum match() limit = 7
Minimum match() recursion limit = 2
 0: aabbccddee

/^(?>(a+))(?>(b+))(?>(c+))(?>(d+))(?>(e+))/
     \Maabbccddee
Minimum match() limit = 17
Minimum match() recursion limit = 16
 0: aabbccddee
 1: aa
 2: bb
 3: cc
 4: dd
 5: ee

/^(?>(a+))(?>b+)(?>(c+))(?>d+)(?>(e+))/
     \Maabbccddee
Minimum match() limit = 13
Minimum match() recursion limit = 10
 0: aabbccddee
 1: aa
 2: cc
 3: ee

/^a\x41z/<JS>
    aAz
 0: aAz
    *** Failers
No match
    ax41z
No match

/^a[m\x41]z/<JS>
    aAz
 0: aAz

/^a\x1z/<JS>
    ax1z
 0: ax1z

/^a\u0041z/<JS>
    aAz
 0: aAz
    *** Failers
No match
    au0041z
No match

/^a[m\u0041]z/<JS>
    aAz
 0: aAz

/^a\u041z/<JS>
    au041z
 0: au041z
    *** Failers
No match
    aAz
No match

/^a\U0041z/<JS>
    aU0041z
 0: aU0041z
    *** Failers
No match
    aAz
No match

/(?(?=c)c|d)++Y/BZ
------------------------------------------------------------------
        Bra
        BraPos
        Cond
        Assert
        c
        Ket
        c
        Alt
        d
        Ket
        KetRpos
        Y
        Ket
        End
------------------------------------------------------------------

/(?(?=c)c|d)*+Y/BZ
------------------------------------------------------------------
        Bra
        Braposzero
        BraPos
        Cond
        Assert
        c
        Ket
        c
        Alt
        d
        Ket
        KetRpos
        Y
        Ket
        End
------------------------------------------------------------------

/a[\NB]c/
Failed: \N is not supported in a class at offset 3

/a[B-\Nc]/ 
Failed: invalid range in character class at offset 5

/a[B\Nc]/ 
Failed: \N is not supported in a class at offset 4

/(a)(?2){0,1999}?(b)/

/(a)(?(DEFINE)(b))(?2){0,1999}?(?2)/

/--- This test, with something more complicated than individual letters, causes
different behaviour in Perl. Perhaps it disables some optimization; no tag is
passed back for the failures, whereas in PCRE there is a tag. ---/
    
/(A|P)(*:A)(B|P) | (X|P)(X|P)(*:B)(Y|P)/xK
    AABC
 0: AB
 1: A
 2: B
MK: A
    XXYZ 
 0: XXY
 1: <unset>
 2: <unset>
 3: X
 4: X
 5: Y
MK: B
    ** Failers
No match
    XAQQ  
No match, mark = A
    XAQQXZZ  
No match, mark = A
    AXQQQ 
No match, mark = A
    AXXQQQ 
No match, mark = B

/-- Perl doesn't give marks for these, though it does if the alternatives are
replaced by single letters. --/
    
/(b|q)(*:m)f|a(*:n)w/K
    aw 
 0: aw
MK: n
    ** Failers 
No match, mark = n
    abc
No match, mark = m

/(q|b)(*:m)f|a(*:n)w/K
    aw 
 0: aw
MK: n
    ** Failers 
No match, mark = n
    abc
No match, mark = m

/-- After a partial match, the behaviour is as for a failure. --/

/^a(*:X)bcde/K
   abc\P
Partial match, mark=X: abc
   
/-- These are here because Perl doesn't return a mark, except for the first --/

/(?=(*:x))(q|)/K+
    abc
 0: 
 0+ abc
 1: 
MK: x

/(?=(*:x))((*:y)q|)/K+
    abc
 0: 
 0+ abc
 1: 
MK: x

/(?=(*:x))(?:(*:y)q|)/K+
    abc
 0: 
 0+ abc
MK: x

/(?=(*:x))(?>(*:y)q|)/K+
    abc
 0: 
 0+ abc
MK: x

/(?=a(*:x))(?!a(*:y)c)/K+
    ab
 0: 
 0+ ab
MK: x

/(?=a(*:x))(?=a(*:y)c|)/K+
    ab
 0: 
 0+ ab
MK: x

/(..)\1/
    ab\P
Partial match: ab
    aba\P
Partial match: aba
    abab\P
 0: abab
 1: ab

/(..)\1/i
    ab\P
Partial match: ab
    abA\P
Partial match: abA
    aBAb\P
 0: aBAb
 1: aB

/(..)\1{2,}/
    ab\P
Partial match: ab
    aba\P
Partial match: aba
    abab\P
Partial match: abab
    ababa\P
Partial match: ababa
    ababab\P
 0: ababab
 1: ab
    ababab\P\P
Partial match: ababab
    abababa\P
 0: ababab
 1: ab
    abababa\P\P
Partial match: abababa

/(..)\1{2,}/i
    ab\P
Partial match: ab
    aBa\P
Partial match: aBa
    aBAb\P
Partial match: aBAb
    AbaBA\P
Partial match: AbaBA
    abABAb\P
 0: abABAb
 1: ab
    aBAbaB\P\P
Partial match: aBAbaB
    abABabA\P
 0: abABab
 1: ab
    abaBABa\P\P
Partial match: abaBABa

/(..)\1{2,}?x/i
    ab\P
Partial match: ab
    abA\P
Partial match: abA
    aBAb\P
Partial match: aBAb
    abaBA\P
Partial match: abaBA
    abAbaB\P
Partial match: abAbaB
    abaBabA\P
Partial match: abaBabA
    abAbABaBx\P
 0: abAbABaBx
 1: ab

/^(..)\1/
    aba\P
Partial match: aba

/^(..)\1{2,3}x/
    aba\P
Partial match: aba
    ababa\P
Partial match: ababa
    ababa\P\P
Partial match: ababa
    abababx
 0: abababx
 1: ab
    ababababx  
 0: ababababx
 1: ab

/^(..)\1{2,3}?x/
    aba\P
Partial match: aba
    ababa\P
Partial match: ababa
    ababa\P\P
Partial match: ababa
    abababx
 0: abababx
 1: ab
    ababababx  
 0: ababababx
 1: ab
    
/^(..)(\1{2,3})ab/
    abababab
 0: abababab
 1: ab
 2: abab

/^\R/
    \r\P
 0: \x0d
    \r\P\P
Partial match: \x0d
    
/^\R{2,3}x/
    \r\P
Partial match: \x0d
    \r\P\P
Partial match: \x0d
    \r\r\P 
Partial match: \x0d\x0d
    \r\r\P\P
Partial match: \x0d\x0d
    \r\r\r\P  
Partial match: \x0d\x0d\x0d
    \r\r\r\P\P
Partial match: \x0d\x0d\x0d
    \r\rx
 0: \x0d\x0dx
    \r\r\rx    
 0: \x0d\x0d\x0dx

/^\R{2,3}?x/
    \r\P
Partial match: \x0d
    \r\P\P
Partial match: \x0d
    \r\r\P 
Partial match: \x0d\x0d
    \r\r\P\P
Partial match: \x0d\x0d
    \r\r\r\P  
Partial match: \x0d\x0d\x0d
    \r\r\r\P\P
Partial match: \x0d\x0d\x0d
    \r\rx
 0: \x0d\x0dx
    \r\r\rx    
 0: \x0d\x0d\x0dx
    
/^\R?x/
    \r\P
Partial match: \x0d
    \r\P\P 
Partial match: \x0d
    x
 0: x
    \rx  
 0: \x0dx

/^\R+x/
    \r\P
Partial match: \x0d
    \r\P\P 
Partial match: \x0d
    \r\n\P
Partial match: \x0d\x0a
    \r\n\P\P  
Partial match: \x0d\x0a
    \rx  
 0: \x0dx

/^a$/<CRLF>
    a\r\P
Partial match: a\x0d
    a\r\P\P 
Partial match: a\x0d

/^a$/m<CRLF>
    a\r\P
Partial match: a\x0d
    a\r\P\P 
Partial match: a\x0d

/^(a$|a\r)/<CRLF>
    a\r\P
 0: a\x0d
 1: a\x0d
    a\r\P\P 
Partial match: a\x0d

/^(a$|a\r)/m<CRLF>
    a\r\P
 0: a\x0d
 1: a\x0d
    a\r\P\P 
Partial match: a\x0d

/./<CRLF>
    \r\P
 0: \x0d
    \r\P\P 
Partial match: \x0d
  
/.{2,3}/<CRLF>
    \r\P 
Partial match: \x0d
    \r\P\P
Partial match: \x0d
    \r\r\P
 0: \x0d\x0d
    \r\r\P\P
Partial match: \x0d\x0d
    \r\r\r\P
 0: \x0d\x0d\x0d
    \r\r\r\P\P     
Partial match: \x0d\x0d\x0d

/.{2,3}?/<CRLF>
    \r\P 
Partial match: \x0d
    \r\P\P
Partial match: \x0d
    \r\r\P
 0: \x0d\x0d
    \r\r\P\P
Partial match: \x0d\x0d
    \r\r\r\P
 0: \x0d\x0d
    \r\r\r\P\P     
 0: \x0d\x0d

"AB(C(D))(E(F))?(?(?=\2)(?=\4))"
    ABCDGHI\O03
Matched, but too many substrings
 0: ABCD
    
/-- These are all run as real matches in test 1; here we are just checking the
settings of the anchored and startline bits. --/ 

/(?>.*?a)(?<=ba)/I
Capturing subpattern count = 0
Max lookbehind = 2
No options
No first char
Need char = 'a'

/(?:.*?a)(?<=ba)/I
Capturing subpattern count = 0
Max lookbehind = 2
No options
First char at start or follows newline
Need char = 'a'

/.*?a(*PRUNE)b/I
Capturing subpattern count = 0
No options
No first char
Need char = 'b'

/.*?a(*PRUNE)b/sI
Capturing subpattern count = 0
Options: dotall
No first char
Need char = 'b'

/^a(*PRUNE)b/sI
Capturing subpattern count = 0
Options: anchored dotall
No first char
No need char

/.*?a(*SKIP)b/I
Capturing subpattern count = 0
No options
No first char
Need char = 'b'

/(?>.*?a)b/sI
Capturing subpattern count = 0
Options: dotall
No first char
Need char = 'b'

/(?>.*?a)b/I
Capturing subpattern count = 0
No options
No first char
Need char = 'b'

/(?>^a)b/sI
Capturing subpattern count = 0
Options: anchored dotall
No first char
No need char

/(?>.*?)(?<=(abcd)|(wxyz))/I
Capturing subpattern count = 2
Max lookbehind = 4
May match empty string
No options
No first char
No need char

/(?>.*)(?<=(abcd)|(wxyz))/I
Capturing subpattern count = 2
Max lookbehind = 4
May match empty string
No options
No first char
No need char

"(?>.*)foo"I
Capturing subpattern count = 0
No options
No first char
Need char = 'o'

"(?>.*?)foo"I
Capturing subpattern count = 0
No options
No first char
Need char = 'o'

/(?>^abc)/mI
Capturing subpattern count = 0
Options: multiline
First char at start or follows newline
Need char = 'c'

/(?>.*abc)/mI
Capturing subpattern count = 0
Options: multiline
No first char
Need char = 'c'

/(?:.*abc)/mI
Capturing subpattern count = 0
Options: multiline
First char at start or follows newline
Need char = 'c'

/-- Check PCRE_STUDY_EXTRA_NEEDED --/

/.?/S-I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
Study returned NULL

/.?/S!I
Capturing subpattern count = 0
May match empty string
No options
No first char
No need char
Subject length lower bound = -1
No starting char list

/(?:(a)+(?C1)bb|aa(?C2)b)/
    aab\C+
Callout 1: last capture = 1
 0: <unset>
 1: a
--->aab
    ^ ^     b
Callout 1: last capture = 1
 0: <unset>
 1: a
--->aab
    ^^      b
Callout 2: last capture = -1
 0: <unset>
--->aab
    ^ ^     b
 0: aab
   
/(?:(a)++(?C1)bb|aa(?C2)b)/
    aab\C+ 
Callout 1: last capture = 1
 0: <unset>
 1: a
--->aab
    ^ ^     b
Callout 2: last capture = -1
 0: <unset>
--->aab
    ^ ^     b
 0: aab
    
/(?:(?>(a))(?C1)bb|aa(?C2)b)/
    aab\C+ 
Callout 1: last capture = 1
 0: <unset>
 1: a
--->aab
    ^^      b
Callout 2: last capture = -1
 0: <unset>
--->aab
    ^ ^     b
 0: aab

/(?:(?1)(?C1)x|ab(?C2))((a)){0}/                                                
    aab\C+ 
Callout 1: last capture = -1
 0: <unset>
--->aab
    ^^      x
Callout 1: last capture = -1
 0: <unset>
--->aab
     ^^     x
Callout 2: last capture = -1
 0: <unset>
--->aab
     ^ ^    )
 0: ab

/(?1)(?C1)((a)(?C2)){0}/   
    aab\C+ 
Callout 2: last capture = 2
 0: <unset>
 1: <unset>
 2: a
--->aab
    ^^      )
Callout 1: last capture = -1
 0: <unset>
--->aab
    ^^      ((a)(?C2)){0}
 0: a

/(?:(a)+(?C1)bb|aa(?C2)b)++/
    aab\C+
Callout 1: last capture = 1
 0: <unset>
 1: a
--->aab
    ^ ^     b
Callout 1: last capture = 1
 0: <unset>
 1: a
--->aab
    ^^      b
Callout 2: last capture = -1
 0: <unset>
--->aab
    ^ ^     b
 0: aab
    aab\C+\O2
Callout 1: last capture = 1
 0: <unset>
--->aab
    ^ ^     b
Callout 1: last capture = 1
 0: <unset>
--->aab
    ^^      b
Callout 2: last capture = -1
 0: <unset>
--->aab
    ^ ^     b
 0: aab

/(ab)x|ab/
    ab\O3
 0: ab
    ab\O2 
 0: ab
  
/(ab)/
    ab\O3
Matched, but too many substrings
 0: ab
    ab\O2 
Matched, but too many substrings
 0: ab
    
/(?<=123)(*MARK:xx)abc/K
    xxxx123a\P\P
Partial match at offset 7, mark=xx: 123a
    xxxx123a\P
Partial match at offset 7, mark=xx: 123a
    
/123\Kabc/
    xxxx123a\P\P
Partial match: 123a
    xxxx123a\P
Partial match: 123a

/^(?(?=a)aa|bb)/C
    bb
--->bb
 +0 ^      ^
 +1 ^      (?(?=a)aa|bb)
 +3 ^      (?=a)
 +6 ^      a
+11 ^      b
+12 ^^     b
+13 ^ ^    )
+14 ^ ^    
 0: bb

/(?C1)^(?C2)(?(?C99)(?=(?C3)a(?C4))(?C5)a(?C6)a(?C7)|(?C8)b(?C9)b(?C10))(?C11)/
    bb
--->bb
  1 ^      ^
  2 ^      (?(?C99)(?=(?C3)a(?C4))(?C5)a(?C6)a(?C7)|(?C8)b(?C9)b(?C10))
 99 ^      (?=(?C3)a(?C4))
  3 ^      a
  8 ^      b
  9 ^^     b
 10 ^ ^    )
 11 ^ ^    
 0: bb

/-- Perl seems to have a bug with this one --/

/aaaaa(*COMMIT)(*PRUNE)b|a+c/
    aaaaaac
 0: aaaac
    
/-- Here are some that Perl treats differently because of the way it handles
backtracking verbs. --/

 /(?!a(*COMMIT)b)ac|ad/
     ac
 0: ac
     ad 
 0: ad

/^(?!a(*THEN)b|ac)../
     ac
No match
     ad 
 0: ad

/^(?=a(*THEN)b|ac)/
    ac
 0: 
    
/\A.*?(?:a|b(*THEN)c)/
    ba
 0: ba

/\A.*?(?:a|b(*THEN)c)++/
    ba
 0: ba

/\A.*?(?:a|b(*THEN)c|d)/
    ba
 0: ba

/(?:(a(*MARK:X)a+(*SKIP:X)b)){0}(?:(?1)|aac)/
    aac 
 0: aac

/\A.*?(a|b(*THEN)c)/
    ba
 0: ba
 1: a

/^(A(*THEN)B|A(*THEN)D)/
    AD           
 0: AD
 1: AD
    
/(?!b(*THEN)a)bn|bnn/
    bnn
 0: bn

/(?(?=b(*SKIP)a)bn|bnn)/
    bnn
No match

/(?=b(*THEN)a|)bn|bnn/
    bnn
 0: bn

/-------------------------/ 

/(*LIMIT_MATCH=12bc)abc/
Failed: (*VERB) not recognized or malformed at offset 7

/(*LIMIT_MATCH=4294967290)abc/
Failed: (*VERB) not recognized or malformed at offset 7

/(*LIMIT_RECURSION=4294967280)abc/I
Capturing subpattern count = 0
Recursion limit = 4294967280
No options
First char = 'a'
Need char = 'c'

/(a+)*zz/
    aaaaaaaaaaaaaz
No match
    aaaaaaaaaaaaaz\q3000
Error -8 (match limit exceeded)

/(a+)*zz/S-
    aaaaaaaaaaaaaz\Q10 
Error -21 (recursion limit exceeded)

/(*LIMIT_MATCH=3000)(a+)*zz/I
Capturing subpattern count = 1
Match limit = 3000
No options
No first char
Need char = 'z'
    aaaaaaaaaaaaaz
Error -8 (match limit exceeded)
    aaaaaaaaaaaaaz\q60000
Error -8 (match limit exceeded)

/(*LIMIT_MATCH=60000)(*LIMIT_MATCH=3000)(a+)*zz/I
Capturing subpattern count = 1
Match limit = 3000
No options
No first char
Need char = 'z'
    aaaaaaaaaaaaaz
Error -8 (match limit exceeded)

/(*LIMIT_MATCH=60000)(a+)*zz/I
Capturing subpattern count = 1
Match limit = 60000
No options
No first char
Need char = 'z'
    aaaaaaaaaaaaaz
No match
    aaaaaaaaaaaaaz\q3000
Error -8 (match limit exceeded)

/(*LIMIT_RECURSION=10)(a+)*zz/IS-
Capturing subpattern count = 1
Recursion limit = 10
No options
No first char
Need char = 'z'
Subject length lower bound = 2
Starting chars: a z 
    aaaaaaaaaaaaaz
Error -21 (recursion limit exceeded)
    aaaaaaaaaaaaaz\Q1000
Error -21 (recursion limit exceeded)

/(*LIMIT_RECURSION=10)(*LIMIT_RECURSION=1000)(a+)*zz/IS-
Capturing subpattern count = 1
Recursion limit = 10
No options
No first char
Need char = 'z'
Subject length lower bound = 2
Starting chars: a z 
    aaaaaaaaaaaaaz
Error -21 (recursion limit exceeded)

/(*LIMIT_RECURSION=1000)(a+)*zz/IS-
Capturing subpattern count = 1
Recursion limit = 1000
No options
No first char
Need char = 'z'
Subject length lower bound = 2
Starting chars: a z 
    aaaaaaaaaaaaaz
No match
    aaaaaaaaaaaaaz\Q10
Error -21 (recursion limit exceeded)

/-- This test causes a segfault with Perl 5.18.0 --/

/^(?=(a)){0}b(?1)/
    backgammon
 0: ba

/(?|(?<n>f)|(?<n>b))/JI
Capturing subpattern count = 1
Named capturing subpatterns:
  n   1
Options: dupnames
No first char
No need char

/(?<a>abc)(?<a>z)\k<a>()/JDZS
------------------------------------------------------------------
        Bra
        CBra 1
        abc
        Ket
        CBra 2
        z
        Ket
        \k<a>2
        CBra 3
        Ket
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 3
Max back reference = 2
Named capturing subpatterns:
  a   1
  a   2
Options: dupnames
First char = 'a'
Need char = 'z'
Subject length lower bound = 5
No starting char list

/a*[bcd]/BZ
------------------------------------------------------------------
        Bra
        a*+
        [b-d]
        Ket
        End
------------------------------------------------------------------

/[bcd]*a/BZ
------------------------------------------------------------------
        Bra
        [b-d]*+
        a
        Ket
        End
------------------------------------------------------------------

/-- A complete set of tests for auto-possessification of character types --/

/\D+\D \D+\d \D+\S \D+\s \D+\W \D+\w \D+. \D+\C \D+\R \D+\H \D+\h \D+\V \D+\v \D+\Z \D+\z \D+$/BZx
------------------------------------------------------------------
        Bra
        \D+
        \D
        \D++
        \d
        \D+
        \S
        \D+
        \s
        \D+
        \W
        \D+
        \w
        \D+
        Any
        \D+
        AllAny
        \D+
        \R
        \D+
        \H
        \D+
        \h
        \D+
        \V
        \D+
        \v
        \D+
        \Z
        \D++
        \z
        \D+
        $
        Ket
        End
------------------------------------------------------------------

/\d+\D \d+\d \d+\S \d+\s \d+\W \d+\w \d+. \d+\C \d+\R \d+\H \d+\h \d+\V \d+\v \d+\Z \d+\z \d+$/BZx
------------------------------------------------------------------
        Bra
        \d++
        \D
        \d+
        \d
        \d+
        \S
        \d++
        \s
        \d++
        \W
        \d+
        \w
        \d+
        Any
        \d+
        AllAny
        \d++
        \R
        \d+
        \H
        \d++
        \h
        \d+
        \V
        \d++
        \v
        \d++
        \Z
        \d++
        \z
        \d++
        $
        Ket
        End
------------------------------------------------------------------

/\S+\D \S+\d \S+\S \S+\s \S+\W \S+\w \S+. \S+\C \S+\R \S+\H \S+\h \S+\V \S+\v \S+\Z \S+\z \S+$/BZx
------------------------------------------------------------------
        Bra
        \S+
        \D
        \S+
        \d
        \S+
        \S
        \S++
        \s
        \S+
        \W
        \S+
        \w
        \S+
        Any
        \S+
        AllAny
        \S++
        \R
        \S+
        \H
        \S++
        \h
        \S+
        \V
        \S++
        \v
        \S++
        \Z
        \S++
        \z
        \S++
        $
        Ket
        End
------------------------------------------------------------------

/\s+\D \s+\d \s+\S \s+\s \s+\W \s+\w \s+. \s+\C \s+\R \s+\H \s+\h \s+\V \s+\v \s+\Z \s+\z \s+$/BZx
------------------------------------------------------------------
        Bra
        \s+
        \D
        \s++
        \d
        \s++
        \S
        \s+
        \s
        \s+
        \W
        \s++
        \w
        \s+
        Any
        \s+
        AllAny
        \s+
        \R
        \s+
        \H
        \s+
        \h
        \s+
        \V
        \s+
        \v
        \s+
        \Z
        \s++
        \z
        \s+
        $
        Ket
        End
------------------------------------------------------------------

/\W+\D \W+\d \W+\S \W+\s \W+\W \W+\w \W+. \W+\C \W+\R \W+\H \W+\h \W+\V \W+\v \W+\Z \W+\z \W+$/BZx
------------------------------------------------------------------
        Bra
        \W+
        \D
        \W++
        \d
        \W+
        \S
        \W+
        \s
        \W+
        \W
        \W++
        \w
        \W+
        Any
        \W+
        AllAny
        \W+
        \R
        \W+
        \H
        \W+
        \h
        \W+
        \V
        \W+
        \v
        \W+
        \Z
        \W++
        \z
        \W+
        $
        Ket
        End
------------------------------------------------------------------

/\w+\D \w+\d \w+\S \w+\s \w+\W \w+\w \w+. \w+\C \w+\R \w+\H \w+\h \w+\V \w+\v \w+\Z \w+\z \w+$/BZx
------------------------------------------------------------------
        Bra
        \w+
        \D
        \w+
        \d
        \w+
        \S
        \w++
        \s
        \w++
        \W
        \w+
        \w
        \w+
        Any
        \w+
        AllAny
        \w++
        \R
        \w+
        \H
        \w++
        \h
        \w+
        \V
        \w++
        \v
        \w++
        \Z
        \w++
        \z
        \w++
        $
        Ket
        End
------------------------------------------------------------------

/\C+\D \C+\d \C+\S \C+\s \C+\W \C+\w \C+. \C+\C \C+\R \C+\H \C+\h \C+\V \C+\v \C+\Z \C+\z \C+$/BZx
------------------------------------------------------------------
        Bra
        AllAny+
        \D
        AllAny+
        \d
        AllAny+
        \S
        AllAny+
        \s
        AllAny+
        \W
        AllAny+
        \w
        AllAny+
        Any
        AllAny+
        AllAny
        AllAny+
        \R
        AllAny+
        \H
        AllAny+
        \h
        AllAny+
        \V
        AllAny+
        \v
        AllAny+
        \Z
        AllAny++
        \z
        AllAny+
        $
        Ket
        End
------------------------------------------------------------------

/\R+\D \R+\d \R+\S \R+\s \R+\W \R+\w \R+. \R+\C \R+\R \R+\H \R+\h \R+\V \R+\v \R+\Z \R+\z \R+$/BZx
------------------------------------------------------------------
        Bra
        \R+
        \D
        \R++
        \d
        \R+
        \S
        \R++
        \s
        \R+
        \W
        \R++
        \w
        \R++
        Any
        \R+
        AllAny
        \R+
        \R
        \R+
        \H
        \R++
        \h
        \R+
        \V
        \R+
        \v
        \R+
        \Z
        \R++
        \z
        \R+
        $
        Ket
        End
------------------------------------------------------------------

/\H+\D \H+\d \H+\S \H+\s \H+\W \H+\w \H+. \H+\C \H+\R \H+\H \H+\h \H+\V \H+\v \H+\Z \H+\z \H+$/BZx
------------------------------------------------------------------
        Bra
        \H+
        \D
        \H+
        \d
        \H+
        \S
        \H+
        \s
        \H+
        \W
        \H+
        \w
        \H+
        Any
        \H+
        AllAny
        \H+
        \R
        \H+
        \H
        \H++
        \h
        \H+
        \V
        \H+
        \v
        \H+
        \Z
        \H++
        \z
        \H+
        $
        Ket
        End
------------------------------------------------------------------

/\h+\D \h+\d \h+\S \h+\s \h+\W \h+\w \h+. \h+\C \h+\R \h+\H \h+\h \h+\V \h+\v \h+\Z \h+\z \h+$/BZx
------------------------------------------------------------------
        Bra
        \h+
        \D
        \h++
        \d
        \h++
        \S
        \h+
        \s
        \h+
        \W
        \h++
        \w
        \h+
        Any
        \h+
        AllAny
        \h++
        \R
        \h++
        \H
        \h+
        \h
        \h+
        \V
        \h++
        \v
        \h+
        \Z
        \h++
        \z
        \h+
        $
        Ket
        End
------------------------------------------------------------------

/\V+\D \V+\d \V+\S \V+\s \V+\W \V+\w \V+. \V+\C \V+\R \V+\H \V+\h \V+\V \V+\v \V+\Z \V+\z \V+$/BZx
------------------------------------------------------------------
        Bra
        \V+
        \D
        \V+
        \d
        \V+
        \S
        \V+
        \s
        \V+
        \W
        \V+
        \w
        \V+
        Any
        \V+
        AllAny
        \V++
        \R
        \V+
        \H
        \V+
        \h
        \V+
        \V
        \V++
        \v
        \V+
        \Z
        \V++
        \z
        \V+
        $
        Ket
        End
------------------------------------------------------------------

/\v+\D \v+\d \v+\S \v+\s \v+\W \v+\w \v+. \v+\C \v+\R \v+\H \v+\h \v+\V \v+\v \v+\Z \v+\z \v+$/BZx
------------------------------------------------------------------
        Bra
        \v+
        \D
        \v++
        \d
        \v++
        \S
        \v+
        \s
        \v+
        \W
        \v++
        \w
        \v+
        Any
        \v+
        AllAny
        \v+
        \R
        \v+
        \H
        \v++
        \h
        \v++
        \V
        \v+
        \v
        \v+
        \Z
        \v++
        \z
        \v+
        $
        Ket
        End
------------------------------------------------------------------

/ a+\D  a+\d  a+\S  a+\s  a+\W  a+\w  a+.  a+\C  a+\R  a+\H  a+\h  a+\V  a+\v  a+\Z  a+\z  a+$/BZx
------------------------------------------------------------------
        Bra
        a+
        \D
        a++
        \d
        a+
        \S
        a++
        \s
        a++
        \W
        a+
        \w
        a+
        Any
        a+
        AllAny
        a++
        \R
        a+
        \H
        a++
        \h
        a+
        \V
        a++
        \v
        a++
        \Z
        a++
        \z
        a++
        $
        Ket
        End
------------------------------------------------------------------

/\n+\D \n+\d \n+\S \n+\s \n+\W \n+\w \n+. \n+\C \n+\R \n+\H \n+\h \n+\V \n+\v \n+\Z \n+\z \n+$/BZx
------------------------------------------------------------------
        Bra
        \x0a+
        \D
        \x0a++
        \d
        \x0a++
        \S
        \x0a+
        \s
        \x0a+
        \W
        \x0a++
        \w
        \x0a+
        Any
        \x0a+
        AllAny
        \x0a+
        \R
        \x0a+
        \H
        \x0a++
        \h
        \x0a++
        \V
        \x0a+
        \v
        \x0a+
        \Z
        \x0a++
        \z
        \x0a+
        $
        Ket
        End
------------------------------------------------------------------

/ .+\D  .+\d  .+\S  .+\s  .+\W  .+\w  .+.  .+\C  .+\R  .+\H  .+\h  .+\V  .+\v  .+\Z  .+\z  .+$/BZx
------------------------------------------------------------------
        Bra
        Any+
        \D
        Any+
        \d
        Any+
        \S
        Any+
        \s
        Any+
        \W
        Any+
        \w
        Any+
        Any
        Any+
        AllAny
        Any++
        \R
        Any+
        \H
        Any+
        \h
        Any+
        \V
        Any+
        \v
        Any+
        \Z
        Any++
        \z
        Any+
        $
        Ket
        End
------------------------------------------------------------------

/ .+\D  .+\d  .+\S  .+\s  .+\W  .+\w  .+.  .+\C  .+\R  .+\H  .+\h  .+\V  .+\v  .+\Z  .+\z  .+$/BZxs
------------------------------------------------------------------
        Bra
        AllAny+
        \D
        AllAny+
        \d
        AllAny+
        \S
        AllAny+
        \s
        AllAny+
        \W
        AllAny+
        \w
        AllAny+
        AllAny
        AllAny+
        AllAny
        AllAny+
        \R
        AllAny+
        \H
        AllAny+
        \h
        AllAny+
        \V
        AllAny+
        \v
        AllAny+
        \Z
        AllAny++
        \z
        AllAny+
        $
        Ket
        End
------------------------------------------------------------------

/\D+$  \d+$  \S+$  \s+$  \W+$  \w+$  \C+$  \R+$  \H+$  \h+$  \V+$  \v+$   a+$  \n+$   .+$  .+$/BZxm
------------------------------------------------------------------
        Bra
        \D+
     /m $
        \d++
     /m $
        \S++
     /m $
        \s+
     /m $
        \W+
     /m $
        \w++
     /m $
        AllAny+
     /m $
        \R+
     /m $
        \H+
     /m $
        \h+
     /m $
        \V+
     /m $
        \v+
     /m $
        a+
     /m $
        \x0a+
     /m $
        Any+
     /m $
        Any+
     /m $
        Ket
        End
------------------------------------------------------------------

/(?=a+)a(a+)++a/BZ
------------------------------------------------------------------
        Bra
        Assert
        a++
        Ket
        a
        CBraPos 1
        a++
        KetRpos
        a
        Ket
        End
------------------------------------------------------------------

/a+(bb|cc)a+(?:bb|cc)a+(?>bb|cc)a+(?:bb|cc)+a+(aa)a+(?:bb|aa)/BZ
------------------------------------------------------------------
        Bra
        a++
        CBra 1
        bb
        Alt
        cc
        Ket
        a++
        Bra
        bb
        Alt
        cc
        Ket
        a++
        Once_NC
        bb
        Alt
        cc
        Ket
        a++
        Bra
        bb
        Alt
        cc
        KetRmax
        a+
        CBra 2
        aa
        Ket
        a+
        Bra
        bb
        Alt
        aa
        Ket
        Ket
        End
------------------------------------------------------------------

/a+(bb|cc)?#a+(?:bb|cc)??#a+(?:bb|cc)?+#a+(?:bb|cc)*#a+(bb|cc)?a#a+(?:aa)?/BZ
------------------------------------------------------------------
        Bra
        a++
        Brazero
        CBra 1
        bb
        Alt
        cc
        Ket
        #
        a++
        Braminzero
        Bra
        bb
        Alt
        cc
        Ket
        #
        a++
        Once
        Brazero
        Bra
        bb
        Alt
        cc
        Ket
        Ket
        #
        a++
        Brazero
        Bra
        bb
        Alt
        cc
        KetRmax
        #
        a+
        Brazero
        CBra 2
        bb
        Alt
        cc
        Ket
        a#
        a+
        Brazero
        Bra
        aa
        Ket
        Ket
        End
------------------------------------------------------------------

/a+(?:bb)?a#a+(?:|||)#a+(?:|b)a#a+(?:|||)?a/BZ
------------------------------------------------------------------
        Bra
        a+
        Brazero
        Bra
        bb
        Ket
        a#
        a++
        Bra
        Alt
        Alt
        Alt
        Ket
        #
        a+
        Bra
        Alt
        b
        Ket
        a#
        a+
        Brazero
        Bra
        Alt
        Alt
        Alt
        Ket
        a
        Ket
        End
------------------------------------------------------------------

/[ab]*/BZ
------------------------------------------------------------------
        Bra
        [ab]*+
        Ket
        End
------------------------------------------------------------------
    aaaa
 0: aaaa

/[ab]*?/BZ
------------------------------------------------------------------
        Bra
        [ab]*?
        Ket
        End
------------------------------------------------------------------
    aaaa
 0: 

/[ab]?/BZ
------------------------------------------------------------------
        Bra
        [ab]?+
        Ket
        End
------------------------------------------------------------------
    aaaa
 0: a

/[ab]??/BZ
------------------------------------------------------------------
        Bra
        [ab]??
        Ket
        End
------------------------------------------------------------------
    aaaa
 0: 

/[ab]+/BZ
------------------------------------------------------------------
        Bra
        [ab]++
        Ket
        End
------------------------------------------------------------------
    aaaa
 0: aaaa

/[ab]+?/BZ
------------------------------------------------------------------
        Bra
        [ab]+?
        Ket
        End
------------------------------------------------------------------
    aaaa
 0: a

/[ab]{2,3}/BZ
------------------------------------------------------------------
        Bra
        [ab]{2,3}+
        Ket
        End
------------------------------------------------------------------
    aaaa
 0: aaa

/[ab]{2,3}?/BZ
------------------------------------------------------------------
        Bra
        [ab]{2,3}?
        Ket
        End
------------------------------------------------------------------
    aaaa
 0: aa

/[ab]{2,}/BZ
------------------------------------------------------------------
        Bra
        [ab]{2,}+
        Ket
        End
------------------------------------------------------------------
    aaaa
 0: aaaa

/[ab]{2,}?/BZ
------------------------------------------------------------------
        Bra
        [ab]{2,}?
        Ket
        End
------------------------------------------------------------------
    aaaa
 0: aa

/\d+\s{0,5}=\s*\S?=\w{0,4}\W*/BZ
------------------------------------------------------------------
        Bra
        \d++
        \s{0,5}+
        =
        \s*+
        \S?
        =
        \w{0,4}+
        \W*+
        Ket
        End
------------------------------------------------------------------

/[a-d]{5,12}[e-z0-9]*#[^a-z]+[b-y]*a[2-7]?[^0-9a-z]+/BZ
------------------------------------------------------------------
        Bra
        [a-d]{5,12}+
        [0-9e-z]*+
        #
        [\x00-`{-\xff] (neg)++
        [b-y]*+
        a
        [2-7]?+
        [\x00-/:-`{-\xff] (neg)++
        Ket
        End
------------------------------------------------------------------

/[a-z]*\s#[ \t]?\S#[a-c]*\S#[C-G]+?\d#[4-8]*\D#[4-9,]*\D#[!$]{0,5}\w#[M-Xf-l]+\W#[a-c,]?\W/BZ
------------------------------------------------------------------
        Bra
        [a-z]*+
        \s
        #
        [\x09 ]?+
        \S
        #
        [a-c]*
        \S
        #
        [C-G]++
        \d
        #
        [4-8]*+
        \D
        #
        [,4-9]*
        \D
        #
        [!$]{0,5}+
        \w
        #
        [M-Xf-l]++
        \W
        #
        [,a-c]?
        \W
        Ket
        End
------------------------------------------------------------------

/a+(aa|bb)*c#a*(bb|cc)*a#a?(bb|cc)*d#[a-f]*(g|hh)*f/BZ
------------------------------------------------------------------
        Bra
        a+
        Brazero
        CBra 1
        aa
        Alt
        bb
        KetRmax
        c#
        a*
        Brazero
        CBra 2
        bb
        Alt
        cc
        KetRmax
        a#
        a?+
        Brazero
        CBra 3
        bb
        Alt
        cc
        KetRmax
        d#
        [a-f]*
        Brazero
        CBra 4
        g
        Alt
        hh
        KetRmax
        f
        Ket
        End
------------------------------------------------------------------

/[a-f]*(g|hh|i)*i#[a-x]{4,}(y{0,6})*y#[a-k]+(ll|mm)+n/BZ
------------------------------------------------------------------
        Bra
        [a-f]*+
        Brazero
        CBra 1
        g
        Alt
        hh
        Alt
        i
        KetRmax
        i#
        [a-x]{4,}
        Brazero
        SCBra 2
        y{0,6}
        KetRmax
        y#
        [a-k]++
        CBra 3
        ll
        Alt
        mm
        KetRmax
        n
        Ket
        End
------------------------------------------------------------------

/[a-f]*(?>gg|hh)+#[a-f]*(?>gg|hh)?#[a-f]*(?>gg|hh)*a#[a-f]*(?>gg|hh)*h/BZ
------------------------------------------------------------------
        Bra
        [a-f]*+
        Once_NC
        gg
        Alt
        hh
        KetRmax
        #
        [a-f]*+
        Brazero
        Once_NC
        gg
        Alt
        hh
        Ket
        #
        [a-f]*
        Brazero
        Once_NC
        gg
        Alt
        hh
        KetRmax
        a#
        [a-f]*+
        Brazero
        Once_NC
        gg
        Alt
        hh
        KetRmax
        h
        Ket
        End
------------------------------------------------------------------

/[a-c]*d/DZS
------------------------------------------------------------------
        Bra
        [a-c]*+
        d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
Need char = 'd'
Subject length lower bound = 1
Starting chars: a b c d 

/[a-c]+d/DZS
------------------------------------------------------------------
        Bra
        [a-c]++
        d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
Need char = 'd'
Subject length lower bound = 2
Starting chars: a b c 

/[a-c]?d/DZS
------------------------------------------------------------------
        Bra
        [a-c]?+
        d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
Need char = 'd'
Subject length lower bound = 1
Starting chars: a b c d 

/[a-c]{4,6}d/DZS
------------------------------------------------------------------
        Bra
        [a-c]{4,6}+
        d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
Need char = 'd'
Subject length lower bound = 5
Starting chars: a b c 

/[a-c]{0,6}d/DZS
------------------------------------------------------------------
        Bra
        [a-c]{0,6}+
        d
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
Need char = 'd'
Subject length lower bound = 1
Starting chars: a b c d 

/-- End of special auto-possessive tests --/

/^A\o{1239}B/
Failed: non-octal character in \o{} (closing brace missing?) at offset 8

/^A\oB/
Failed: missing opening brace after \o at offset 3

/^A\x{zz}B/ 
Failed: non-hex character in \x{} (closing brace missing?) at offset 5

/^A\x{12Z/
Failed: non-hex character in \x{} (closing brace missing?) at offset 7

/^A\x{/
Failed: non-hex character in \x{} (closing brace missing?) at offset 5

/[ab]++/BZO
------------------------------------------------------------------
        Bra
        [ab]++
        Ket
        End
------------------------------------------------------------------

/[^ab]*+/BZO
------------------------------------------------------------------
        Bra
        [\x00-`c-\xff] (neg)*+
        Ket
        End
------------------------------------------------------------------

/a{4}+/BZO
------------------------------------------------------------------
        Bra
        a{4}
        Ket
        End
------------------------------------------------------------------

/a{4}+/BZOi
------------------------------------------------------------------
        Bra
     /i a{4}
        Ket
        End
------------------------------------------------------------------

/[a-[:digit:]]+/
Failed: invalid range in character class at offset 3

/[A-[:digit:]]+/
Failed: invalid range in character class at offset 3

/[a-[.xxx.]]+/
Failed: invalid range in character class at offset 3

/[a-[=xxx=]]+/
Failed: invalid range in character class at offset 3

/[a-[!xxx!]]+/
Failed: range out of order in character class at offset 3

/[A-[!xxx!]]+/
    A]]]
 0: A]]]

/[a-\d]+/
Failed: invalid range in character class at offset 4

/(?<0abc>xx)/
Failed: group name must start with a non-digit at offset 3

/(?&1abc)xx(?<1abc>y)/
Failed: group name must start with a non-digit at offset 3

/(?<ab-cd>xx)/
Failed: syntax error in subpattern name (missing terminator) at offset 5

/(?'0abc'xx)/
Failed: group name must start with a non-digit at offset 3

/(?P<0abc>xx)/
Failed: group name must start with a non-digit at offset 4

/\k<5ghj>/
Failed: group name must start with a non-digit at offset 3

/\k'5ghj'/
Failed: group name must start with a non-digit at offset 3

/\k{2fgh}/
Failed: group name must start with a non-digit at offset 3

/(?P=8yuki)/
Failed: group name must start with a non-digit at offset 4

/\g{4df}/
Failed: group name must start with a non-digit at offset 3

/(?&1abc)xx(?<1abc>y)/
Failed: group name must start with a non-digit at offset 3

/(?P>1abc)xx(?<1abc>y)/
Failed: group name must start with a non-digit at offset 4

/\g'3gh'/
Failed: \g is not followed by a braced, angle-bracketed, or quoted name/number or by a plain number at offset 7

/\g<5fg>/
Failed: \g is not followed by a braced, angle-bracketed, or quoted name/number or by a plain number at offset 7

/(?(<4gh>)abc)/
Failed: group name must start with a non-digit at offset 4

/(?('4gh')abc)/
Failed: group name must start with a non-digit at offset 4

/(?(4gh)abc)/
Failed: malformed number or name after (?( at offset 4

/(?(R&6yh)abc)/
Failed: group name must start with a non-digit at offset 5

/(((a\2)|(a*)\g<-1>))*a?/BZ
------------------------------------------------------------------
        Bra
        Brazero
        SCBra 1
        Once
        CBra 2
        CBra 3
        a
        \2
        Ket
        Alt
        CBra 4
        a*
        Ket
        Recurse
        Ket
        Ket
        KetRmax
        a?+
        Ket
        End
------------------------------------------------------------------

/-- Test the ugly "start or end of word" compatibility syntax --/

/[[:<:]]red[[:>:]]/BZ
------------------------------------------------------------------
        Bra
        \b
        Assert
        \w
        Ket
        red
        \b
        AssertB
        Reverse
        \w
        Ket
        Ket
        End
------------------------------------------------------------------
    little red riding hood
 0: red
    a /red/ thing 
 0: red
    red is a colour
 0: red
    put it all on red  
 0: red
    ** Failers
No match
    no reduction
No match
    Alfred Winifred
No match
    
/[a[:<:]] should give error/ 
Failed: unknown POSIX class name at offset 4

/(?=ab\K)/+
    abcd
Start of matched string is beyond its end - displaying from end to start.
 0: ab
 0+ abcd

/abcd/f<lf>
    xx\nxabcd
No match
    
/ -- Test stack check external calls --/ 

/(((((a)))))/Q0

/(((((a)))))/Q1
Failed: parentheses are too deeply nested (stack check) at offset 0

/(((((a)))))/Q
** Missing 0 or 1 after /Q

/^\w+(?>\s*)(?<=\w)/BZ
------------------------------------------------------------------
        Bra
        ^
        \w+
        Once_NC
        \s*+
        Ket
        AssertB
        Reverse
        \w
        Ket
        Ket
        End
------------------------------------------------------------------

/\othing/
Failed: missing opening brace after \o at offset 1

/\o{}/
Failed: digits missing in \x{} or \o{} at offset 1

/\o{whatever}/
Failed: non-octal character in \o{} (closing brace missing?) at offset 3

/\xthing/

/\x{}/
Failed: digits missing in \x{} or \o{} at offset 3

/\x{whatever}/
Failed: non-hex character in \x{} (closing brace missing?) at offset 3

"((?=(?(?=(?(?=(?(?=()))))))))"
    a
 0: 
 1: 
 2: 

"(?(?=)==)(((((((((?=)))))))))"
    a
No match

/^(?:(a)|b)(?(1)A|B)/I
Capturing subpattern count = 1
Max back reference = 1
Options: anchored
No first char
No need char
    aA123\O3
Matched, but too many substrings
 0: aA
    aA123\O6
 0: aA
 1: a

'^(?:(?<AA>a)|b)(?(<AA>)A|B)'
    aA123\O3
Matched, but too many substrings
 0: aA
    aA123\O6
 0: aA
 1: a

'^(?<AA>)(?:(?<AA>a)|b)(?(<AA>)A|B)'J
    aA123\O3
Matched, but too many substrings
 0: aA
    aA123\O6
Matched, but too many substrings
 0: aA
 1: 

'^(?:(?<AA>X)|)(?:(?<AA>a)|b)\k{AA}'J
    aa123\O3
Matched, but too many substrings
 0: aa
    aa123\O6
Matched, but too many substrings
 0: aa
 1: <unset>

/(?<N111>(?J)(?<N111>1(111111)11|)1|1|)(?(<N111>)1)/

/(?(?=0)?)+/
Failed: nothing to repeat at offset 7

/(?(?=0)(?=00)?00765)/
     00765
 0: 00765

/(?(?=0)(?=00)?00765|(?!3).56)/
     00765
 0: 00765
     456
 0: 456
     ** Failers
No match
     356   
No match

'^(a)*+(\w)'
    g
 0: g
 1: <unset>
 2: g
    g\O3
Matched, but too many substrings
 0: g

'^(?:a)*+(\w)'
    g
 0: g
 1: g
    g\O3
Matched, but too many substrings
 0: g

//C
    \O\C+
Callout 255: last capture = -1
--->
 +0 ^    
Matched, but too many substrings

"((?2){0,1999}())?"

/((?+1)(\1))/BZ
------------------------------------------------------------------
        Bra
        Once
        CBra 1
        Recurse
        CBra 2
        \1
        Ket
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------

/(?(?!)a|b)/
    bbb
 0: b
    aaa 
No match

"((?2)+)((?1))"

"(?(?<E>.*!.*)?)"
Failed: assertion expected after (?( at offset 3

"X((?2)()*+){2}+"BZ
------------------------------------------------------------------
        Bra
        X
        Once
        CBra 1
        Recurse
        Braposzero
        SCBraPos 2
        KetRpos
        Ket
        CBra 1
        Recurse
        Braposzero
        SCBraPos 2
        KetRpos
        Ket
        Ket
        Ket
        End
------------------------------------------------------------------

"X((?2)()*+){2}"BZ
------------------------------------------------------------------
        Bra
        X
        CBra 1
        Recurse
        Braposzero
        SCBraPos 2
        KetRpos
        Ket
        CBra 1
        Recurse
        Braposzero
        SCBraPos 2
        KetRpos
        Ket
        Ket
        End
------------------------------------------------------------------

"(?<=((?2))((?1)))"
Failed: lookbehind assertion is not fixed length at offset 17

/(?<=\Ka)/g+
    aaaaa
 0: a
 0+ aaaa
 0: a
 0+ aaaa
 0: a
 0+ aaa
 0: a
 0+ aa
 0: a
 0+ a
 0: a
 0+ 

/(?<=\Ka)/G+
    aaaaa
 0: a
 0+ aaaa
 0: a
 0+ aaa
 0: a
 0+ aa
 0: a
 0+ a
 0: a
 0+ 

/((?2){73}(?2))((?1))/

/-- End of testinput2 --/
