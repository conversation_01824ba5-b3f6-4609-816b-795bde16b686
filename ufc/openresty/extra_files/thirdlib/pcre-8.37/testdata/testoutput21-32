/-- Tests for reloading pre-compiled patterns. The first one gives an error
right away, and can be any old pattern compiled in 8-bit mode ("abc" is
typical). The others require the link size to be 2. */x

<!testsaved8
Compiled pattern loaded from testsaved8
No study data
Error -28 from pcre32_fullinfo(0)
Running in 32-bit mode but pattern was compiled in 8-bit mode

%-- Generated from: 
    /^[aL](?P<name>(?:[AaLl]+)[^xX-]*?)(?P<other>[\x{150}-\x{250}\x{300}]|
      [^\x{800}aAs-uS-U\x{d800}-\x{dfff}])++[^#\b\x{500}\x{1000}]{3,5}$
      /x

    In 16-bit mode with options:  S>testdata/saved16LE-1
                                 FS>testdata/saved16BE-1
    In 32-bit mode with options:  S>testdata/saved32LE-1
                                 FS>testdata/saved32BE-1
--%x

<!testsaved16LE-1
Compiled pattern loaded from testsaved16LE-1
Study data loaded from testsaved16LE-1
Error -28 from pcre32_fullinfo(0)
Running in 32-bit mode but pattern was compiled in 16-bit mode

<!testsaved16BE-1
Compiled pattern loaded from testsaved16BE-1
Study data loaded from testsaved16BE-1
Error -28 from pcre32_fullinfo(0)
Running in 32-bit mode but pattern was compiled in 16-bit mode

<!testsaved32LE-1
Compiled pattern loaded from testsaved32LE-1
Study data loaded from testsaved32LE-1
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     ^
  3     [La]
 12  27 CBra 1
 15  12 Bra
 17     [ALal]+
 27  12 Ket
 29     [\x00-,.-WY-wy-\xff] (neg)*?
 39  27 Ket
 41  12 CBraPos 2
 44     [\x{150}-\x{250}\x{300}]
 53  19 Alt
 55     [^AS-Uas-u\x{800}\x{d800}-\x{dfff}]
 72  31 KetRpos
 74     [^\x08#\x{500}\x{1000}]{3,5}
 93     $
 94  94 Ket
 96     End
------------------------------------------------------------------
Capturing subpattern count = 2
Named capturing subpatterns:
  name    1
  other   2
Options: anchored extended
No first char
No need char
Subject length lower bound = 6
No starting char list

<!testsaved32BE-1
Compiled pattern loaded from testsaved32BE-1
Study data loaded from testsaved32BE-1
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     ^
  3     [La]
 12  27 CBra 1
 15  12 Bra
 17     [ALal]+
 27  12 Ket
 29     [\x00-,.-WY-wy-\xff] (neg)*?
 39  27 Ket
 41  12 CBraPos 2
 44     [\x{150}-\x{250}\x{300}]
 53  19 Alt
 55     [^AS-Uas-u\x{800}\x{d800}-\x{dfff}]
 72  31 KetRpos
 74     [^\x08#\x{500}\x{1000}]{3,5}
 93     $
 94  94 Ket
 96     End
------------------------------------------------------------------
Capturing subpattern count = 2
Named capturing subpatterns:
  name    1
  other   2
Options: anchored extended
No first char
No need char
Subject length lower bound = 6
No starting char list

/-- End of testinput21 --/
