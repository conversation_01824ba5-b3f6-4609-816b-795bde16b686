/-- This set of tests check Unicode property support with the DFA matching 
    functionality of pcre_dfa_exec(). The -dfa flag must be used with pcretest
    when running it. --/

/\pL\P{Nd}/8
    AB
 0: AB
    *** Failers
 0: Fa
    A0
No match
    00   
No match

/\X./8
    AB
 0: AB
    A\x{300}BC 
 0: A\x{300}B
    A\x{300}\x{301}\x{302}BC 
 0: A\x{300}\x{301}\x{302}B
    *** Failers
 0: **
    \x{300}  
No match

/\X\X/8
    ABC
 0: AB
    A\x{300}B\x{300}\x{301}C 
 0: A\x{300}B\x{300}\x{301}
    A\x{300}\x{301}\x{302}BC 
 0: A\x{300}\x{301}\x{302}B
    *** Failers
 0: **
    \x{300}  
No match

/^\pL+/8
    abcd
 0: abcd
    a 
 0: a
    *** Failers 
No match

/^\PL+/8
    1234
 0: 1234
    = 
 0: =
    *** Failers 
 0: *** 
    abcd 
No match

/^\X+/8
    abcdA\x{300}\x{301}\x{302}
 0: abcdA\x{300}\x{301}\x{302}
    A\x{300}\x{301}\x{302}
 0: A\x{300}\x{301}\x{302}
    A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}
 0: A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}
    a 
 0: a
    *** Failers 
 0: *** Failers
    \x{300}\x{301}\x{302}
 0: \x{300}\x{301}\x{302}

/\X?abc/8
    abc
 0: abc
    A\x{300}abc
 0: A\x{300}abc
    A\x{300}\x{301}\x{302}A\x{300}A\x{300}A\x{300}abcxyz
 0: A\x{300}abc
    \x{300}abc  
 0: \x{300}abc
    *** Failers
No match

/^\X?abc/8
    abc
 0: abc
    A\x{300}abc
 0: A\x{300}abc
    *** Failers
No match
    A\x{300}\x{301}\x{302}A\x{300}A\x{300}A\x{300}abcxyz
No match
    \x{300}abc  
 0: \x{300}abc

/\X*abc/8
    abc
 0: abc
    A\x{300}abc
 0: A\x{300}abc
    A\x{300}\x{301}\x{302}A\x{300}A\x{300}A\x{300}abcxyz
 0: A\x{300}\x{301}\x{302}A\x{300}A\x{300}A\x{300}abc
    \x{300}abc  
 0: \x{300}abc
    *** Failers
No match

/^\X*abc/8
    abc
 0: abc
    A\x{300}abc
 0: A\x{300}abc
    A\x{300}\x{301}\x{302}A\x{300}A\x{300}A\x{300}abcxyz
 0: A\x{300}\x{301}\x{302}A\x{300}A\x{300}A\x{300}abc
    *** Failers
No match
    \x{300}abc  
 0: \x{300}abc

/^\pL?=./8
    A=b
 0: A=b
    =c 
 0: =c
    *** Failers
No match
    1=2 
No match
    AAAA=b  
No match

/^\pL*=./8
    AAAA=b
 0: AAAA=b
    =c 
 0: =c
    *** Failers
No match
    1=2  
No match

/^\X{2,3}X/8
    A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}X
 0: A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}X
    A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}X 
 0: A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}X
    *** Failers
No match
    X
No match
    A\x{300}\x{301}\x{302}X
No match
    A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}X
No match

/^\pC\pL\pM\pN\pP\pS\pZ</8
    \x7f\x{c0}\x{30f}\x{660}\x{66c}\x{f01}\x{1680}<
 0: \x{7f}\x{c0}\x{30f}\x{660}\x{66c}\x{f01}\x{1680}<
    \np\x{300}9!\$ < 
 0: \x{0a}p\x{300}9!$ <
    ** Failers 
No match
    ap\x{300}9!\$ < 
No match
  
/^\PC/8
    X
 0: X
    ** Failers 
 0: *
    \x7f
No match
  
/^\PL/8
    9
 0: 9
    ** Failers 
 0: *
    \x{c0}
No match
  
/^\PM/8
    X
 0: X
    ** Failers 
 0: *
    \x{30f}
No match
  
/^\PN/8
    X
 0: X
    ** Failers 
 0: *
    \x{660}
No match
  
/^\PP/8
    X
 0: X
    ** Failers 
No match
    \x{66c}
No match
  
/^\PS/8
    X
 0: X
    ** Failers 
 0: *
    \x{f01}
No match
  
/^\PZ/8
    X
 0: X
    ** Failers 
 0: *
    \x{1680}
No match
    
/^\p{Cc}/8
    \x{017}
 0: \x{17}
    \x{09f} 
 0: \x{9f}
    ** Failers
No match
    \x{0600} 
No match
  
/^\p{Cf}/8
    \x{601}
 0: \x{601}
    \x{180e}
 0: \x{180e}
    \x{061c}
 0: \x{61c}
    \x{2066}
 0: \x{2066}
    \x{2067}
 0: \x{2067}
    \x{2068}
 0: \x{2068}
    \x{2069}
 0: \x{2069}
    ** Failers
No match
    \x{09f} 
No match
  
/^\p{Cn}/8
    ** Failers
No match
    \x{09f} 
No match
  
/^\p{Co}/8
    \x{f8ff}
 0: \x{f8ff}
    ** Failers
No match
    \x{09f} 
No match
  
/^\p{Cs}/8
    \?\x{dfff}
 0: \x{dfff}
    ** Failers
No match
    \x{09f} 
No match
  
/^\p{Ll}/8
    a
 0: a
    ** Failers 
No match
    Z
No match
    \x{e000}  
No match
  
/^\p{Lm}/8
    \x{2b0}
 0: \x{2b0}
    ** Failers
No match
    a 
No match
  
/^\p{Lo}/8
    \x{1bb}
 0: \x{1bb}
    ** Failers
No match
    a 
No match
    \x{2b0}
No match
  
/^\p{Lt}/8
    \x{1c5}
 0: \x{1c5}
    ** Failers
No match
    a 
No match
    \x{2b0}
No match
  
/^\p{Lu}/8
    A
 0: A
    ** Failers
No match
    \x{2b0}
No match
  
/^\p{Mc}/8
    \x{903}
 0: \x{903}
    ** Failers
No match
    X
No match
    \x{300}
No match
       
/^\p{Me}/8
    \x{488}
 0: \x{488}
    ** Failers
No match
    X
No match
    \x{903}
No match
    \x{300}
No match
  
/^\p{Mn}/8
    \x{300}
 0: \x{300}
    \x{1a1b}
 0: \x{1a1b}
    ** Failers
No match
    X
No match
    \x{903}
No match
  
/^\p{Nd}+/8O
    0123456789\x{660}\x{661}\x{662}\x{663}\x{664}\x{665}\x{666}\x{667}\x{668}\x{669}\x{66a}
 0: 0123456789\x{660}\x{661}\x{662}\x{663}\x{664}\x{665}\x{666}\x{667}\x{668}\x{669}
 1: 0123456789\x{660}\x{661}\x{662}\x{663}\x{664}\x{665}\x{666}\x{667}\x{668}
 2: 0123456789\x{660}\x{661}\x{662}\x{663}\x{664}\x{665}\x{666}\x{667}
 3: 0123456789\x{660}\x{661}\x{662}\x{663}\x{664}\x{665}\x{666}
 4: 0123456789\x{660}\x{661}\x{662}\x{663}\x{664}\x{665}
 5: 0123456789\x{660}\x{661}\x{662}\x{663}\x{664}
 6: 0123456789\x{660}\x{661}\x{662}\x{663}
 7: 0123456789\x{660}\x{661}\x{662}
 8: 0123456789\x{660}\x{661}
 9: 0123456789\x{660}
10: 0123456789
11: 012345678
12: 01234567
13: 0123456
14: 012345
15: 01234
16: 0123
17: 012
18: 01
19: 0
    \x{6f0}\x{6f1}\x{6f2}\x{6f3}\x{6f4}\x{6f5}\x{6f6}\x{6f7}\x{6f8}\x{6f9}\x{6fa}
 0: \x{6f0}\x{6f1}\x{6f2}\x{6f3}\x{6f4}\x{6f5}\x{6f6}\x{6f7}\x{6f8}\x{6f9}
 1: \x{6f0}\x{6f1}\x{6f2}\x{6f3}\x{6f4}\x{6f5}\x{6f6}\x{6f7}\x{6f8}
 2: \x{6f0}\x{6f1}\x{6f2}\x{6f3}\x{6f4}\x{6f5}\x{6f6}\x{6f7}
 3: \x{6f0}\x{6f1}\x{6f2}\x{6f3}\x{6f4}\x{6f5}\x{6f6}
 4: \x{6f0}\x{6f1}\x{6f2}\x{6f3}\x{6f4}\x{6f5}
 5: \x{6f0}\x{6f1}\x{6f2}\x{6f3}\x{6f4}
 6: \x{6f0}\x{6f1}\x{6f2}\x{6f3}
 7: \x{6f0}\x{6f1}\x{6f2}
 8: \x{6f0}\x{6f1}
 9: \x{6f0}
    \x{966}\x{967}\x{968}\x{969}\x{96a}\x{96b}\x{96c}\x{96d}\x{96e}\x{96f}\x{970}
 0: \x{966}\x{967}\x{968}\x{969}\x{96a}\x{96b}\x{96c}\x{96d}\x{96e}\x{96f}
 1: \x{966}\x{967}\x{968}\x{969}\x{96a}\x{96b}\x{96c}\x{96d}\x{96e}
 2: \x{966}\x{967}\x{968}\x{969}\x{96a}\x{96b}\x{96c}\x{96d}
 3: \x{966}\x{967}\x{968}\x{969}\x{96a}\x{96b}\x{96c}
 4: \x{966}\x{967}\x{968}\x{969}\x{96a}\x{96b}
 5: \x{966}\x{967}\x{968}\x{969}\x{96a}
 6: \x{966}\x{967}\x{968}\x{969}
 7: \x{966}\x{967}\x{968}
 8: \x{966}\x{967}
 9: \x{966}
    ** Failers
No match
    X
No match
  
/^\p{Nl}/8
    \x{16ee}
 0: \x{16ee}
    ** Failers
No match
    X
No match
    \x{966}
No match
  
/^\p{No}/8
    \x{b2}
 0: \x{b2}
    \x{b3}
 0: \x{b3}
    ** Failers
No match
    X
No match
    \x{16ee}
No match
  
/^\p{Pc}/8
    \x5f
 0: _
    \x{203f}
 0: \x{203f}
    ** Failers
No match
    X
No match
    -
No match
    \x{58a}
No match
  
/^\p{Pd}/8
    -
 0: -
    \x{58a}
 0: \x{58a}
    ** Failers
No match
    X
No match
    \x{203f}
No match
  
/^\p{Pe}/8
    )
 0: )
    ]
 0: ]
    }
 0: }
    \x{f3b}
 0: \x{f3b}
    \x{2309}
 0: \x{2309}
    \x{230b}
 0: \x{230b}
    ** Failers
No match
    X
No match
    \x{203f}
No match
    (
No match
    [
No match
    {
No match
    \x{f3c}
No match

/^\p{Pf}/8
    \x{bb}
 0: \x{bb}
    \x{2019}
 0: \x{2019}
    ** Failers
No match
    X
No match
    \x{203f}
No match
  
/^\p{Pi}/8
    \x{ab}
 0: \x{ab}
    \x{2018}
 0: \x{2018}
    ** Failers
No match
    X
No match
    \x{203f}
No match
  
/^\p{Po}/8
    !
 0: !
    \x{37e}
 0: \x{37e}
    ** Failers
 0: *
    X
No match
    \x{203f}
No match
  
/^\p{Ps}/8
    (
 0: (
    [
 0: [
    {
 0: {
    \x{f3c}
 0: \x{f3c}
    \x{2308}
 0: \x{2308}
    \x{230a}
 0: \x{230a}
    ** Failers
No match
    X
No match
    )
No match
    ]
No match
    }
No match
    \x{f3b}
No match
  
/^\p{Sc}+/8
    $\x{a2}\x{a3}\x{a4}\x{a5}\x{a6}
 0: $\x{a2}\x{a3}\x{a4}\x{a5}
    \x{9f2}
 0: \x{9f2}
    ** Failers
No match
    X
No match
    \x{2c2}
No match
  
/^\p{Sk}/8
    \x{2c2}
 0: \x{2c2}
    ** Failers
No match
    X
No match
    \x{9f2}
No match
  
/^\p{Sm}+/8
    +<|~\x{ac}\x{2044}
 0: +<|~\x{ac}\x{2044}
    ** Failers
No match
    X
No match
    \x{9f2}
No match
  
/^\p{So}/8
    \x{a6}
 0: \x{a6}
    \x{482} 
 0: \x{482}
    ** Failers
No match
    X
No match
    \x{9f2}
No match
  
/^\p{Zl}/8
    \x{2028}
 0: \x{2028}
    ** Failers
No match
    X
No match
    \x{2029}
No match
  
/^\p{Zp}/8
    \x{2029}
 0: \x{2029}
    ** Failers
No match
    X
No match
    \x{2028}
No match
  
/^\p{Zs}/8
    \ \
 0:  
    \x{a0}
 0: \x{a0}
    \x{1680}
 0: \x{1680}
    \x{2000}
 0: \x{2000}
    \x{2001}     
 0: \x{2001}
    ** Failers
No match
    \x{2028}
No match
    \x{200d} 
No match
  
/\p{Nd}+(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}AB
 1: \x{660}\x{661}\x{662}A
 2: \x{660}\x{661}\x{662}
  
/\p{Nd}+?(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}AB
 1: \x{660}\x{661}\x{662}A
 2: \x{660}\x{661}\x{662}
  
/\p{Nd}{2,}(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}AB
 1: \x{660}\x{661}\x{662}A
  
/\p{Nd}{2,}?(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}AB
 1: \x{660}\x{661}\x{662}A
  
/\p{Nd}*(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}AB
 1: \x{660}\x{661}\x{662}A
 2: \x{660}\x{661}\x{662}
 3: \x{660}\x{661}
  
/\p{Nd}*?(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}AB
 1: \x{660}\x{661}\x{662}A
 2: \x{660}\x{661}\x{662}
 3: \x{660}\x{661}
  
/\p{Nd}{2}(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}A
  
/\p{Nd}{2,3}(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}AB
 1: \x{660}\x{661}\x{662}A
  
/\p{Nd}{2,3}?(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}AB
 1: \x{660}\x{661}\x{662}A
  
/\p{Nd}?(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}
 1: \x{660}\x{661}
  
/\p{Nd}??(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}
 1: \x{660}\x{661}
  
/\p{Nd}*+(..)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}AB
  
/\p{Nd}*+(...)/8
      \x{660}\x{661}\x{662}ABC
 0: \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*+(....)/8
      ** Failers
 0: ** F
      \x{660}\x{661}\x{662}ABC
No match
  
/\p{Lu}/8i
    A
 0: A
    a\x{10a0}B 
 0: \x{10a0}
    ** Failers 
 0: F
    a
No match
    \x{1d00}  
No match

/\p{^Lu}/8i
    1234
 0: 1
    ** Failers
 0: *
    ABC 
No match

/\P{Lu}/8i
    1234
 0: 1
    ** Failers
 0: *
    ABC 
No match

/(?<=A\p{Nd})XYZ/8
    A2XYZ
 0: XYZ
    123A5XYZPQR
 0: XYZ
    ABA\x{660}XYZpqr
 0: XYZ
    ** Failers
No match
    AXYZ
No match
    XYZ     
No match
    
/(?<!\pL)XYZ/8
    1XYZ
 0: XYZ
    AB=XYZ.. 
 0: XYZ
    XYZ 
 0: XYZ
    ** Failers
No match
    WXYZ 
No match

/[\p{Nd}]/8
    1234
 0: 1

/[\p{Nd}+-]+/8
    1234
 0: 1234
    12-34
 0: 12-34
    12+\x{661}-34  
 0: 12+\x{661}-34
    ** Failers
No match
    abcd  
No match

/[\P{Nd}]+/8
    abcd
 0: abcd
    ** Failers
 0: ** Failers
    1234
No match

/\D+/8O
    11111111111111111111111111111111111111111111111111111111111111111111111
No match
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
Matched, but offsets vector is too small to show all matches
 0: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 1: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 2: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 3: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 4: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 5: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 6: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 7: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 8: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 9: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
10: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
11: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
12: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
13: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
14: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
15: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
16: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
17: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
18: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
19: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
20: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
21: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
     
/\P{Nd}+/8O
    11111111111111111111111111111111111111111111111111111111111111111111111
No match
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
Matched, but offsets vector is too small to show all matches
 0: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 1: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 2: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 3: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 4: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 5: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 6: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 7: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 8: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 9: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
10: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
11: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
12: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
13: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
14: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
15: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
16: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
17: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
18: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
19: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
20: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
21: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/[\D]+/8O
    11111111111111111111111111111111111111111111111111111111111111111111111
No match
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
Matched, but offsets vector is too small to show all matches
 0: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 1: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 2: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 3: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 4: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 5: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 6: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 7: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 8: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 9: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
10: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
11: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
12: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
13: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
14: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
15: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
16: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
17: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
18: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
19: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
20: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
21: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/[\P{Nd}]+/8O
    11111111111111111111111111111111111111111111111111111111111111111111111
No match
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
Matched, but offsets vector is too small to show all matches
 0: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 1: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 2: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 3: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 4: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 5: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 6: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 7: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 8: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 9: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
10: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
11: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
12: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
13: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
14: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
15: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
16: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
17: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
18: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
19: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
20: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
21: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/[\D\P{Nd}]+/8O
    11111111111111111111111111111111111111111111111111111111111111111111111
No match
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
Matched, but offsets vector is too small to show all matches
 0: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 1: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 2: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 3: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 4: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 5: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 6: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 7: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 8: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 9: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
10: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
11: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
12: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
13: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
14: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
15: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
16: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
17: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
18: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
19: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
20: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
21: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/\pL/8
    a
 0: a
    A 
 0: A

/\pL/8i
    a
 0: a
    A 
 0: A
    
/\p{Lu}/8 
    A
 0: A
    aZ
 0: Z
    ** Failers
 0: F
    abc   
No match

/\p{Lu}/8i
    A
 0: A
    aZ
 0: Z
    ** Failers
 0: F
    abc   
No match

/\p{Ll}/8 
    a
 0: a
    Az
 0: z
    ** Failers
 0: a
    ABC   
No match

/\p{Ll}/8i 
    a
 0: a
    Az
 0: z
    ** Failers
 0: a
    ABC   
No match

/^\x{c0}$/8i
    \x{c0}
 0: \x{c0}
    \x{e0} 
 0: \x{e0}

/^\x{e0}$/8i
    \x{c0}
 0: \x{c0}
    \x{e0} 
 0: \x{e0}

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8
    A\x{391}\x{10427}\x{ff3a}\x{1fb0}
 0: A\x{391}\x{10427}\x{ff3a}\x{1fb0}
    ** Failers
No match
    a\x{391}\x{10427}\x{ff3a}\x{1fb0}   
No match
    A\x{3b1}\x{10427}\x{ff3a}\x{1fb0}
No match
    A\x{391}\x{1044F}\x{ff3a}\x{1fb0}
No match
    A\x{391}\x{10427}\x{ff5a}\x{1fb0}
No match
    A\x{391}\x{10427}\x{ff3a}\x{1fb8}
No match

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8i
    A\x{391}\x{10427}\x{ff3a}\x{1fb0}
 0: A\x{391}\x{10427}\x{ff3a}\x{1fb0}
    a\x{391}\x{10427}\x{ff3a}\x{1fb0}   
 0: a\x{391}\x{10427}\x{ff3a}\x{1fb0}
    A\x{3b1}\x{10427}\x{ff3a}\x{1fb0}
 0: A\x{3b1}\x{10427}\x{ff3a}\x{1fb0}
    A\x{391}\x{1044F}\x{ff3a}\x{1fb0}
 0: A\x{391}\x{1044f}\x{ff3a}\x{1fb0}
    A\x{391}\x{10427}\x{ff5a}\x{1fb0}
 0: A\x{391}\x{10427}\x{ff5a}\x{1fb0}
    A\x{391}\x{10427}\x{ff3a}\x{1fb8}
 0: A\x{391}\x{10427}\x{ff3a}\x{1fb8}

/\x{391}+/8i
    \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}
 0: \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}

/\x{391}{3,5}(.)/8i
    \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}X
 0: \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}X
 1: \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}
 2: \x{391}\x{3b1}\x{3b1}\x{3b1}

/\x{391}{3,5}?(.)/8i
    \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}X
 0: \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}X
 1: \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}
 2: \x{391}\x{3b1}\x{3b1}\x{3b1}

/[\x{391}\x{ff3a}]/8i
    \x{391}
 0: \x{391}
    \x{ff3a}
 0: \x{ff3a}
    \x{3b1}
 0: \x{3b1}
    \x{ff5a}   
 0: \x{ff5a}
    
/[\x{c0}\x{391}]/8i
    \x{c0}
 0: \x{c0}
    \x{e0} 
 0: \x{e0}

/[\x{105}-\x{109}]/8i
    \x{104}
 0: \x{104}
    \x{105}
 0: \x{105}
    \x{109}  
 0: \x{109}
    ** Failers
No match
    \x{100}
No match
    \x{10a} 
No match
    
/[z-\x{100}]/8i 
    Z
 0: Z
    z
 0: z
    \x{39c}
 0: \x{39c}
    \x{178}
 0: \x{178}
    |
 0: |
    \x{80}
 0: \x{80}
    \x{ff}
 0: \x{ff}
    \x{100}
 0: \x{100}
    \x{101} 
 0: \x{101}
    ** Failers
No match
    \x{102}
No match
    Y
No match
    y           
No match

/[z-\x{100}]/8i

/^\X/8
    A
 0: A
    A\x{300}BC 
 0: A\x{300}
    A\x{300}\x{301}\x{302}BC 
 0: A\x{300}\x{301}\x{302}
    *** Failers
 0: *
    \x{300}  
 0: \x{300}

/^[\X]/8
    X123
 0: X
    *** Failers
No match
    AXYZ
No match

/^(\X*)C/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
 0: A\x{300}\x{301}\x{302}BC
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 
 0: A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C
 1: A\x{300}\x{301}\x{302}BC

/^(\X*?)C/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
 0: A\x{300}\x{301}\x{302}BC
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 
 0: A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C
 1: A\x{300}\x{301}\x{302}BC

/^(\X*)(.)/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
 0: A\x{300}\x{301}\x{302}BCA
 1: A\x{300}\x{301}\x{302}BC
 2: A\x{300}\x{301}\x{302}B
 3: A
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 
 0: A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C
 1: A\x{300}\x{301}\x{302}BCA
 2: A\x{300}\x{301}\x{302}BC
 3: A\x{300}\x{301}\x{302}B
 4: A

/^(\X*?)(.)/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
 0: A\x{300}\x{301}\x{302}BCA
 1: A\x{300}\x{301}\x{302}BC
 2: A\x{300}\x{301}\x{302}B
 3: A
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 
 0: A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C
 1: A\x{300}\x{301}\x{302}BCA
 2: A\x{300}\x{301}\x{302}BC
 3: A\x{300}\x{301}\x{302}B
 4: A

/^\X(.)/8
    *** Failers
 0: **
    A\x{300}\x{301}\x{302}
No match

/^\X{2,3}(.)/8
    A\x{300}\x{301}B\x{300}X
 0: A\x{300}\x{301}B\x{300}X
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}
 0: A\x{300}\x{301}B\x{300}C
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}X
 0: A\x{300}\x{301}B\x{300}C\x{300}\x{301}X
 1: A\x{300}\x{301}B\x{300}C
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}DA\x{300}X
 0: A\x{300}\x{301}B\x{300}C\x{300}\x{301}D
 1: A\x{300}\x{301}B\x{300}C
    
/^\X{2,3}?(.)/8
    A\x{300}\x{301}B\x{300}X
 0: A\x{300}\x{301}B\x{300}X
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}
 0: A\x{300}\x{301}B\x{300}C
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}X
 0: A\x{300}\x{301}B\x{300}C\x{300}\x{301}X
 1: A\x{300}\x{301}B\x{300}C
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}DA\x{300}X
 0: A\x{300}\x{301}B\x{300}C\x{300}\x{301}D
 1: A\x{300}\x{301}B\x{300}C

/^\pN{2,3}X/
    12X
 0: 12X
    123X
 0: 123X
    *** Failers
No match
    X
No match
    1X
No match
    1234X     
No match

/\x{100}/i8
    \x{100}   
 0: \x{100}
    \x{101} 
 0: \x{101}
    
/^\p{Han}+/8
    \x{2e81}\x{3007}\x{2f804}\x{31a0}
 0: \x{2e81}\x{3007}\x{2f804}
    ** Failers
No match
    \x{2e7f}  
No match

/^\P{Katakana}+/8
    \x{3105}
 0: \x{3105}
    ** Failers
 0: ** Failers
    \x{30ff}  
No match

/^[\p{Arabic}]/8
    \x{06e9}
 0: \x{6e9}
    \x{060b}
 0: \x{60b}
    ** Failers
No match
    X\x{06e9}   
No match

/^[\P{Yi}]/8
    \x{2f800}
 0: \x{2f800}
    ** Failers
 0: *
    \x{a014}
No match
    \x{a4c6}   
No match

/^\p{Any}X/8
    AXYZ
 0: AX
    \x{1234}XYZ 
 0: \x{1234}X
    ** Failers
No match
    X  
No match
    
/^\P{Any}X/8
    ** Failers
No match
    AX
No match
    
/^\p{Any}?X/8
    XYZ
 0: X
    AXYZ
 0: AX
    \x{1234}XYZ 
 0: \x{1234}X
    ** Failers
No match
    ABXYZ   
No match

/^\P{Any}?X/8
    XYZ
 0: X
    ** Failers
No match
    AXYZ
No match
    \x{1234}XYZ 
No match
    ABXYZ   
No match

/^\p{Any}+X/8
    AXYZ
 0: AX
    \x{1234}XYZ
 0: \x{1234}X
    A\x{1234}XYZ
 0: A\x{1234}X
    ** Failers
No match
    XYZ
No match

/^\P{Any}+X/8
    ** Failers
No match
    AXYZ
No match
    \x{1234}XYZ
No match
    A\x{1234}XYZ
No match
    XYZ
No match

/^\p{Any}*X/8
    XYZ
 0: X
    AXYZ
 0: AX
    \x{1234}XYZ
 0: \x{1234}X
    A\x{1234}XYZ
 0: A\x{1234}X
    ** Failers
No match

/^\P{Any}*X/8
    XYZ
 0: X
    ** Failers
No match
    AXYZ
No match
    \x{1234}XYZ
No match
    A\x{1234}XYZ
No match

/^[\p{Any}]X/8
    AXYZ
 0: AX
    \x{1234}XYZ 
 0: \x{1234}X
    ** Failers
No match
    X  
No match
    
/^[\P{Any}]X/8
    ** Failers
No match
    AX
No match
    
/^[\p{Any}]?X/8
    XYZ
 0: X
    AXYZ
 0: AX
    \x{1234}XYZ 
 0: \x{1234}X
    ** Failers
No match
    ABXYZ   
No match

/^[\P{Any}]?X/8
    XYZ
 0: X
    ** Failers
No match
    AXYZ
No match
    \x{1234}XYZ 
No match
    ABXYZ   
No match

/^[\p{Any}]+X/8
    AXYZ
 0: AX
    \x{1234}XYZ
 0: \x{1234}X
    A\x{1234}XYZ
 0: A\x{1234}X
    ** Failers
No match
    XYZ
No match

/^[\P{Any}]+X/8
    ** Failers
No match
    AXYZ
No match
    \x{1234}XYZ
No match
    A\x{1234}XYZ
No match
    XYZ
No match

/^[\p{Any}]*X/8
    XYZ
 0: X
    AXYZ
 0: AX
    \x{1234}XYZ
 0: \x{1234}X
    A\x{1234}XYZ
 0: A\x{1234}X
    ** Failers
No match

/^[\P{Any}]*X/8
    XYZ
 0: X
    ** Failers
No match
    AXYZ
No match
    \x{1234}XYZ
No match
    A\x{1234}XYZ
No match

/^\p{Any}{3,5}?/8
    abcdefgh
 0: abcde
 1: abcd
 2: abc
    \x{1234}\n\r\x{3456}xyz 
 0: \x{1234}\x{0a}\x{0d}\x{3456}x
 1: \x{1234}\x{0a}\x{0d}\x{3456}
 2: \x{1234}\x{0a}\x{0d}

/^\p{Any}{3,5}/8
    abcdefgh
 0: abcde
    \x{1234}\n\r\x{3456}xyz 
 0: \x{1234}\x{0a}\x{0d}\x{3456}x

/^\P{Any}{3,5}?/8
    ** Failers
No match
    abcdefgh
No match
    \x{1234}\n\r\x{3456}xyz 
No match

/^\p{L&}X/8
     AXY
 0: AX
     aXY
 0: aX
     \x{1c5}XY
 0: \x{1c5}X
     ** Failers
No match
     \x{1bb}XY
No match
     \x{2b0}XY
No match
     !XY      
No match

/^[\p{L&}]X/8
     AXY
 0: AX
     aXY
 0: aX
     \x{1c5}XY
 0: \x{1c5}X
     ** Failers
No match
     \x{1bb}XY
No match
     \x{2b0}XY
No match
     !XY      
No match

/^\p{L&}+X/8
     AXY
 0: AX
     aXY
 0: aX
     AbcdeXyz 
 0: AbcdeX
     \x{1c5}AbXY
 0: \x{1c5}AbX
     abcDEXypqreXlmn 
 0: abcDEXypqreX
 1: abcDEX
     ** Failers
No match
     \x{1bb}XY
No match
     \x{2b0}XY
No match
     !XY      
No match

/^[\p{L&}]+X/8
     AXY
 0: AX
     aXY
 0: aX
     AbcdeXyz 
 0: AbcdeX
     \x{1c5}AbXY
 0: \x{1c5}AbX
     abcDEXypqreXlmn 
 0: abcDEXypqreX
 1: abcDEX
     ** Failers
No match
     \x{1bb}XY
No match
     \x{2b0}XY
No match
     !XY      
No match

/^\p{L&}+?X/8
     AXY
 0: AX
     aXY
 0: aX
     AbcdeXyz 
 0: AbcdeX
     \x{1c5}AbXY
 0: \x{1c5}AbX
     abcDEXypqreXlmn 
 0: abcDEXypqreX
 1: abcDEX
     ** Failers
No match
     \x{1bb}XY
No match
     \x{2b0}XY
No match
     !XY      
No match

/^[\p{L&}]+?X/8
     AXY
 0: AX
     aXY
 0: aX
     AbcdeXyz 
 0: AbcdeX
     \x{1c5}AbXY
 0: \x{1c5}AbX
     abcDEXypqreXlmn 
 0: abcDEXypqreX
 1: abcDEX
     ** Failers
No match
     \x{1bb}XY
No match
     \x{2b0}XY
No match
     !XY      
No match

/^\P{L&}X/8
     !XY
 0: !X
     \x{1bb}XY
 0: \x{1bb}X
     \x{2b0}XY
 0: \x{2b0}X
     ** Failers
No match
     \x{1c5}XY
No match
     AXY      
No match

/^[\P{L&}]X/8
     !XY
 0: !X
     \x{1bb}XY
 0: \x{1bb}X
     \x{2b0}XY
 0: \x{2b0}X
     ** Failers
No match
     \x{1c5}XY
No match
     AXY      
No match

/^\x{023a}+?(\x{0130}+)/8i
  \x{023a}\x{2c65}\x{0130}
 0: \x{23a}\x{2c65}\x{130}
  
/^\x{023a}+([^X])/8i
  \x{023a}\x{2c65}X
 0: \x{23a}\x{2c65}
 
/\x{c0}+\x{116}+/8i
    \x{c0}\x{e0}\x{116}\x{117}
 0: \x{c0}\x{e0}\x{116}\x{117}

/[\x{c0}\x{116}]+/8i
    \x{c0}\x{e0}\x{116}\x{117}
 0: \x{c0}\x{e0}\x{116}\x{117}

/Check property support in non-UTF-8 mode/
 
/\p{L}{4}/
    123abcdefg
 0: abcd
    123abc\xc4\xc5zz
 0: abc\xc4

/\p{Carian}\p{Cham}\p{Kayah_Li}\p{Lepcha}\p{Lycian}\p{Lydian}\p{Ol_Chiki}\p{Rejang}\p{Saurashtra}\p{Sundanese}\p{Vai}/8
    \x{102A4}\x{AA52}\x{A91D}\x{1C46}\x{10283}\x{1092E}\x{1C6B}\x{A93B}\x{A8BF}\x{1BA0}\x{A50A}====
 0: \x{102a4}\x{aa52}\x{a91d}\x{1c46}\x{10283}\x{1092e}\x{1c6b}\x{a93b}\x{a8bf}\x{1ba0}\x{a50a}

/\x{a77d}\x{1d79}/8i
    \x{a77d}\x{1d79}
 0: \x{a77d}\x{1d79}
    \x{1d79}\x{a77d} 
 0: \x{1d79}\x{a77d}

/\x{a77d}\x{1d79}/8
    \x{a77d}\x{1d79}
 0: \x{a77d}\x{1d79}
    ** Failers 
No match
    \x{1d79}\x{a77d} 
No match

/^\p{Xan}/8
    ABCD
 0: A
    1234
 0: 1
    \x{6ca}
 0: \x{6ca}
    \x{a6c}
 0: \x{a6c}
    \x{10a7}   
 0: \x{10a7}
    ** Failers
No match
    _ABC   
No match

/^\p{Xan}+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}
    ** Failers
No match
    _ABC   
No match

/^\p{Xan}*/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}
    
/^\p{Xan}{2,9}/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}
    
/^[\p{Xan}]/8
    ABCD1234_
 0: A
    1234abcd_
 0: 1
    \x{6ca}
 0: \x{6ca}
    \x{a6c}
 0: \x{a6c}
    \x{10a7}   
 0: \x{10a7}
    ** Failers
No match
    _ABC   
No match
 
/^[\p{Xan}]+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}
    ** Failers
No match
    _ABC   
No match

/^>\p{Xsp}/8
    >\x{1680}\x{2028}\x{0b}
 0: >\x{1680}
    ** Failers
No match
    \x{0b} 
No match

/^>\p{Xsp}+/8O
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 1: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}
 2: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}
 3: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}
 4: > \x{09}\x{0a}\x{0c}\x{0d}
 5: > \x{09}\x{0a}\x{0c}
 6: > \x{09}\x{0a}
 7: > \x{09}
 8: > 

/^>\p{Xsp}*/8O
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 1: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}
 2: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}
 3: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}
 4: > \x{09}\x{0a}\x{0c}\x{0d}
 5: > \x{09}\x{0a}\x{0c}
 6: > \x{09}\x{0a}
 7: > \x{09}
 8: > 
 9: >
    
/^>\p{Xsp}{2,9}/8O
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 1: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}
 2: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}
 3: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}
 4: > \x{09}\x{0a}\x{0c}\x{0d}
 5: > \x{09}\x{0a}\x{0c}
 6: > \x{09}\x{0a}
 7: > \x{09}
    
/^>[\p{Xsp}]/8O
    >\x{2028}\x{0b}
 0: >\x{2028}
 
/^>[\p{Xsp}]+/8O
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 1: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}
 2: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}
 3: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}
 4: > \x{09}\x{0a}\x{0c}\x{0d}
 5: > \x{09}\x{0a}\x{0c}
 6: > \x{09}\x{0a}
 7: > \x{09}
 8: > 

/^>\p{Xps}/8
    >\x{1680}\x{2028}\x{0b}
 0: >\x{1680}
    >\x{a0} 
 0: >\x{a0}
    ** Failers
No match
    \x{0b} 
No match

/^>\p{Xps}+/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}

/^>\p{Xps}+?/8
    >\x{1680}\x{2028}\x{0b}
 0: >\x{1680}\x{2028}\x{0b}
 1: >\x{1680}\x{2028}
 2: >\x{1680}

/^>\p{Xps}*/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>\p{Xps}{2,9}/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>\p{Xps}{2,9}?/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 1: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}
 2: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}
 3: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}
 4: > \x{09}\x{0a}\x{0c}\x{0d}
 5: > \x{09}\x{0a}\x{0c}
 6: > \x{09}\x{0a}
 7: > \x{09}
    
/^>[\p{Xps}]/8
    >\x{2028}\x{0b}
 0: >\x{2028}
 
/^>[\p{Xps}]+/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}

/^\p{Xwd}/8
    ABCD
 0: A
    1234
 0: 1
    \x{6ca}
 0: \x{6ca}
    \x{a6c}
 0: \x{a6c}
    \x{10a7}
 0: \x{10a7}
    _ABC    
 0: _
    ** Failers
No match
    [] 
No match

/^\p{Xwd}+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}_

/^\p{Xwd}*/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}_
    
/^\p{Xwd}{2,9}/8
    A_12\x{6ca}\x{a6c}\x{10a7}
 0: A_12\x{6ca}\x{a6c}\x{10a7}
    
/^[\p{Xwd}]/8
    ABCD1234_
 0: A
    1234abcd_
 0: 1
    \x{6ca}
 0: \x{6ca}
    \x{a6c}
 0: \x{a6c}
    \x{10a7}   
 0: \x{10a7}
    _ABC 
 0: _
    ** Failers
No match
    []   
No match
 
/^[\p{Xwd}]+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}_

/-- Unicode properties for \b abd \B --/

/\b...\B/8W
    abc_
 0: abc
    \x{37e}abc\x{376} 
 0: abc
    \x{37e}\x{376}\x{371}\x{393}\x{394} 
 0: \x{376}\x{371}\x{393}
    !\x{c0}++\x{c1}\x{c2} 
 0: ++\x{c1}
    !\x{c0}+++++ 
 0: \x{c0}++

/-- Without PCRE_UCP, non-ASCII always fail, even if < 256  --/

/\b...\B/8
    abc_
 0: abc
    ** Failers 
 0: Fai
    \x{37e}abc\x{376} 
No match
    \x{37e}\x{376}\x{371}\x{393}\x{394} 
No match
    !\x{c0}++\x{c1}\x{c2} 
No match
    !\x{c0}+++++ 
No match

/-- With PCRE_UCP, non-UTF8 chars that are < 256 still check properties  --/

/\b...\B/W
    abc_
 0: abc
    !\x{c0}++\x{c1}\x{c2} 
 0: ++\xc1
    !\x{c0}+++++ 
 0: \xc0++
    
/-- Caseless single negated characters > 127 need UCP support --/

/[^\x{100}]/8i
    \x{100}\x{101}X
 0: X

/[^\x{100}]+/8i
    \x{100}\x{101}XX
 0: XX

/^\X/8
    A\P
 0: A
    A\P\P 
Partial match: A
    A\x{300}\x{301}\P
 0: A\x{300}\x{301}
    A\x{300}\x{301}\P\P  
Partial match: A\x{300}\x{301}
    A\x{301}\P
 0: A\x{301}
    A\x{301}\P\P  
Partial match: A\x{301}
    
/^\X{2,3}/8
    A\P
Partial match: A
    A\P\P 
Partial match: A
    AA\P
 0: AA
    AA\P\P  
Partial match: AA
    A\x{300}\x{301}\P
Partial match: A\x{300}\x{301}
    A\x{300}\x{301}\P\P  
Partial match: A\x{300}\x{301}
    A\x{300}\x{301}A\x{300}\x{301}\P
 0: A\x{300}\x{301}A\x{300}\x{301}
    A\x{300}\x{301}A\x{300}\x{301}\P\P  
Partial match: A\x{300}\x{301}A\x{300}\x{301}

/^\X{2}/8
    AA\P
 0: AA
    AA\P\P  
Partial match: AA
    A\x{300}\x{301}A\x{300}\x{301}\P
 0: A\x{300}\x{301}A\x{300}\x{301}
    A\x{300}\x{301}A\x{300}\x{301}\P\P  
Partial match: A\x{300}\x{301}A\x{300}\x{301}
    
/^\X+/8
    AA\P
 0: AA
    AA\P\P  
Partial match: AA

/^\X+?Z/8
    AA\P
Partial match: AA
    AA\P\P 
Partial match: AA

/-- These are tests for extended grapheme clusters --/ 

/^\X/8+
    G\x{34e}\x{34e}X
 0: G\x{34e}\x{34e}
 0+ X
    \x{34e}\x{34e}X
 0: \x{34e}\x{34e}
 0+ X
    \x04X
 0: \x{04}
 0+ X
    \x{1100}X
 0: \x{1100}
 0+ X
    \x{1100}\x{34e}X
 0: \x{1100}\x{34e}
 0+ X
    \x{1b04}\x{1b04}X 
 0: \x{1b04}\x{1b04}
 0+ X
    *These match up to the roman letters
 0: *
 0+ These match up to the roman letters
    \x{1111}\x{1111}L,L
 0: \x{1111}\x{1111}
 0+ L,L
    \x{1111}\x{1111}\x{1169}L,L,V
 0: \x{1111}\x{1111}\x{1169}
 0+ L,L,V
    \x{1111}\x{ae4c}L, LV
 0: \x{1111}\x{ae4c}
 0+ L, LV
    \x{1111}\x{ad89}L, LVT
 0: \x{1111}\x{ad89}
 0+ L, LVT
    \x{1111}\x{ae4c}\x{1169}L, LV, V
 0: \x{1111}\x{ae4c}\x{1169}
 0+ L, LV, V
    \x{1111}\x{ae4c}\x{1169}\x{1169}L, LV, V, V
 0: \x{1111}\x{ae4c}\x{1169}\x{1169}
 0+ L, LV, V, V
    \x{1111}\x{ae4c}\x{1169}\x{11fe}L, LV, V, T
 0: \x{1111}\x{ae4c}\x{1169}\x{11fe}
 0+ L, LV, V, T
    \x{1111}\x{ad89}\x{11fe}L, LVT, T
 0: \x{1111}\x{ad89}\x{11fe}
 0+ L, LVT, T
    \x{1111}\x{ad89}\x{11fe}\x{11fe}L, LVT, T, T
 0: \x{1111}\x{ad89}\x{11fe}\x{11fe}
 0+ L, LVT, T, T
    \x{ad89}\x{11fe}\x{11fe}LVT, T, T
 0: \x{ad89}\x{11fe}\x{11fe}
 0+ LVT, T, T
    *These match just the first codepoint (invalid sequence)
 0: *
 0+ These match just the first codepoint (invalid sequence)
    \x{1111}\x{11fe}L, T
 0: \x{1111}
 0+ \x{11fe}L, T
    \x{ae4c}\x{1111}LV, L
 0: \x{ae4c}
 0+ \x{1111}LV, L
    \x{ae4c}\x{ae4c}LV, LV
 0: \x{ae4c}
 0+ \x{ae4c}LV, LV
    \x{ae4c}\x{ad89}LV, LVT
 0: \x{ae4c}
 0+ \x{ad89}LV, LVT
    \x{1169}\x{1111}V, L
 0: \x{1169}
 0+ \x{1111}V, L
    \x{1169}\x{ae4c}V, LV
 0: \x{1169}
 0+ \x{ae4c}V, LV
    \x{1169}\x{ad89}V, LVT
 0: \x{1169}
 0+ \x{ad89}V, LVT
    \x{ad89}\x{1111}LVT, L
 0: \x{ad89}
 0+ \x{1111}LVT, L
    \x{ad89}\x{1169}LVT, V
 0: \x{ad89}
 0+ \x{1169}LVT, V
    \x{ad89}\x{ae4c}LVT, LV
 0: \x{ad89}
 0+ \x{ae4c}LVT, LV
    \x{ad89}\x{ad89}LVT, LVT
 0: \x{ad89}
 0+ \x{ad89}LVT, LVT
    \x{11fe}\x{1111}T, L
 0: \x{11fe}
 0+ \x{1111}T, L
    \x{11fe}\x{1169}T, V
 0: \x{11fe}
 0+ \x{1169}T, V
    \x{11fe}\x{ae4c}T, LV
 0: \x{11fe}
 0+ \x{ae4c}T, LV
    \x{11fe}\x{ad89}T, LVT
 0: \x{11fe}
 0+ \x{ad89}T, LVT
    *Test extend and spacing mark
 0: *
 0+ Test extend and spacing mark
    \x{1111}\x{ae4c}\x{0711}L, LV, extend
 0: \x{1111}\x{ae4c}\x{711}
 0+ L, LV, extend
    \x{1111}\x{ae4c}\x{1b04}L, LV, spacing mark
 0: \x{1111}\x{ae4c}\x{1b04}
 0+ L, LV, spacing mark
    \x{1111}\x{ae4c}\x{1b04}\x{0711}\x{1b04}L, LV, spacing mark, extend, spacing mark
 0: \x{1111}\x{ae4c}\x{1b04}\x{711}\x{1b04}
 0+ L, LV, spacing mark, extend, spacing mark
    *Test CR, LF, and control
 0: *
 0+ Test CR, LF, and control
    \x0d\x{0711}CR, extend
 0: \x{0d}
 0+ \x{711}CR, extend
    \x0d\x{1b04}CR, spacingmark
 0: \x{0d}
 0+ \x{1b04}CR, spacingmark
    \x0a\x{0711}LF, extend
 0: \x{0a}
 0+ \x{711}LF, extend
    \x0a\x{1b04}LF, spacingmark
 0: \x{0a}
 0+ \x{1b04}LF, spacingmark
    \x0b\x{0711}Control, extend
 0: \x{0b}
 0+ \x{711}Control, extend
    \x09\x{1b04}Control, spacingmark
 0: \x{09}
 0+ \x{1b04}Control, spacingmark
    *There are no Prepend characters, so we can't test Prepend, CR
 0: *
 0+ There are no Prepend characters, so we can't test Prepend, CR
    
/^(?>\X{2})X/8+
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0: \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0+ 
    
/^\X{2,4}X/8+
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0: \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0+ 
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0: \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0+ 
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0: \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0+ 

/^\X{2,4}?X/8+
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0: \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0+ 
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0: \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0+ 
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0: \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
 0+ 

/-- --/

/\x{1e9e}+/8i
    \x{1e9e}\x{00df}
 0: \x{1e9e}\x{df}

/[z\x{1e9e}]+/8i
    \x{1e9e}\x{00df}
 0: \x{1e9e}\x{df}

/\x{00df}+/8i
    \x{1e9e}\x{00df}
 0: \x{1e9e}\x{df}

/[z\x{00df}]+/8i
    \x{1e9e}\x{00df}
 0: \x{1e9e}\x{df}

/\x{1f88}+/8i
    \x{1f88}\x{1f80} 
 0: \x{1f88}\x{1f80}

/[z\x{1f88}]+/8i
    \x{1f88}\x{1f80} 
 0: \x{1f88}\x{1f80}

/-- Perl matches these --/

/\x{00b5}+/8i
    \x{00b5}\x{039c}\x{03bc}
 0: \x{b5}\x{39c}\x{3bc}

/\x{039c}+/8i
    \x{00b5}\x{039c}\x{03bc}
 0: \x{b5}\x{39c}\x{3bc}

/\x{03bc}+/8i
    \x{00b5}\x{039c}\x{03bc}
 0: \x{b5}\x{39c}\x{3bc}


/\x{00c5}+/8i
    \x{00c5}\x{00e5}\x{212b}
 0: \x{c5}\x{e5}\x{212b}

/\x{00e5}+/8i
    \x{00c5}\x{00e5}\x{212b}
 0: \x{c5}\x{e5}\x{212b}

/\x{212b}+/8i
    \x{00c5}\x{00e5}\x{212b}
 0: \x{c5}\x{e5}\x{212b}


/\x{01c4}+/8i
    \x{01c4}\x{01c5}\x{01c6}
 0: \x{1c4}\x{1c5}\x{1c6}

/\x{01c5}+/8i
    \x{01c4}\x{01c5}\x{01c6}
 0: \x{1c4}\x{1c5}\x{1c6}

/\x{01c6}+/8i
    \x{01c4}\x{01c5}\x{01c6}
 0: \x{1c4}\x{1c5}\x{1c6}


/\x{01c7}+/8i
    \x{01c7}\x{01c8}\x{01c9}
 0: \x{1c7}\x{1c8}\x{1c9}

/\x{01c8}+/8i
    \x{01c7}\x{01c8}\x{01c9}
 0: \x{1c7}\x{1c8}\x{1c9}

/\x{01c9}+/8i
    \x{01c7}\x{01c8}\x{01c9}
 0: \x{1c7}\x{1c8}\x{1c9}


/\x{01ca}+/8i
    \x{01ca}\x{01cb}\x{01cc}
 0: \x{1ca}\x{1cb}\x{1cc}

/\x{01cb}+/8i
    \x{01ca}\x{01cb}\x{01cc}
 0: \x{1ca}\x{1cb}\x{1cc}

/\x{01cc}+/8i
    \x{01ca}\x{01cb}\x{01cc}
 0: \x{1ca}\x{1cb}\x{1cc}


/\x{01f1}+/8i
    \x{01f1}\x{01f2}\x{01f3}
 0: \x{1f1}\x{1f2}\x{1f3}

/\x{01f2}+/8i
    \x{01f1}\x{01f2}\x{01f3}
 0: \x{1f1}\x{1f2}\x{1f3}

/\x{01f3}+/8i
    \x{01f1}\x{01f2}\x{01f3}
 0: \x{1f1}\x{1f2}\x{1f3}


/\x{0345}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}
 0: \x{345}\x{399}\x{3b9}\x{1fbe}

/\x{0399}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}
 0: \x{345}\x{399}\x{3b9}\x{1fbe}

/\x{03b9}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}
 0: \x{345}\x{399}\x{3b9}\x{1fbe}

/\x{1fbe}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}
 0: \x{345}\x{399}\x{3b9}\x{1fbe}


/\x{0392}+/8i
    \x{0392}\x{03b2}\x{03d0}
 0: \x{392}\x{3b2}\x{3d0}

/\x{03b2}+/8i
    \x{0392}\x{03b2}\x{03d0}
 0: \x{392}\x{3b2}\x{3d0}

/\x{03d0}+/8i
    \x{0392}\x{03b2}\x{03d0}
 0: \x{392}\x{3b2}\x{3d0}
    

/\x{0395}+/8i
    \x{0395}\x{03b5}\x{03f5}
 0: \x{395}\x{3b5}\x{3f5}

/\x{03b5}+/8i
    \x{0395}\x{03b5}\x{03f5}
 0: \x{395}\x{3b5}\x{3f5}

/\x{03f5}+/8i
    \x{0395}\x{03b5}\x{03f5}
 0: \x{395}\x{3b5}\x{3f5}


/\x{0398}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}
 0: \x{398}\x{3b8}\x{3d1}\x{3f4}

/\x{03b8}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}
 0: \x{398}\x{3b8}\x{3d1}\x{3f4}

/\x{03d1}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}
 0: \x{398}\x{3b8}\x{3d1}\x{3f4}

/\x{03f4}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}
 0: \x{398}\x{3b8}\x{3d1}\x{3f4}
    

/\x{039a}+/8i
    \x{039a}\x{03ba}\x{03f0}
 0: \x{39a}\x{3ba}\x{3f0}

/\x{03ba}+/8i
    \x{039a}\x{03ba}\x{03f0}
 0: \x{39a}\x{3ba}\x{3f0}

/\x{03f0}+/8i
    \x{039a}\x{03ba}\x{03f0}
 0: \x{39a}\x{3ba}\x{3f0}
    

/\x{03a0}+/8i
    \x{03a0}\x{03c0}\x{03d6} 
 0: \x{3a0}\x{3c0}\x{3d6}

/\x{03c0}+/8i
    \x{03a0}\x{03c0}\x{03d6} 
 0: \x{3a0}\x{3c0}\x{3d6}

/\x{03d6}+/8i
    \x{03a0}\x{03c0}\x{03d6} 
 0: \x{3a0}\x{3c0}\x{3d6}


/\x{03a1}+/8i
    \x{03a1}\x{03c1}\x{03f1}
 0: \x{3a1}\x{3c1}\x{3f1}

/\x{03c1}+/8i
    \x{03a1}\x{03c1}\x{03f1}
 0: \x{3a1}\x{3c1}\x{3f1}

/\x{03f1}+/8i
    \x{03a1}\x{03c1}\x{03f1}
 0: \x{3a1}\x{3c1}\x{3f1}


/\x{03a3}+/8i
    \x{03A3}\x{03C2}\x{03C3}
 0: \x{3a3}\x{3c2}\x{3c3}

/\x{03c2}+/8i
    \x{03A3}\x{03C2}\x{03C3}
 0: \x{3a3}\x{3c2}\x{3c3}

/\x{03c3}+/8i
    \x{03A3}\x{03C2}\x{03C3}
 0: \x{3a3}\x{3c2}\x{3c3}
    

/\x{03a6}+/8i
    \x{03a6}\x{03c6}\x{03d5} 
 0: \x{3a6}\x{3c6}\x{3d5}

/\x{03c6}+/8i
    \x{03a6}\x{03c6}\x{03d5} 
 0: \x{3a6}\x{3c6}\x{3d5}

/\x{03d5}+/8i
    \x{03a6}\x{03c6}\x{03d5} 
 0: \x{3a6}\x{3c6}\x{3d5}


/\x{03c9}+/8i
    \x{03c9}\x{03a9}\x{2126}
 0: \x{3c9}\x{3a9}\x{2126}

/\x{03a9}+/8i
    \x{03c9}\x{03a9}\x{2126}
 0: \x{3c9}\x{3a9}\x{2126}

/\x{2126}+/8i
    \x{03c9}\x{03a9}\x{2126}
 0: \x{3c9}\x{3a9}\x{2126}
    

/\x{1e60}+/8i
    \x{1e60}\x{1e61}\x{1e9b}
 0: \x{1e60}\x{1e61}\x{1e9b}

/\x{1e61}+/8i
    \x{1e60}\x{1e61}\x{1e9b}
 0: \x{1e60}\x{1e61}\x{1e9b}

/\x{1e9b}+/8i
    \x{1e60}\x{1e61}\x{1e9b}
 0: \x{1e60}\x{1e61}\x{1e9b}
    

/\x{1e9e}+/8i
    \x{1e9e}\x{00df}
 0: \x{1e9e}\x{df}

/\x{00df}+/8i
    \x{1e9e}\x{00df}
 0: \x{1e9e}\x{df}
    

/\x{1f88}+/8i
    \x{1f88}\x{1f80} 
 0: \x{1f88}\x{1f80}

/\x{1f80}+/8i
    \x{1f88}\x{1f80} 
 0: \x{1f88}\x{1f80}

/\x{004b}+/8i
    \x{004b}\x{006b}\x{212a}
 0: Kk\x{212a}

/\x{006b}+/8i
    \x{004b}\x{006b}\x{212a}
 0: Kk\x{212a}

/\x{212a}+/8i
    \x{004b}\x{006b}\x{212a}
 0: Kk\x{212a}


/\x{0053}+/8i
    \x{0053}\x{0073}\x{017f}
 0: Ss\x{17f}

/\x{0073}+/8i
    \x{0053}\x{0073}\x{017f}
 0: Ss\x{17f}

/\x{017f}+/8i
    \x{0053}\x{0073}\x{017f}
 0: Ss\x{17f}

/ist/8i
    ikt
No match

/is+t/8i
    iSs\x{17f}t
 0: iSs\x{17f}t
    ikt
No match

/is+?t/8i
    ikt
No match

/is?t/8i
    ikt
No match

/is{2}t/8i
    iskt
No match

/^\p{Xuc}/8
    $abc
 0: $
    @abc
 0: @
    `abc
 0: `
    \x{1234}abc
 0: \x{1234}
    ** Failers
No match
    abc     
No match

/^\p{Xuc}+/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}\x{e000}
    ** Failers
No match
    \x{9f}
No match

/^\p{Xuc}+?/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}\x{e000}
 1: $@`\x{a0}\x{1234}
 2: $@`\x{a0}
 3: $@`
 4: $@
 5: $
    ** Failers
No match
    \x{9f}
No match

/^\p{Xuc}+?\*/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}\x{e000}*
    ** Failers
No match
    \x{9f}
No match

/^\p{Xuc}++/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}\x{e000}
    ** Failers
No match
    \x{9f}
No match

/^\p{Xuc}{3,5}/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}
    ** Failers
No match
    \x{9f}
No match

/^\p{Xuc}{3,5}?/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}
 1: $@`\x{a0}
 2: $@`
    ** Failers
No match
    \x{9f}
No match

/^[\p{Xuc}]/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $
    ** Failers
No match
    \x{9f}
No match

/^[\p{Xuc}]+/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}\x{e000}
    ** Failers
No match
    \x{9f}
No match

/^\P{Xuc}/8
    abc
 0: a
    ** Failers
 0: *
    $abc
No match
    @abc
No match
    `abc
No match
    \x{1234}abc
No match

/^[\P{Xuc}]/8
    abc
 0: a
    ** Failers
 0: *
    $abc
No match
    @abc
No match
    `abc
No match
    \x{1234}abc
No match

/^A\s+Z/8W
    A\x{2005}Z
 0: A\x{2005}Z
    A\x{85}\x{180e}\x{2005}Z
 0: A\x{85}\x{180e}\x{2005}Z

/^A[\s]+Z/8W
    A\x{2005}Z
 0: A\x{2005}Z
    A\x{85}\x{180e}\x{2005}Z
 0: A\x{85}\x{180e}\x{2005}Z

/-- End of testinput10 --/ 
