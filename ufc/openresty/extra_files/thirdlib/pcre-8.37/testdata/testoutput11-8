/-- These are a few representative patterns whose lengths and offsets are to be 
shown when the link size is 2. This is just a doublecheck test to ensure the 
sizes don't go horribly wrong when something is changed. The pattern contents 
are all themselves checked in other tests. Unicode, including property support, 
is required for these tests. --/

/((?i)b)/BM
Memory allocation (code space): 17
------------------------------------------------------------------
  0  <USER> <GROUP>
  3   7 CBra 1
  8  /i b
 10   7 Ket
 13  13 Ket
 16     End
------------------------------------------------------------------

/(?s)(.*X|^B)/BM
Memory allocation (code space): 25
------------------------------------------------------------------
  0  <USER> <GROUP>
  3   9 CBra 1
  8     AllAny*
 10     X
 12   6 Alt
 15     ^
 16     B
 18  15 Ket
 21  21 Ket
 24     End
------------------------------------------------------------------

/(?s:.*X|^B)/BM
Memory allocation (code space): 23
------------------------------------------------------------------
  0  <USER> <GROUP>
  3   7 Bra
  6     AllAny*
  8     X
 10   6 Alt
 13     ^
 14     B
 16  13 Ket
 19  19 Ket
 22     End
------------------------------------------------------------------

/^[[:alnum:]]/BM
Memory allocation (code space): 41
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     ^
  4     [0-9A-Za-z]
 37  37 Ket
 40     End
------------------------------------------------------------------

/#/IxMD
Memory allocation (code space): 7
------------------------------------------------------------------
  0   <USER> <GROUP>
  3   3 Ket
  6     End
------------------------------------------------------------------
Capturing subpattern count = 0
May match empty string
Options: extended
No first char
No need char

/a#/IxMD
Memory allocation (code space): 9
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     a
  5   5 Ket
  8     End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: extended
First char = 'a'
No need char

/x?+/BM
Memory allocation (code space): 9
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     x?+
  5   5 Ket
  8     End
------------------------------------------------------------------

/x++/BM
Memory allocation (code space): 9
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     x++
  5   5 Ket
  8     End
------------------------------------------------------------------

/x{1,3}+/BM 
Memory allocation (code space): 13
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     x
  5     x{0,2}+
  9   9 Ket
 12     End
------------------------------------------------------------------

/(x)*+/BM
Memory allocation (code space): 18
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     Braposzero
  4   7 CBraPos 1
  9     x
 11   7 KetRpos
 14  14 Ket
 17     End
------------------------------------------------------------------

/^((a+)(?U)([ab]+)(?-U)([bc]+)(\w*))/BM
Memory allocation (code space): 120
------------------------------------------------------------------
  0 <USER> <GROUP>
  3     ^
  4 109 CBra 1
  9   7 CBra 2
 14     a+
 16   7 Ket
 19  39 CBra 3
 24     [ab]+?
 58  39 Ket
 61  39 CBra 4
 66     [bc]+
100  39 Ket
103   7 CBra 5
108     \w*+
110   7 Ket
113 109 Ket
116 116 Ket
119     End
------------------------------------------------------------------

|8J\$WE\<\.rX\+ix\[d1b\!H\#\?vV0vrK\:ZH1\=2M\>iV\;\?aPhFB\<\*vW\@QW\@sO9\}cfZA\-i\'w\%hKd6gt1UJP\,15_\#QY\$M\^Mss_U\/\]\&LK9\[5vQub\^w\[KDD\<EjmhUZ\?\.akp2dF\>qmj\;2\}YWFdYx\.Ap\]hjCPTP\(n28k\+3\;o\&WXqs\/gOXdr\$\:r\'do0\;b4c\(f_Gr\=\"\\4\)\[01T7ajQJvL\$W\~mL_sS\/4h\:x\*\[ZN\=KLs\&L5zX\/\/\>it\,o\:aU\(\;Z\>pW\&T7oP\'2K\^E\:x9\'c\[\%z\-\,64JQ5AeH_G\#KijUKghQw\^\\vea3a\?kka_G\$8\#\`\*kynsxzBLru\'\]k_\[7FrVx\}\^\=\$blx\>s\-N\%j\;D\*aZDnsw\:YKZ\%Q\.Kne9\#hP\?\+b3\(SOvL\,\^\;\&u5\@\?5C5Bhb\=m\-vEh_L15Jl\]U\)0RP6\{q\%L\^_z5E\'Dw6X\b|BM
Memory allocation (code space): 826
------------------------------------------------------------------
  0 <USER> <GROUP>
  3     8J$WE<.rX+ix[d1b!H#?vV0vrK:ZH1=2M>iV;?aPhFB<*vW@QW@sO9}cfZA-i'w%hKd6gt1UJP,15_#QY$M^Mss_U/]&LK9[5vQub^w[KDD<EjmhUZ?.akp2dF>qmj;2}YWFdYx.Ap]hjCPTP(n28k+3;o&WXqs/gOXdr$:r'do0;b4c(f_Gr="\4)[01T7ajQJvL$W~mL_sS/4h:x*[ZN=KLs&L5zX//>it,o:aU(;Z>pW&T7oP'2K^E:x9'c[%z-,64JQ5AeH_G#KijUKghQw^\vea3a?kka_G$8#`*kynsxzBLru']k_[7FrVx}^=$blx>s-N%j;D*aZDnsw:YKZ%Q.Kne9#hP?+b3(SOvL,^;&u5@?5C5Bhb=m-vEh_L15Jl]U)0RP6{q%L^_z5E'Dw6X
821     \b
822 822 Ket
825     End
------------------------------------------------------------------

|\$\<\.X\+ix\[d1b\!H\#\?vV0vrK\:ZH1\=2M\>iV\;\?aPhFB\<\*vW\@QW\@sO9\}cfZA\-i\'w\%hKd6gt1UJP\,15_\#QY\$M\^Mss_U\/\]\&LK9\[5vQub\^w\[KDD\<EjmhUZ\?\.akp2dF\>qmj\;2\}YWFdYx\.Ap\]hjCPTP\(n28k\+3\;o\&WXqs\/gOXdr\$\:r\'do0\;b4c\(f_Gr\=\"\\4\)\[01T7ajQJvL\$W\~mL_sS\/4h\:x\*\[ZN\=KLs\&L5zX\/\/\>it\,o\:aU\(\;Z\>pW\&T7oP\'2K\^E\:x9\'c\[\%z\-\,64JQ5AeH_G\#KijUKghQw\^\\vea3a\?kka_G\$8\#\`\*kynsxzBLru\'\]k_\[7FrVx\}\^\=\$blx\>s\-N\%j\;D\*aZDnsw\:YKZ\%Q\.Kne9\#hP\?\+b3\(SOvL\,\^\;\&u5\@\?5C5Bhb\=m\-vEh_L15Jl\]U\)0RP6\{q\%L\^_z5E\'Dw6X\b|BM
Memory allocation (code space): 816
------------------------------------------------------------------
  0 <USER> <GROUP>
  3     $<.X+ix[d1b!H#?vV0vrK:ZH1=2M>iV;?aPhFB<*vW@QW@sO9}cfZA-i'w%hKd6gt1UJP,15_#QY$M^Mss_U/]&LK9[5vQub^w[KDD<EjmhUZ?.akp2dF>qmj;2}YWFdYx.Ap]hjCPTP(n28k+3;o&WXqs/gOXdr$:r'do0;b4c(f_Gr="\4)[01T7ajQJvL$W~mL_sS/4h:x*[ZN=KLs&L5zX//>it,o:aU(;Z>pW&T7oP'2K^E:x9'c[%z-,64JQ5AeH_G#KijUKghQw^\vea3a?kka_G$8#`*kynsxzBLru']k_[7FrVx}^=$blx>s-N%j;D*aZDnsw:YKZ%Q.Kne9#hP?+b3(SOvL,^;&u5@?5C5Bhb=m-vEh_L15Jl]U)0RP6{q%L^_z5E'Dw6X
811     \b
812 812 Ket
815     End
------------------------------------------------------------------

/(a(?1)b)/BM
Memory allocation (code space): 22
------------------------------------------------------------------
  0  <USER> <GROUP>
  3  12 CBra 1
  8     a
 10   3 Recurse
 13     b
 15  12 Ket
 18  18 Ket
 21     End
------------------------------------------------------------------

/(a(?1)+b)/BM
Memory allocation (code space): 28
------------------------------------------------------------------
  0  <USER> <GROUP>
  3  18 CBra 1
  8     a
 10   6 Once
 13   3 Recurse
 16   6 KetRmax
 19     b
 21  18 Ket
 24  24 Ket
 27     End
------------------------------------------------------------------

/a(?P<name1>b|c)d(?P<longername2>e)/BM
Memory allocation (code space): 36
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     a
  5   7 CBra 1
 10     b
 12   5 Alt
 15     c
 17  12 Ket
 20     d
 22   7 CBra 2
 27     e
 29   7 Ket
 32  32 Ket
 35     End
------------------------------------------------------------------

/(?:a(?P<c>c(?P<d>d)))(?P<a>a)/BM
Memory allocation (code space): 45
------------------------------------------------------------------
  0  <USER> <GROUP>
  3  25 Bra
  6     a
  8  17 CBra 1
 13     c
 15   7 CBra 2
 20     d
 22   7 Ket
 25  17 Ket
 28  25 Ket
 31   7 CBra 3
 36     a
 38   7 Ket
 41  41 Ket
 44     End
------------------------------------------------------------------

/(?P<a>a)...(?P=a)bbb(?P>a)d/BM
Memory allocation (code space): 38
------------------------------------------------------------------
  0  <USER> <GROUP>
  3   7 CBra 1
  8     a
 10   7 Ket
 13     Any
 14     Any
 15     Any
 16     \1
 19     bbb
 25   3 Recurse
 28     d
 30  30 Ket
 33     End
------------------------------------------------------------------

/abc(?C255)de(?C)f/BM
Memory allocation (code space): 31
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     abc
  9     Callout 255 10 1
 15     de
 19     Callout 0 16 1
 25     f
 27  27 Ket
 30     End
------------------------------------------------------------------

/abcde/CBM
Memory allocation (code space): 53
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     Callout 255 0 1
  9     a
 11     Callout 255 1 1
 17     b
 19     Callout 255 2 1
 25     c
 27     Callout 255 3 1
 33     d
 35     Callout 255 4 1
 41     e
 43     Callout 255 5 0
 49  49 Ket
 52     End
------------------------------------------------------------------

/\x{100}/8BM
Memory allocation (code space): 10
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{100}
  6   6 Ket
  9     End
------------------------------------------------------------------

/\x{1000}/8BM
Memory allocation (code space): 11
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{1000}
  7   7 Ket
 10     End
------------------------------------------------------------------

/\x{10000}/8BM
Memory allocation (code space): 12
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{10000}
  8   8 Ket
 11     End
------------------------------------------------------------------

/\x{100000}/8BM
Memory allocation (code space): 12
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{100000}
  8   8 Ket
 11     End
------------------------------------------------------------------

/\x{10ffff}/8BM
Memory allocation (code space): 12
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{10ffff}
  8   8 Ket
 11     End
------------------------------------------------------------------

/\x{110000}/8BM
Failed: character value in \x{} or \o{} is too large at offset 9

/[\x{ff}]/8BM
Memory allocation (code space): 10
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{ff}
  6   6 Ket
  9     End
------------------------------------------------------------------

/[\x{100}]/8BM
Memory allocation (code space): 10
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{100}
  6   6 Ket
  9     End
------------------------------------------------------------------

/\x80/8BM
Memory allocation (code space): 10
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{80}
  6   6 Ket
  9     End
------------------------------------------------------------------

/\xff/8BM
Memory allocation (code space): 10
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{ff}
  6   6 Ket
  9     End
------------------------------------------------------------------

/\x{0041}\x{2262}\x{0391}\x{002e}/D8M
Memory allocation (code space): 18
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     A\x{2262}\x{391}.
 14  14 Ket
 17     End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
First char = 'A'
Need char = '.'
    
/\x{D55c}\x{ad6d}\x{C5B4}/D8M 
Memory allocation (code space): 19
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     \x{d55c}\x{ad6d}\x{c5b4}
 15  15 Ket
 18     End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
First char = \x{ed}
Need char = \x{b4}

/\x{65e5}\x{672c}\x{8a9e}/D8M
Memory allocation (code space): 19
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     \x{65e5}\x{672c}\x{8a9e}
 15  15 Ket
 18     End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
First char = \x{e6}
Need char = \x{9e}

/[\x{100}]/8BM
Memory allocation (code space): 10
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{100}
  6   6 Ket
  9     End
------------------------------------------------------------------

/[Z\x{100}]/8BM
Memory allocation (code space): 47
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [Z\x{100}]
 43  43 Ket
 46     End
------------------------------------------------------------------

/^[\x{100}\E-\Q\E\x{150}]/B8M
Memory allocation (code space): 18
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     ^
  4     [\x{100}-\x{150}]
 14  14 Ket
 17     End
------------------------------------------------------------------

/^[\QĀ\E-\QŐ\E]/B8M
Memory allocation (code space): 18
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     ^
  4     [\x{100}-\x{150}]
 14  14 Ket
 17     End
------------------------------------------------------------------

/^[\QĀ\E-\QŐ\E/B8M
Failed: missing terminating ] for character class at offset 15

/[\p{L}]/BM
Memory allocation (code space): 15
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [\p{L}]
 11  11 Ket
 14     End
------------------------------------------------------------------

/[\p{^L}]/BM
Memory allocation (code space): 15
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [\P{L}]
 11  11 Ket
 14     End
------------------------------------------------------------------

/[\P{L}]/BM
Memory allocation (code space): 15
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [\P{L}]
 11  11 Ket
 14     End
------------------------------------------------------------------

/[\P{^L}]/BM
Memory allocation (code space): 15
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [\p{L}]
 11  11 Ket
 14     End
------------------------------------------------------------------

/[abc\p{L}\x{0660}]/8BM
Memory allocation (code space): 50
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [a-c\p{L}\x{660}]
 46  46 Ket
 49     End
------------------------------------------------------------------

/[\p{Nd}]/8BM
Memory allocation (code space): 15
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [\p{Nd}]
 11  11 Ket
 14     End
------------------------------------------------------------------

/[\p{Nd}+-]+/8BM
Memory allocation (code space): 48
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [+\-\p{Nd}]++
 44  44 Ket
 47     End
------------------------------------------------------------------

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8iBM
Memory allocation (code space): 25
------------------------------------------------------------------
  0  <USER> <GROUP>
  3  /i A\x{391}\x{10427}\x{ff3a}\x{1fb0}
 21  21 Ket
 24     End
------------------------------------------------------------------

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8BM
Memory allocation (code space): 25
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     A\x{391}\x{10427}\x{ff3a}\x{1fb0}
 21  21 Ket
 24     End
------------------------------------------------------------------

/[\x{105}-\x{109}]/8iBM
Memory allocation (code space): 17
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [\x{104}-\x{109}]
 13  13 Ket
 16     End
------------------------------------------------------------------

/( ( (?(1)0|) )*   )/xBM
Memory allocation (code space): 38
------------------------------------------------------------------
  0  <USER> <GROUP>
  3  28 CBra 1
  8     Brazero
  9  19 SCBra 2
 14   8 Cond
 17   1 Cond ref
 20     0
 22   3 Alt
 25  11 Ket
 28  19 KetRmax
 31  28 Ket
 34  34 Ket
 37     End
------------------------------------------------------------------

/(  (?(1)0|)*   )/xBM
Memory allocation (code space): 30
------------------------------------------------------------------
  0  <USER> <GROUP>
  3  20 CBra 1
  8     Brazero
  9   8 SCond
 12   1 Cond ref
 15     0
 17   3 Alt
 20  11 KetRmax
 23  20 Ket
 26  26 Ket
 29     End
------------------------------------------------------------------

/[a]/BM
Memory allocation (code space): 9
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     a
  5   5 Ket
  8     End
------------------------------------------------------------------

/[a]/8BM
Memory allocation (code space): 9
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     a
  5   5 Ket
  8     End
------------------------------------------------------------------

/[\xaa]/BM
Memory allocation (code space): 9
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{aa}
  5   5 Ket
  8     End
------------------------------------------------------------------

/[\xaa]/8BM
Memory allocation (code space): 10
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     \x{aa}
  6   6 Ket
  9     End
------------------------------------------------------------------

/[^a]/BM
Memory allocation (code space): 9
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     [^a]
  5   5 Ket
  8     End
------------------------------------------------------------------

/[^a]/8BM
Memory allocation (code space): 9
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     [^a]
  5   5 Ket
  8     End
------------------------------------------------------------------

/[^\xaa]/BM
Memory allocation (code space): 9
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     [^\x{aa}]
  5   5 Ket
  8     End
------------------------------------------------------------------

/[^\xaa]/8BM
Memory allocation (code space): 10
------------------------------------------------------------------
  0   <USER> <GROUP>
  3     [^\x{aa}]
  6   6 Ket
  9     End
------------------------------------------------------------------

/[^\d]/8WB
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [^\p{Nd}]
 11  11 Ket
 14     End
------------------------------------------------------------------

/[[:^alpha:][:^cntrl:]]+/8WB
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [ -~\x80-\xff\P{L}]++
 44  44 Ket
 47     End
------------------------------------------------------------------

/[[:^cntrl:][:^alpha:]]+/8WB
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [ -~\x80-\xff\P{L}]++
 44  44 Ket
 47     End
------------------------------------------------------------------

/[[:alpha:]]+/8WB
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [\p{L}]++
 12  12 Ket
 15     End
------------------------------------------------------------------

/[[:^alpha:]\S]+/8WB
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     [\P{L}\P{Xsp}]++
 15  15 Ket
 18     End
------------------------------------------------------------------

/abc(d|e)(*THEN)x(123(*THEN)4|567(b|q)(*THEN)xx)/B
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     abc
  9   7 CBra 1
 14     d
 16   5 Alt
 19     e
 21  12 Ket
 24     *THEN
 25     x
 27  14 CBra 2
 32     123
 38     *THEN
 39     4
 41  29 Alt
 44     567
 50   7 CBra 3
 55     b
 57   5 Alt
 60     q
 62  12 Ket
 65     *THEN
 66     xx
 70  43 Ket
 73  73 Ket
 76     End
------------------------------------------------------------------

/(((a\2)|(a*)\g<-1>))*a?/B
------------------------------------------------------------------
  0  <USER> <GROUP>
  3     Brazero
  4  48 SCBra 1
  9  40 Once
 12  18 CBra 2
 17  10 CBra 3
 22     a
 24     \2
 27  10 Ket
 30  16 Alt
 33   7 CBra 4
 38     a*
 40   7 Ket
 43  33 Recurse
 46  34 Ket
 49  40 Ket
 52  48 KetRmax
 55     a?+
 57  57 Ket
 60     End
------------------------------------------------------------------

/((?+1)(\1))/B
------------------------------------------------------------------
  0  <USER> <GROUP>
  3  25 Once
  6  19 CBra 1
 11  14 Recurse
 14   8 CBra 2
 19     \1
 22   8 Ket
 25  19 Ket
 28  25 Ket
 31  31 Ket
 34     End
------------------------------------------------------------------

/-- End of testinput11 --/
