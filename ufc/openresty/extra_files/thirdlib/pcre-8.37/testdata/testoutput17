/-- This set of tests is for the 16- and 32-bit library's basic (non-UTF-16 
    or -32) features that are not compatible with the 8-bit library, or which 
    give different output in 16- or 32-bit mode. --/
    
< forbid 8W 

/a\Cb/
    aXb
 0: aXb
    a\nb
 0: a\x0ab
  
/[^\x{c4}]/DZ
------------------------------------------------------------------
        Bra
        [^\x{c4}]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char
  
/\x{100}/I
Capturing subpattern count = 0
No options
First char = \x{100}
No need char

/  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*                          # optional leading comment
(?:    (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|
" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
)                    # initial word
(?:  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|
" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
)  )* # further okay, if led by a period
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  @  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*    (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                           # initial subdomain
(?:                                  #
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.                        # if led by a period...
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                     #   ...further okay
)*
# address
|                     #  or
(?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|
" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
)             # one word, optionally followed by....
(?:
[^()<>@,;:".\\\[\]\x80-\xff\000-\010\012-\037]  |  # atom and space parts, or...
\(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)       |  # comments, or...

" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
# quoted strings
)*
<  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*                     # leading <
(?:  @  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*    (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                           # initial subdomain
(?:                                  #
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.                        # if led by a period...
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                     #   ...further okay
)*

(?:  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  ,  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  @  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*    (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                           # initial subdomain
(?:                                  #
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.                        # if led by a period...
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                     #   ...further okay
)*
)* # further okay, if led by comma
:                                # closing colon
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  )? #       optional route
(?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|
" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
)                    # initial word
(?:  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|
" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
)  )* # further okay, if led by a period
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  @  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*    (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                           # initial subdomain
(?:                                  #
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.                        # if led by a period...
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                     #   ...further okay
)*
#       address spec
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  > #                  trailing >
# name and address
)  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*                       # optional trailing comment
/xSI
Capturing subpattern count = 0
Contains explicit CR or LF match
Options: extended
No first char
No need char
Subject length lower bound = 3
Starting chars: \x09 \x20 ! " # $ % & ' ( * + - / 0 1 2 3 4 5 6 7 8 
  9 = ? A B C D E F G H I J K L M N O P Q R S T U V W X Y Z ^ _ ` a b c d e 
  f g h i j k l m n o p q r s t u v w x y z { | } ~ \x7f \xff 

/[\h]/BZ
------------------------------------------------------------------
        Bra
        [\x09 \xa0\x{1680}\x{180e}\x{2000}-\x{200a}\x{202f}\x{205f}\x{3000}]
        Ket
        End
------------------------------------------------------------------
    >\x09<
 0: \x09

/[\h]+/BZ
------------------------------------------------------------------
        Bra
        [\x09 \xa0\x{1680}\x{180e}\x{2000}-\x{200a}\x{202f}\x{205f}\x{3000}]++
        Ket
        End
------------------------------------------------------------------
    >\x09\x20\xa0<
 0: \x09 \xa0

/[\v]/BZ
------------------------------------------------------------------
        Bra
        [\x0a-\x0d\x85\x{2028}-\x{2029}]
        Ket
        End
------------------------------------------------------------------

/[^\h]/BZ
------------------------------------------------------------------
        Bra
        [^\x09 \xa0\x{1680}\x{180e}\x{2000}-\x{200a}\x{202f}\x{205f}\x{3000}]
        Ket
        End
------------------------------------------------------------------

/\h+/SI
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: \x09 \x20 \xa0 \xff 
    \x{1681}\x{200b}\x{1680}\x{2000}\x{202f}\x{3000}
 0: \x{1680}\x{2000}\x{202f}\x{3000}
    \x{3001}\x{2fff}\x{200a}\xa0\x{2000}
 0: \x{200a}\xa0\x{2000}

/[\h\x{dc00}]+/BZSI
------------------------------------------------------------------
        Bra
        [\x09 \xa0\x{1680}\x{180e}\x{2000}-\x{200a}\x{202f}\x{205f}\x{3000}\x{dc00}]++
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: \x09 \x20 \xa0 \xff 
    \x{1681}\x{200b}\x{1680}\x{2000}\x{202f}\x{3000}
 0: \x{1680}\x{2000}\x{202f}\x{3000}
    \x{3001}\x{2fff}\x{200a}\xa0\x{2000}
 0: \x{200a}\xa0\x{2000}

/\H+/SI
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
No starting char list
    \x{1680}\x{180e}\x{167f}\x{1681}\x{180d}\x{180f}
 0: \x{167f}\x{1681}\x{180d}\x{180f}
    \x{2000}\x{200a}\x{1fff}\x{200b}
 0: \x{1fff}\x{200b}
    \x{202f}\x{205f}\x{202e}\x{2030}\x{205e}\x{2060}
 0: \x{202e}\x{2030}\x{205e}\x{2060}
    \xa0\x{3000}\x9f\xa1\x{2fff}\x{3001}
 0: \x9f\xa1\x{2fff}\x{3001}

/[\H\x{d800}]+/
    \x{1680}\x{180e}\x{167f}\x{1681}\x{180d}\x{180f}
 0: \x{167f}\x{1681}\x{180d}\x{180f}
    \x{2000}\x{200a}\x{1fff}\x{200b}
 0: \x{1fff}\x{200b}
    \x{202f}\x{205f}\x{202e}\x{2030}\x{205e}\x{2060}
 0: \x{202e}\x{2030}\x{205e}\x{2060}
    \xa0\x{3000}\x9f\xa1\x{2fff}\x{3001}
 0: \x9f\xa1\x{2fff}\x{3001}

/\v+/SI
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: \x0a \x0b \x0c \x0d \x85 \xff 
    \x{2027}\x{2030}\x{2028}\x{2029}
 0: \x{2028}\x{2029}
    \x09\x0e\x84\x86\x85\x0a\x0b\x0c\x0d
 0: \x85\x0a\x0b\x0c\x0d

/[\v\x{dc00}]+/BZSI
------------------------------------------------------------------
        Bra
        [\x0a-\x0d\x85\x{2028}-\x{2029}\x{dc00}]++
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: \x0a \x0b \x0c \x0d \x85 \xff 
    \x{2027}\x{2030}\x{2028}\x{2029}
 0: \x{2028}\x{2029}
    \x09\x0e\x84\x86\x85\x0a\x0b\x0c\x0d
 0: \x85\x0a\x0b\x0c\x0d

/\V+/SI
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
No starting char list
    \x{2028}\x{2029}\x{2027}\x{2030}
 0: \x{2027}\x{2030}
    \x85\x0a\x0b\x0c\x0d\x09\x0e\x84\x86
 0: \x09\x0e\x84\x86

/[\V\x{d800}]+/
    \x{2028}\x{2029}\x{2027}\x{2030}
 0: \x{2027}\x{2030}
    \x85\x0a\x0b\x0c\x0d\x09\x0e\x84\x86
 0: \x09\x0e\x84\x86

/\R+/SI<bsr_unicode>
Capturing subpattern count = 0
Options: bsr_unicode
No first char
No need char
Subject length lower bound = 1
Starting chars: \x0a \x0b \x0c \x0d \x85 \xff 
    \x{2027}\x{2030}\x{2028}\x{2029}
 0: \x{2028}\x{2029}
    \x09\x0e\x84\x86\x85\x0a\x0b\x0c\x0d
 0: \x85\x0a\x0b\x0c\x0d

/\x{d800}\x{d7ff}\x{dc00}\x{dc00}\x{dcff}\x{dd00}/I
Capturing subpattern count = 0
No options
First char = \x{d800}
Need char = \x{dd00}
    \x{d800}\x{d7ff}\x{dc00}\x{dc00}\x{dcff}\x{dd00}
 0: \x{d800}\x{d7ff}\x{dc00}\x{dc00}\x{dcff}\x{dd00}

/[^\x{80}][^\x{ff}][^\x{100}][^\x{1000}][^\x{ffff}]/BZ
------------------------------------------------------------------
        Bra
        [^\x80]
        [^\x{ff}]
        [^\x{100}]
        [^\x{1000}]
        [^\x{ffff}]
        Ket
        End
------------------------------------------------------------------

/[^\x{80}][^\x{ff}][^\x{100}][^\x{1000}][^\x{ffff}]/BZi
------------------------------------------------------------------
        Bra
     /i [^\x80]
     /i [^\x{ff}]
     /i [^\x{100}]
     /i [^\x{1000}]
     /i [^\x{ffff}]
        Ket
        End
------------------------------------------------------------------

/[^\x{100}]*[^\x{1000}]+[^\x{ffff}]??[^\x{8000}]{4,}[^\x{7fff}]{2,9}?[^\x{100}]{5,6}+/BZ
------------------------------------------------------------------
        Bra
        [^\x{100}]*
        [^\x{1000}]+
        [^\x{ffff}]??
        [^\x{8000}]{4}
        [^\x{8000}]*
        [^\x{7fff}]{2}
        [^\x{7fff}]{0,7}?
        [^\x{100}]{5}
        [^\x{100}]?+
        Ket
        End
------------------------------------------------------------------

/[^\x{100}]*[^\x{1000}]+[^\x{ffff}]??[^\x{8000}]{4,}[^\x{7fff}]{2,9}?[^\x{100}]{5,6}+/BZi
------------------------------------------------------------------
        Bra
     /i [^\x{100}]*
     /i [^\x{1000}]+
     /i [^\x{ffff}]??
     /i [^\x{8000}]{4}
     /i [^\x{8000}]*
     /i [^\x{7fff}]{2}
     /i [^\x{7fff}]{0,7}?
     /i [^\x{100}]{5}
     /i [^\x{100}]?+
        Ket
        End
------------------------------------------------------------------

/(*:0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF)XX/K
    XX
 0: XX
MK: 0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF
     
/(*:0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDE)XX/K
    XX
 0: XX
MK: 0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDE

/\u0100/<JS>BZ
------------------------------------------------------------------
        Bra
        \x{100}
        Ket
        End
------------------------------------------------------------------

/[\u0100-\u0200]/<JS>BZ
------------------------------------------------------------------
        Bra
        [\x{100}-\x{200}]
        Ket
        End
------------------------------------------------------------------

/\ud800/<JS>BZ
------------------------------------------------------------------
        Bra
        \x{d800}
        Ket
        End
------------------------------------------------------------------

/^\x{ffff}+/i
    \x{ffff}
 0: \x{ffff}

/^\x{ffff}?/i
    \x{ffff}
 0: \x{ffff}

/^\x{ffff}*/i
    \x{ffff}
 0: \x{ffff}

/^\x{ffff}{3}/i
    \x{ffff}\x{ffff}\x{ffff}
 0: \x{ffff}\x{ffff}\x{ffff}

/^\x{ffff}{0,3}/i
    \x{ffff}
 0: \x{ffff}

/[^\x00-a]{12,}[^b-\xff]*/BZ
------------------------------------------------------------------
        Bra
        [b-\xff] (neg){12,}
        [\x00-a] (neg)*+
        Ket
        End
------------------------------------------------------------------

/[^\s]*\s* [^\W]+\W+ [^\d]*?\d0 [^\d\w]{4,6}?\w*A/BZ
------------------------------------------------------------------
        Bra
        [\x00-\x08\x0e-\x1f!-\xff] (neg)*
        \s*
         
        [0-9A-Z_a-z]++
        \W+
         
        [\x00-/:-\xff] (neg)*?
        \d
        0 
        [\x00-/:-@[-^`{-\xff] (neg){4,6}?
        \w*
        A
        Ket
        End
------------------------------------------------------------------

/a*[b-\x{200}]?a#a*[b-\x{200}]?b#[a-f]*[g-\x{200}]*#[g-\x{200}]*[a-c]*#[g-\x{200}]*[a-h]*/BZ
------------------------------------------------------------------
        Bra
        a*
        [b-\xff\x{100}-\x{200}]?+
        a#
        a*+
        [b-\xff\x{100}-\x{200}]?
        b#
        [a-f]*+
        [g-\xff\x{100}-\x{200}]*+
        #
        [g-\xff\x{100}-\x{200}]*+
        [a-c]*+
        #
        [g-\xff\x{100}-\x{200}]*
        [a-h]*+
        Ket
        End
------------------------------------------------------------------

/^[\x{1234}\x{4321}]{2,4}?/
    \x{1234}\x{1234}\x{1234}
 0: \x{1234}\x{1234}

/-- End of testinput17 --/
