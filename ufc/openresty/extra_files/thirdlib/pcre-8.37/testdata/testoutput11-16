/-- These are a few representative patterns whose lengths and offsets are to be 
shown when the link size is 2. This is just a doublecheck test to ensure the 
sizes don't go horribly wrong when something is changed. The pattern contents 
are all themselves checked in other tests. Unicode, including property support, 
is required for these tests. --/

/((?i)b)/BM
Memory allocation (code space): 24
------------------------------------------------------------------
  0   <USER> <GROUP>
  2   5 CBra 1
  5  /i b
  7   5 Ket
  9   9 Ket
 11     End
------------------------------------------------------------------

/(?s)(.*X|^B)/BM
Memory allocation (code space): 38
------------------------------------------------------------------
  0  <USER> <GROUP>
  2   7 CBra 1
  5     AllAny*
  7     X
  9   5 Alt
 11     ^
 12     B
 14  12 Ket
 16  16 Ket
 18     End
------------------------------------------------------------------

/(?s:.*X|^B)/BM
Memory allocation (code space): 36
------------------------------------------------------------------
  0  <USER> <GROUP>
  2   6 Bra
  4     AllAny*
  6     X
  8   5 Alt
 10     ^
 11     B
 13  11 Ket
 15  15 Ket
 17     End
------------------------------------------------------------------

/^[[:alnum:]]/BM
Memory allocation (code space): 46
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     ^
  3     [0-9A-Za-z]
 20  20 Ket
 22     End
------------------------------------------------------------------

/#/IxMD
Memory allocation (code space): 10
------------------------------------------------------------------
  0   <USER> <GROUP>
  2   2 Ket
  4     End
------------------------------------------------------------------
Capturing subpattern count = 0
May match empty string
Options: extended
No first char
No need char

/a#/IxMD
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     a
  4   4 Ket
  6     End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: extended
First char = 'a'
No need char

/x?+/BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     x?+
  4   4 Ket
  6     End
------------------------------------------------------------------

/x++/BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     x++
  4   4 Ket
  6     End
------------------------------------------------------------------

/x{1,3}+/BM 
Memory allocation (code space): 20
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     x
  4     x{0,2}+
  7   7 Ket
  9     End
------------------------------------------------------------------

/(x)*+/BM
Memory allocation (code space): 26
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     Braposzero
  3   5 CBraPos 1
  6     x
  8   5 KetRpos
 10  10 Ket
 12     End
------------------------------------------------------------------

/^((a+)(?U)([ab]+)(?-U)([bc]+)(\w*))/BM
Memory allocation (code space): 142
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     ^
  3  63 CBra 1
  6   5 CBra 2
  9     a+
 11   5 Ket
 13  21 CBra 3
 16     [ab]+?
 34  21 Ket
 36  21 CBra 4
 39     [bc]+
 57  21 Ket
 59   5 CBra 5
 62     \w*+
 64   5 Ket
 66  63 Ket
 68  68 Ket
 70     End
------------------------------------------------------------------

|8J\$WE\<\.rX\+ix\[d1b\!H\#\?vV0vrK\:ZH1\=2M\>iV\;\?aPhFB\<\*vW\@QW\@sO9\}cfZA\-i\'w\%hKd6gt1UJP\,15_\#QY\$M\^Mss_U\/\]\&LK9\[5vQub\^w\[KDD\<EjmhUZ\?\.akp2dF\>qmj\;2\}YWFdYx\.Ap\]hjCPTP\(n28k\+3\;o\&WXqs\/gOXdr\$\:r\'do0\;b4c\(f_Gr\=\"\\4\)\[01T7ajQJvL\$W\~mL_sS\/4h\:x\*\[ZN\=KLs\&L5zX\/\/\>it\,o\:aU\(\;Z\>pW\&T7oP\'2K\^E\:x9\'c\[\%z\-\,64JQ5AeH_G\#KijUKghQw\^\\vea3a\?kka_G\$8\#\`\*kynsxzBLru\'\]k_\[7FrVx\}\^\=\$blx\>s\-N\%j\;D\*aZDnsw\:YKZ\%Q\.Kne9\#hP\?\+b3\(SOvL\,\^\;\&u5\@\?5C5Bhb\=m\-vEh_L15Jl\]U\)0RP6\{q\%L\^_z5E\'Dw6X\b|BM
Memory allocation (code space): 1648
------------------------------------------------------------------
  0 <USER> <GROUP>
  2     8J$WE<.rX+ix[d1b!H#?vV0vrK:ZH1=2M>iV;?aPhFB<*vW@QW@sO9}cfZA-i'w%hKd6gt1UJP,15_#QY$M^Mss_U/]&LK9[5vQub^w[KDD<EjmhUZ?.akp2dF>qmj;2}YWFdYx.Ap]hjCPTP(n28k+3;o&WXqs/gOXdr$:r'do0;b4c(f_Gr="\4)[01T7ajQJvL$W~mL_sS/4h:x*[ZN=KLs&L5zX//>it,o:aU(;Z>pW&T7oP'2K^E:x9'c[%z-,64JQ5AeH_G#KijUKghQw^\vea3a?kka_G$8#`*kynsxzBLru']k_[7FrVx}^=$blx>s-N%j;D*aZDnsw:YKZ%Q.Kne9#hP?+b3(SOvL,^;&u5@?5C5Bhb=m-vEh_L15Jl]U)0RP6{q%L^_z5E'Dw6X
820     \b
821 821 Ket
823     End
------------------------------------------------------------------

|\$\<\.X\+ix\[d1b\!H\#\?vV0vrK\:ZH1\=2M\>iV\;\?aPhFB\<\*vW\@QW\@sO9\}cfZA\-i\'w\%hKd6gt1UJP\,15_\#QY\$M\^Mss_U\/\]\&LK9\[5vQub\^w\[KDD\<EjmhUZ\?\.akp2dF\>qmj\;2\}YWFdYx\.Ap\]hjCPTP\(n28k\+3\;o\&WXqs\/gOXdr\$\:r\'do0\;b4c\(f_Gr\=\"\\4\)\[01T7ajQJvL\$W\~mL_sS\/4h\:x\*\[ZN\=KLs\&L5zX\/\/\>it\,o\:aU\(\;Z\>pW\&T7oP\'2K\^E\:x9\'c\[\%z\-\,64JQ5AeH_G\#KijUKghQw\^\\vea3a\?kka_G\$8\#\`\*kynsxzBLru\'\]k_\[7FrVx\}\^\=\$blx\>s\-N\%j\;D\*aZDnsw\:YKZ\%Q\.Kne9\#hP\?\+b3\(SOvL\,\^\;\&u5\@\?5C5Bhb\=m\-vEh_L15Jl\]U\)0RP6\{q\%L\^_z5E\'Dw6X\b|BM
Memory allocation (code space): 1628
------------------------------------------------------------------
  0 <USER> <GROUP>
  2     $<.X+ix[d1b!H#?vV0vrK:ZH1=2M>iV;?aPhFB<*vW@QW@sO9}cfZA-i'w%hKd6gt1UJP,15_#QY$M^Mss_U/]&LK9[5vQub^w[KDD<EjmhUZ?.akp2dF>qmj;2}YWFdYx.Ap]hjCPTP(n28k+3;o&WXqs/gOXdr$:r'do0;b4c(f_Gr="\4)[01T7ajQJvL$W~mL_sS/4h:x*[ZN=KLs&L5zX//>it,o:aU(;Z>pW&T7oP'2K^E:x9'c[%z-,64JQ5AeH_G#KijUKghQw^\vea3a?kka_G$8#`*kynsxzBLru']k_[7FrVx}^=$blx>s-N%j;D*aZDnsw:YKZ%Q.Kne9#hP?+b3(SOvL,^;&u5@?5C5Bhb=m-vEh_L15Jl]U)0RP6{q%L^_z5E'Dw6X
810     \b
811 811 Ket
813     End
------------------------------------------------------------------

/(a(?1)b)/BM
Memory allocation (code space): 32
------------------------------------------------------------------
  0  <USER> <GROUP>
  2   9 CBra 1
  5     a
  7   2 Recurse
  9     b
 11   9 Ket
 13  13 Ket
 15     End
------------------------------------------------------------------

/(a(?1)+b)/BM
Memory allocation (code space): 40
------------------------------------------------------------------
  0  <USER> <GROUP>
  2  13 CBra 1
  5     a
  7   4 Once
  9   2 Recurse
 11   4 KetRmax
 13     b
 15  13 Ket
 17  17 Ket
 19     End
------------------------------------------------------------------

/a(?P<name1>b|c)d(?P<longername2>e)/BM
Memory allocation (code space): 80
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     a
  4   5 CBra 1
  7     b
  9   4 Alt
 11     c
 13   9 Ket
 15     d
 17   5 CBra 2
 20     e
 22   5 Ket
 24  24 Ket
 26     End
------------------------------------------------------------------

/(?:a(?P<c>c(?P<d>d)))(?P<a>a)/BM
Memory allocation (code space): 73
------------------------------------------------------------------
  0  <USER> <GROUP>
  2  18 Bra
  4     a
  6  12 CBra 1
  9     c
 11   5 CBra 2
 14     d
 16   5 Ket
 18  12 Ket
 20  18 Ket
 22   5 CBra 3
 25     a
 27   5 Ket
 29  29 Ket
 31     End
------------------------------------------------------------------

/(?P<a>a)...(?P=a)bbb(?P>a)d/BM
Memory allocation (code space): 61
------------------------------------------------------------------
  0  <USER> <GROUP>
  2   5 CBra 1
  5     a
  7   5 Ket
  9     Any
 10     Any
 11     Any
 12     \1
 14     bbb
 20   2 Recurse
 22     d
 24  24 Ket
 26     End
------------------------------------------------------------------

/abc(?C255)de(?C)f/BM
Memory allocation (code space): 50
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     abc
  8     Callout 255 10 1
 12     de
 16     Callout 0 16 1
 20     f
 22  22 Ket
 24     End
------------------------------------------------------------------

/abcde/CBM
Memory allocation (code space): 78
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     Callout 255 0 1
  6     a
  8     Callout 255 1 1
 12     b
 14     Callout 255 2 1
 18     c
 20     Callout 255 3 1
 24     d
 26     Callout 255 4 1
 30     e
 32     Callout 255 5 0
 36  36 Ket
 38     End
------------------------------------------------------------------

/\x{100}/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{100}
  4   4 Ket
  6     End
------------------------------------------------------------------

/\x{1000}/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{1000}
  4   4 Ket
  6     End
------------------------------------------------------------------

/\x{10000}/8BM
Memory allocation (code space): 16
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{10000}
  5   5 Ket
  7     End
------------------------------------------------------------------

/\x{100000}/8BM
Memory allocation (code space): 16
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{100000}
  5   5 Ket
  7     End
------------------------------------------------------------------

/\x{10ffff}/8BM
Memory allocation (code space): 16
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{10ffff}
  5   5 Ket
  7     End
------------------------------------------------------------------

/\x{110000}/8BM
Failed: character value in \x{} or \o{} is too large at offset 9

/[\x{ff}]/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{ff}
  4   4 Ket
  6     End
------------------------------------------------------------------

/[\x{100}]/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{100}
  4   4 Ket
  6     End
------------------------------------------------------------------

/\x80/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x80
  4   4 Ket
  6     End
------------------------------------------------------------------

/\xff/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{ff}
  4   4 Ket
  6     End
------------------------------------------------------------------

/\x{0041}\x{2262}\x{0391}\x{002e}/D8M
Memory allocation (code space): 26
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     A\x{2262}\x{391}.
 10  10 Ket
 12     End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
First char = 'A'
Need char = '.'
    
/\x{D55c}\x{ad6d}\x{C5B4}/D8M 
Memory allocation (code space): 22
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{d55c}\x{ad6d}\x{c5b4}
  8   8 Ket
 10     End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
First char = \x{d55c}
Need char = \x{c5b4}

/\x{65e5}\x{672c}\x{8a9e}/D8M
Memory allocation (code space): 22
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{65e5}\x{672c}\x{8a9e}
  8   8 Ket
 10     End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
First char = \x{65e5}
Need char = \x{8a9e}

/[\x{100}]/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{100}
  4   4 Ket
  6     End
------------------------------------------------------------------

/[Z\x{100}]/8BM
Memory allocation (code space): 54
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     [Z\x{100}]
 24  24 Ket
 26     End
------------------------------------------------------------------

/^[\x{100}\E-\Q\E\x{150}]/B8M
Memory allocation (code space): 26
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     ^
  3     [\x{100}-\x{150}]
 10  10 Ket
 12     End
------------------------------------------------------------------

/^[\QĀ\E-\QŐ\E]/B8M
Memory allocation (code space): 26
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     ^
  3     [\x{100}-\x{150}]
 10  10 Ket
 12     End
------------------------------------------------------------------

/^[\QĀ\E-\QŐ\E/B8M
Failed: missing terminating ] for character class at offset 13

/[\p{L}]/BM
Memory allocation (code space): 24
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [\p{L}]
  9   9 Ket
 11     End
------------------------------------------------------------------

/[\p{^L}]/BM
Memory allocation (code space): 24
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [\P{L}]
  9   9 Ket
 11     End
------------------------------------------------------------------

/[\P{L}]/BM
Memory allocation (code space): 24
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [\P{L}]
  9   9 Ket
 11     End
------------------------------------------------------------------

/[\P{^L}]/BM
Memory allocation (code space): 24
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [\p{L}]
  9   9 Ket
 11     End
------------------------------------------------------------------

/[abc\p{L}\x{0660}]/8BM
Memory allocation (code space): 60
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     [a-c\p{L}\x{660}]
 27  27 Ket
 29     End
------------------------------------------------------------------

/[\p{Nd}]/8BM
Memory allocation (code space): 24
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [\p{Nd}]
  9   9 Ket
 11     End
------------------------------------------------------------------

/[\p{Nd}+-]+/8BM
Memory allocation (code space): 58
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     [+\-\p{Nd}]++
 26  26 Ket
 28     End
------------------------------------------------------------------

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8iBM
Memory allocation (code space): 32
------------------------------------------------------------------
  0  <USER> <GROUP>
  2  /i A\x{391}\x{10427}\x{ff3a}\x{1fb0}
 13  13 Ket
 15     End
------------------------------------------------------------------

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8BM
Memory allocation (code space): 32
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     A\x{391}\x{10427}\x{ff3a}\x{1fb0}
 13  13 Ket
 15     End
------------------------------------------------------------------

/[\x{105}-\x{109}]/8iBM
Memory allocation (code space): 24
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [\x{104}-\x{109}]
  9   9 Ket
 11     End
------------------------------------------------------------------

/( ( (?(1)0|) )*   )/xBM
Memory allocation (code space): 52
------------------------------------------------------------------
  0  <USER> <GROUP>
  2  19 CBra 1
  5     Brazero
  6  13 SCBra 2
  9   6 Cond
 11   1 Cond ref
 13     0
 15   2 Alt
 17   8 Ket
 19  13 KetRmax
 21  19 Ket
 23  23 Ket
 25     End
------------------------------------------------------------------

/(  (?(1)0|)*   )/xBM
Memory allocation (code space): 42
------------------------------------------------------------------
  0  <USER> <GROUP>
  2  14 CBra 1
  5     Brazero
  6   6 SCond
  8   1 Cond ref
 10     0
 12   2 Alt
 14   8 KetRmax
 16  14 Ket
 18  18 Ket
 20     End
------------------------------------------------------------------

/[a]/BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     a
  4   4 Ket
  6     End
------------------------------------------------------------------

/[a]/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     a
  4   4 Ket
  6     End
------------------------------------------------------------------

/[\xaa]/BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{aa}
  4   4 Ket
  6     End
------------------------------------------------------------------

/[\xaa]/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     \x{aa}
  4   4 Ket
  6     End
------------------------------------------------------------------

/[^a]/BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [^a]
  4   4 Ket
  6     End
------------------------------------------------------------------

/[^a]/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [^a]
  4   4 Ket
  6     End
------------------------------------------------------------------

/[^\xaa]/BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [^\x{aa}]
  4   4 Ket
  6     End
------------------------------------------------------------------

/[^\xaa]/8BM
Memory allocation (code space): 14
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [^\x{aa}]
  4   4 Ket
  6     End
------------------------------------------------------------------

/[^\d]/8WB
------------------------------------------------------------------
  0   <USER> <GROUP>
  2     [^\p{Nd}]
  9   9 Ket
 11     End
------------------------------------------------------------------

/[[:^alpha:][:^cntrl:]]+/8WB
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     [ -~\x80-\xff\P{L}]++
 26  26 Ket
 28     End
------------------------------------------------------------------

/[[:^cntrl:][:^alpha:]]+/8WB
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     [ -~\x80-\xff\P{L}]++
 26  26 Ket
 28     End
------------------------------------------------------------------

/[[:alpha:]]+/8WB
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     [\p{L}]++
 10  10 Ket
 12     End
------------------------------------------------------------------

/[[:^alpha:]\S]+/8WB
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     [\P{L}\P{Xsp}]++
 13  13 Ket
 15     End
------------------------------------------------------------------

/abc(d|e)(*THEN)x(123(*THEN)4|567(b|q)(*THEN)xx)/B
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     abc
  8   5 CBra 1
 11     d
 13   4 Alt
 15     e
 17   9 Ket
 19     *THEN
 20     x
 22  12 CBra 2
 25     123
 31     *THEN
 32     4
 34  24 Alt
 36     567
 42   5 CBra 3
 45     b
 47   4 Alt
 49     q
 51   9 Ket
 53     *THEN
 54     xx
 58  36 Ket
 60  60 Ket
 62     End
------------------------------------------------------------------

/(((a\2)|(a*)\g<-1>))*a?/B
------------------------------------------------------------------
  0  <USER> <GROUP>
  2     Brazero
  3  32 SCBra 1
  6  27 Once
  8  12 CBra 2
 11   7 CBra 3
 14     a
 16     \2
 18   7 Ket
 20  11 Alt
 22   5 CBra 4
 25     a*
 27   5 Ket
 29  22 Recurse
 31  23 Ket
 33  27 Ket
 35  32 KetRmax
 37     a?+
 39  39 Ket
 41     End
------------------------------------------------------------------

/((?+1)(\1))/B
------------------------------------------------------------------
  0  <USER> <GROUP>
  2  16 Once
  4  12 CBra 1
  7   9 Recurse
  9   5 CBra 2
 12     \1
 14   5 Ket
 16  12 Ket
 18  16 Ket
 20  20 Ket
 22     End
------------------------------------------------------------------

/-- End of testinput11 --/
