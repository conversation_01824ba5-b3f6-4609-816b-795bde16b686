/-- This set of tests is for UTF support, excluding Unicode properties. It is
    compatible with all versions of Perl >= 5.10 and both the 8-bit and 16-bit
    PCRE libraries. --/
    
< forbid 9?=ABCDEFfGILMNPTUWXZ<
   
/a.b/8
    acb
 0: acb
    a\x7fb
 0: a\x{7f}b
    a\x{100}b 
 0: a\x{100}b
    *** Failers
No match
    a\nb  
No match

/a(.{3})b/8
    a\x{4000}xyb 
 0: a\x{4000}xyb
 1: \x{4000}xy
    a\x{4000}\x7fyb 
 0: a\x{4000}\x{7f}yb
 1: \x{4000}\x{7f}y
    a\x{4000}\x{100}yb 
 0: a\x{4000}\x{100}yb
 1: \x{4000}\x{100}y
    *** Failers
No match
    a\x{4000}b 
No match
    ac\ncb 
No match

/a(.*?)(.)/
    a\xc0\x88b
 0: a\xc0
 1: 
 2: \xc0

/a(.*?)(.)/8
    a\x{100}b
 0: a\x{100}
 1: 
 2: \x{100}

/a(.*)(.)/
    a\xc0\x88b
 0: a\xc0\x88b
 1: \xc0\x88
 2: b

/a(.*)(.)/8
    a\x{100}b
 0: a\x{100}b
 1: \x{100}
 2: b

/a(.)(.)/
    a\xc0\x92bcd
 0: a\xc0\x92
 1: \xc0
 2: \x92

/a(.)(.)/8
    a\x{240}bcd
 0: a\x{240}b
 1: \x{240}
 2: b

/a(.?)(.)/
    a\xc0\x92bcd
 0: a\xc0\x92
 1: \xc0
 2: \x92

/a(.?)(.)/8
    a\x{240}bcd
 0: a\x{240}b
 1: \x{240}
 2: b

/a(.??)(.)/
    a\xc0\x92bcd
 0: a\xc0
 1: 
 2: \xc0

/a(.??)(.)/8
    a\x{240}bcd
 0: a\x{240}
 1: 
 2: \x{240}

/a(.{3})b/8
    a\x{1234}xyb 
 0: a\x{1234}xyb
 1: \x{1234}xy
    a\x{1234}\x{4321}yb 
 0: a\x{1234}\x{4321}yb
 1: \x{1234}\x{4321}y
    a\x{1234}\x{4321}\x{3412}b 
 0: a\x{1234}\x{4321}\x{3412}b
 1: \x{1234}\x{4321}\x{3412}
    *** Failers
No match
    a\x{1234}b 
No match
    ac\ncb 
No match

/a(.{3,})b/8
    a\x{1234}xyb 
 0: a\x{1234}xyb
 1: \x{1234}xy
    a\x{1234}\x{4321}yb 
 0: a\x{1234}\x{4321}yb
 1: \x{1234}\x{4321}y
    a\x{1234}\x{4321}\x{3412}b 
 0: a\x{1234}\x{4321}\x{3412}b
 1: \x{1234}\x{4321}\x{3412}
    axxxxbcdefghijb 
 0: axxxxbcdefghijb
 1: xxxxbcdefghij
    a\x{1234}\x{4321}\x{3412}\x{3421}b 
 0: a\x{1234}\x{4321}\x{3412}\x{3421}b
 1: \x{1234}\x{4321}\x{3412}\x{3421}
    *** Failers
No match
    a\x{1234}b 
No match

/a(.{3,}?)b/8
    a\x{1234}xyb 
 0: a\x{1234}xyb
 1: \x{1234}xy
    a\x{1234}\x{4321}yb 
 0: a\x{1234}\x{4321}yb
 1: \x{1234}\x{4321}y
    a\x{1234}\x{4321}\x{3412}b 
 0: a\x{1234}\x{4321}\x{3412}b
 1: \x{1234}\x{4321}\x{3412}
    axxxxbcdefghijb 
 0: axxxxb
 1: xxxx
    a\x{1234}\x{4321}\x{3412}\x{3421}b 
 0: a\x{1234}\x{4321}\x{3412}\x{3421}b
 1: \x{1234}\x{4321}\x{3412}\x{3421}
    *** Failers
No match
    a\x{1234}b 
No match

/a(.{3,5})b/8
    a\x{1234}xyb 
 0: a\x{1234}xyb
 1: \x{1234}xy
    a\x{1234}\x{4321}yb 
 0: a\x{1234}\x{4321}yb
 1: \x{1234}\x{4321}y
    a\x{1234}\x{4321}\x{3412}b 
 0: a\x{1234}\x{4321}\x{3412}b
 1: \x{1234}\x{4321}\x{3412}
    axxxxbcdefghijb 
 0: axxxxb
 1: xxxx
    a\x{1234}\x{4321}\x{3412}\x{3421}b 
 0: a\x{1234}\x{4321}\x{3412}\x{3421}b
 1: \x{1234}\x{4321}\x{3412}\x{3421}
    axbxxbcdefghijb 
 0: axbxxb
 1: xbxx
    axxxxxbcdefghijb 
 0: axxxxxb
 1: xxxxx
    *** Failers
No match
    a\x{1234}b 
No match
    axxxxxxbcdefghijb 
No match

/a(.{3,5}?)b/8
    a\x{1234}xyb 
 0: a\x{1234}xyb
 1: \x{1234}xy
    a\x{1234}\x{4321}yb 
 0: a\x{1234}\x{4321}yb
 1: \x{1234}\x{4321}y
    a\x{1234}\x{4321}\x{3412}b 
 0: a\x{1234}\x{4321}\x{3412}b
 1: \x{1234}\x{4321}\x{3412}
    axxxxbcdefghijb 
 0: axxxxb
 1: xxxx
    a\x{1234}\x{4321}\x{3412}\x{3421}b 
 0: a\x{1234}\x{4321}\x{3412}\x{3421}b
 1: \x{1234}\x{4321}\x{3412}\x{3421}
    axbxxbcdefghijb 
 0: axbxxb
 1: xbxx
    axxxxxbcdefghijb 
 0: axxxxxb
 1: xxxxx
    *** Failers
No match
    a\x{1234}b 
No match
    axxxxxxbcdefghijb 
No match

/^[a\x{c0}]/8
    *** Failers
No match
    \x{100}
No match

/(?<=aXb)cd/8
    aXbcd
 0: cd

/(?<=a\x{100}b)cd/8
    a\x{100}bcd
 0: cd

/(?<=a\x{100000}b)cd/8
    a\x{100000}bcd
 0: cd
    
/(?:\x{100}){3}b/8
    \x{100}\x{100}\x{100}b
 0: \x{100}\x{100}\x{100}b
    *** Failers 
No match
    \x{100}\x{100}b
No match

/\x{ab}/8
    \x{ab} 
 0: \x{ab}
    \xc2\xab
 0: \x{ab}
    *** Failers 
No match
    \x00{ab}
No match

/(?<=(.))X/8
    WXYZ
 0: X
 1: W
    \x{256}XYZ 
 0: X
 1: \x{256}
    *** Failers
No match
    XYZ 
No match

/[^a]+/8g
    bcd
 0: bcd
    \x{100}aY\x{256}Z 
 0: \x{100}
 0: Y\x{256}Z
    
/^[^a]{2}/8
    \x{100}bc
 0: \x{100}b
 
/^[^a]{2,}/8
    \x{100}bcAa
 0: \x{100}bcA

/^[^a]{2,}?/8
    \x{100}bca
 0: \x{100}b

/[^a]+/8ig
    bcd
 0: bcd
    \x{100}aY\x{256}Z 
 0: \x{100}
 0: Y\x{256}Z
    
/^[^a]{2}/8i
    \x{100}bc
 0: \x{100}b
 
/^[^a]{2,}/8i
    \x{100}bcAa
 0: \x{100}bc

/^[^a]{2,}?/8i
    \x{100}bca
 0: \x{100}b

/\x{100}{0,0}/8
    abcd
 0: 
 
/\x{100}?/8
    abcd
 0: 
    \x{100}\x{100} 
 0: \x{100}

/\x{100}{0,3}/8 
    \x{100}\x{100} 
 0: \x{100}\x{100}
    \x{100}\x{100}\x{100}\x{100} 
 0: \x{100}\x{100}\x{100}
    
/\x{100}*/8
    abce
 0: 
    \x{100}\x{100}\x{100}\x{100} 
 0: \x{100}\x{100}\x{100}\x{100}

/\x{100}{1,1}/8
    abcd\x{100}\x{100}\x{100}\x{100} 
 0: \x{100}

/\x{100}{1,3}/8
    abcd\x{100}\x{100}\x{100}\x{100} 
 0: \x{100}\x{100}\x{100}

/\x{100}+/8
    abcd\x{100}\x{100}\x{100}\x{100} 
 0: \x{100}\x{100}\x{100}\x{100}

/\x{100}{3}/8
    abcd\x{100}\x{100}\x{100}XX
 0: \x{100}\x{100}\x{100}

/\x{100}{3,5}/8
    abcd\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}XX
 0: \x{100}\x{100}\x{100}\x{100}\x{100}

/\x{100}{3,}/8
    abcd\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}XX
 0: \x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}

/(?<=a\x{100}{2}b)X/8+
    Xyyya\x{100}\x{100}bXzzz
 0: X
 0+ zzz

/\D*/8
  aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
 0: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/\D*/8
  \x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}
 0: \x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}

/\D/8
    1X2
 0: X
    1\x{100}2 
 0: \x{100}
  
/>\S/8
    > >X Y
 0: >X
    > >\x{100} Y
 0: >\x{100}
  
/\d/8
    \x{100}3
 0: 3
    
/\s/8
    \x{100} X
 0:  
    
/\D+/8
    12abcd34
 0: abcd
    *** Failers
 0: *** Failers
    1234  
No match

/\D{2,3}/8
    12abcd34
 0: abc
    12ab34
 0: ab
    *** Failers  
 0: ***
    1234
No match
    12a34  
No match

/\D{2,3}?/8
    12abcd34
 0: ab
    12ab34
 0: ab
    *** Failers  
 0: **
    1234
No match
    12a34  
No match

/\d+/8
    12abcd34
 0: 12
    *** Failers
No match

/\d{2,3}/8
    12abcd34
 0: 12
    1234abcd
 0: 123
    *** Failers  
No match
    1.4 
No match

/\d{2,3}?/8
    12abcd34
 0: 12
    1234abcd
 0: 12
    *** Failers  
No match
    1.4 
No match

/\S+/8
    12abcd34
 0: 12abcd34
    *** Failers
 0: ***
    \    \ 
No match

/\S{2,3}/8
    12abcd34
 0: 12a
    1234abcd
 0: 123
    *** Failers
 0: ***
    \     \  
No match

/\S{2,3}?/8
    12abcd34
 0: 12
    1234abcd
 0: 12
    *** Failers
 0: **
    \     \  
No match

/>\s+</8+
    12>      <34
 0: >      <
 0+ 34
    *** Failers
No match

/>\s{2,3}</8+
    ab>  <cd
 0: >  <
 0+ cd
    ab>   <ce
 0: >   <
 0+ ce
    *** Failers
No match
    ab>    <cd 
No match

/>\s{2,3}?</8+
    ab>  <cd
 0: >  <
 0+ cd
    ab>   <ce
 0: >   <
 0+ ce
    *** Failers
No match
    ab>    <cd 
No match

/\w+/8
    12      34
 0: 12
    *** Failers
 0: Failers
    +++=*! 
No match

/\w{2,3}/8
    ab  cd
 0: ab
    abcd ce
 0: abc
    *** Failers
 0: Fai
    a.b.c
No match

/\w{2,3}?/8
    ab  cd
 0: ab
    abcd ce
 0: ab
    *** Failers
 0: Fa
    a.b.c
No match

/\W+/8
    12====34
 0: ====
    *** Failers
 0: *** 
    abcd 
No match

/\W{2,3}/8
    ab====cd
 0: ===
    ab==cd
 0: ==
    *** Failers
 0: ***
    a.b.c
No match

/\W{2,3}?/8
    ab====cd
 0: ==
    ab==cd
 0: ==
    *** Failers
 0: **
    a.b.c
No match

/[\x{100}]/8
    \x{100}
 0: \x{100}
    Z\x{100}
 0: \x{100}
    \x{100}Z
 0: \x{100}
    *** Failers 
No match

/[Z\x{100}]/8
    Z\x{100}
 0: Z
    \x{100}
 0: \x{100}
    \x{100}Z
 0: \x{100}
    *** Failers 
No match

/[\x{100}\x{200}]/8
   ab\x{100}cd
 0: \x{100}
   ab\x{200}cd
 0: \x{200}
   *** Failers  
No match

/[\x{100}-\x{200}]/8
   ab\x{100}cd
 0: \x{100}
   ab\x{200}cd
 0: \x{200}
   ab\x{111}cd 
 0: \x{111}
   *** Failers  
No match

/[z-\x{200}]/8
   ab\x{100}cd
 0: \x{100}
   ab\x{200}cd
 0: \x{200}
   ab\x{111}cd 
 0: \x{111}
   abzcd
 0: z
   ab|cd  
 0: |
   *** Failers  
No match

/[Q\x{100}\x{200}]/8
   ab\x{100}cd
 0: \x{100}
   ab\x{200}cd
 0: \x{200}
   Q? 
 0: Q
   *** Failers  
No match

/[Q\x{100}-\x{200}]/8
   ab\x{100}cd
 0: \x{100}
   ab\x{200}cd
 0: \x{200}
   ab\x{111}cd 
 0: \x{111}
   Q? 
 0: Q
   *** Failers  
No match

/[Qz-\x{200}]/8
   ab\x{100}cd
 0: \x{100}
   ab\x{200}cd
 0: \x{200}
   ab\x{111}cd 
 0: \x{111}
   abzcd
 0: z
   ab|cd  
 0: |
   Q? 
 0: Q
   *** Failers  
No match

/[\x{100}\x{200}]{1,3}/8
   ab\x{100}cd
 0: \x{100}
   ab\x{200}cd
 0: \x{200}
   ab\x{200}\x{100}\x{200}\x{100}cd
 0: \x{200}\x{100}\x{200}
   *** Failers  
No match

/[\x{100}\x{200}]{1,3}?/8
   ab\x{100}cd
 0: \x{100}
   ab\x{200}cd
 0: \x{200}
   ab\x{200}\x{100}\x{200}\x{100}cd
 0: \x{200}
   *** Failers  
No match

/[Q\x{100}\x{200}]{1,3}/8
   ab\x{100}cd
 0: \x{100}
   ab\x{200}cd
 0: \x{200}
   ab\x{200}\x{100}\x{200}\x{100}cd
 0: \x{200}\x{100}\x{200}
   *** Failers  
No match

/[Q\x{100}\x{200}]{1,3}?/8
   ab\x{100}cd
 0: \x{100}
   ab\x{200}cd
 0: \x{200}
   ab\x{200}\x{100}\x{200}\x{100}cd
 0: \x{200}
   *** Failers  
No match

/(?<=[\x{100}\x{200}])X/8
    abc\x{200}X
 0: X
    abc\x{100}X 
 0: X
    *** Failers
No match
    X  
No match

/(?<=[Q\x{100}\x{200}])X/8
    abc\x{200}X
 0: X
    abc\x{100}X 
 0: X
    abQX 
 0: X
    *** Failers
No match
    X  
No match

/(?<=[\x{100}\x{200}]{3})X/8
    abc\x{100}\x{200}\x{100}X
 0: X
    *** Failers
No match
    abc\x{200}X
No match
    X  
No match

/[^\x{100}\x{200}]X/8
    AX
 0: AX
    \x{150}X
 0: \x{150}X
    \x{500}X 
 0: \x{500}X
    *** Failers
No match
    \x{100}X
No match
    \x{200}X   
No match

/[^Q\x{100}\x{200}]X/8
    AX
 0: AX
    \x{150}X
 0: \x{150}X
    \x{500}X 
 0: \x{500}X
    *** Failers
No match
    \x{100}X
No match
    \x{200}X   
No match
    QX 
No match

/[^\x{100}-\x{200}]X/8
    AX
 0: AX
    \x{500}X 
 0: \x{500}X
    *** Failers
No match
    \x{100}X
No match
    \x{150}X
No match
    \x{200}X   
No match

/[z-\x{100}]/8i
    z
 0: z
    Z 
 0: Z
    \x{100}
 0: \x{100}
    *** Failers
No match
    \x{102}
No match
    y    
No match

/[\xFF]/
    >\xff<
 0: \xff

/[\xff]/8
    >\x{ff}<
 0: \x{ff}

/[^\xFF]/
    XYZ
 0: X

/[^\xff]/8
    XYZ
 0: X
    \x{123} 
 0: \x{123}

/^[ac]*b/8
  xb
No match

/^[ac\x{100}]*b/8
  xb
No match

/^[^x]*b/8i
  xb
No match

/^[^x]*b/8
  xb
No match
  
/^\d*b/8
  xb 
No match

/(|a)/g8
    catac
 0: 
 1: 
 0: 
 1: 
 0: a
 1: a
 0: 
 1: 
 0: 
 1: 
 0: a
 1: a
 0: 
 1: 
 0: 
 1: 
    a\x{256}a 
 0: 
 1: 
 0: a
 1: a
 0: 
 1: 
 0: 
 1: 
 0: a
 1: a
 0: 
 1: 

/^\x{85}$/8i
    \x{85}
 0: \x{85}

/^ሴ/8
    ሴ 
 0: \x{1234}

/^\ሴ/8
    ሴ 
 0: \x{1234}

"(?s)(.{1,5})"8
    abcdefg
 0: abcde
 1: abcde
    ab
 0: ab
 1: ab

/a*\x{100}*\w/8
    a 
 0: a

/\S\S/8g
    A\x{a3}BC
 0: A\x{a3}
 0: BC
    
/\S{2}/8g
    A\x{a3}BC
 0: A\x{a3}
 0: BC
    
/\W\W/8g
    +\x{a3}== 
 0: +\x{a3}
 0: ==

/\W{2}/8g
    +\x{a3}== 
 0: +\x{a3}
 0: ==

/\S/8g
    \x{442}\x{435}\x{441}\x{442}
 0: \x{442}
 0: \x{435}
 0: \x{441}
 0: \x{442}

/[\S]/8g
    \x{442}\x{435}\x{441}\x{442}
 0: \x{442}
 0: \x{435}
 0: \x{441}
 0: \x{442}

/\D/8g
    \x{442}\x{435}\x{441}\x{442}
 0: \x{442}
 0: \x{435}
 0: \x{441}
 0: \x{442}

/[\D]/8g
    \x{442}\x{435}\x{441}\x{442}
 0: \x{442}
 0: \x{435}
 0: \x{441}
 0: \x{442}

/\W/8g
    \x{2442}\x{2435}\x{2441}\x{2442}
 0: \x{2442}
 0: \x{2435}
 0: \x{2441}
 0: \x{2442}

/[\W]/8g
    \x{2442}\x{2435}\x{2441}\x{2442}
 0: \x{2442}
 0: \x{2435}
 0: \x{2441}
 0: \x{2442}
    
/[\S\s]*/8
    abc\n\r\x{442}\x{435}\x{441}\x{442}xyz 
 0: abc\x{0a}\x{0d}\x{442}\x{435}\x{441}\x{442}xyz

/[\x{41f}\S]/8g
    \x{442}\x{435}\x{441}\x{442}
 0: \x{442}
 0: \x{435}
 0: \x{441}
 0: \x{442}

/.[^\S]./8g
    abc def\x{442}\x{443}xyz\npqr
 0: c d
 0: z\x{0a}p

/.[^\S\n]./8g
    abc def\x{442}\x{443}xyz\npqr
 0: c d

/[[:^alnum:]]/8g  
    +\x{2442}
 0: +
 0: \x{2442}
    
/[[:^alpha:]]/8g 
    +\x{2442}
 0: +
 0: \x{2442}
    
/[[:^ascii:]]/8g 
    A\x{442}
 0: \x{442}
    
/[[:^blank:]]/8g 
    A\x{442}
 0: A
 0: \x{442}
    
/[[:^cntrl:]]/8g 
    A\x{442}
 0: A
 0: \x{442}
    
/[[:^digit:]]/8g 
    A\x{442}
 0: A
 0: \x{442}
    
/[[:^graph:]]/8g 
    \x19\x{e01ff}
 0: \x{19}
 0: \x{e01ff}
    
/[[:^lower:]]/8g 
    A\x{422}
 0: A
 0: \x{422}
    
/[[:^print:]]/8g 
    \x{19}\x{e01ff}
 0: \x{19}
 0: \x{e01ff}
    
/[[:^punct:]]/8g 
    A\x{442}
 0: A
 0: \x{442}
    
/[[:^space:]]/8g 
    A\x{442}
 0: A
 0: \x{442}
    
/[[:^upper:]]/8g 
    a\x{442}
 0: a
 0: \x{442}
    
/[[:^word:]]/8g  
    +\x{2442}
 0: +
 0: \x{2442}
    
/[[:^xdigit:]]/8g
    M\x{442}
 0: M
 0: \x{442}

/[^ABCDEFGHIJKLMNOPQRSTUVWXYZÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞĀĂĄĆĈĊČĎĐĒĔĖĘĚĜĞĠĢĤĦĨĪĬĮİĲĴĶĹĻĽĿŁŃŅŇŊŌŎŐŒŔŖŘŚŜŞŠŢŤŦŨŪŬŮŰŲŴŶŸŹŻŽƁƂƄƆƇƉƊƋƎƏƐƑƓƔƖƗƘƜƝƟƠƢƤƦƧƩƬƮƯƱƲƳƵƷƸƼǄǇǊǍǏǑǓǕǗǙǛǞǠǢǤǦǨǪǬǮǱǴǶǷǸǺǼǾȀȂȄȆȈȊȌȎȐȒȔȖȘȚȜȞȠȢȤȦȨȪȬȮȰȲȺȻȽȾɁΆΈΉΊΌΎΏΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩΪΫϒϓϔϘϚϜϞϠϢϤϦϨϪϬϮϴϷϹϺϽϾϿЀЁЂЃЄЅІЇЈЉЊЋЌЍЎЏАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯѠѢѤѦѨѪѬѮѰѲѴѶѸѺѼѾҀҊҌҎҐҒҔҖҘҚҜҞҠҢҤҦҨҪҬҮҰҲҴҶҸҺҼҾӀӁӃӅӇӉӋӍӐӒӔӖӘӚӜӞӠӢӤӦӨӪӬӮӰӲӴӶӸԀԂԄԆԈԊԌԎԱԲԳԴԵԶԷԸԹԺԻԼԽԾԿՀՁՂՃՄՅՆՇՈՉՊՋՌՍՎՏՐՑՒՓՔՕՖႠႡႢႣႤႥႦႧႨႩႪႫႬႭႮႯႰႱႲႳႴႵႶႷႸႹႺႻႼႽႾႿჀჁჂჃჄჅḀḂḄḆḈḊḌḎḐḒḔḖḘḚḜḞḠḢḤḦḨḪḬḮḰḲḴḶḸḺḼḾṀṂṄṆṈṊṌṎṐṒṔṖṘṚṜṞṠṢṤṦṨṪṬṮṰṲṴṶṸṺṼṾẀẂẄẆẈẊẌẎẐẒẔẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼẾỀỂỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴỶỸἈἉἊἋἌἍἎἏἘἙἚἛἜἝἨἩἪἫἬἭἮἯἸἹἺἻἼἽἾἿὈὉὊὋὌὍὙὛὝὟὨὩὪὫὬὭὮὯᾸᾹᾺΆῈΈῊΉῘῙῚΊῨῩῪΎῬῸΌῺΏabcdefghijklmnopqrstuvwxyzªµºßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿāăąćĉċčďđēĕėęěĝğġģĥħĩīĭįıĳĵķĸĺļľŀłńņňŉŋōŏőœŕŗřśŝşšţťŧũūŭůűųŵŷźżžſƀƃƅƈƌƍƒƕƙƚƛƞơƣƥƨƪƫƭưƴƶƹƺƽƾƿǆǉǌǎǐǒǔǖǘǚǜǝǟǡǣǥǧǩǫǭǯǰǳǵǹǻǽǿȁȃȅȇȉȋȍȏȑȓȕȗșțȝȟȡȣȥȧȩȫȭȯȱȳȴȵȶȷȸȹȼȿɀɐɑɒɓɔɕɖɗɘəɚɛɜɝɞɟɠɡɢɣɤɥɦɧɨɩɪɫɬɭɮɯɰɱɲɳɴɵɶɷɸɹɺɻɼɽɾɿʀʁʂʃʄʅʆʇʈʉʊʋʌʍʎʏʐʑʒʓʔʕʖʗʘʙʚʛʜʝʞʟʠʡʢʣʤʥʦʧʨʩʪʫʬʭʮʯΐάέήίΰαβγδεζηθικλμνξοπρςστυφχψωϊϋόύώϐϑϕϖϗϙϛϝϟϡϣϥϧϩϫϭϯϰϱϲϳϵϸϻϼабвгдежзийклмнопрстуфхцчшщъыьэюяѐёђѓєѕіїјљњћќѝўџѡѣѥѧѩѫѭѯѱѳѵѷѹѻѽѿҁҋҍҏґғҕҗҙқҝҟҡңҥҧҩҫҭүұҳҵҷҹһҽҿӂӄӆӈӊӌӎӑӓӕӗәӛӝӟӡӣӥӧөӫӭӯӱӳӵӷӹԁԃԅԇԉԋԍԏաբգդեզէըթժիլխծկհձղճմյնշոչպջռսվտրցւփքօֆևᴀᴁᴂᴃᴄᴅᴆᴇᴈᴉᴊᴋᴌᴍᴎᴏᴐᴑᴒᴓᴔᴕᴖᴗᴘᴙᴚᴛᴜᴝᴞᴟᴠᴡᴢᴣᴤᴥᴦᴧᴨᴩᴪᴫᵢᵣᵤᵥᵦᵧᵨᵩᵪᵫᵬᵭᵮᵯᵰᵱᵲᵳᵴᵵᵶᵷᵹᵺᵻᵼᵽᵾᵿᶀᶁᶂᶃᶄᶅᶆᶇᶈᶉᶊᶋᶌᶍᶎᶏᶐᶑᶒᶓᶔᶕᶖᶗᶘᶙᶚḁḃḅḇḉḋḍḏḑḓḕḗḙḛḝḟḡḣḥḧḩḫḭḯḱḳḵḷḹḻḽḿṁṃṅṇṉṋṍṏṑṓṕṗṙṛṝṟṡṣṥṧṩṫṭṯṱṳṵṷṹṻṽṿẁẃẅẇẉẋẍẏẑẓẕẖẗẘẙẚẛạảấầẩẫậắằẳẵặẹẻẽếềểễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹἀἁἂἃἄἅἆἇἐἑἒἓἔἕἠἡἢἣἤἥἦἧἰἱἲἳἴἵἶἷὀὁὂὃὄὅὐὑὒὓὔὕὖὗὠὡὢὣὤὥὦὧὰάὲέὴήὶίὸόὺύὼώᾀᾁᾂᾃᾄᾅᾆᾇᾐᾑᾒᾓᾔᾕᾖᾗᾠᾡᾢᾣᾤᾥᾦᾧᾰᾱᾲᾳᾴᾶᾷιῂῃῄῆῇῐῑῒΐῖῗῠῡῢΰῤῥῦῧῲῳῴῶῷⲁⲃⲅⲇⲉⲋⲍⲏⲑⲓⲕⲗⲙⲛⲝⲟⲡⲣⲥⲧⲩⲫⲭⲯⲱⲳⲵⲷⲹⲻⲽⲿⳁⳃⳅⳇⳉⳋⳍⳏⳑⳓⳕⳗⳙⳛⳝⳟⳡⳣⳤⴀⴁⴂⴃⴄⴅⴆⴇⴈⴉⴊⴋⴌⴍⴎⴏⴐⴑⴒⴓⴔⴕⴖⴗⴘⴙⴚⴛⴜⴝⴞⴟⴠⴡⴢⴣⴤⴥﬀﬁﬂﬃﬄﬅﬆﬓﬔﬕﬖﬗ\d-_^]/8

/^[^d]*?$/
    abc
 0: abc

/^[^d]*?$/8
    abc
 0: abc

/^[^d]*?$/i
    abc
 0: abc

/^[^d]*?$/8i
    abc
 0: abc

/(?i)[\xc3\xa9\xc3\xbd]|[\xc3\xa9\xc3\xbdA]/8

/^[a\x{c0}]b/8
    \x{c0}b
 0: \x{c0}b
    
/^([a\x{c0}]*?)aa/8
    a\x{c0}aaaa/ 
 0: a\x{c0}aa
 1: a\x{c0}

/^([a\x{c0}]*?)aa/8
    a\x{c0}aaaa/ 
 0: a\x{c0}aa
 1: a\x{c0}
    a\x{c0}a\x{c0}aaa/ 
 0: a\x{c0}a\x{c0}aa
 1: a\x{c0}a\x{c0}

/^([a\x{c0}]*)aa/8
    a\x{c0}aaaa/ 
 0: a\x{c0}aaaa
 1: a\x{c0}aa
    a\x{c0}a\x{c0}aaa/ 
 0: a\x{c0}a\x{c0}aaa
 1: a\x{c0}a\x{c0}a

/^([a\x{c0}]*)a\x{c0}/8
    a\x{c0}aaaa/ 
 0: a\x{c0}
 1: 
    a\x{c0}a\x{c0}aaa/ 
 0: a\x{c0}a\x{c0}
 1: a\x{c0}

/A*/g8
    AAB\x{123}BAA
 0: AA
 0: 
 0: 
 0: 
 0: AA
 0: 

/(abc)\1/8i
   abc
No match

/(abc)\1/8
   abc
No match

/a(*:a\x{1234}b)/8K
    abc
 0: a
MK: a\x{1234}b

/a(*:a£b)/8K 
    abc
 0: a
MK: a\x{a3}b

/-- Noncharacters --/

/./8
    \x{fffe}
 0: \x{fffe}
    \x{ffff}
 0: \x{ffff}
    \x{1fffe}
 0: \x{1fffe}
    \x{1ffff}
 0: \x{1ffff}
    \x{2fffe}
 0: \x{2fffe}
    \x{2ffff}
 0: \x{2ffff}
    \x{3fffe}
 0: \x{3fffe}
    \x{3ffff}
 0: \x{3ffff}
    \x{4fffe}
 0: \x{4fffe}
    \x{4ffff}
 0: \x{4ffff}
    \x{5fffe}
 0: \x{5fffe}
    \x{5ffff}
 0: \x{5ffff}
    \x{6fffe}
 0: \x{6fffe}
    \x{6ffff}
 0: \x{6ffff}
    \x{7fffe}
 0: \x{7fffe}
    \x{7ffff}
 0: \x{7ffff}
    \x{8fffe}
 0: \x{8fffe}
    \x{8ffff}
 0: \x{8ffff}
    \x{9fffe}
 0: \x{9fffe}
    \x{9ffff}
 0: \x{9ffff}
    \x{afffe}
 0: \x{afffe}
    \x{affff}
 0: \x{affff}
    \x{bfffe}
 0: \x{bfffe}
    \x{bffff}
 0: \x{bffff}
    \x{cfffe}
 0: \x{cfffe}
    \x{cffff}
 0: \x{cffff}
    \x{dfffe}
 0: \x{dfffe}
    \x{dffff}
 0: \x{dffff}
    \x{efffe}
 0: \x{efffe}
    \x{effff}
 0: \x{effff}
    \x{ffffe}
 0: \x{ffffe}
    \x{fffff}
 0: \x{fffff}
    \x{10fffe}
 0: \x{10fffe}
    \x{10ffff}
 0: \x{10ffff}
    \x{fdd0}
 0: \x{fdd0}
    \x{fdd1}
 0: \x{fdd1}
    \x{fdd2}
 0: \x{fdd2}
    \x{fdd3}
 0: \x{fdd3}
    \x{fdd4}
 0: \x{fdd4}
    \x{fdd5}
 0: \x{fdd5}
    \x{fdd6}
 0: \x{fdd6}
    \x{fdd7}
 0: \x{fdd7}
    \x{fdd8}
 0: \x{fdd8}
    \x{fdd9}
 0: \x{fdd9}
    \x{fdda}
 0: \x{fdda}
    \x{fddb}
 0: \x{fddb}
    \x{fddc}
 0: \x{fddc}
    \x{fddd}
 0: \x{fddd}
    \x{fdde}
 0: \x{fdde}
    \x{fddf}
 0: \x{fddf}
    \x{fde0}
 0: \x{fde0}
    \x{fde1}
 0: \x{fde1}
    \x{fde2}
 0: \x{fde2}
    \x{fde3}
 0: \x{fde3}
    \x{fde4}
 0: \x{fde4}
    \x{fde5}
 0: \x{fde5}
    \x{fde6}
 0: \x{fde6}
    \x{fde7}
 0: \x{fde7}
    \x{fde8}
 0: \x{fde8}
    \x{fde9}
 0: \x{fde9}
    \x{fdea}
 0: \x{fdea}
    \x{fdeb}
 0: \x{fdeb}
    \x{fdec}
 0: \x{fdec}
    \x{fded}
 0: \x{fded}
    \x{fdee}
 0: \x{fdee}
    \x{fdef}
 0: \x{fdef}

/^\d*\w{4}/8
    1234
 0: 1234
    123 
No match
    
/^[^b]*\w{4}/8
    aaaa
 0: aaaa
    aaa  
No match
 
/^[^b]*\w{4}/8i
    aaaa
 0: aaaa
    aaa  
No match
 
/^\x{100}*.{4}/8
    \x{100}\x{100}\x{100}\x{100}
 0: \x{100}\x{100}\x{100}\x{100}
    \x{100}\x{100}\x{100}
No match

/^\x{100}*.{4}/8i
    \x{100}\x{100}\x{100}\x{100}
 0: \x{100}\x{100}\x{100}\x{100}
    \x{100}\x{100}\x{100}
No match

/^a+[a\x{200}]/8
    aa
 0: aa

/^.\B.\B./8
    \x{10123}\x{10124}\x{10125}
 0: \x{10123}\x{10124}\x{10125}

/^#[^\x{ffff}]#[^\x{ffff}]#[^\x{ffff}]#/8
    #\x{10000}#\x{100}#\x{10ffff}#
 0: #\x{10000}#\x{100}#\x{10ffff}#

"[\S\V\H]"8

/\C(\W?ſ)'?{{/8
    \\C(\\W?ſ)'?{{
No match

/-- End of testinput4 --/
