/-- This set of tests is run only with the 8-bit library. They do not require 
    UTF-8 or Unicode property support. The file starts with all the tests of
    the POSIX interface, because that is supported only with the 8-bit library.
    --/
    
< forbid 8W 

/abc/P
    abc
 0: abc
    *** Failers
No match: POSIX code 17: match failed

/^abc|def/P
    abcdef
 0: abc
    abcdef\B
 0: def

/.*((abc)$|(def))/P
    defabc
 0: defabc
 1: abc
 2: abc
    \Zdefabc
 0: def
 1: def
 3: def

/the quick brown fox/P
    the quick brown fox
 0: the quick brown fox
    *** Failers
No match: POSIX code 17: match failed
    The Quick Brown Fox
No match: POSIX code 17: match failed

/the quick brown fox/Pi
    the quick brown fox
 0: the quick brown fox
    The Quick Brown Fox
 0: The Quick Brown Fox

/abc.def/P
    *** Failers
No match: POSIX code 17: match failed
    abc\ndef
No match: POSIX code 17: match failed

/abc$/P
    abc
 0: abc
    abc\n
 0: abc

/(abc)\2/P
Failed: POSIX code 15: bad back reference at offset 7     

/(abc\1)/P
    abc
No match: POSIX code 17: match failed

/a*(b+)(z)(z)/P
    aaaabbbbzzzz
 0: aaaabbbbzz
 1: bbbb
 2: z
 3: z
    aaaabbbbzzzz\O0
    aaaabbbbzzzz\O1
 0: aaaabbbbzz
    aaaabbbbzzzz\O2
 0: aaaabbbbzz
 1: bbbb
    aaaabbbbzzzz\O3
 0: aaaabbbbzz
 1: bbbb
 2: z
    aaaabbbbzzzz\O4
 0: aaaabbbbzz
 1: bbbb
 2: z
 3: z
    aaaabbbbzzzz\O5
 0: aaaabbbbzz
 1: bbbb
 2: z
 3: z

/ab.cd/P
    ab-cd
 0: ab-cd
    ab=cd
 0: ab=cd
    ** Failers
No match: POSIX code 17: match failed
    ab\ncd
No match: POSIX code 17: match failed

/ab.cd/Ps
    ab-cd
 0: ab-cd
    ab=cd
 0: ab=cd
    ab\ncd
 0: ab\x0acd

/a(b)c/PN
    abc
Matched with REG_NOSUB

/a(?P<name>b)c/PN
    abc
Matched with REG_NOSUB

/a?|b?/P
    abc
 0: a
    ** Failers
 0: 
    ddd\N   
No match: POSIX code 17: match failed

/\w+A/P
   CDAAAAB 
 0: CDAAAA

/\w+A/PU
   CDAAAAB 
 0: CDA
   
/\Biss\B/I+P
    Mississippi
 0: iss
 0+ issippi

/abc/\P
Failed: POSIX code 9: bad escape sequence at offset 4     

/-- End of POSIX tests --/ 

/a\Cb/
    aXb
 0: aXb
    a\nb
 0: a\x0ab
    ** Failers (too big char) 
No match
    A\x{123}B 
** Character \x{123} is greater than 255 and UTF-8 mode is not enabled.
** Truncation will probably give the wrong result.
No match
    A\o{443}B 
** Character \x{123} is greater than 255 and UTF-8 mode is not enabled.
** Truncation will probably give the wrong result.
No match
  
/\x{100}/I
Failed: character value in \x{} or \o{} is too large at offset 6

/\o{400}/I
Failed: character value in \x{} or \o{} is too large at offset 6

/  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*                          # optional leading comment
(?:    (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|
" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
)                    # initial word
(?:  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|
" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
)  )* # further okay, if led by a period
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  @  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*    (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                           # initial subdomain
(?:                                  #
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.                        # if led by a period...
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                     #   ...further okay
)*
# address
|                     #  or
(?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|
" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
)             # one word, optionally followed by....
(?:
[^()<>@,;:".\\\[\]\x80-\xff\000-\010\012-\037]  |  # atom and space parts, or...
\(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)       |  # comments, or...

" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
# quoted strings
)*
<  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*                     # leading <
(?:  @  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*    (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                           # initial subdomain
(?:                                  #
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.                        # if led by a period...
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                     #   ...further okay
)*

(?:  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  ,  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  @  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*    (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                           # initial subdomain
(?:                                  #
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.                        # if led by a period...
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                     #   ...further okay
)*
)* # further okay, if led by comma
:                                # closing colon
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  )? #       optional route
(?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|
" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
)                    # initial word
(?:  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|
" (?:                      # opening quote...
[^\\\x80-\xff\n\015"]                #   Anything except backslash and quote
|                     #    or
\\ [^\x80-\xff]           #   Escaped something (something != CR)
)* "  # closing quote
)  )* # further okay, if led by a period
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  @  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*    (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                           # initial subdomain
(?:                                  #
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  \.                        # if led by a period...
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*   (?:
[^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]+    # some number of atom characters...
(?![^(\040)<>@,;:".\\\[\]\000-\037\x80-\xff]) # ..not followed by something that could be part of an atom
|   \[                         # [
(?: [^\\\x80-\xff\n\015\[\]] |  \\ [^\x80-\xff]  )*    #    stuff
\]                        #           ]
)                     #   ...further okay
)*
#       address spec
(?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*  > #                  trailing >
# name and address
)  (?: [\040\t] |  \(
(?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  |  \( (?:  [^\\\x80-\xff\n\015()]  |  \\ [^\x80-\xff]  )* \)  )*
\)  )*                       # optional trailing comment
/xSI
Capturing subpattern count = 0
Contains explicit CR or LF match
Options: extended
No first char
No need char
Subject length lower bound = 3
Starting chars: \x09 \x20 ! " # $ % & ' ( * + - / 0 1 2 3 4 5 6 7 8 
  9 = ? A B C D E F G H I J K L M N O P Q R S T U V W X Y Z ^ _ ` a b c d e 
  f g h i j k l m n o p q r s t u v w x y z { | } ~ \x7f 

/-- Although this saved pattern was compiled with link-size=2, it does no harm
to run this test with other link sizes because it is going to generated a
"compiled in wrong mode" error as soon as it is loaded, so the link size does 
not matter. --/

<!testsaved16
Compiled pattern loaded from testsaved16
No study data
Error -28 from pcre_fullinfo(0)
Running in 8-bit mode but pattern was compiled in 16-bit mode

<!testsaved32
Compiled pattern loaded from testsaved32
No study data
Error -28 from pcre_fullinfo(0)
Running in 8-bit mode but pattern was compiled in 32-bit mode

/\h/SI
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: \x09 \x20 \xa0 

/\H/SI
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
No starting char list

/\v/SI
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: \x0a \x0b \x0c \x0d \x85 

/\V/SI
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
No starting char list

/\R/SI
Capturing subpattern count = 0
No options
No first char
No need char
Subject length lower bound = 1
Starting chars: \x0a \x0b \x0c \x0d \x85 

/[\h]/BZ
------------------------------------------------------------------
        Bra
        [\x09 \xa0]
        Ket
        End
------------------------------------------------------------------
    >\x09<
 0: \x09

/[\h]+/BZ
------------------------------------------------------------------
        Bra
        [\x09 \xa0]++
        Ket
        End
------------------------------------------------------------------
    >\x09\x20\xa0<
 0: \x09 \xa0

/[\v]/BZ
------------------------------------------------------------------
        Bra
        [\x0a-\x0d\x85]
        Ket
        End
------------------------------------------------------------------

/[\H]/BZ
------------------------------------------------------------------
        Bra
        [\x00-\x08\x0a-\x1f!-\x9f\xa1-\xff]
        Ket
        End
------------------------------------------------------------------

/[^\h]/BZ
------------------------------------------------------------------
        Bra
        [\x00-\x08\x0a-\x1f!-\x9f\xa1-\xff] (neg)
        Ket
        End
------------------------------------------------------------------

/[\V]/BZ
------------------------------------------------------------------
        Bra
        [\x00-\x09\x0e-\x84\x86-\xff]
        Ket
        End
------------------------------------------------------------------

/[\x0a\V]/BZ
------------------------------------------------------------------
        Bra
        [\x00-\x0a\x0e-\x84\x86-\xff]
        Ket
        End
------------------------------------------------------------------

/\777/I
Failed: octal value is greater than \377 in 8-bit non-UTF-8 mode at offset 3

/(*:0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF)XX/K
Failed: name is too long in (*MARK), (*PRUNE), (*SKIP), or (*THEN) at offset 259

/(*:0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDE)XX/K
    XX
 0: XX
MK: 0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDE

/\u0100/<JS>
Failed: character value in \u.... sequence is too large at offset 5

/[\u0100-\u0200]/<JS>
Failed: character value in \u.... sequence is too large at offset 6

/[^\x00-a]{12,}[^b-\xff]*/BZ
------------------------------------------------------------------
        Bra
        [b-\xff] (neg){12,}+
        [\x00-a] (neg)*+
        Ket
        End
------------------------------------------------------------------

/[^\s]*\s* [^\W]+\W+ [^\d]*?\d0 [^\d\w]{4,6}?\w*A/BZ
------------------------------------------------------------------
        Bra
        [\x00-\x08\x0e-\x1f!-\xff] (neg)*+
        \s*
         
        [0-9A-Z_a-z]++
        \W+
         
        [\x00-/:-\xff] (neg)*+
        \d
        0 
        [\x00-/:-@[-^`{-\xff] (neg){4,6}+
        \w*
        A
        Ket
        End
------------------------------------------------------------------

/-- End of testinput14 --/
