/-- These tests for Unicode property support test PCRE's API and show some of
    the compiled code. They are not Perl-compatible. --/

/[\p{L}]/DZ
------------------------------------------------------------------
        Bra
        [\p{L}]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/[\p{^L}]/DZ
------------------------------------------------------------------
        Bra
        [\P{L}]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/[\P{L}]/DZ
------------------------------------------------------------------
        Bra
        [\P{L}]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/[\P{^L}]/DZ
------------------------------------------------------------------
        Bra
        [\p{L}]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
No options
No first char
No need char

/[abc\p{L}\x{0660}]/8DZ
------------------------------------------------------------------
        Bra
        [a-c\p{L}\x{660}]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
No first char
No need char

/[\p{Nd}]/8DZ
------------------------------------------------------------------
        Bra
        [\p{Nd}]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
No first char
No need char
    1234
 0: 1

/[\p{Nd}+-]+/8DZ
------------------------------------------------------------------
        Bra
        [+\-\p{Nd}]++
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: utf
No first char
No need char
    1234
 0: 1234
    12-34
 0: 12-34
    12+\x{661}-34  
 0: 12+\x{661}-34
    ** Failers
No match
    abcd  
No match

/[\x{105}-\x{109}]/8iDZ
------------------------------------------------------------------
        Bra
        [\x{104}-\x{109}]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: caseless utf
No first char
No need char
    \x{104}
 0: \x{104}
    \x{105}
 0: \x{105}
    \x{109}  
 0: \x{109}
    ** Failers
No match
    \x{100}
No match
    \x{10a} 
No match
    
/[z-\x{100}]/8iDZ 
------------------------------------------------------------------
        Bra
        [Zz-\xff\x{39c}\x{3bc}\x{212b}\x{1e9e}\x{212b}\x{178}\x{100}-\x{101}]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: caseless utf
No first char
No need char
    Z
 0: Z
    z
 0: z
    \x{39c}
 0: \x{39c}
    \x{178}
 0: \x{178}
    |
 0: |
    \x{80}
 0: \x{80}
    \x{ff}
 0: \x{ff}
    \x{100}
 0: \x{100}
    \x{101} 
 0: \x{101}
    ** Failers
No match
    \x{102}
No match
    Y
No match
    y           
No match

/[z-\x{100}]/8DZi
------------------------------------------------------------------
        Bra
        [Zz-\xff\x{39c}\x{3bc}\x{212b}\x{1e9e}\x{212b}\x{178}\x{100}-\x{101}]
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: caseless utf
No first char
No need char

/(?:[\PPa*]*){8,}/

/[\P{Any}]/BZ
------------------------------------------------------------------
        Bra
        [\P{Any}]
        Ket
        End
------------------------------------------------------------------

/[\P{Any}\E]/BZ
------------------------------------------------------------------
        Bra
        [\P{Any}]
        Ket
        End
------------------------------------------------------------------

/(\P{Yi}+\277)/

/(\P{Yi}+\277)?/

/(?<=\P{Yi}{3}A)X/

/\p{Yi}+(\P{Yi}+)(?1)/

/(\P{Yi}{2}\277)?/

/[\P{Yi}A]/

/[\P{Yi}\P{Yi}\P{Yi}A]/

/[^\P{Yi}A]/

/[^\P{Yi}\P{Yi}\P{Yi}A]/

/(\P{Yi}*\277)*/

/(\P{Yi}*?\277)*/

/(\p{Yi}*+\277)*/

/(\P{Yi}?\277)*/

/(\P{Yi}??\277)*/

/(\p{Yi}?+\277)*/

/(\P{Yi}{0,3}\277)*/

/(\P{Yi}{0,3}?\277)*/

/(\p{Yi}{0,3}+\277)*/

/\p{Zl}{2,3}+/8BZ
------------------------------------------------------------------
        Bra
        prop Zl {2}
        prop Zl ?+
        Ket
        End
------------------------------------------------------------------
      
 0: \x{2028}\x{2028}
    \x{2028}\x{2028}\x{2028}
 0: \x{2028}\x{2028}\x{2028}
    
/\p{Zl}/8BZ
------------------------------------------------------------------
        Bra
        prop Zl
        Ket
        End
------------------------------------------------------------------

/\p{Lu}{3}+/8BZ
------------------------------------------------------------------
        Bra
        prop Lu {3}
        Ket
        End
------------------------------------------------------------------

/\pL{2}+/8BZ
------------------------------------------------------------------
        Bra
        prop L {2}
        Ket
        End
------------------------------------------------------------------

/\p{Cc}{2}+/8BZ
------------------------------------------------------------------
        Bra
        prop Cc {2}
        Ket
        End
------------------------------------------------------------------

/^\p{Cf}/8
    \x{180e}
 0: \x{180e}
    \x{061c}
 0: \x{61c}
    \x{2066}
 0: \x{2066}
    \x{2067}
 0: \x{2067}
    \x{2068}
 0: \x{2068}
    \x{2069}
 0: \x{2069}

/^\p{Cs}/8
    \?\x{dfff}
 0: \x{dfff}
    ** Failers
No match
    \x{09f} 
No match
  
/^\p{Mn}/8
    \x{1a1b}
 0: \x{1a1b}

/^\p{Pe}/8
    \x{2309}
 0: \x{2309}
    \x{230b}
 0: \x{230b}

/^\p{Ps}/8
    \x{2308}
 0: \x{2308}
    \x{230a}
 0: \x{230a}

/^\p{Sc}+/8
    $\x{a2}\x{a3}\x{a4}\x{a5}\x{a6}
 0: $\x{a2}\x{a3}\x{a4}\x{a5}
    \x{9f2}
 0: \x{9f2}
    ** Failers
No match
    X
No match
    \x{2c2}
No match
  
/^\p{Zs}/8
    \ \
 0:  
    \x{a0}
 0: \x{a0}
    \x{1680}
 0: \x{1680}
    \x{2000}
 0: \x{2000}
    \x{2001}     
 0: \x{2001}
    ** Failers
No match
    \x{2028}
No match
    \x{200d} 
No match
  
/-- These are here rather than in test 6 because Perl has problems with
    the negative versions of the properties and behaves has changed how
    it behaves for caseless matching. --/
      
/\p{^Lu}/8i
    1234
 0: 1
    ** Failers
 0: *
    ABC 
No match

/\P{Lu}/8i
    1234
 0: 1
    ** Failers
 0: *
    ABC 
No match

/\p{Ll}/8i 
    a
 0: a
    Az
 0: z
    ** Failers
 0: a
    ABC   
No match

/\p{Lu}/8i
    A
 0: A
    a\x{10a0}B 
 0: \x{10a0}
    ** Failers 
 0: F
    a
No match
    \x{1d00}  
No match

/\p{Lu}/8i
    A
 0: A
    aZ
 0: Z
    ** Failers
 0: F
    abc   
No match

/[\x{c0}\x{391}]/8i
    \x{c0}
 0: \x{c0}
    \x{e0} 
 0: \x{e0}

/-- The next two are special cases where the lengths of the different cases of
the same character differ. The first went wrong with heap frame storage; the
second was broken in all cases. --/

/^\x{023a}+?(\x{0130}+)/8i
  \x{023a}\x{2c65}\x{0130}
 0: \x{23a}\x{2c65}\x{130}
 1: \x{130}
  
/^\x{023a}+([^X])/8i
  \x{023a}\x{2c65}X
 0: \x{23a}\x{2c65}
 1: \x{2c65}

/\x{c0}+\x{116}+/8i
    \x{c0}\x{e0}\x{116}\x{117}
 0: \x{c0}\x{e0}\x{116}\x{117}

/[\x{c0}\x{116}]+/8i
    \x{c0}\x{e0}\x{116}\x{117}
 0: \x{c0}\x{e0}\x{116}\x{117}

/(\x{de})\1/8i
    \x{de}\x{de}
 0: \x{de}\x{de}
 1: \x{de}
    \x{de}\x{fe}
 0: \x{de}\x{fe}
 1: \x{de}
    \x{fe}\x{fe}
 0: \x{fe}\x{fe}
 1: \x{fe}
    \x{fe}\x{de}
 0: \x{fe}\x{de}
 1: \x{fe}

/^\x{c0}$/8i
    \x{c0}
 0: \x{c0}
    \x{e0} 
 0: \x{e0}

/^\x{e0}$/8i
    \x{c0}
 0: \x{c0}
    \x{e0} 
 0: \x{e0}

/-- The next two should be Perl-compatible, but it fails to match \x{e0}. PCRE
will match it only with UCP support, because without that it has no notion
of case for anything other than the ASCII letters. --/ 

/((?i)[\x{c0}])/8
    \x{c0}
 0: \x{c0}
 1: \x{c0}
    \x{e0} 
 0: \x{e0}
 1: \x{e0}

/(?i:[\x{c0}])/8
    \x{c0}
 0: \x{c0}
    \x{e0} 
 0: \x{e0}

/-- These are PCRE's extra properties to help with Unicodizing \d etc. --/

/^\p{Xan}/8
    ABCD
 0: A
    1234
 0: 1
    \x{6ca}
 0: \x{6ca}
    \x{a6c}
 0: \x{a6c}
    \x{10a7}   
 0: \x{10a7}
    ** Failers
No match
    _ABC   
No match

/^\p{Xan}+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}
    ** Failers
No match
    _ABC   
No match

/^\p{Xan}+?/8
    \x{6ca}\x{a6c}\x{10a7}_
 0: \x{6ca}

/^\p{Xan}*/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}
    
/^\p{Xan}{2,9}/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}
    
/^\p{Xan}{2,9}?/8
    \x{6ca}\x{a6c}\x{10a7}_
 0: \x{6ca}\x{a6c}
    
/^[\p{Xan}]/8
    ABCD1234_
 0: A
    1234abcd_
 0: 1
    \x{6ca}
 0: \x{6ca}
    \x{a6c}
 0: \x{a6c}
    \x{10a7}   
 0: \x{10a7}
    ** Failers
No match
    _ABC   
No match
 
/^[\p{Xan}]+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}
    ** Failers
No match
    _ABC   
No match

/^>\p{Xsp}/8
    >\x{1680}\x{2028}\x{0b}
 0: >\x{1680}
    >\x{a0} 
 0: >\x{a0}
    ** Failers
No match
    \x{0b} 
No match

/^>\p{Xsp}+/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}

/^>\p{Xsp}+?/8
    >\x{1680}\x{2028}\x{0b}
 0: >\x{1680}

/^>\p{Xsp}*/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>\p{Xsp}{2,9}/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>\p{Xsp}{2,9}?/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}
    
/^>[\p{Xsp}]/8
    >\x{2028}\x{0b}
 0: >\x{2028}
 
/^>[\p{Xsp}]+/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}

/^>\p{Xps}/8
    >\x{1680}\x{2028}\x{0b}
 0: >\x{1680}
    >\x{a0} 
 0: >\x{a0}
    ** Failers
No match
    \x{0b} 
No match

/^>\p{Xps}+/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}

/^>\p{Xps}+?/8
    >\x{1680}\x{2028}\x{0b}
 0: >\x{1680}

/^>\p{Xps}*/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>\p{Xps}{2,9}/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>\p{Xps}{2,9}?/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}
    
/^>[\p{Xps}]/8
    >\x{2028}\x{0b}
 0: >\x{2028}
 
/^>[\p{Xps}]+/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
 0: > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}

/^\p{Xwd}/8
    ABCD
 0: A
    1234
 0: 1
    \x{6ca}
 0: \x{6ca}
    \x{a6c}
 0: \x{a6c}
    \x{10a7}
 0: \x{10a7}
    _ABC    
 0: _
    ** Failers
No match
    [] 
No match

/^\p{Xwd}+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}_

/^\p{Xwd}+?/8
    \x{6ca}\x{a6c}\x{10a7}_
 0: \x{6ca}

/^\p{Xwd}*/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}_
    
/^\p{Xwd}{2,9}/8
    A_B12\x{6ca}\x{a6c}\x{10a7}
 0: A_B12\x{6ca}\x{a6c}\x{10a7}
    
/^\p{Xwd}{2,9}?/8
    \x{6ca}\x{a6c}\x{10a7}_
 0: \x{6ca}\x{a6c}
    
/^[\p{Xwd}]/8
    ABCD1234_
 0: A
    1234abcd_
 0: 1
    \x{6ca}
 0: \x{6ca}
    \x{a6c}
 0: \x{a6c}
    \x{10a7}   
 0: \x{10a7}
    _ABC 
 0: _
    ** Failers
No match
    []   
No match
 
/^[\p{Xwd}]+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
 0: ABCD1234\x{6ca}\x{a6c}\x{10a7}_

/-- A check not in UTF-8 mode --/

/^[\p{Xwd}]+/
    ABCD1234_
 0: ABCD1234_
    
/-- Some negative checks --/

/^[\P{Xwd}]+/8
    !.+\x{019}\x{35a}AB
 0: !.+\x{19}\x{35a}

/^[\p{^Xwd}]+/8
    !.+\x{019}\x{35a}AB
 0: !.+\x{19}\x{35a}

/[\D]/WBZ8
------------------------------------------------------------------
        Bra
        [\P{Nd}]
        Ket
        End
------------------------------------------------------------------
    1\x{3c8}2
 0: \x{3c8}

/[\d]/WBZ8
------------------------------------------------------------------
        Bra
        [\p{Nd}]
        Ket
        End
------------------------------------------------------------------
    >\x{6f4}<
 0: \x{6f4}

/[\S]/WBZ8
------------------------------------------------------------------
        Bra
        [\P{Xsp}]
        Ket
        End
------------------------------------------------------------------
    \x{1680}\x{6f4}\x{1680}
 0: \x{6f4}

/[\s]/WBZ8
------------------------------------------------------------------
        Bra
        [\p{Xsp}]
        Ket
        End
------------------------------------------------------------------
    >\x{1680}<
 0: \x{1680}

/[\W]/WBZ8
------------------------------------------------------------------
        Bra
        [\P{Xwd}]
        Ket
        End
------------------------------------------------------------------
    A\x{1712}B
 0: \x{1712}

/[\w]/WBZ8
------------------------------------------------------------------
        Bra
        [\p{Xwd}]
        Ket
        End
------------------------------------------------------------------
    >\x{1723}<
 0: \x{1723}

/\D/WBZ8
------------------------------------------------------------------
        Bra
        notprop Nd
        Ket
        End
------------------------------------------------------------------
    1\x{3c8}2
 0: \x{3c8}

/\d/WBZ8
------------------------------------------------------------------
        Bra
        prop Nd
        Ket
        End
------------------------------------------------------------------
    >\x{6f4}<
 0: \x{6f4}

/\S/WBZ8
------------------------------------------------------------------
        Bra
        notprop Xsp
        Ket
        End
------------------------------------------------------------------
    \x{1680}\x{6f4}\x{1680}
 0: \x{6f4}

/\s/WBZ8
------------------------------------------------------------------
        Bra
        prop Xsp
        Ket
        End
------------------------------------------------------------------
    >\x{1680}>
 0: \x{1680}

/\W/WBZ8
------------------------------------------------------------------
        Bra
        notprop Xwd
        Ket
        End
------------------------------------------------------------------
    A\x{1712}B
 0: \x{1712}

/\w/WBZ8
------------------------------------------------------------------
        Bra
        prop Xwd
        Ket
        End
------------------------------------------------------------------
    >\x{1723}<
 0: \x{1723}

/[[:alpha:]]/WBZ
------------------------------------------------------------------
        Bra
        [\p{L}]
        Ket
        End
------------------------------------------------------------------

/[[:lower:]]/WBZ
------------------------------------------------------------------
        Bra
        [\p{Ll}]
        Ket
        End
------------------------------------------------------------------

/[[:upper:]]/WBZ
------------------------------------------------------------------
        Bra
        [\p{Lu}]
        Ket
        End
------------------------------------------------------------------

/[[:alnum:]]/WBZ
------------------------------------------------------------------
        Bra
        [\p{Xan}]
        Ket
        End
------------------------------------------------------------------

/[[:ascii:]]/WBZ
------------------------------------------------------------------
        Bra
        [\x00-\x7f]
        Ket
        End
------------------------------------------------------------------

/[[:cntrl:]]/WBZ
------------------------------------------------------------------
        Bra
        [\x00-\x1f\x7f]
        Ket
        End
------------------------------------------------------------------

/[[:digit:]]/WBZ
------------------------------------------------------------------
        Bra
        [\p{Nd}]
        Ket
        End
------------------------------------------------------------------

/[[:graph:]]/WBZ
------------------------------------------------------------------
        Bra
        [[:graph:]]
        Ket
        End
------------------------------------------------------------------

/[[:print:]]/WBZ
------------------------------------------------------------------
        Bra
        [[:print:]]
        Ket
        End
------------------------------------------------------------------

/[[:punct:]]/WBZ
------------------------------------------------------------------
        Bra
        [[:punct:]]
        Ket
        End
------------------------------------------------------------------

/[[:space:]]/WBZ
------------------------------------------------------------------
        Bra
        [\p{Xps}]
        Ket
        End
------------------------------------------------------------------

/[[:word:]]/WBZ
------------------------------------------------------------------
        Bra
        [\p{Xwd}]
        Ket
        End
------------------------------------------------------------------

/[[:xdigit:]]/WBZ
------------------------------------------------------------------
        Bra
        [0-9A-Fa-f]
        Ket
        End
------------------------------------------------------------------

/-- Unicode properties for \b abd \B --/

/\b...\B/8W
    abc_
 0: abc
    \x{37e}abc\x{376} 
 0: abc
    \x{37e}\x{376}\x{371}\x{393}\x{394} 
 0: \x{376}\x{371}\x{393}
    !\x{c0}++\x{c1}\x{c2} 
 0: ++\x{c1}
    !\x{c0}+++++ 
 0: \x{c0}++

/-- Without PCRE_UCP, non-ASCII always fail, even if < 256  --/

/\b...\B/8
    abc_
 0: abc
    ** Failers 
 0: Fai
    \x{37e}abc\x{376} 
No match
    \x{37e}\x{376}\x{371}\x{393}\x{394} 
No match
    !\x{c0}++\x{c1}\x{c2} 
No match
    !\x{c0}+++++ 
No match

/-- With PCRE_UCP, non-UTF8 chars that are < 256 still check properties  --/

/\b...\B/W
    abc_
 0: abc
    !\x{c0}++\x{c1}\x{c2} 
 0: ++\xc1
    !\x{c0}+++++ 
 0: \xc0++

/-- Some of these are silly, but they check various combinations --/

/[[:^alpha:][:^cntrl:]]+/8WBZ
------------------------------------------------------------------
        Bra
        [ -~\x80-\xff\P{L}]++
        Ket
        End
------------------------------------------------------------------
    123
 0: 123
    abc 
 0: abc

/[[:^cntrl:][:^alpha:]]+/8WBZ
------------------------------------------------------------------
        Bra
        [ -~\x80-\xff\P{L}]++
        Ket
        End
------------------------------------------------------------------
    123
 0: 123
    abc 
 0: abc

/[[:alpha:]]+/8WBZ
------------------------------------------------------------------
        Bra
        [\p{L}]++
        Ket
        End
------------------------------------------------------------------
    abc
 0: abc

/[[:^alpha:]\S]+/8WBZ
------------------------------------------------------------------
        Bra
        [\P{L}\P{Xsp}]++
        Ket
        End
------------------------------------------------------------------
    123
 0: 123
    abc 
 0: abc

/[^\d]+/8WBZ
------------------------------------------------------------------
        Bra
        [^\p{Nd}]++
        Ket
        End
------------------------------------------------------------------
    abc123
 0: abc
    abc\x{123}
 0: abc\x{123}
    \x{660}abc   
 0: abc

/\p{Lu}+9\p{Lu}+B\p{Lu}+b/BZ
------------------------------------------------------------------
        Bra
        prop Lu ++
        9
        prop Lu +
        B
        prop Lu ++
        b
        Ket
        End
------------------------------------------------------------------

/\p{^Lu}+9\p{^Lu}+B\p{^Lu}+b/BZ
------------------------------------------------------------------
        Bra
        notprop Lu +
        9
        notprop Lu ++
        B
        notprop Lu +
        b
        Ket
        End
------------------------------------------------------------------

/\P{Lu}+9\P{Lu}+B\P{Lu}+b/BZ
------------------------------------------------------------------
        Bra
        notprop Lu +
        9
        notprop Lu ++
        B
        notprop Lu +
        b
        Ket
        End
------------------------------------------------------------------

/\p{Han}+X\p{Greek}+\x{370}/BZ8
------------------------------------------------------------------
        Bra
        prop Han ++
        X
        prop Greek +
        \x{370}
        Ket
        End
------------------------------------------------------------------

/\p{Xan}+!\p{Xan}+A/BZ
------------------------------------------------------------------
        Bra
        prop Xan ++
        !
        prop Xan +
        A
        Ket
        End
------------------------------------------------------------------

/\p{Xsp}+!\p{Xsp}\t/BZ
------------------------------------------------------------------
        Bra
        prop Xsp ++
        !
        prop Xsp
        \x09
        Ket
        End
------------------------------------------------------------------

/\p{Xps}+!\p{Xps}\t/BZ
------------------------------------------------------------------
        Bra
        prop Xps ++
        !
        prop Xps
        \x09
        Ket
        End
------------------------------------------------------------------

/\p{Xwd}+!\p{Xwd}_/BZ
------------------------------------------------------------------
        Bra
        prop Xwd ++
        !
        prop Xwd
        _
        Ket
        End
------------------------------------------------------------------

/A+\p{N}A+\dB+\p{N}*B+\d*/WBZ
------------------------------------------------------------------
        Bra
        A++
        prop N
        A++
        prop Nd
        B+
        prop N *+
        B++
        prop Nd *+
        Ket
        End
------------------------------------------------------------------

/-- These behaved oddly in Perl, so they are kept in this test --/

/(\x{23a}\x{23a}\x{23a})?\1/8i
    \x{23a}\x{23a}\x{23a}\x{2c65}\x{2c65}
No match

/(ȺȺȺ)?\1/8i
    ȺȺȺⱥⱥ
No match

/(\x{23a}\x{23a}\x{23a})?\1/8i
    \x{23a}\x{23a}\x{23a}\x{2c65}\x{2c65}\x{2c65}
 0: \x{23a}\x{23a}\x{23a}\x{2c65}\x{2c65}\x{2c65}
 1: \x{23a}\x{23a}\x{23a}

/(ȺȺȺ)?\1/8i
    ȺȺȺⱥⱥⱥ
 0: \x{23a}\x{23a}\x{23a}\x{2c65}\x{2c65}\x{2c65}
 1: \x{23a}\x{23a}\x{23a}

/(\x{23a}\x{23a}\x{23a})\1/8i
    \x{23a}\x{23a}\x{23a}\x{2c65}\x{2c65}
No match

/(ȺȺȺ)\1/8i
    ȺȺȺⱥⱥ
No match

/(\x{23a}\x{23a}\x{23a})\1/8i
    \x{23a}\x{23a}\x{23a}\x{2c65}\x{2c65}\x{2c65}
 0: \x{23a}\x{23a}\x{23a}\x{2c65}\x{2c65}\x{2c65}
 1: \x{23a}\x{23a}\x{23a}

/(ȺȺȺ)\1/8i
    ȺȺȺⱥⱥⱥ
 0: \x{23a}\x{23a}\x{23a}\x{2c65}\x{2c65}\x{2c65}
 1: \x{23a}\x{23a}\x{23a}

/(\x{2c65}\x{2c65})\1/8i
    \x{2c65}\x{2c65}\x{23a}\x{23a}
 0: \x{2c65}\x{2c65}\x{23a}\x{23a}
 1: \x{2c65}\x{2c65}
    
/(ⱥⱥ)\1/8i
    ⱥⱥȺȺ 
 0: \x{2c65}\x{2c65}\x{23a}\x{23a}
 1: \x{2c65}\x{2c65}
    
/(\x{23a}\x{23a}\x{23a})\1Y/8i
    X\x{23a}\x{23a}\x{23a}\x{2c65}\x{2c65}\x{2c65}YZ
 0: \x{23a}\x{23a}\x{23a}\x{2c65}\x{2c65}\x{2c65}Y
 1: \x{23a}\x{23a}\x{23a}

/(\x{2c65}\x{2c65})\1Y/8i
    X\x{2c65}\x{2c65}\x{23a}\x{23a}YZ
 0: \x{2c65}\x{2c65}\x{23a}\x{23a}Y
 1: \x{2c65}\x{2c65}

/-- --/ 

/-- These scripts weren't yet in Perl when I added Unicode 6.0.0 to PCRE --/

/^[\p{Batak}]/8
    \x{1bc0}
 0: \x{1bc0}
    \x{1bff}
 0: \x{1bff}
    ** Failers
No match
    \x{1bf4}
No match
    
/^[\p{Brahmi}]/8
    \x{11000}
 0: \x{11000}
    \x{1106f}
 0: \x{1106f}
    ** Failers
No match
    \x{1104e}
No match
    
/^[\p{Mandaic}]/8
    \x{840}
 0: \x{840}
    \x{85e}
 0: \x{85e}
    ** Failers
No match
    \x{85c}
No match
    \x{85d}    
No match

/-- --/ 

/(\X*)(.)/s8
    A\x{300}
 0: A
 1: 
 2: A

/^S(\X*)e(\X*)$/8
    Stéréo
 0: Ste\x{301}re\x{301}o
 1: te\x{301}r
 2: \x{301}o
    
/^\X/8 
    ́réo
 0: \x{301}

/^a\X41z/<JS>
    aX41z
 0: aX41z
    *** Failers
No match
    aAz
No match

/(?<=ab\Cde)X/8
Failed: \C not allowed in lookbehind assertion at offset 10

/\X/
    a\P
 0: a
    a\P\P
Partial match: a

/\Xa/
    aa\P
 0: aa
    aa\P\P
 0: aa

/\X{2}/
    aa\P
 0: aa
    aa\P\P
Partial match: aa

/\X+a/
    a\P
Partial match: a
    aa\P
 0: aa
    aa\P\P
Partial match: aa

/\X+?a/
    a\P
Partial match: a
    ab\P
Partial match: ab
    aa\P
 0: aa
    aa\P\P
 0: aa
    aba\P
 0: aba
    
/-- These Unicode 6.1.0 scripts are not known to Perl. --/ 

/\p{Chakma}\d/8W
    \x{11100}\x{1113c}
 0: \x{11100}\x{1113c}

/\p{Takri}\d/8W
    \x{11680}\x{116c0}
 0: \x{11680}\x{116c0}

/^\X/8
    A\P
 0: A
    A\P\P 
Partial match: A
    A\x{300}\x{301}\P
 0: A\x{300}\x{301}
    A\x{300}\x{301}\P\P  
Partial match: A\x{300}\x{301}
    A\x{301}\P
 0: A\x{301}
    A\x{301}\P\P  
Partial match: A\x{301}
    
/^\X{2,3}/8
    A\P
Partial match: A
    A\P\P 
Partial match: A
    AA\P
 0: AA
    AA\P\P  
Partial match: AA
    A\x{300}\x{301}\P
Partial match: A\x{300}\x{301}
    A\x{300}\x{301}\P\P  
Partial match: A\x{300}\x{301}
    A\x{300}\x{301}A\x{300}\x{301}\P
 0: A\x{300}\x{301}A\x{300}\x{301}
    A\x{300}\x{301}A\x{300}\x{301}\P\P  
Partial match: A\x{300}\x{301}A\x{300}\x{301}

/^\X{2}/8
    AA\P
 0: AA
    AA\P\P  
Partial match: AA
    A\x{300}\x{301}A\x{300}\x{301}\P
 0: A\x{300}\x{301}A\x{300}\x{301}
    A\x{300}\x{301}A\x{300}\x{301}\P\P  
Partial match: A\x{300}\x{301}A\x{300}\x{301}
    
/^\X+/8
    AA\P
 0: AA
    AA\P\P  
Partial match: AA

/^\X+?Z/8
    AA\P
Partial match: AA
    AA\P\P 
Partial match: AA

/A\x{3a3}B/8iDZ
------------------------------------------------------------------
        Bra
     /i A
        clist 03a3 03c2 03c3
     /i B
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: caseless utf
First char = 'A' (caseless)
Need char = 'B' (caseless)

/\x{3a3}B/8iDZ
------------------------------------------------------------------
        Bra
        clist 03a3 03c2 03c3
     /i B
        Ket
        End
------------------------------------------------------------------
Capturing subpattern count = 0
Options: caseless utf
No first char
Need char = 'B' (caseless)

/[\x{3a3}]/8iBZ
------------------------------------------------------------------
        Bra
        clist 03a3 03c2 03c3
        Ket
        End
------------------------------------------------------------------

/[^\x{3a3}]/8iBZ
------------------------------------------------------------------
        Bra
        not clist 03a3 03c2 03c3
        Ket
        End
------------------------------------------------------------------

/[\x{3a3}]+/8iBZ
------------------------------------------------------------------
        Bra
        clist 03a3 03c2 03c3 ++
        Ket
        End
------------------------------------------------------------------

/[^\x{3a3}]+/8iBZ
------------------------------------------------------------------
        Bra
        not clist 03a3 03c2 03c3 ++
        Ket
        End
------------------------------------------------------------------

/a*\x{3a3}/8iBZ
------------------------------------------------------------------
        Bra
     /i a*+
        clist 03a3 03c2 03c3
        Ket
        End
------------------------------------------------------------------

/\x{3a3}+a/8iBZ
------------------------------------------------------------------
        Bra
        clist 03a3 03c2 03c3 ++
     /i a
        Ket
        End
------------------------------------------------------------------

/\x{3a3}*\x{3c2}/8iBZ
------------------------------------------------------------------
        Bra
        clist 03a3 03c2 03c3 *
        clist 03a3 03c2 03c3
        Ket
        End
------------------------------------------------------------------

/\x{3a3}{3}/8i+
    \x{3a3}\x{3c3}\x{3c2}\x{3a3}\x{3c3}\x{3c2}
 0: \x{3a3}\x{3c3}\x{3c2}
 0+ \x{3a3}\x{3c3}\x{3c2}

/\x{3a3}{2,4}/8i+
    \x{3a3}\x{3c3}\x{3c2}\x{3a3}\x{3c3}\x{3c2}
 0: \x{3a3}\x{3c3}\x{3c2}\x{3a3}
 0+ \x{3c3}\x{3c2}

/\x{3a3}{2,4}?/8i+
    \x{3a3}\x{3c3}\x{3c2}\x{3a3}\x{3c3}\x{3c2}
 0: \x{3a3}\x{3c3}
 0+ \x{3c2}\x{3a3}\x{3c3}\x{3c2}

/\x{3a3}+./8i+
    \x{3a3}\x{3c3}\x{3c2}\x{3a3}\x{3c3}\x{3c2}
 0: \x{3a3}\x{3c3}\x{3c2}\x{3a3}\x{3c3}\x{3c2}
 0+ 

/\x{3a3}++./8i+
    ** Failers
No match
    \x{3a3}\x{3c3}\x{3c2}\x{3a3}\x{3c3}\x{3c2}
No match

/\x{3a3}*\x{3c2}/8iBZ
------------------------------------------------------------------
        Bra
        clist 03a3 03c2 03c3 *
        clist 03a3 03c2 03c3
        Ket
        End
------------------------------------------------------------------

/[^\x{3a3}]*\x{3c2}/8iBZ
------------------------------------------------------------------
        Bra
        not clist 03a3 03c2 03c3 *+
        clist 03a3 03c2 03c3
        Ket
        End
------------------------------------------------------------------

/[^a]*\x{3c2}/8iBZ
------------------------------------------------------------------
        Bra
     /i [^a]*
        clist 03a3 03c2 03c3
        Ket
        End
------------------------------------------------------------------

/ist/8iBZ
------------------------------------------------------------------
        Bra
     /i i
        clist 0053 0073 017f
     /i t
        Ket
        End
------------------------------------------------------------------
    ikt
No match

/is+t/8i
    iSs\x{17f}t
 0: iSs\x{17f}t
    ikt
No match

/is+?t/8i
    ikt
No match

/is?t/8i
    ikt
No match

/is{2}t/8i
    iskt
No match
    
/-- This property is a PCRE special --/

/^\p{Xuc}/8
    $abc
 0: $
    @abc
 0: @
    `abc
 0: `
    \x{1234}abc
 0: \x{1234}
    ** Failers
No match
    abc     
No match

/^\p{Xuc}+/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}\x{e000}
    ** Failers
No match
    \x{9f}
No match

/^\p{Xuc}+?/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $
    ** Failers
No match
    \x{9f}
No match

/^\p{Xuc}+?\*/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}\x{e000}*
    ** Failers
No match
    \x{9f}
No match

/^\p{Xuc}++/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}\x{e000}
    ** Failers
No match
    \x{9f}
No match

/^\p{Xuc}{3,5}/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}
    ** Failers
No match
    \x{9f}
No match

/^\p{Xuc}{3,5}?/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`
    ** Failers
No match
    \x{9f}
No match

/^[\p{Xuc}]/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $
    ** Failers
No match
    \x{9f}
No match

/^[\p{Xuc}]+/8
    $@`\x{a0}\x{1234}\x{e000}**
 0: $@`\x{a0}\x{1234}\x{e000}
    ** Failers
No match
    \x{9f}
No match

/^\P{Xuc}/8
    abc
 0: a
    ** Failers
 0: *
    $abc
No match
    @abc
No match
    `abc
No match
    \x{1234}abc
No match

/^[\P{Xuc}]/8
    abc
 0: a
    ** Failers
 0: *
    $abc
No match
    @abc
No match
    `abc
No match
    \x{1234}abc
No match
    
/-- Some auto-possessification tests --/

/\pN+\z/BZ
------------------------------------------------------------------
        Bra
        prop N ++
        \z
        Ket
        End
------------------------------------------------------------------

/\PN+\z/BZ
------------------------------------------------------------------
        Bra
        notprop N ++
        \z
        Ket
        End
------------------------------------------------------------------

/\pN+/BZ
------------------------------------------------------------------
        Bra
        prop N ++
        Ket
        End
------------------------------------------------------------------

/\PN+/BZ
------------------------------------------------------------------
        Bra
        notprop N ++
        Ket
        End
------------------------------------------------------------------

/\p{Any}+\p{Any} \p{Any}+\P{Any} \p{Any}+\p{L&} \p{Any}+\p{L} \p{Any}+\p{Lu} \p{Any}+\p{Han} \p{Any}+\p{Xan} \p{Any}+\p{Xsp} \p{Any}+\p{Xps} \p{Xwd}+\p{Any} \p{Any}+\p{Xuc}/BWZx  
------------------------------------------------------------------
        Bra
        prop Any +
        prop Any
        prop Any +
        notprop Any
        prop Any +
        prop L&
        prop Any +
        prop L
        prop Any +
        prop Lu
        prop Any +
        prop Han
        prop Any +
        prop Xan
        prop Any +
        prop Xsp
        prop Any +
        prop Xps
        prop Xwd +
        prop Any
        prop Any +
        prop Xuc
        Ket
        End
------------------------------------------------------------------

/\p{L&}+\p{Any} \p{L&}+\p{L&} \P{L&}+\p{L&} \p{L&}+\p{L} \p{L&}+\p{Lu} \p{L&}+\p{Han} \p{L&}+\p{Xan} \p{L&}+\P{Xan} \p{L&}+\p{Xsp} \p{L&}+\p{Xps} \p{Xwd}+\p{L&} \p{L&}+\p{Xuc}/BWZx  
------------------------------------------------------------------
        Bra
        prop L& +
        prop Any
        prop L& +
        prop L&
        notprop L& ++
        prop L&
        prop L& +
        prop L
        prop L& +
        prop Lu
        prop L& +
        prop Han
        prop L& +
        prop Xan
        prop L& ++
        notprop Xan
        prop L& ++
        prop Xsp
        prop L& ++
        prop Xps
        prop Xwd +
        prop L&
        prop L& +
        prop Xuc
        Ket
        End
------------------------------------------------------------------

/\p{N}+\p{Any} \p{N}+\p{L&} \p{N}+\p{L} \p{N}+\P{L} \p{N}+\P{N} \p{N}+\p{Lu} \p{N}+\p{Han} \p{N}+\p{Xan} \p{N}+\p{Xsp} \p{N}+\p{Xps} \p{Xwd}+\p{N} \p{N}+\p{Xuc}/BWZx  
------------------------------------------------------------------
        Bra
        prop N +
        prop Any
        prop N +
        prop L&
        prop N ++
        prop L
        prop N +
        notprop L
        prop N ++
        notprop N
        prop N ++
        prop Lu
        prop N +
        prop Han
        prop N +
        prop Xan
        prop N ++
        prop Xsp
        prop N ++
        prop Xps
        prop Xwd +
        prop N
        prop N +
        prop Xuc
        Ket
        End
------------------------------------------------------------------

/\p{Lu}+\p{Any} \p{Lu}+\p{L&} \p{Lu}+\p{L} \p{Lu}+\p{Lu} \P{Lu}+\p{Lu} \p{Lu}+\p{Nd} \p{Lu}+\P{Nd} \p{Lu}+\p{Han} \p{Lu}+\p{Xan} \p{Lu}+\p{Xsp} \p{Lu}+\p{Xps} \p{Xwd}+\p{Lu} \p{Lu}+\p{Xuc}/BWZx  
------------------------------------------------------------------
        Bra
        prop Lu +
        prop Any
        prop Lu +
        prop L&
        prop Lu +
        prop L
        prop Lu +
        prop Lu
        notprop Lu ++
        prop Lu
        prop Lu ++
        prop Nd
        prop Lu +
        notprop Nd
        prop Lu +
        prop Han
        prop Lu +
        prop Xan
        prop Lu ++
        prop Xsp
        prop Lu ++
        prop Xps
        prop Xwd +
        prop Lu
        prop Lu +
        prop Xuc
        Ket
        End
------------------------------------------------------------------

/\p{Han}+\p{Lu} \p{Han}+\p{L&} \p{Han}+\p{L} \p{Han}+\p{Lu} \p{Han}+\p{Arabic} \p{Arabic}+\p{Arabic} \p{Han}+\p{Xan} \p{Han}+\p{Xsp} \p{Han}+\p{Xps} \p{Xwd}+\p{Han} \p{Han}+\p{Xuc}/BWZx  
------------------------------------------------------------------
        Bra
        prop Han +
        prop Lu
        prop Han +
        prop L&
        prop Han +
        prop L
        prop Han +
        prop Lu
        prop Han ++
        prop Arabic
        prop Arabic +
        prop Arabic
        prop Han +
        prop Xan
        prop Han +
        prop Xsp
        prop Han +
        prop Xps
        prop Xwd +
        prop Han
        prop Han +
        prop Xuc
        Ket
        End
------------------------------------------------------------------

/\p{Xan}+\p{Any} \p{Xan}+\p{L&} \P{Xan}+\p{L&} \p{Xan}+\p{L} \p{Xan}+\p{Lu} \p{Xan}+\p{Han} \p{Xan}+\p{Xan} \p{Xan}+\P{Xan} \p{Xan}+\p{Xsp} \p{Xan}+\p{Xps} \p{Xwd}+\p{Xan} \p{Xan}+\p{Xuc}/BWZx  
------------------------------------------------------------------
        Bra
        prop Xan +
        prop Any
        prop Xan +
        prop L&
        notprop Xan ++
        prop L&
        prop Xan +
        prop L
        prop Xan +
        prop Lu
        prop Xan +
        prop Han
        prop Xan +
        prop Xan
        prop Xan ++
        notprop Xan
        prop Xan ++
        prop Xsp
        prop Xan ++
        prop Xps
        prop Xwd +
        prop Xan
        prop Xan +
        prop Xuc
        Ket
        End
------------------------------------------------------------------

/\p{Xsp}+\p{Any} \p{Xsp}+\p{L&} \p{Xsp}+\p{L} \p{Xsp}+\p{Lu} \p{Xsp}+\p{Han} \p{Xsp}+\p{Xan} \p{Xsp}+\p{Xsp} \P{Xsp}+\p{Xsp} \p{Xsp}+\p{Xps} \p{Xwd}+\p{Xsp} \p{Xsp}+\p{Xuc}/BWZx  
------------------------------------------------------------------
        Bra
        prop Xsp +
        prop Any
        prop Xsp ++
        prop L&
        prop Xsp ++
        prop L
        prop Xsp ++
        prop Lu
        prop Xsp +
        prop Han
        prop Xsp ++
        prop Xan
        prop Xsp +
        prop Xsp
        notprop Xsp ++
        prop Xsp
        prop Xsp +
        prop Xps
        prop Xwd ++
        prop Xsp
        prop Xsp +
        prop Xuc
        Ket
        End
------------------------------------------------------------------

/\p{Xwd}+\p{Any} \p{Xwd}+\p{L&} \p{Xwd}+\p{L} \p{Xwd}+\p{Lu} \p{Xwd}+\p{Han} \p{Xwd}+\p{Xan} \p{Xwd}+\p{Xsp} \p{Xwd}+\p{Xps} \p{Xwd}+\p{Xwd} \p{Xwd}+\P{Xwd} \p{Xwd}+\p{Xuc}/BWZx  
------------------------------------------------------------------
        Bra
        prop Xwd +
        prop Any
        prop Xwd +
        prop L&
        prop Xwd +
        prop L
        prop Xwd +
        prop Lu
        prop Xwd +
        prop Han
        prop Xwd +
        prop Xan
        prop Xwd ++
        prop Xsp
        prop Xwd ++
        prop Xps
        prop Xwd +
        prop Xwd
        prop Xwd ++
        notprop Xwd
        prop Xwd +
        prop Xuc
        Ket
        End
------------------------------------------------------------------

/\p{Xuc}+\p{Any} \p{Xuc}+\p{L&} \p{Xuc}+\p{L} \p{Xuc}+\p{Lu} \p{Xuc}+\p{Han} \p{Xuc}+\p{Xan} \p{Xuc}+\p{Xsp} \p{Xuc}+\p{Xps} \p{Xwd}+\p{Xuc} \p{Xuc}+\p{Xuc} \p{Xuc}+\P{Xuc}/BWZx  
------------------------------------------------------------------
        Bra
        prop Xuc +
        prop Any
        prop Xuc +
        prop L&
        prop Xuc +
        prop L
        prop Xuc +
        prop Lu
        prop Xuc +
        prop Han
        prop Xuc +
        prop Xan
        prop Xuc +
        prop Xsp
        prop Xuc +
        prop Xps
        prop Xwd +
        prop Xuc
        prop Xuc +
        prop Xuc
        prop Xuc ++
        notprop Xuc
        Ket
        End
------------------------------------------------------------------

/\p{N}+\p{Ll} \p{N}+\p{Nd} \p{N}+\P{Nd}/BWZx
------------------------------------------------------------------
        Bra
        prop N ++
        prop Ll
        prop N +
        prop Nd
        prop N +
        notprop Nd
        Ket
        End
------------------------------------------------------------------

/\p{Xan}+\p{L} \p{Xan}+\p{N} \p{Xan}+\p{C} \p{Xan}+\P{L} \P{Xan}+\p{N} \p{Xan}+\P{C}/BWZx
------------------------------------------------------------------
        Bra
        prop Xan +
        prop L
        prop Xan +
        prop N
        prop Xan ++
        prop C
        prop Xan +
        notprop L
        notprop Xan ++
        prop N
        prop Xan +
        notprop C
        Ket
        End
------------------------------------------------------------------

/\p{L}+\p{Xan} \p{N}+\p{Xan} \p{C}+\p{Xan} \P{L}+\p{Xan} \p{N}+\p{Xan} \P{C}+\p{Xan} \p{L}+\P{Xan}/BWZx
------------------------------------------------------------------
        Bra
        prop L +
        prop Xan
        prop N +
        prop Xan
        prop C ++
        prop Xan
        notprop L +
        prop Xan
        prop N +
        prop Xan
        notprop C +
        prop Xan
        prop L ++
        notprop Xan
        Ket
        End
------------------------------------------------------------------

/\p{Xan}+\p{Lu} \p{Xan}+\p{Nd} \p{Xan}+\p{Cc} \p{Xan}+\P{Ll} \P{Xan}+\p{No} \p{Xan}+\P{Cf}/BWZx
------------------------------------------------------------------
        Bra
        prop Xan +
        prop Lu
        prop Xan +
        prop Nd
        prop Xan ++
        prop Cc
        prop Xan +
        notprop Ll
        notprop Xan ++
        prop No
        prop Xan +
        notprop Cf
        Ket
        End
------------------------------------------------------------------

/\p{Lu}+\p{Xan} \p{Nd}+\p{Xan} \p{Cs}+\p{Xan} \P{Lt}+\p{Xan} \p{Nl}+\p{Xan} \P{Cc}+\p{Xan} \p{Lt}+\P{Xan}/BWZx
------------------------------------------------------------------
        Bra
        prop Lu +
        prop Xan
        prop Nd +
        prop Xan
        prop Cs ++
        prop Xan
        notprop Lt +
        prop Xan
        prop Nl +
        prop Xan
        notprop Cc +
        prop Xan
        prop Lt ++
        notprop Xan
        Ket
        End
------------------------------------------------------------------

/\w+\p{P} \w+\p{Po} \w+\s \p{Xan}+\s \s+\p{Xan} \s+\w/BWZx
------------------------------------------------------------------
        Bra
        prop Xwd +
        prop P
        prop Xwd +
        prop Po
        prop Xwd ++
        prop Xsp
        prop Xan ++
        prop Xsp
        prop Xsp ++
        prop Xan
        prop Xsp ++
        prop Xwd
        Ket
        End
------------------------------------------------------------------

/\w+\P{P} \W+\p{Po} \w+\S \P{Xan}+\s \s+\P{Xan} \s+\W/BWZx
------------------------------------------------------------------
        Bra
        prop Xwd +
        notprop P
        notprop Xwd +
        prop Po
        prop Xwd +
        notprop Xsp
        notprop Xan +
        prop Xsp
        prop Xsp +
        notprop Xan
        prop Xsp +
        notprop Xwd
        Ket
        End
------------------------------------------------------------------

/\w+\p{Po} \w+\p{Pc} \W+\p{Po} \W+\p{Pc} \w+\P{Po} \w+\P{Pc}/BWZx
------------------------------------------------------------------
        Bra
        prop Xwd +
        prop Po
        prop Xwd ++
        prop Pc
        notprop Xwd +
        prop Po
        notprop Xwd +
        prop Pc
        prop Xwd +
        notprop Po
        prop Xwd +
        notprop Pc
        Ket
        End
------------------------------------------------------------------

/\p{Nl}+\p{Xan} \P{Nl}+\p{Xan} \p{Nl}+\P{Xan} \P{Nl}+\P{Xan}/BWZx
------------------------------------------------------------------
        Bra
        prop Nl +
        prop Xan
        notprop Nl +
        prop Xan
        prop Nl ++
        notprop Xan
        notprop Nl +
        notprop Xan
        Ket
        End
------------------------------------------------------------------

/\p{Xan}+\p{Nl} \P{Xan}+\p{Nl} \p{Xan}+\P{Nl} \P{Xan}+\P{Nl}/BWZx
------------------------------------------------------------------
        Bra
        prop Xan +
        prop Nl
        notprop Xan ++
        prop Nl
        prop Xan +
        notprop Nl
        notprop Xan +
        notprop Nl
        Ket
        End
------------------------------------------------------------------

/\p{Xan}+\p{Nd} \P{Xan}+\p{Nd} \p{Xan}+\P{Nd} \P{Xan}+\P{Nd}/BWZx
------------------------------------------------------------------
        Bra
        prop Xan +
        prop Nd
        notprop Xan ++
        prop Nd
        prop Xan +
        notprop Nd
        notprop Xan +
        notprop Nd
        Ket
        End
------------------------------------------------------------------

/-- End auto-possessification tests --/ 

/\w+/8CWBZ
------------------------------------------------------------------
        Bra
        Callout 255 0 3
        prop Xwd ++
        Callout 255 3 0
        Ket
        End
------------------------------------------------------------------
    abcd
--->abcd
 +0 ^        \w+
 +3 ^   ^    
 0: abcd

/[\p{N}]?+/BZO
------------------------------------------------------------------
        Bra
        [\p{N}]?+
        Ket
        End
------------------------------------------------------------------

/[\p{L}ab]{2,3}+/BZO
------------------------------------------------------------------
        Bra
        [ab\p{L}]{2,3}+
        Ket
        End
------------------------------------------------------------------

/\D+\X \d+\X \S+\X \s+\X \W+\X \w+\X \C+\X \R+\X \H+\X \h+\X \V+\X \v+\X a+\X \n+\X .+\X/BZx
------------------------------------------------------------------
        Bra
        \D+
        extuni
        \d+
        extuni
        \S+
        extuni
        \s+
        extuni
        \W+
        extuni
        \w+
        extuni
        AllAny+
        extuni
        \R+
        extuni
        \H+
        extuni
        \h+
        extuni
        \V+
        extuni
        \v+
        extuni
        a+
        extuni
        \x0a+
        extuni
        Any+
        extuni
        Ket
        End
------------------------------------------------------------------

/.+\X/BZxs
------------------------------------------------------------------
        Bra
        AllAny+
        extuni
        Ket
        End
------------------------------------------------------------------

/\X+$/BZxm
------------------------------------------------------------------
        Bra
        extuni+
     /m $
        Ket
        End
------------------------------------------------------------------

/\X+\D \X+\d \X+\S \X+\s \X+\W \X+\w \X+. \X+\C \X+\R \X+\H \X+\h \X+\V \X+\v \X+\X \X+\Z \X+\z \X+$/BZx
------------------------------------------------------------------
        Bra
        extuni+
        \D
        extuni+
        \d
        extuni+
        \S
        extuni+
        \s
        extuni+
        \W
        extuni+
        \w
        extuni+
        Any
        extuni+
        AllAny
        extuni+
        \R
        extuni+
        \H
        extuni+
        \h
        extuni+
        \V
        extuni+
        \v
        extuni+
        extuni
        extuni+
        \Z
        extuni++
        \z
        extuni+
        $
        Ket
        End
------------------------------------------------------------------

/\d+\s{0,5}=\s*\S?=\w{0,4}\W*/8WBZ
------------------------------------------------------------------
        Bra
        prop Nd ++
        prop Xsp {0,5}+
        =
        prop Xsp *+
        notprop Xsp ?
        =
        prop Xwd {0,4}+
        notprop Xwd *+
        Ket
        End
------------------------------------------------------------------

/[RST]+/8iWBZ
------------------------------------------------------------------
        Bra
        [R-Tr-t\x{17f}]++
        Ket
        End
------------------------------------------------------------------
    
/[R-T]+/8iWBZ 
------------------------------------------------------------------
        Bra
        [R-Tr-t\x{17f}]++
        Ket
        End
------------------------------------------------------------------

/[Q-U]+/8iWBZ 
------------------------------------------------------------------
        Bra
        [Q-Uq-u\x{17f}]++
        Ket
        End
------------------------------------------------------------------

/^s?c/mi8I
Capturing subpattern count = 0
Options: caseless multiline utf
First char at start or follows newline
Need char = 'c' (caseless)
    scat
 0: sc

/-- End of testinput7 --/
