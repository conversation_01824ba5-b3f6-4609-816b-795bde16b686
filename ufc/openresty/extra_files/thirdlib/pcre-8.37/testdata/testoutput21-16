/-- Tests for reloading pre-compiled patterns. The first one gives an error
right away, and can be any old pattern compiled in 8-bit mode ("abc" is
typical). The others require the link size to be 2. */x

<!testsaved8
Compiled pattern loaded from testsaved8
No study data
Error -28 from pcre16_fullinfo(0)
Running in 16-bit mode but pattern was compiled in 8-bit mode

%-- Generated from: 
    /^[aL](?P<name>(?:[AaLl]+)[^xX-]*?)(?P<other>[\x{150}-\x{250}\x{300}]|
      [^\x{800}aAs-uS-U\x{d800}-\x{dfff}])++[^#\b\x{500}\x{1000}]{3,5}$
      /x

    In 16-bit mode with options:  S>testdata/saved16LE-1
                                 FS>testdata/saved16BE-1
    In 32-bit mode with options:  S>testdata/saved32LE-1
                                 FS>testdata/saved32BE-1
--%x

<!testsaved16LE-1
Compiled pattern loaded from testsaved16LE-1
Study data loaded from testsaved16LE-1
------------------------------------------------------------------
  0 <USER> <GROUP>
  2     ^
  3     [La]
 20  43 CBra 1
 23  20 Bra
 25     [ALal]+
 43  20 Ket
 45     [\x00-,.-WY-wy-\xff] (neg)*?
 63  43 Ket
 65  12 CBraPos 2
 68     [\x{150}-\x{250}\x{300}]
 77  27 Alt
 79     [^AS-Uas-u\x{800}\x{d800}-\x{dfff}]
104  39 KetRpos
106     [^\x08#\x{500}\x{1000}]{3,5}
133     $
134 134 Ket
136     End
------------------------------------------------------------------
Capturing subpattern count = 2
Named capturing subpatterns:
  name    1
  other   2
Options: anchored extended
No first char
No need char
Subject length lower bound = 6
No starting char list

<!testsaved16BE-1
Compiled pattern loaded from testsaved16BE-1
Study data loaded from testsaved16BE-1
------------------------------------------------------------------
  0 <USER> <GROUP>
  2     ^
  3     [La]
 20  43 CBra 1
 23  20 Bra
 25     [ALal]+
 43  20 Ket
 45     [\x00-,.-WY-wy-\xff] (neg)*?
 63  43 Ket
 65  12 CBraPos 2
 68     [\x{150}-\x{250}\x{300}]
 77  27 Alt
 79     [^AS-Uas-u\x{800}\x{d800}-\x{dfff}]
104  39 KetRpos
106     [^\x08#\x{500}\x{1000}]{3,5}
133     $
134 134 Ket
136     End
------------------------------------------------------------------
Capturing subpattern count = 2
Named capturing subpatterns:
  name    1
  other   2
Options: anchored extended
No first char
No need char
Subject length lower bound = 6
No starting char list

<!testsaved32LE-1
Compiled pattern loaded from testsaved32LE-1
Study data loaded from testsaved32LE-1
Error -28 from pcre16_fullinfo(0)
Running in 16-bit mode but pattern was compiled in 32-bit mode

<!testsaved32BE-1
Compiled pattern loaded from testsaved32BE-1
Study data loaded from testsaved32BE-1
Error -28 from pcre16_fullinfo(0)
Running in 16-bit mode but pattern was compiled in 32-bit mode

/-- End of testinput21 --/
