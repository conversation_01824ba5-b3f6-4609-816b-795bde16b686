/-- This set of tests is for Unicode property support. It is compatible with
    Perl >= 5.15. --/
    
< forbid 9?=ABCDEFfGILMNPTUXZ<

/^\pC\pL\pM\pN\pP\pS\pZ</8
    \x7f\x{c0}\x{30f}\x{660}\x{66c}\x{f01}\x{1680}<
    \np\x{300}9!\$ < 
    ** Failers 
    ap\x{300}9!\$ < 
  
/^\PC/8
    X
    ** Failers 
    \x7f
  
/^\PL/8
    9
    ** Failers 
    \x{c0}
  
/^\PM/8
    X
    ** Failers 
    \x{30f}
  
/^\PN/8
    X
    ** Failers 
    \x{660}
  
/^\PP/8
    X
    ** Failers 
    \x{66c}
  
/^\PS/8
    X
    ** Failers 
    \x{f01}
  
/^\PZ/8
    X
    ** Failers 
    \x{1680}
    
/^\p{Cc}/8
    \x{017}
    \x{09f} 
    ** Failers
    \x{0600} 
  
/^\p{Cf}/8
    \x{601}
    ** Failers
    \x{09f} 
  
/^\p{Cn}/8
    \x{e0000}
    ** Failers
    \x{09f} 
  
/^\p{Co}/8
    \x{f8ff}
    ** Failers
    \x{09f} 
  
/^\p{Ll}/8
    a
    ** Failers 
    Z
    \x{e000}  
  
/^\p{Lm}/8
    \x{2b0}
    ** Failers
    a 
  
/^\p{Lo}/8
    \x{1bb}
    \x{3400}
    \x{3401}
    \x{4d00}
    \x{4db4}
    \x{4db5}     
    ** Failers
    a 
    \x{2b0}
    \x{4db6} 
  
/^\p{Lt}/8
    \x{1c5}
    ** Failers
    a 
    \x{2b0}
  
/^\p{Lu}/8
    A
    ** Failers
    \x{2b0}
  
/^\p{Mc}/8
    \x{903}
    ** Failers
    X
    \x{300}
       
/^\p{Me}/8
    \x{488}
    ** Failers
    X
    \x{903}
    \x{300}
  
/^\p{Mn}/8
    \x{300}
    ** Failers
    X
    \x{903}
  
/^\p{Nd}+/8
    0123456789\x{660}\x{661}\x{662}\x{663}\x{664}\x{665}\x{666}\x{667}\x{668}\x{669}\x{66a}
    \x{6f0}\x{6f1}\x{6f2}\x{6f3}\x{6f4}\x{6f5}\x{6f6}\x{6f7}\x{6f8}\x{6f9}\x{6fa}
    \x{966}\x{967}\x{968}\x{969}\x{96a}\x{96b}\x{96c}\x{96d}\x{96e}\x{96f}\x{970}
    ** Failers
    X
  
/^\p{Nl}/8
    \x{16ee}
    ** Failers
    X
    \x{966}
  
/^\p{No}/8
    \x{b2}
    \x{b3}
    ** Failers
    X
    \x{16ee}
  
/^\p{Pc}/8
    \x5f
    \x{203f}
    ** Failers
    X
    -
    \x{58a}
  
/^\p{Pd}/8
    -
    \x{58a}
    ** Failers
    X
    \x{203f}
  
/^\p{Pe}/8
    )
    ]
    }
    \x{f3b}
    ** Failers
    X
    \x{203f}
    (
    [
    {
    \x{f3c}
  
/^\p{Pf}/8
    \x{bb}
    \x{2019}
    ** Failers
    X
    \x{203f}
  
/^\p{Pi}/8
    \x{ab}
    \x{2018}
    ** Failers
    X
    \x{203f}
  
/^\p{Po}/8
    !
    \x{37e}
    ** Failers
    X
    \x{203f}
  
/^\p{Ps}/8
    (
    [
    {
    \x{f3c}
    ** Failers
    X
    )
    ]
    }
    \x{f3b}
  
/^\p{Sk}/8
    \x{2c2}
    ** Failers
    X
    \x{9f2}
  
/^\p{Sm}+/8
    +<|~\x{ac}\x{2044}
    ** Failers
    X
    \x{9f2}
  
/^\p{So}/8
    \x{a6}
    \x{482} 
    ** Failers
    X
    \x{9f2}
  
/^\p{Zl}/8
    \x{2028}
    ** Failers
    X
    \x{2029}
  
/^\p{Zp}/8
    \x{2029}
    ** Failers
    X
    \x{2028}
  
/\p{Nd}+(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}+?(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}{2,}(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}{2,}?(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*?(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}{2}(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}{2,3}(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}{2,3}?(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}?(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}??(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*+(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*+(...)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*+(....)/8
      ** Failers
      \x{660}\x{661}\x{662}ABC
  
/(?<=A\p{Nd})XYZ/8
    A2XYZ
    123A5XYZPQR
    ABA\x{660}XYZpqr
    ** Failers
    AXYZ
    XYZ     
    
/(?<!\pL)XYZ/8
    1XYZ
    AB=XYZ.. 
    XYZ 
    ** Failers
    WXYZ 

/[\P{Nd}]+/8
    abcd
    ** Failers
    1234

/\D+/8
    11111111111111111111111111111111111111111111111111111111111111111111111
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
     
/\P{Nd}+/8
    11111111111111111111111111111111111111111111111111111111111111111111111
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/[\D]+/8
    11111111111111111111111111111111111111111111111111111111111111111111111
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/[\P{Nd}]+/8
    11111111111111111111111111111111111111111111111111111111111111111111111
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/[\D\P{Nd}]+/8
    11111111111111111111111111111111111111111111111111111111111111111111111
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/\pL/8
    a
    A 

/\pL/8i
    a
    A 
    
/\p{Lu}/8 
    A
    aZ
    ** Failers
    abc   

/\p{Ll}/8 
    a
    Az
    ** Failers
    ABC   

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8
    A\x{391}\x{10427}\x{ff3a}\x{1fb0}
    ** Failers
    a\x{391}\x{10427}\x{ff3a}\x{1fb0}   
    A\x{3b1}\x{10427}\x{ff3a}\x{1fb0}
    A\x{391}\x{1044F}\x{ff3a}\x{1fb0}
    A\x{391}\x{10427}\x{ff5a}\x{1fb0}
    A\x{391}\x{10427}\x{ff3a}\x{1fb8}

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8i
    A\x{391}\x{10427}\x{ff3a}\x{1fb0}
    a\x{391}\x{10427}\x{ff3a}\x{1fb0}   
    A\x{3b1}\x{10427}\x{ff3a}\x{1fb0}
    A\x{391}\x{1044F}\x{ff3a}\x{1fb0}
    A\x{391}\x{10427}\x{ff5a}\x{1fb0}
    A\x{391}\x{10427}\x{ff3a}\x{1fb8}

/\x{391}+/8i
    \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}

/\x{391}{3,5}(.)/8i
    \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}X

/\x{391}{3,5}?(.)/8i
    \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}X

/[\x{391}\x{ff3a}]/8i
    \x{391}
    \x{ff3a}
    \x{3b1}
    \x{ff5a}   
    
/^[\X]/8
    X123
    *** Failers
    AXYZ

/^(\X*)C/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 

/^(\X*?)C/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 

/^(\X*)(.)/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 

/^(\X*?)(.)/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 

/^\X(.)/8
    *** Failers
    A\x{300}\x{301}\x{302}

/^\X{2,3}(.)/8
    A\x{300}\x{301}B\x{300}X
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}X
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}DA\x{300}X
    
/^\X{2,3}?(.)/8
    A\x{300}\x{301}B\x{300}X
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}X
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}DA\x{300}X

/^\X/8
    A
    A\x{300}BC 
    A\x{300}\x{301}\x{302}BC 
    \x{300}  

/^\p{Han}+/8
    \x{2e81}\x{3007}\x{2f804}\x{31a0}
    ** Failers
    \x{2e7f}  

/^\P{Katakana}+/8
    \x{3105}
    ** Failers
    \x{30ff}  

/^[\p{Arabic}]/8
    \x{06e9}
    \x{060b}
    ** Failers
    \x{061c}
    X\x{06e9}   

/^[\P{Yi}]/8
    \x{2f800}
    ** Failers
    \x{a014}
    \x{a4c6}   
    
/^\p{Any}X/8
    AXYZ
    \x{1234}XYZ 
    ** Failers
    X  
    
/^\P{Any}X/8
    ** Failers
    AX
    
/^\p{Any}?X/8
    XYZ
    AXYZ
    \x{1234}XYZ 
    ** Failers
    ABXYZ   

/^\P{Any}?X/8
    XYZ
    ** Failers
    AXYZ
    \x{1234}XYZ 
    ABXYZ   

/^\p{Any}+X/8
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    ** Failers
    XYZ

/^\P{Any}+X/8
    ** Failers
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    XYZ

/^\p{Any}*X/8
    XYZ
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    ** Failers

/^\P{Any}*X/8
    XYZ
    ** Failers
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ

/^[\p{Any}]X/8
    AXYZ
    \x{1234}XYZ 
    ** Failers
    X  
    
/^[\P{Any}]X/8
    ** Failers
    AX
    
/^[\p{Any}]?X/8
    XYZ
    AXYZ
    \x{1234}XYZ 
    ** Failers
    ABXYZ   

/^[\P{Any}]?X/8
    XYZ
    ** Failers
    AXYZ
    \x{1234}XYZ 
    ABXYZ   

/^[\p{Any}]+X/8
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    ** Failers
    XYZ

/^[\P{Any}]+X/8
    ** Failers
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    XYZ

/^[\p{Any}]*X/8
    XYZ
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    ** Failers

/^[\P{Any}]*X/8
    XYZ
    ** Failers
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ

/^\p{Any}{3,5}?/8
    abcdefgh
    \x{1234}\n\r\x{3456}xyz 

/^\p{Any}{3,5}/8
    abcdefgh
    \x{1234}\n\r\x{3456}xyz 

/^\P{Any}{3,5}?/8
    ** Failers
    abcdefgh
    \x{1234}\n\r\x{3456}xyz 

/^\p{L&}X/8
     AXY
     aXY
     \x{1c5}XY
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^[\p{L&}]X/8
     AXY
     aXY
     \x{1c5}XY
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^\p{L&}+X/8
     AXY
     aXY
     AbcdeXyz 
     \x{1c5}AbXY
     abcDEXypqreXlmn 
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^[\p{L&}]+X/8
     AXY
     aXY
     AbcdeXyz 
     \x{1c5}AbXY
     abcDEXypqreXlmn 
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^\p{L&}+?X/8
     AXY
     aXY
     AbcdeXyz 
     \x{1c5}AbXY
     abcDEXypqreXlmn 
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^[\p{L&}]+?X/8
     AXY
     aXY
     AbcdeXyz 
     \x{1c5}AbXY
     abcDEXypqreXlmn 
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^\P{L&}X/8
     !XY
     \x{1bb}XY
     \x{2b0}XY
     ** Failers
     \x{1c5}XY
     AXY      

/^[\P{L&}]X/8
     !XY
     \x{1bb}XY
     \x{2b0}XY
     ** Failers
     \x{1c5}XY
     AXY      

/^(\p{Z}[^\p{C}\p{Z}]+)*$/
    \xa0!

/^[\pL](abc)(?1)/
    AabcabcYZ    

/([\pL]=(abc))*X/
    L=abcX

/^\p{Balinese}\p{Cuneiform}\p{Nko}\p{Phags_Pa}\p{Phoenician}/8
    \x{1b00}\x{12000}\x{7c0}\x{a840}\x{10900}

/Check property support in non-UTF-8 mode/
 
/\p{L}{4}/
    123abcdefg
    123abc\xc4\xc5zz

/\X{1,3}\d/
  \x8aBCD
  
/\X?\d/
  \x8aBCD 

/\P{L}?\d/
  \x8aBCD 

/[\PPP\x8a]{1,}\x80/
    A\x80

/^[\p{Arabic}]/8
    \x{604}
    \x{60e} 
    \x{656} 
    \x{657} 
    \x{658} 
    \x{659} 
    \x{65a} 
    \x{65b} 
    \x{65c} 
    \x{65d} 
    \x{65e} 
    \x{65f}
    \x{66a} 
    \x{6e9} 
    \x{6ef}
    \x{6fa}  
    ** Failers
    \x{650}
    \x{651}  
    \x{652}  
    \x{653}  
    \x{654} 
    \x{655} 
    
/^\p{Cyrillic}/8
    \x{1d2b} 
    
/^\p{Common}/8
    \x{589}
    \x{60c}
    \x{61f}  
    \x{964}
    \x{965}  

/^\p{Inherited}/8
    \x{64b}
    \x{654}
    \x{655}
    \x{200c} 
    ** Failers
    \x{64a}
    \x{656}     

/^\p{Shavian}/8
    \x{10450}
    \x{1047f}
    
/^\p{Deseret}/8
    \x{10400}
    \x{1044f}
    
/^\p{Osmanya}/8
    \x{10480}
    \x{1049d}
    \x{104a0}
    \x{104a9}
    ** Failers
    \x{1049e}
    \x{1049f}
    \x{104aa}           

/\p{Carian}\p{Cham}\p{Kayah_Li}\p{Lepcha}\p{Lycian}\p{Lydian}\p{Ol_Chiki}\p{Rejang}\p{Saurashtra}\p{Sundanese}\p{Vai}/8
    \x{102A4}\x{AA52}\x{A91D}\x{1C46}\x{10283}\x{1092E}\x{1C6B}\x{A93B}\x{A8BF}\x{1BA0}\x{A50A}====

/\x{a77d}\x{1d79}/8i
    \x{a77d}\x{1d79}
    \x{1d79}\x{a77d} 

/\x{a77d}\x{1d79}/8
    \x{a77d}\x{1d79}
    ** Failers 
    \x{1d79}\x{a77d} 

/(A)\1/8i
    AA
    Aa
    aa
    aA

/(\x{10a})\1/8i
    \x{10a}\x{10a}
    \x{10a}\x{10b}
    \x{10b}\x{10b}
    \x{10b}\x{10a}
    
/The next two tests are for property support in non-UTF-8 mode/

/(?:\p{Lu}|\x20)+/
    \x41\x20\x50\xC2\x54\xC9\x20\x54\x4F\x44\x41\x59

/[\p{Lu}\x20]+/
    \x41\x20\x50\xC2\x54\xC9\x20\x54\x4F\x44\x41\x59

/\p{Avestan}\p{Bamum}\p{Egyptian_Hieroglyphs}\p{Imperial_Aramaic}\p{Inscriptional_Pahlavi}\p{Inscriptional_Parthian}\p{Javanese}\p{Kaithi}\p{Lisu}\p{Meetei_Mayek}\p{Old_South_Arabian}\p{Old_Turkic}\p{Samaritan}\p{Tai_Tham}\p{Tai_Viet}/8
    \x{10b00}\x{a6ef}\x{13007}\x{10857}\x{10b78}\x{10b58}\x{a980}\x{110c1}\x{a4ff}\x{abc0}\x{10a7d}\x{10c48}\x{0800}\x{1aad}\x{aac0}

/^\w+/8W
    Az_\x{aa}\x{c0}\x{1c5}\x{2b0}\x{3b6}\x{1d7c9}\x{2fa1d}1\x{660}\x{bef}\x{16ee}

/^[[:xdigit:]]*/8W
    1a\x{660}\x{bef}\x{16ee}
  
/^\d+/8W
    1\x{660}\x{bef}\x{16ee}
  
/^[[:digit:]]+/8W
    1\x{660}\x{bef}\x{16ee}

/^>\s+/8W
    >\x{20}\x{a0}\x{1680}\x{2028}\x{2029}\x{202f}\x{9}\x{b} 
  
/^>\pZ+/8W
    >\x{20}\x{a0}\x{1680}\x{2028}\x{2029}\x{202f}\x{9}\x{b} 
  
/^>[[:space:]]*/8W
    >\x{20}\x{a0}\x{1680}\x{2028}\x{2029}\x{202f}\x{9}\x{b} 

/^>[[:blank:]]*/8W
    >\x{20}\x{a0}\x{1680}\x{180e}\x{2000}\x{202f}\x{9}\x{b}\x{2028} 

/^[[:alpha:]]*/8W
    Az\x{aa}\x{c0}\x{1c5}\x{2b0}\x{3b6}\x{1d7c9}\x{2fa1d}

/^[[:alnum:]]*/8W
    Az\x{aa}\x{c0}\x{1c5}\x{2b0}\x{3b6}\x{1d7c9}\x{2fa1d}1\x{660}\x{bef}\x{16ee}

/^[[:cntrl:]]*/8W
    \x{0}\x{09}\x{1f}\x{7f}\x{9f} 

/^[[:graph:]]*/8W
    A\x{a1}\x{a0}

/^[[:print:]]*/8W
    A z\x{a0}\x{a1}

/^[[:punct:]]*/8W
    .+\x{a1}\x{a0}

/\p{Zs}*?\R/
    ** Failers
    a\xFCb   

/\p{Zs}*\R/                                                                    
    ** Failers 
    a\xFCb   

/ⱥ/8i
    ⱥ
    Ⱥx 
    Ⱥ 

/[ⱥ]/8i
    ⱥ
    Ⱥx 
    Ⱥ 

/Ⱥ/8i
    Ⱥ
    ⱥ
    
/-- These are tests for extended grapheme clusters --/ 

/^\X/8+
    G\x{34e}\x{34e}X
    \x{34e}\x{34e}X
    \x04X
    \x{1100}X
    \x{1100}\x{34e}X
    \x{1b04}\x{1b04}X 
    *These match up to the roman letters
    \x{1111}\x{1111}L,L
    \x{1111}\x{1111}\x{1169}L,L,V
    \x{1111}\x{ae4c}L, LV
    \x{1111}\x{ad89}L, LVT
    \x{1111}\x{ae4c}\x{1169}L, LV, V
    \x{1111}\x{ae4c}\x{1169}\x{1169}L, LV, V, V
    \x{1111}\x{ae4c}\x{1169}\x{11fe}L, LV, V, T
    \x{1111}\x{ad89}\x{11fe}L, LVT, T
    \x{1111}\x{ad89}\x{11fe}\x{11fe}L, LVT, T, T
    \x{ad89}\x{11fe}\x{11fe}LVT, T, T
    *These match just the first codepoint (invalid sequence)
    \x{1111}\x{11fe}L, T
    \x{ae4c}\x{1111}LV, L
    \x{ae4c}\x{ae4c}LV, LV
    \x{ae4c}\x{ad89}LV, LVT
    \x{1169}\x{1111}V, L
    \x{1169}\x{ae4c}V, LV
    \x{1169}\x{ad89}V, LVT
    \x{ad89}\x{1111}LVT, L
    \x{ad89}\x{1169}LVT, V
    \x{ad89}\x{ae4c}LVT, LV
    \x{ad89}\x{ad89}LVT, LVT
    \x{11fe}\x{1111}T, L
    \x{11fe}\x{1169}T, V
    \x{11fe}\x{ae4c}T, LV
    \x{11fe}\x{ad89}T, LVT
    *Test extend and spacing mark
    \x{1111}\x{ae4c}\x{0711}L, LV, extend
    \x{1111}\x{ae4c}\x{1b04}L, LV, spacing mark
    \x{1111}\x{ae4c}\x{1b04}\x{0711}\x{1b04}L, LV, spacing mark, extend, spacing mark
    *Test CR, LF, and control
    \x0d\x{0711}CR, extend
    \x0d\x{1b04}CR, spacingmark
    \x0a\x{0711}LF, extend
    \x0a\x{1b04}LF, spacingmark
    \x0b\x{0711}Control, extend
    \x09\x{1b04}Control, spacingmark
    *There are no Prepend characters, so we can't test Prepend, CR
    
/^(?>\X{2})X/8+
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
    
/^\X{2,4}X/8+
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X

/^\X{2,4}?X/8+
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X

/\X*Z/8Y
  A\x{300}

/\X*(.)/8Y
  A\x{1111}\x{ae4c}\x{1169}

/\X?abc/8Y
\xff\x7f\x00\x00\x03\x00\x41\xcc\x80\x41\x{300}\x61\x62\x63\x00\>06\?

/-- --/

/\x{1e9e}+/8i
    \x{1e9e}\x{00df}

/[z\x{1e9e}]+/8i
    \x{1e9e}\x{00df}

/\x{00df}+/8i
    \x{1e9e}\x{00df}

/[z\x{00df}]+/8i
    \x{1e9e}\x{00df}

/\x{1f88}+/8i
    \x{1f88}\x{1f80} 

/[z\x{1f88}]+/8i
    \x{1f88}\x{1f80} 
    
/-- Characters with more than one other case; test in classes --/

/[z\x{00b5}]+/8i
    \x{00b5}\x{039c}\x{03bc}

/[z\x{039c}]+/8i
    \x{00b5}\x{039c}\x{03bc}

/[z\x{03bc}]+/8i
    \x{00b5}\x{039c}\x{03bc}

/[z\x{00c5}]+/8i
    \x{00c5}\x{00e5}\x{212b}

/[z\x{00e5}]+/8i
    \x{00c5}\x{00e5}\x{212b}

/[z\x{212b}]+/8i
    \x{00c5}\x{00e5}\x{212b}

/[z\x{01c4}]+/8i
    \x{01c4}\x{01c5}\x{01c6}

/[z\x{01c5}]+/8i
    \x{01c4}\x{01c5}\x{01c6}

/[z\x{01c6}]+/8i
    \x{01c4}\x{01c5}\x{01c6}

/[z\x{01c7}]+/8i
    \x{01c7}\x{01c8}\x{01c9}

/[z\x{01c8}]+/8i
    \x{01c7}\x{01c8}\x{01c9}

/[z\x{01c9}]+/8i
    \x{01c7}\x{01c8}\x{01c9}

/[z\x{01ca}]+/8i
    \x{01ca}\x{01cb}\x{01cc}

/[z\x{01cb}]+/8i
    \x{01ca}\x{01cb}\x{01cc}

/[z\x{01cc}]+/8i
    \x{01ca}\x{01cb}\x{01cc}

/[z\x{01f1}]+/8i
    \x{01f1}\x{01f2}\x{01f3}

/[z\x{01f2}]+/8i
    \x{01f1}\x{01f2}\x{01f3}

/[z\x{01f3}]+/8i
    \x{01f1}\x{01f2}\x{01f3}

/[z\x{0345}]+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}

/[z\x{0399}]+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}

/[z\x{03b9}]+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}

/[z\x{1fbe}]+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}

/[z\x{0392}]+/8i
    \x{0392}\x{03b2}\x{03d0}

/[z\x{03b2}]+/8i
    \x{0392}\x{03b2}\x{03d0}

/[z\x{03d0}]+/8i
    \x{0392}\x{03b2}\x{03d0}

/[z\x{0395}]+/8i
    \x{0395}\x{03b5}\x{03f5}

/[z\x{03b5}]+/8i
    \x{0395}\x{03b5}\x{03f5}

/[z\x{03f5}]+/8i
    \x{0395}\x{03b5}\x{03f5}

/[z\x{0398}]+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}

/[z\x{03b8}]+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}

/[z\x{03d1}]+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}

/[z\x{03f4}]+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}

/[z\x{039a}]+/8i
    \x{039a}\x{03ba}\x{03f0}

/[z\x{03ba}]+/8i
    \x{039a}\x{03ba}\x{03f0}

/[z\x{03f0}]+/8i
    \x{039a}\x{03ba}\x{03f0}

/[z\x{03a0}]+/8i
    \x{03a0}\x{03c0}\x{03d6} 

/[z\x{03c0}]+/8i
    \x{03a0}\x{03c0}\x{03d6} 

/[z\x{03d6}]+/8i
    \x{03a0}\x{03c0}\x{03d6} 

/[z\x{03a1}]+/8i
    \x{03a1}\x{03c1}\x{03f1}

/[z\x{03c1}]+/8i
    \x{03a1}\x{03c1}\x{03f1}

/[z\x{03f1}]+/8i
    \x{03a1}\x{03c1}\x{03f1}

/[z\x{03a3}]+/8i
    \x{03A3}\x{03C2}\x{03C3}

/[z\x{03c2}]+/8i
    \x{03A3}\x{03C2}\x{03C3}

/[z\x{03c3}]+/8i
    \x{03A3}\x{03C2}\x{03C3}

/[z\x{03a6}]+/8i
    \x{03a6}\x{03c6}\x{03d5} 

/[z\x{03c6}]+/8i
    \x{03a6}\x{03c6}\x{03d5} 

/[z\x{03d5}]+/8i
    \x{03a6}\x{03c6}\x{03d5} 

/[z\x{03c9}]+/8i
    \x{03c9}\x{03a9}\x{2126}

/[z\x{03a9}]+/8i
    \x{03c9}\x{03a9}\x{2126}

/[z\x{2126}]+/8i
    \x{03c9}\x{03a9}\x{2126}

/[z\x{1e60}]+/8i
    \x{1e60}\x{1e61}\x{1e9b}

/[z\x{1e61}]+/8i
    \x{1e60}\x{1e61}\x{1e9b}

/[z\x{1e9b}]+/8i
    \x{1e60}\x{1e61}\x{1e9b}

/-- Perl 5.12.4 gets these wrong, but 5.15.3 is OK --/

/[z\x{004b}]+/8i
    \x{004b}\x{006b}\x{212a}

/[z\x{006b}]+/8i
    \x{004b}\x{006b}\x{212a}

/[z\x{212a}]+/8i
    \x{004b}\x{006b}\x{212a}

/[z\x{0053}]+/8i
    \x{0053}\x{0073}\x{017f}

/[z\x{0073}]+/8i
    \x{0053}\x{0073}\x{017f}

/[z\x{017f}]+/8i
    \x{0053}\x{0073}\x{017f}
    
/-- --/ 

/(ΣΆΜΟΣ) \1/8i
    ΣΆΜΟΣ ΣΆΜΟΣ
    ΣΆΜΟΣ σάμος
    σάμος σάμος
    σάμος σάμοσ
    σάμος ΣΆΜΟΣ  

/(σάμος) \1/8i
    ΣΆΜΟΣ ΣΆΜΟΣ
    ΣΆΜΟΣ σάμος
    σάμος σάμος
    σάμος σάμοσ
    σάμος ΣΆΜΟΣ  

/(ΣΆΜΟΣ) \1*/8i
    ΣΆΜΟΣ\x20
    ΣΆΜΟΣ ΣΆΜΟΣσάμοςσάμος

/-- Perl matches these --/

/\x{00b5}+/8i
    \x{00b5}\x{039c}\x{03bc}

/\x{039c}+/8i
    \x{00b5}\x{039c}\x{03bc}

/\x{03bc}+/8i
    \x{00b5}\x{039c}\x{03bc}


/\x{00c5}+/8i
    \x{00c5}\x{00e5}\x{212b}

/\x{00e5}+/8i
    \x{00c5}\x{00e5}\x{212b}

/\x{212b}+/8i
    \x{00c5}\x{00e5}\x{212b}


/\x{01c4}+/8i
    \x{01c4}\x{01c5}\x{01c6}

/\x{01c5}+/8i
    \x{01c4}\x{01c5}\x{01c6}

/\x{01c6}+/8i
    \x{01c4}\x{01c5}\x{01c6}


/\x{01c7}+/8i
    \x{01c7}\x{01c8}\x{01c9}

/\x{01c8}+/8i
    \x{01c7}\x{01c8}\x{01c9}

/\x{01c9}+/8i
    \x{01c7}\x{01c8}\x{01c9}


/\x{01ca}+/8i
    \x{01ca}\x{01cb}\x{01cc}

/\x{01cb}+/8i
    \x{01ca}\x{01cb}\x{01cc}

/\x{01cc}+/8i
    \x{01ca}\x{01cb}\x{01cc}


/\x{01f1}+/8i
    \x{01f1}\x{01f2}\x{01f3}

/\x{01f2}+/8i
    \x{01f1}\x{01f2}\x{01f3}

/\x{01f3}+/8i
    \x{01f1}\x{01f2}\x{01f3}


/\x{0345}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}

/\x{0399}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}

/\x{03b9}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}

/\x{1fbe}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}


/\x{0392}+/8i
    \x{0392}\x{03b2}\x{03d0}

/\x{03b2}+/8i
    \x{0392}\x{03b2}\x{03d0}

/\x{03d0}+/8i
    \x{0392}\x{03b2}\x{03d0}
    

/\x{0395}+/8i
    \x{0395}\x{03b5}\x{03f5}

/\x{03b5}+/8i
    \x{0395}\x{03b5}\x{03f5}

/\x{03f5}+/8i
    \x{0395}\x{03b5}\x{03f5}


/\x{0398}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}

/\x{03b8}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}

/\x{03d1}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}

/\x{03f4}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}
    

/\x{039a}+/8i
    \x{039a}\x{03ba}\x{03f0}

/\x{03ba}+/8i
    \x{039a}\x{03ba}\x{03f0}

/\x{03f0}+/8i
    \x{039a}\x{03ba}\x{03f0}
    

/\x{03a0}+/8i
    \x{03a0}\x{03c0}\x{03d6} 

/\x{03c0}+/8i
    \x{03a0}\x{03c0}\x{03d6} 

/\x{03d6}+/8i
    \x{03a0}\x{03c0}\x{03d6} 


/\x{03a1}+/8i
    \x{03a1}\x{03c1}\x{03f1}

/\x{03c1}+/8i
    \x{03a1}\x{03c1}\x{03f1}

/\x{03f1}+/8i
    \x{03a1}\x{03c1}\x{03f1}


/\x{03a3}+/8i
    \x{03A3}\x{03C2}\x{03C3}

/\x{03c2}+/8i
    \x{03A3}\x{03C2}\x{03C3}

/\x{03c3}+/8i
    \x{03A3}\x{03C2}\x{03C3}
    

/\x{03a6}+/8i
    \x{03a6}\x{03c6}\x{03d5} 

/\x{03c6}+/8i
    \x{03a6}\x{03c6}\x{03d5} 

/\x{03d5}+/8i
    \x{03a6}\x{03c6}\x{03d5} 


/\x{03c9}+/8i
    \x{03c9}\x{03a9}\x{2126}

/\x{03a9}+/8i
    \x{03c9}\x{03a9}\x{2126}

/\x{2126}+/8i
    \x{03c9}\x{03a9}\x{2126}
    

/\x{1e60}+/8i
    \x{1e60}\x{1e61}\x{1e9b}

/\x{1e61}+/8i
    \x{1e60}\x{1e61}\x{1e9b}

/\x{1e9b}+/8i
    \x{1e60}\x{1e61}\x{1e9b}
    

/\x{1e9e}+/8i
    \x{1e9e}\x{00df}

/\x{00df}+/8i
    \x{1e9e}\x{00df}
    

/\x{1f88}+/8i
    \x{1f88}\x{1f80} 

/\x{1f80}+/8i
    \x{1f88}\x{1f80} 


/-- Perl 5.12.4 gets these wrong, but 5.15.3 is OK --/

/\x{004b}+/8i
    \x{004b}\x{006b}\x{212a}

/\x{006b}+/8i
    \x{004b}\x{006b}\x{212a}

/\x{212a}+/8i
    \x{004b}\x{006b}\x{212a}


/\x{0053}+/8i
    \x{0053}\x{0073}\x{017f}

/\x{0073}+/8i
    \x{0053}\x{0073}\x{017f}

/\x{017f}+/8i
    \x{0053}\x{0073}\x{017f}

/^\p{Any}*\d{4}/8
    1234
    123 

/^\X*\w{4}/8
    1234
    123  

/^A\s+Z/8W
    A\x{2005}Z
    A\x{85}\x{180e}\x{2005}Z

/^A[\s]+Z/8W
    A\x{2005}Z
    A\x{85}\x{180e}\x{2005}Z

/^[[:graph:]]+$/8W
    Letter:ABC
    Mark:\x{300}\x{1d172}\x{1d17b}
    Number:9\x{660}
    Punctuation:\x{66a},;
    Symbol:\x{6de}<>\x{fffc}
    Cf-property:\x{ad}\x{600}\x{601}\x{602}\x{603}\x{604}\x{6dd}\x{70f}
    \x{200b}\x{200c}\x{200d}\x{200e}\x{200f}
    \x{202a}\x{202b}\x{202c}\x{202d}\x{202e}
    \x{2060}\x{2061}\x{2062}\x{2063}\x{2064}
    \x{206a}\x{206b}\x{206c}\x{206d}\x{206e}\x{206f}
    \x{feff}
    \x{fff9}\x{fffa}\x{fffb}
    \x{110bd}
    \x{1d173}\x{1d174}\x{1d175}\x{1d176}\x{1d177}\x{1d178}\x{1d179}\x{1d17a}
    \x{e0001}
    \x{e0020}\x{e0030}\x{e0040}\x{e0050}\x{e0060}\x{e0070}\x{e007f}
    ** Failers
    \x{09}
    \x{0a}
    \x{1D}
    \x{20}
    \x{85}
    \x{a0}
    \x{61c}
    \x{1680}
    \x{180e}
    \x{2028}
    \x{2029}
    \x{202f}
    \x{2065}
    \x{2066}
    \x{2067}
    \x{2068}
    \x{2069}
    \x{3000}
    \x{e0002}
    \x{e001f}
    \x{e0080} 

/^[[:print:]]+$/8W
    Space: \x{a0}
    \x{1680}\x{2000}\x{2001}\x{2002}\x{2003}\x{2004}\x{2005}
    \x{2006}\x{2007}\x{2008}\x{2009}\x{200a} 
    \x{202f}\x{205f} 
    \x{3000}
    Letter:ABC
    Mark:\x{300}\x{1d172}\x{1d17b}
    Number:9\x{660}
    Punctuation:\x{66a},;
    Symbol:\x{6de}<>\x{fffc}
    Cf-property:\x{ad}\x{600}\x{601}\x{602}\x{603}\x{604}\x{6dd}\x{70f}
    \x{180e}
    \x{200b}\x{200c}\x{200d}\x{200e}\x{200f}
    \x{202a}\x{202b}\x{202c}\x{202d}\x{202e}
    \x{202f}
    \x{2060}\x{2061}\x{2062}\x{2063}\x{2064}
    \x{206a}\x{206b}\x{206c}\x{206d}\x{206e}\x{206f}
    \x{feff}
    \x{fff9}\x{fffa}\x{fffb}
    \x{110bd}
    \x{1d173}\x{1d174}\x{1d175}\x{1d176}\x{1d177}\x{1d178}\x{1d179}\x{1d17a}
    \x{e0001}
    \x{e0020}\x{e0030}\x{e0040}\x{e0050}\x{e0060}\x{e0070}\x{e007f}
    ** Failers
    \x{09}
    \x{1D}
    \x{85}
    \x{61c}
    \x{2028}
    \x{2029}
    \x{2065}
    \x{2066}
    \x{2067}
    \x{2068}
    \x{2069}
    \x{e0002}
    \x{e001f}
    \x{e0080} 

/^[[:punct:]]+$/8W
    \$+<=>^`|~
    !\"#%&'()*,-./:;?@[\\]_{}
    \x{a1}\x{a7}  
    \x{37e} 
    ** Failers
    abcde  

/^[[:^graph:]]+$/8W
    \x{09}\x{0a}\x{1D}\x{20}\x{85}\x{a0}\x{61c}\x{1680}\x{180e}
    \x{2028}\x{2029}\x{202f}\x{2065}\x{2066}\x{2067}\x{2068}\x{2069}
    \x{3000}\x{e0002}\x{e001f}\x{e0080}
    ** Failers
    Letter:ABC
    Mark:\x{300}\x{1d172}\x{1d17b}
    Number:9\x{660}
    Punctuation:\x{66a},;
    Symbol:\x{6de}<>\x{fffc}
    Cf-property:\x{ad}\x{600}\x{601}\x{602}\x{603}\x{604}\x{6dd}\x{70f}
    \x{200b}\x{200c}\x{200d}\x{200e}\x{200f}
    \x{202a}\x{202b}\x{202c}\x{202d}\x{202e}
    \x{2060}\x{2061}\x{2062}\x{2063}\x{2064}
    \x{206a}\x{206b}\x{206c}\x{206d}\x{206e}\x{206f}
    \x{feff}
    \x{fff9}\x{fffa}\x{fffb}
    \x{110bd}
    \x{1d173}\x{1d174}\x{1d175}\x{1d176}\x{1d177}\x{1d178}\x{1d179}\x{1d17a}
    \x{e0001}
    \x{e0020}\x{e0030}\x{e0040}\x{e0050}\x{e0060}\x{e0070}\x{e007f}

/^[[:^print:]]+$/8W
    \x{09}\x{1D}\x{85}\x{61c}\x{2028}\x{2029}\x{2065}\x{2066}\x{2067}
    \x{2068}\x{2069}\x{e0002}\x{e001f}\x{e0080}
    ** Failers
    Space: \x{a0}
    \x{1680}\x{2000}\x{2001}\x{2002}\x{2003}\x{2004}\x{2005}
    \x{2006}\x{2007}\x{2008}\x{2009}\x{200a} 
    \x{202f}\x{205f} 
    \x{3000}
    Letter:ABC
    Mark:\x{300}\x{1d172}\x{1d17b}
    Number:9\x{660}
    Punctuation:\x{66a},;
    Symbol:\x{6de}<>\x{fffc}
    Cf-property:\x{ad}\x{600}\x{601}\x{602}\x{603}\x{604}\x{6dd}\x{70f}
    \x{180e}
    \x{200b}\x{200c}\x{200d}\x{200e}\x{200f}
    \x{202a}\x{202b}\x{202c}\x{202d}\x{202e}
    \x{202f}
    \x{2060}\x{2061}\x{2062}\x{2063}\x{2064}
    \x{206a}\x{206b}\x{206c}\x{206d}\x{206e}\x{206f}
    \x{feff}
    \x{fff9}\x{fffa}\x{fffb}
    \x{110bd}
    \x{1d173}\x{1d174}\x{1d175}\x{1d176}\x{1d177}\x{1d178}\x{1d179}\x{1d17a}
    \x{e0001}
    \x{e0020}\x{e0030}\x{e0040}\x{e0050}\x{e0060}\x{e0070}\x{e007f}

/^[[:^punct:]]+$/8W
    abcde  
    ** Failers
    \$+<=>^`|~
    !\"#%&'()*,-./:;?@[\\]_{}
    \x{a1}\x{a7}  
    \x{37e} 

/[RST]+/8iW
    Ss\x{17f}
    
/[R-T]+/8iW 
    Ss\x{17f}

/[q-u]+/8iW 
    Ss\x{17f}

/^s?c/mi8
    scat

/[A-`]/i8
    abcdefghijklmno

/\C\X*QT/8
    Ӆ\x0aT

/-- End of testinput6 --/
