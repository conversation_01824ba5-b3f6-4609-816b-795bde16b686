worker_processes 2;
# TODO 有些机器自动复制开2个

# max fd per worker
worker_rlimit_nofile 10240;

error_log  logs/error.log  notice;
pid        logs/nginx.pid;
worker_rlimit_core 2048m;
working_directory	./;

events {
    use epoll;
    worker_connections  10240;
}

http {
    resolver *********** ***********;
    # testtest
    lua_code_cache on;
    lua_package_path "./ufc/?.lua;./lualib/?.lua";
    lua_package_cpath "./ufc/luaso/?.so;./lualib/?.so";
    lua_shared_dict dump_header 16m;
    lua_shared_dict dump 16m;
    lua_shared_dict fault 16m;
    lua_shared_dict http_ip 16m;
    lua_shared_dict static_cache 16m;
    lua_shared_dict static_cache-services 256m;
    lua_shared_dict static_cache-bnss 256m;
    lua_shared_dict static_cache-idcmaps 256m;
    lua_shared_dict static_cache-platformmaps 256m;
    lua_shared_dict static_cache-platformidcmaps 256m;
    lua_shared_dict static_cache-configs 16m;
    lua_shared_dict static_cache-http_ips 8m;
    lua_shared_dict static_cache-stream_ips 8m;

    lua_shared_dict dynamic_cache 16m;
    lua_shared_dict dynamic_cache-moniter 16m;
    lua_shared_dict dynamic_cache-service 128m;
    lua_shared_dict dynamic_cache-backend 1024m;
    lua_shared_dict dynamic_cache-breaker 16m;
    lua_shared_dict dynamic_cache-forbid_backends 16m;
    lua_shared_dict dynamic_cache-backend-response_times 128m;

    init_by_lua_file "./ufc/nginx_init.lua";
    init_worker_by_lua_file "./ufc/nginx_cron.lua";

    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    tcp_nopush     	on;
    tcp_nodelay 	on;
    underscores_in_headers on;

    client_header_buffer_size 8k;
    large_client_header_buffers 4 32k;
    client_max_body_size 21m;
    client_body_buffer_size 4m;

    proxy_http_version 1.1;
    keepalive_timeout  256;
    keepalive_requests 1024;
    client_header_timeout 60;
    client_body_timeout 60;
    send_timeout 360;
    lingering_close off;

    open_file_cache max=1024 inactive=1s;
    # request_time是个内置变量
    log_format performance '[ISIS] [$time_local] logid=$ufc_logid callid=$ufc_callid local_hostname=$hostname remote_addr=$remote_addr server_addr=- host=$host client_costtime=$ufc_client_costtime '
    'http_code=$status req_time=$request_time client_timeout=$client_timeout timeout_quantile=$timeout_quantile fault=$ufc_fault_injection sdk_version=$ufc_sdk_version request_from=$ufc_request_from '
    'ufc_time=$ufc_cost_time to_module=$ufc_service_name ufc_backup=- from_service=$from_service_name update_lantency=$update_lantency ori_to_service=$ori_ufc_service_name api=$ufc_uri method=$method ori_from_service=$ori_from_service_name from_idc=$from_logic_idc interact=['
    '$ufc_log1$ufc_log2$ufc_log3$ufc_log4$ufc_log5$ufc_log6$ufc_log7$ufc_log8';
    access_log logs/access.log performance;

    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 5;
    gzip_buffers 4 16k;
    gzip_http_version 1.0;
    gzip_types text/plain application/x-javascript text/css application/xml text/javascript text/xml;
    gzip_vary on;


    fastcgi_buffer_size 1024k;
    fastcgi_buffers 4 512k;
    fastcgi_busy_buffers_size 1024k;

    proxy_set_header        host $ufc_host;

    proxy_buffer_size 1024k;
    proxy_buffers 4 512k;
    proxy_busy_buffers_size 1024k;
    proxy_max_temp_file_size 1024m;
    proxy_next_upstream error timeout invalid_header non_idempotent http_502 http_504;
    proxy_next_upstream_tries 4;

    upstream backend {
        server 0.0.0.0:4242;
        balancer_by_lua_file "ufc/flow.lua";
    }

    server {
        listen       8240;
        server_name  default;
        set $ufc_logid "-";
        set $ufc_callid "-";
        set $ufc_time "-";
        set $ufc_cost_time "-";
        set $ufc_service_name "-";
        set $update_lantency "-";
        set $from_service_name "-";
        set $ori_ufc_service_name "-";
        set $method "-";
        set $from_logic_idc "-";
        set $ori_from_service_name "-";

        set $ufc_log1 "-";
        set $ufc_log2 "-";
        set $ufc_log3 "-";
        set $ufc_log4 "-";
        set $ufc_log5 "-";
        set $ufc_log6 "-";
        set $ufc_log7 "-";
        set $ufc_log8 "-";
        set $ufc_host "-";
        set $ufc_product "pcs";
        set $ufc_sdk_version "-";
        set $ufc_fault_injection "-";
        set $ufc_request_from "local"; 
        set $client_timeout "-";
        set $timeout_quantile "-";
        set $ufc_client_costtime "-";
        set $ufc_uri $uri;

        location / {
            access_by_lua_file "ufc/access.lua";
            proxy_pass http://backend;
            header_filter_by_lua_file "ufc/header_filter.lua";
            body_filter_by_lua_file "ufc/body_filter.lua";
            log_by_lua_file "ufc/log.lua";
        }
        location /bypass {
            content_by_lua_file "ufc/bypass.lua";
            log_by_lua_file "ufc/log_bypass.lua";
        }
        location /dump {
            content_by_lua_file "ufc/dump_interface.lua";
            log_by_lua_file "ufc/log_bypass.lua";
        }

        location /fault {
            content_by_lua_file "ufc/fault_interface.lua";
            log_by_lua_file "ufc/log_bypass.lua";
        }
        location /gossip {
            content_by_lua_file "ufc/gossip_interface.lua";
            log_by_lua_file "ufc/log_bypass.lua";
        }
    }
}
