local dynamic_conf = require "dynamic_conf"
local conf = require "conf"
local utils = require "utils"
local ngx_worker_cache = require "ngx_worker_cache"
local bit = require("bit")
local stream_utils = require("stream_utils")

local _M = {
	["doc"] = "preread",
}

--从http模块发送过来的信息中解析映射关系
function _M.http_data_parse()
	--参数为true才能以双工的方式打开
	--这个socket 没有close方法
	local sock, err = ngx.req.socket(true)
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("read data from ufc http moudle failed, error is %s", err))
	end

	--读取一行数据
	local data, err, partial = sock:receive()
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("read data from ufc http moudle failed, error is %s", err))
		return nil, err
	end
	--发送回应
	local bytes, err = sock:send("ok/r/n")
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("send data to http moudle failed, error is %s", err))
	end
	--解析映射关系
	local split_data = utils.explode("|", data)
	if type(split_data) ~= "table" or #split_data ~= 3 then
		ngx.log(ngx.WARN, "split data format error")
		return nil
	end
	--写到共享内存里面去
	local from_service = split_data[1]
	local to_service = split_data[2]
	local ip = split_data[3]
	--stream 模块对共享内存的操作不加锁，因为不涉及到ip分配不会有冲突问题
	--如果加锁并且抢锁失败映射关系就丢了
	--理论上由于竞争会存在动态分配的映射关系丢失的可能
	stream_utils.add_ip_mapping(ip, from_service .. "|" .. to_service, 1)
end


--解析上下游信息
function _M.service_data_parse(data)
	local split_data = utils.explode("|", data)
	if #split_data == 1 then
		--只有to serivice 没有from service
		return nil, data
	end
	return split_data[1], split_data[2]
end

--从remote_addr中解析上下游信息
function _M.remote_addr_parse(remote_addr)
	if remote_addr == nil or type(remote_addr) ~= "string" then
		return nil, nil
	end

	local split_addrs = utils.explode(".", remote_addr)
	if split_addrs[1] ~= "127" then
		--不是回环ip
		return nil, nil
	end

	--把ip变成数字，方便后面做位操作
	local ip_nums = 0
	for i = 0, 2 do
		local temp = tonumber(split_addrs[4 - i])
		--左移8位
		temp = bit.lshift(temp, i * 8)
		ip_nums = bit.bor(ip_nums, temp)
	end

	--预留最高两位表示协议类型，剩下的高11位表示上游，低11位表示下游
	local proto_type = bit.band(ip_nums, 0xc00000)
	--目前默认最高两位是00，后面可能有其他协议解析规则
	if proto_type ~= 0x00 then
		return nil, nil
	end

	--目前内存中直接有该remote ip的映射数据
	local static_cache = ngx_worker_cache["static_cache"]
	if static_cache and static_cache["stream_ips"] and static_cache["stream_ips"][remote_addr] and static_cache["stream_ips"][remote_addr]["content"]then
		return _M.service_data_parse(static_cache["stream_ips"][remote_addr]["content"])
	end

	--如果没有，先同步下共享内存
	ngx_worker_cache.load_ip_mapping_data()
	--同步之后再看看有没有
	if static_cache and static_cache["stream_ips"] and static_cache["stream_ips"][remote_addr] and static_cache["stream_ips"][remote_addr]["content"]then
		return _M.service_data_parse(static_cache["stream_ips"][remote_addr]["content"])
	end

	--可能是动态分配的上下游映射数据没有，看下游映射数据有没有
	--基础ip *********
	local base_num = 0x7f000000
	--低11位获取下游信息
	local low_11_bits = bit.band(ip_nums, 0x7ff)
	--组合获取下游ip
	local service_ip_num = bit.bor(low_11_bits, base_num)
	--数字转换成string
	local ip_str =  stream_utils.numIp_to_str(service_ip_num)
	if static_cache and static_cache["stream_ips"] and static_cache["stream_ips"][ip_str] and static_cache["stream_ips"][ip_str]["content"] then
		return _M.service_data_parse(static_cache["stream_ips"][ip_str]["content"])
	end

	--都匹配不到，返回nil
	return nil, nil
end

function _M.main()
	local from_service_name, ufc_service_name, ori_service_name
	local error_msg
	--为了复用原有的逻辑，这里直接把header设置成一个空table
	local headers = {}
	local ctxs = {}
	ngx.ctx.ufc_ctxs = ctxs
	stream_utils.gen_ufc_log()

	if not ngx_worker_cache.is_inited() then
		if not conf["backup_ufc"] then
			return conf.exit("E_NO_SERVICE")
		else
			return false
		end
	end
	--这里用$server_addr这个变量，
	--nginx listen不指定地址就是监听0.0.0.0（INADDR_ANY），nginx调用getsockname获取server地址
	--client用的哪个回环ip访问，server地址就是哪个
	local remote_addr = ngx.var.server_addr
	--判断一下remote addr是否是非法的，避免出现虚拟机那种情况
	if remote_addr == nil or conf["ufc_stream_illegal_remote_addr"][remote_addr] then
		ngx.log(ngx.WARN, string.format("[UFC_STREAM_ERR]request is from illegal_remote_addr: %s", remote_addr))
		return conf.exit("E_NO_SUPPORT")
	end

	if remote_addr == conf["ufc_stream_addr"] then
		--消息是从http模块发送过来的，解析动态生成的映射关系
		_M.http_data_parse()
		ctxs["ufc_service_name"] = "ufc-stream-moudle"
		ctxs["ori_ufc_service_name"] = "ufc-stream-moudle"
		ctxs["from_service_name"] = "ufc-http-moudle"
		return conf.exit("E_OK")
	else 
		--从remoteip中解析上下游信息
		from_service_name, ufc_service_name = _M.remote_addr_parse(remote_addr)
	end

	--为了复用原来的代码，在header里面手动加入如下信息
	headers["x-ufc-service-name"] = ufc_service_name
	headers["x-ufc-self-service-name"] = from_service_name
	--这个from service 有可能是nil
	from_service_name, ufc_service_name, ori_service_name = dynamic_conf.get_ufc_service_name(nil, headers)

	ctxs["ufc_service_name"] = ufc_service_name
	ctxs["ori_ufc_service_name"] = ufc_service_name
	ctxs["from_service_name"] = from_service_name

	--地域合法性检查
	local verification_flag = ngx_worker_cache.request_verification(from_service_name, ufc_service_name)
	if verification_flag == false then
		return
	end

	if not ufc_service_name then
		error_msg = "E_NO_SERVICE"
	else
		error_msg = dynamic_conf.check_flow_limit(from_service_name, ufc_service_name, headers)
	end
	if error_msg then
		return conf.exit(error_msg)
	end
	ctxs["headers"] = headers
	ngx.ctx.ufc_ctxs = ctxs
end

_M.main()
