local math = require "math"
local ffi = require "ffi"
local C = ffi.C
local bit = require("bit")
local cjson = require "cjson.safe"

ffi.cdef[[
int gethostname(char *name, size_t len);
]]

local _M = {
	["doc"] = "lua ufc tools",
	["hostname"] = nil,
	["last_update_error_time"] = nil,
	["update_error_log_interval"] = 5,
	["max_update_interval"] = 300,
}

--配置更新延迟报警日志, 频控5s一次
function _M.update_error_log(update_lantency)
	local now = ngx.now()
	if not _M["last_update_error_time"] then
		_M["last_update_error_time"] = now
		return
	end

	if now - _M["last_update_error_time"] > _M["update_error_log_interval"] then
		ngx.log(ngx.WARN, string.format("stack traceback config hasn't updated for %d seconds", update_lantency))
		_M["last_update_error_time"] = now
	end
	return
end

function _M.get_hostname()
	if _M["hostname"] then
		return _M["hostname"]
	end

	local size = 128
	local buf = ffi.new("unsigned char[?]", size)

	local res = C.gethostname(buf, size)
	local hostname, host
	if res == 0 then
		hostname = ffi.string(buf, size)
		host = string.gsub(hostname, "%z+$", "")
		_M["hostname"] = host
		return host
	end
	_M["hostname"] = "localhost"
	return "localhost"
end

function _M.gen_logid(headers)
	return ngx.crc32_long(ngx.now() .. ngx.worker.pid() .. _M.get_client_ip(headers) .. math.random(1, 10240) .. _M.get_hostname())
end

function _M.is_backup() -- TODO backup集群也有没有ufc字样的,新扩容的都没有ufc字样
	if _M["backup"] then
		return _M["backup"]
	end
	local hostname = _M.get_hostname()
	local s, _ = string.find(hostname, "ufc")
	local s2, _ = string.find(hostname, "tc-proxy-platform0")
	local s3, _ = string.find(hostname, "cq01-proxy-platform0")

	if s or s2 or s3 then
		_M["backup"] = "1"
	else
		_M["backup"] = "0"
	end
	return _M["backup"]
end

function _M.gen_ufc_log(headers, ctxs, params)
	local ufc_time = math.floor(ngx.now())
	ngx.var.ufc_time = ufc_time 

	ctxs["ufc_time"] = ufc_time

	local res
	local logid, callid, wenku_logid
	local args = params or ngx.req.get_uri_args()

	--local method = ngx.ctx.method
	if not ctxs["method"] or ctxs["method"] == "-" then
		ctxs["method"] = args["method"] or "-"
	end

	if not headers["x-http-flow-control"] then
		headers["x-http-flow-control"] = args["x-http-flow-control"]
	end

	res = {}

	logid = headers["http-x-isis-logid"]
	if not logid then
		logid = headers["x-yun-logid64"] or args["logid"] or args["dp-logid"]
	end
	if (not logid) or type(logid) == type(true) or logid == "nil" or logid == "" then
		logid = _M.gen_logid(headers)
		ngx.req.set_header("Http-X-Isis-Logid", logid)
	end

	if type(logid) == type({}) then
		logid = logid[1]
	end

	wenku_logid = headers["logid"]
	if not wenku_logid or type(wenku_logid) == type(true) or type(wenku_logid) == "nil" or wenku_logid == "" then 
		-- 用 isis logid 赋值
		wenku_logid = logid
		ngx.req.set_header("logid", wenku_logid)
	end

	res["ufc_logid"] = logid
	-- 保存起来供 dump 回放使用
	ctxs["ufc_logid"] = logid
	ngx.var.ufc_logid = logid  -- var中两个变量

	callid = headers["http-x-isis-callid"]
	if (not callid) or type(callid) == type(true) or callid == "nil" or callid == "" then
		callid = args["callid"] or args["dp-callid"]
		if (not callid) or type(callid) == type(true) or callid == "nil" or callid == "" then
			callid = "0"
		end
		ngx.req.set_header("Http-X-Isis-Callid", callid)
	end

	if type(callid) == type({}) then
		callid = callid[1]
	end

	res["ufc_callid"] = callid
	ngx.var.ufc_callid = callid

	ngx.req.set_header("Http-X-Isis-Logid", logid)
	--ngx.req.set_header("X-Yun-Logid64", logid)
	ngx.req.set_header("Http-X-Isis-Callid", callid)

	return res
end

function _M.split_backend(backend)
	local s
	local host, port

	if not backend then
		return nil, nil
	end

	s = string.find(backend, ":")
	if s == -1 then
		return nil, nil
	end

	host = string.sub(backend, 1, s - 1)
	port = tonumber(string.sub(backend, s + 1, string.len(backend)))
	return host, port
end

function _M.get_client_ip(headers)
	local ip
	local pos
	local x_forwarded_for

	x_forwarded_for	= _M.explode(",", headers['x_forwarded_for'])
	if type(x_forwarded_for) == type({}) then
		ip = x_forwarded_for[#x_forwarded_for]
	end

	if ip then
		return ip
	end

	ip = ngx.var.clientip or ngx.var.remote_addr
	if ip then
		return ip
	end

	pos = string.find(ip, "," , 1, true)
	if pos then
		ip = string.sub(ip, 1, pos - 1)
	end

	if ip then
		return ip
	end

	return "unknown"
end

function _M.explode(delimiter, str)
	if type(delimiter) ~= "string" or type(str) ~= "string" then
		return
	end

	local t, pos, len = {}, 1, #delimiter
	repeat
		pos = string.find(str, delimiter, 1, true)
		if pos then
			table.insert(t, string.sub(str, 1, pos -1))
			str = string.sub(str, pos + len)
		end
	until(not pos)

	if str and #str > 0 then
		table.insert(t, str)
	end

	return t
end

function _M.get_boundary()
	--旁路式callback逻辑会用这个获取callback参数
	--这个参数放在url里面的不是在body里面，但是保险起见我们还是加个0把所有header读出来
	local header = ngx.req.get_headers(0)["content-type"]
	local m
	if not header then
		return nil
	end

	if type(header) == "table" then
		header = header[1]
	end

	m = string.match(header, ";%s*boundary=\"([^\"]+)\"")
	if m then
		return m
	end

	return string.match(header, ";%s*boundary=([^\",;]+)")
end

function _M.get_post_params()
	ngx.req.read_body()
	local boundary = _M.get_boundary()
	if not boundary then
		return ngx.req.get_post_args()
	end

	local body_str ,ret = ngx.req.get_body_data(),{}
	if type(body_str) == 'string' then
		local body_arr = _M.explode(boundary, body_str)
		if type(body_arr) == 'table' and next(body_arr) then
			for _,v in pairs(body_arr) do
				local tmp = _M.explode("\r\n\r\n", v)
				if type(tmp) == 'table' and tmp[2] then
					local name = string.match(tmp[1], "name=\"([^\"]+)\"") or tmp[1]
					local t = _M.explode("\r\n",tmp[2])
					ret[name] = t[1] or tmp[2]
				end
			end
		end
	end
	return ret
end

function _M.deepcopy(orig)
	local orig_type = type(orig)
	local copy
	if orig_type == type({}) then
		copy = {}
		for orig_key, orig_value in next, orig, nil do
			copy[_M.deepcopy(orig_key)] = _M.deepcopy(orig_value)
		end
		setmetatable(copy, _M.deepcopy(getmetatable(orig)))

	else -- number, string, boolean, etc
		copy = orig
	end
	return copy
end

function _M.hash(s)
	local s = tostring(s)
	if s == nil then
		return 0
	end

	local sum = 0
	local q = 1
	for i=#s, 1, -1 do
		sum = sum + string.byte(s, i) * q
		q = q * 10
	end
	return sum
end

function _M.judge_type(variable_to_be_judged, variable_model)
	if type(variable_to_be_judged) == type(variable_model) then
		return true
	end
	return false
end

function _M.format_redis_hash_res(res)
	if type(res) ~= type({}) then
		return res
	end

	local formated_res = {}
	for index = 1, #res, 2 do
		formated_res[res[index]] = res[index+1]
	end

	return formated_res
end

--[[
@comment 判断一个字符是否存在于一个数组内
@param element:待判断字符, arr:待判断数组
@return bool
]]
function _M.in_array(element, arr)
	if not arr or type(arr) ~= "table" or #arr == 0 then
		return false
	end
	for _, val in pairs(arr) do
		if val == element then
			return true
		end
	end
	return false
end

--求以2为底的对数
function _M.log2(x)
	local y = bit.band(x,x - 1)
	y = bit.bor(y, -y)
	y = bit.arshift(y, 31)
	x = bit.bor(x, bit.rshift(x, 1))
	x = bit.bor(x, bit.rshift(x, 2))
	x = bit.bor(x, bit.rshift(x, 4))
	x = bit.bor(x, bit.rshift(x, 8))
	x = bit.bor(x, bit.rshift(x, 16))
	return _M.ones32(x) - 1 -y
end

function _M.ones32(x)
	x = x - bit.band(bit.rshift(x, 1), 0x55555555)
	x = bit.band(bit.rshift(x, 2), 0x33333333) + bit.band(x, 0x33333333)
	x = bit.band(bit.rshift(x, 4) + x, 0x0f0f0f0f)
	x = x + bit.rshift(x, 8)
	x = x + bit.rshift(x, 16)
	x = bit.band(x, 0x0000003f)
	return x
end

--如果cache_table不为nil说明需要把写磁盘的数据先缓存起来，最后再统一写磁盘
--避免磁盘满的情况下，特权进程core导致配置更新失败
function _M.basic_save(dir_name, file_name, content, cache_table)
	if content == nil then
		ngx.log(ngx.WARN, string.format("file name: %s, content is nil, save failed", file_name))
		return
	end

	if cache_table ~= nil then
		cache_table[file_name] = content
		return
	end

	local cache_fname = string.format("%s/%s", dir_name, file_name)
	local tmp_cache_fname = string.format("%s/%s_%s_%s",
		dir_name,
		file_name,
		ngx.now(),
		ngx.worker.pid()
	)
	local cache_encode, fd

	local cache_encode = cjson.encode(content)
	if not cache_encode then
		ngx.log(ngx.WARN, string.format("file name: %s, json encode failed", file_name))
		return false
	end

	fd = io.open(tmp_cache_fname, "w+")
	if fd == nil then
		ngx.log(ngx.WARN, string.format("file name: %s, open file failed", tmp_cache_fname))
		return false
	end

	local rs = fd:write(cache_encode)
	fd:close()

	local res, err
	if not rs then
		ngx.log(ngx.WARN, string.format("file name: %s, write file failed", tmp_cache_fname))
		res,err = os.remove(tmp_cache_fname)
		if res ~= true then
			ngx.log(ngx.WARN, string.format("file name: %s, remove file failed, error is %s", tmp_cache_fname, err))
		end
		return false
	end

	res, err = os.rename(tmp_cache_fname, cache_fname)
	if not res then
		ngx.log(ngx.WARN, string.format("old name: %s, new name: %s, rename failed, error is %s", tmp_cache_fname, cache_fname, err))
		--rename失败需要把原来的临时文件删掉
		res,err = os.remove(tmp_cache_fname)
		if res ~= true then
			ngx.log(ngx.WARN, string.format("file name: %s, remove file failed, error is %s", tmp_cache_fname, err))
		end
	end
	return true
end

return _M