local ngx_worker_cache = require "ngx_worker_cache"
local breaker = require "breaker"
local conf = require "conf"
local utils = require "utils"
local string = string
local tonumber = tonumber
local tostring = tostring

local _M = {
	["doc"] = "ufc fault inject",
	["fault_bns"] = "fault-bns.all",
	["max_len"] = 16,
	["uid_len"] = 12,
	["new_uid_len"] = 6,
}

--[[ 
	在沙盒中且服务没有注册检查是否命中沙盒小流量配置
]]
function _M.service_not_exist_sandbox_check(from_service_name, to_service_name, logid)
	--在沙盒中且服务没有注册
	local service_meta = {}
	local sandbox_ip, sandbox_port = _M.sandbox_check(from_service_name, to_service_name, logid)
	if sandbox_ip and sandbox_port then
		--服务没有注册,返回一个假bns
		service_meta["bns"] = "sandbox_bns"
		--这里如果header里面没有重试次数，直接返回conf.lua里面的
		service_meta["backends_max_tries"] = conf.get_max_retries(nil)
		--用access日志中的fault字段标识触发了沙盒小流量策略
		ngx.var.ufc_fault_injection = "sandbox"
		return service_meta, sandbox_ip, sandbox_port
	end

	--服务没有注册也没有匹配到沙盒策略直接返回
	return nil, nil, nil
end

--[[
	检查是否命中沙盒小流量配置
]]
function _M.sandbox_check(from_service, to_service, logid)
	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		return nil, nil
	end

	local configs = static_cache["configs"]
	if not configs then
		return nil, nil 
	end 

	return _M.sandbox_backend_select(configs, from_service, to_service)
end

function _M.logid_to_uid(logid)
	if not logid or #logid < _M["uid_len"] then
		return nil
	end

	local start = _M["max_len"] - _M["uid_len"] + 1
	local len = #logid
	if len == _M["max_len"] and string.sub(logid, 5, 6) == "66" then
		start = _M["max_len"] - _M["new_uid_len"] + 1
	end

	--把前面的0去掉
	while(true)
	do
		if start >= len then
			--全是0
			return nil
		end
		if string.char(string.byte(logid, start)) == "0" then
			start = start + 1
		else
			break
		end
	end
	return string.sub(logid, start, -1)
end

function _M.sandbox_backend_select(configs, from_service, to_service)
	local ip, port = _M.advanced_sandbox_backend_select(configs, from_service, to_service)
	if ip ~= nil and port ~= nil then
		return ip, port
	end


	ip, port = _M.sandbox_backend_select_by_uid(configs, from_service, to_service)
	if ip ~= nil and port ~= nil then
		return ip, port
	end
	
	return nil, nil 
end 

function _M.advanced_sandbox_backend_select(configs, from_service, to_service)
	local advanced_sandbox_config = configs["advanced_sandbox"]
	if not advanced_sandbox_config or not advanced_sandbox_config["enable"] or advanced_sandbox_config["enable"] < 1 then 
		return nil, nil 
	end 


	local link = advanced_sandbox_config["link"]
	if not link then
		return nil, nil 
	end 

	local from_ip = _M.get_from_ip()
	if not link[from_ip] then
		return nil, nil
	end 
	
	-- from_service 可以不传
	local config = link[from_ip][from_service]
	if not config then 
		config = link[from_ip][""]
		if not config then 
			return nil, nil
		end
	end 
	
	if not config["deadline"] or config["deadline"] < ngx.now() then
		return nil, nil 
	end

	if not config["to"] or not config["to"][to_service] then 
		return nil, nil 
	end 
	
	local to_service_config = config["to"][to_service]
	local default_ip, default_port = utils.split_backend(to_service_config["default"])
	local route_rule = to_service_config["route"]
	if not route_rule then
		return default_ip, default_port
	end 

	local url_path = ngx.var.uri
	local url_path_config = route_rule[url_path]
	if not url_path_config then
		return default_ip, default_port
	end

	local matched = false
	local query_strings = url_path_config["query_string"]
	if not query_strings then
		matched = true
	end 

	if not matched and query_strings ~= nil and _M.match_query_string(query_strings) then 
		matched = true 
	end 

	if matched then 
		local ip, port = utils.split_backend(url_path_config["to_ip"])
		if ip ~= nil and port ~= nil then
			return ip, port
		end
	end 

	return default_ip, default_port
end 

function _M.match_query_string(query_strings)
	if not query_strings then
		return true 
	end

	local args = ngx.req.get_uri_args()
	for key, value in pairs(query_strings) do
		if not args[key] or args[key] ~= value then 
			return false
		end 
	end
	
	return true 
end 

function _M.get_from_ip()
	local from_ip = ngx.var.remote_addr
	if from_ip == "127.0.0.1" then
		from_ip = ngx_worker_cache["local_ip"]
	end 
	return from_ip
end

function _M.sandbox_backend_select_by_uid(configs, from_service, to_service)
	local sandbox_config = configs["sandbox"]
	if not sandbox_config or not sandbox_config["enable"] or sandbox_config["enable"] < 1 then
		return nil, nil
	end

	local logid = ngx.var.ufc_logid
	if not from_service or not to_service or not logid then
		return nil, nil
	end

	if sandbox_config["key"] ~= "uid" then
		--匹配规则不是uid的情况以后再扩展
		return nil, nil
	end


	local link = sandbox_config["link"]
	local uid = _M.logid_to_uid(logid)

	if not link or not link[uid] then
		return nil, nil
	end

	local uid_link = link[uid]
	local to_ips = nil
	local uri = ngx.var.uri

	for _, v in ipairs(uid_link) do
		if not v["from_service"] or not v["to_service"] or not v["to_ip"] then
			goto done
		end

		if v["from_service"] ~= from_service and v["from_service"] ~= "all" then
			goto done
		end

		if v["to_service"] ~= to_service and v["to_service"] ~= "all" then
			goto done
		end

		--uri前缀匹配，有这个配置才做检查，没有不做
		if v["uri_prefix"] then
			--这里仅仅关心第一个匹配返回结果，不用管err信息
			--做前缀正则
			local m = ngx.re.match(uri, v["uri_prefix"] .. ".*")
			if not m then
				--前缀没有匹配到
				goto done
			end
		end

		--匹配到了
		to_ips = v["to_ip"]
		break

		:: done ::
	end

	if type(to_ips) ~= type({}) or #to_ips <= 0 then
		return nil, nil
	end

	return _M._random_select(to_service, to_ips)
end

function _M._random_select(ufc_service_name, to_ips)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	local backends_cnt = #to_ips

	local host, port, backend, index
	local max_tries = conf["random_max_tries"] or backends_cnt

	local info
	for i = 1, max_tries do
		index = math.random(1, backends_cnt)
		backend = to_ips[index]
		host, port = utils.split_backend(backend)
		if host and port then
			return host, port
		end
	end

	return nil, nil
end

return _M