local utils = require "utils"
local trace = require "trace"

--local cjson = require "cjson.safe"
local ngx_worker_cache = require "ngx_worker_cache"

local conf = require "conf"
local dynamic_conf = require "dynamic_conf"
local tonumber = tonumber
local table = table

local ctxs = ngx.ctx.ufc_ctxs or {}

--[[
local ufc_service_name = ngx.ctx.ufc_service_name or "-"
local ori_ufc_service_name = ngx.ctx.ori_ufc_service_name or "-"
local from_service_name = ngx.ctx.from_service_name or "-"
--]]

local ufc_service_name = ctxs["ufc_service_name"] or "-"
local ori_ufc_service_name = ctxs["ori_ufc_service_name"] or "-"
local from_service_name = ctxs["from_service_name"] or "-"
local ufc_time = ctxs["ufc_time"]

local ufc_backup = utils.is_backup()
local callid = ngx.var.ufc_callid

--local bnss = ngx.ctx.bnss or {"-"}
local bnss = ctxs["bnss"] or {"-"}

--local from_idc = ngx.ctx.from_idc
local from_idc = ctxs["from_idc"] or "-"

local from_logic_idc = "-"
if from_idc and conf["idc_map"][from_idc] then
	from_logic_idc = conf["idc_map"][from_idc]
end

local to_logic_idc = ngx.ctx.idc or "-"
local platforms_idc = ctxs["idc"] or "-"
local to_physics_idc = ctxs["to_physics_idc"] or "-"

--local method = ngx.ctx.method
local method = ctxs["method"] or ""
if type(method) == "table" then
	method = method[#method]
end

local var_upstream_addr = ngx.var.upstream_addr
--stream 模块的upstream耗时是这个变量
local var_upstream_response_time = ngx.var.upstream_session_time
--nginx从后端收到的字节数
local var_upstream_bytes_received = ngx.var.upstream_bytes_received
local upstream_status = {}
local upstream_addr, upstream_response_time, upstream_bytes_received
local ufc_log = "to_module=" .. ufc_service_name .. " ufc_backup=" .. ufc_backup .. " from_service=" .. from_service_name ..
" to_service=" .. ufc_service_name .. " ori_to_service=" .. ori_ufc_service_name .. " api=" .. "/stream"..
" method=" .. method .. " from_idc=".. from_logic_idc .. " interact=["  -- TODO 可以通过is_bypass来区分是否旁路式和穿透式

--stream模块没有状态码这个东西了，需要手动判断
if var_upstream_addr then
	if string.sub(var_upstream_addr, -1, -1) == " " then
		upstream_addr = utils.explode(", ", string.sub(var_upstream_addr, 1, -4))
		upstream_response_time = utils.explode(", ", string.sub(var_upstream_response_time, 1, -4))
		upstream_bytes_received = utils.explode(", ", string.sub(var_upstream_bytes_received, 1, -4))
	else
		upstream_addr = utils.explode(", ", var_upstream_addr)
		upstream_response_time = utils.explode(", ", var_upstream_response_time)
		upstream_bytes_received = utils.explode(", ", var_upstream_bytes_received)
	end

	--接下来根据策略手动判断upstram状态码
	if upstream_addr then
		for i = 1, #upstream_addr do
			--如果有重试，可以认定本次upstream状态码是502(只会对连接失败重试)
			if #upstream_addr > i and upstream_addr[i + 1] ~= nil then
				upstream_status[i] = "502"
				goto done
			end

			--没有重试就根据后端发送数据的字节数来判断是否是执行超时(没有收到下游发送过来的数据肯定是有问题的)
			if upstream_bytes_received[i] and tonumber(upstream_bytes_received[i]) == 0 then
				upstream_status[i] = "504"
				goto done
			end
			--剩下的情况都认为是200状态码
			upstream_status[i] = "200"
			:: done ::
		end
	end

	--ngx.log(ngx.DEBUG, "var_upstream_status is " .. var_upstream_status .. " upstream_addr is " .. var_upstream_addr .. " upstream_response_time is " .. var_upstream_response_time)
	local logid = ngx.var.ufc_logid or "-"

	--ngx.log(ngx.NOTICE, "logid " ..logid.." var_upstream_status is " .. cjson.encode(upstream_status) .. " upstream_addr is " .. cjson.encode(upstream_addr) .. " upstream_response_time is " .. cjson.encode(upstream_response_time))

	if upstream_addr then
		for i=1,#upstream_addr do
			--对于双端模式log穿透式阶段做额外的处理，双端模式的上游ufc端口替换成真实端口，下游不进行request_call back计数
			local backend = upstream_addr[i]
			if ctxs["double_ends"] and ctxs["double_ends"] == 0 then
				--双端模式的上游替换
				backend = dynamic_conf.port_replace(backend, ctxs)
			end

			local double_ends_down = ctxs["double_ends"] or -1

			ufc_log = ufc_log .. "[callid=" .. callid  .. " cost_time=" .. upstream_response_time[i] .. " to_module=" .. ufc_service_name .. " to_bns=" .. bnss[i] .. " to_ip=" .. backend .. " to_idc=" .. to_physics_idc .. " upstream_status=" .. upstream_status[i] .."]"
			--双端模式的下游不进行request计数
			if conf["backup_ufc"] ~= backend and backend ~= "-" and double_ends_down ~= 1 then
				if platforms_idc and platforms_idc == "-" then
					dynamic_conf.request_callback(from_service_name, ufc_service_name, bnss[i], backend, tonumber(upstream_status[i]), upstream_response_time[i], 0, nil)
				else
					dynamic_conf.request_callback(from_service_name, ufc_service_name, bnss[i], backend, tonumber(upstream_status[i]), upstream_response_time[i], 0, to_logic_idc)
				end
			end

			--log trace
			--local logs = trace.get_trace_log(logid, callid, 0, upstream_addr[i], from_service_name, ufc_service_name, upstream_response_time[i], upstream_status[i], ufc_time)

			--local cache
			--trace.set_local_logs(logs)

			--cloud cache
			--if trace.is_simple(logs) then
				--trace.set_cloud_logs(logs)
			--end
		end
	end
end

local ufc_cost_time = ctxs["ufc_cost_time"] or 0
ngx.var.ufc_log = " ufc_time=" .. ufc_cost_time .. " " .. ufc_log.. "]"

