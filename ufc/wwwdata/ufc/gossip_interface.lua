local ngx_worker_cache = require "ngx_worker_cache"
local utils = require "utils"
local cjson = require "cjson.safe"
local gossip_utils = require "gossip_utils"
local shared_memory = require "shared_memory"

local _M = {
	["doc"] = "ufc fault inject",
}

function _M.output(res, status)
	res["status"] = status
	ngx.status = status 
	ngx.say(cjson.encode(res))
end

function _M.start(args, res)
	res["token"] = gossip_utils["token"]
	local peer_mtime = args["mtime"]
	local token = args["token"]

	if not peer_mtime or not token or token ~= gossip_utils["token"] then
		res["res"] = "invalid_args"
		return _M.output(res, 400)
	end

	--配置已经关掉了拒绝gossip扩散请求
	if not gossip_utils.check_gossip_enable() then
		ngx.log(ngx.NOTICE, "gossip protocol no enable, reject this request")
		res["res"] = "end"
		return _M.output(res, 200)
	end

	res["res"] = "end"
	--没有global mtime肯定要更新
	local local_mtime = shared_memory.get_global_mtime() or -1
	--比远端机器配置新, 不用更新
	if tonumber(local_mtime) >= tonumber(peer_mtime) then
		res["res"] = local_mtime
		return _M.output(res, 200)
	end
		
	--正在从redis或者从其他peer拉配置拒绝更新
	if not gossip_utils.config_update_lock() then
		res["res"] = "end"
		return _M.output(res, 200)
	end
	--本地比远端机器老，共享内存设一个值，告诉特权进程从peer拉配置
	gossip_utils.set_gossip_flag(peer_mtime, ngx.var.remote_addr)
	res["res"] = peer_mtime
	return _M.output(res, 200)
end


function _M.get(args, res)
	res["token"] = gossip_utils["token"]
	local config_type = args["config_type"]
	local key = args["key"]
	if not config_type or not key then
		ngx.status = 400 
		ngx.say("invalid_args")
		return
	end
	local config
	--获取global mtime要特殊处理
	if key == "mtime" then
		config = shared_memory.get_global_mtime()
	elseif key == "batch" then
		--batch 接口
		config = gossip_utils.batch_get_config_from_cache(config_type)
	else
		config = shared_memory.get_static_data(config_type, key)
	end
	if not config then
		res["res"] = "NULL"
		return _M.output(res, 404)
	end
	res["res"] = config
	return  _M.output(res, 200)
end

function _M.main()
	local start_time =  os.clock()
	ngx.var.ufc_time = math.floor(ngx.now())
	ngx.ctx.ufc_ctxs = {}
	local ctxs = ngx.ctx.ufc_ctxs 

	local args, cmd
	args = ngx.req.get_uri_args()
	local headers = ngx.req.get_headers()
	local res = utils.gen_ufc_log(headers, ctxs, args)

	if not ngx_worker_cache.is_inited() then
		res["error_msg"] = "E_RETRY"
		_M.output(res)
		return
	end

	cmd = args["cmd"]
	ctxs["method"] = cmd
	ctxs["gossip"] = true
	ctxs["ufc_service_name"] = headers["x-ufc-service-name"]
	ctxs["from_service_name"] = headers["x-ufc-self-service-name"]
	ctxs["ori_ufc_service_name"] = headers["x-ufc-service-name"]
	--ngx.log(ngx.DEBUG, string.format("bypass cmd is %s", cmd))
	if not _M.cmds[cmd] then
		res["error_msg"] = "E_NO_SUPPORT"
		_M.output(res)
	else
		_M.cmds[cmd](args, res, ctxs) -- args是get_uri_args()
	end

	local end_time = os.clock()
	ctxs["ufc_cost_time"] = math.floor((end_time - start_time) * 1000 * 1000) * 0.001
end

_M["cmds"] = {
	["start"] = _M.start,
	["get"] = _M.get
}

_M.main()