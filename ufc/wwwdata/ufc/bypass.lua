local math = require "math"
local cjson = require "cjson.safe"

local utils = require "utils"
local conf = require "conf"
local moniter = require "moniter"
local dynamic_conf = require "dynamic_conf"
local ngx_worker_cache = require "ngx_worker_cache"
local fault = require "fault"

local _M = {
	["version"] = "0.2",
	["doc"] = "ufc bypass mode, return host:port for a service_name",
}

function _M.output(res)
	local status = conf["http_code"][res["error_msg"]]
	res["status"] = status
	ngx.status = status 
	ngx.say(cjson.encode(res))
end

function _M.get_backend(headers, args, res, ctxs) -- args是get_uri_args()
	local service_meta, backup
	local bns, host, port, backend
	local error_msg, ori_service_name, from_service_name
	local not_exist_service_in_sandbox = false

	local ufc_service_name = args["x-ufc-service-name"]
	if ufc_service_name == nil then
		res["error_msg"] = "E_BAD_FORMAT"
		return _M.output(res)
	end


	----------------------------------------------------------------------------------------------------
	--check whether ufc_service_name registered
	from_service_name, ufc_service_name, ori_service_name = dynamic_conf.get_ufc_service_name(nil, headers)

	--地域合法性校检查
	local verification_flag = ngx_worker_cache.request_verification(from_service_name, ufc_service_name)
	if verification_flag == false then
		return
	end

	--在沙盒环境中服务没有注册也可以访问
	if conf["sandbox"] == true then
		if ori_service_name ~= nil and ufc_service_name == nil then
			ufc_service_name = ori_service_name
			not_exist_service_in_sandbox = true
		end
	end

	if ufc_service_name == nil then
		res["error_msg"] = "E_NO_SERVICE"
		return _M.output(res)
	end
	----------------------------------------------------------------------------------------------------

	----------------------------------------------------------------------------------------------------
	--check is limit over
	error_msg = dynamic_conf.check_flow_limit(from_service_name, ufc_service_name, headers)
	if error_msg ~= nil then
		res["error_msg"] = error_msg
		return _M.output(res)
	end
	----------------------------------------------------------------------------------------------------

	ctxs["ufc_service_name"] = ufc_service_name
	ctxs["ori_ufc_service_name"] = ori_service_name
	ctxs["from_service_name"] = from_service_name

	local pass
	service_meta, host, port, pass = dynamic_conf.select_backend(ufc_service_name, headers, 1, nil, not_exist_service_in_sandbox)
	if pass then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s server %s message %s", logid, ufc_service_name, "service is lawine"))
		res["error_msg"] = "E_DEGRADE_4XX"
		return _M.output(res)
	end
	
	--旁路式故障注入判断
	local fault_injection_result, fault_injection_flag, fault_id
	local from_idc = ctxs["from_idc"]
	fault_injection_flag, fault_injection_result, fault_id = fault.fault_injection_check(from_service_name, ufc_service_name,from_idc, "up")
	if fault_injection_flag then
		local res = fault.bypass_fault_deal(fault_injection_result, res, ctxs, ufc_service_name, from_service_name)
		if res then
			ngx.var.ufc_fault_injection = fault_id
			return _M.output(res)
		end
	end

	--ngx.log(ngx.DEBUG, string.format("for ufc_service_name %s service_meta is %s", ufc_service_name, cjson.encode(service_meta)))
	if service_meta == nil then
		res["error_msg"] = "E_NO_SERVICE"
		return _M.output(res)
	end

	bns = service_meta["bns"]
	if bns == nil then
		res["error_msg"] = "E_NO_BNS"
		return _M.output(res)
	end

	ctxs["bns"] = bns
	if host == nil or port == nil then
		host = host or "-"
		port = port or "-"
		res["error_msg"] = "E_NO_BACKEND"

		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s bns %s host %s port %s server %s message %s", logid, bns, host, port, ufc_service_name, "select backend faild"))
		return _M.output(res)
	end

	backend = host .. ":" .. port
	ctxs["backend"] = backend

	-- ctxs 是 platforms-idc 其中 idc 是逻辑idc
	local platforms_idc = ctxs["idc"]
	local is_bypass = 1
	--如果走stram模式http模块就不用做计数处理
	if port ~= conf["ufc_stream_port"] then
		dynamic_conf.request_before(ufc_service_name, bns, platforms_idc, backend, is_bypass)
	end

	backup = dynamic_conf.get_backup(ufc_service_name)
	if backup ~= nil then
		res["backup"] = backup
	end

	res["host"] = host
	res["port"] = port
	res["bns"] = bns
	res["platform_idc"] = platforms_idc
	
	if service_meta["protocol"] then
		res["protocol"] = service_meta["protocol"]
	end

	if service_meta["options"] then
		res["options"] = service_meta["options"]
	end

	res["x-ufc-service-name"] = ufc_service_name
	res["x-ufc-self-service-name"] = from_service_name

	res["error_msg"] = "E_OK"
	return _M.output(res)
end

function _M._add_extra_info_for_master(res, entire_config, bns)
	if entire_config["bns_list"][bns] ~= nil then
		res["protocol"] = entire_config["protocol"]
		res["options"] = entire_config["options"]
		return
	end

	local small_flow = entire_config["small_flow"]
	if small_flow == nil then
		return
	end

	for small_flow_config in ipairs(small_flow) do
		if small_flow_config["bns_list"][bns] ~= nil then
			res["protocol"] = small_flow_config["protocol"]
			res["options"] = small_flow_config["options"]
		end
	end
end

function _M.callback(headers, args, res, ctxs)  -- args是get_uri_args()
	local data_encode, data, err
	local ufc_service_name, bns, from_service_name, ori_service_name
	local host, port, backend, http_code, platform_idc

	--ngx.log(ngx.DEBUG, "args is %s" .. cjson.encode(args))
	data_encode = args["data"]
	if data_encode == nil then
		ngx.req.read_body()
		data_encode = ngx.req.get_post_args()["data"]
		if data_encode == nil then
			data_encode = utils.get_post_params()["data"]
			if data_encode == nil then
				--ngx.log(ngx.DEBUG, "no data in post && get param, return 511") -- TODO 这里明明是400的返回
				res["error_msg"] = "E_BAD_FORMAT"
				return _M.output(res)
			end
		end
	end

	data, err = cjson.decode(data_encode)
	if err ~= nil then
		res["error_msg"] = "E_BAD_FORMAT"
		return _M.output(res)
	end

	ufc_service_name = data["x-ufc-service-name"]
	if ufc_service_name == nil then
		res["error_msg"] = "E_BAD_FORMAT"
		return _M.output(res)
	end

	if not headers["x-ufc-service-name"] then
		headers["x-ufc-service-name"] = ufc_service_name
	end

	if not headers["x-ufc-self-service-name"] then
		headers["x-ufc-self-service-name"] = data["x-ufc-self-service-name"] or "-"
	end

	from_service_name, ufc_service_name, ori_service_name = dynamic_conf.get_ufc_service_name(nil, headers)
	if ufc_service_name == nil then
		res["error_msg"] = "E_NO_SERVICE"
		return _M.output(res)
	end

	host = data["host"]
	port = tonumber(data["port"])
	bns = data["bns"]
	if data["platform_idc"] and data["platform_idc"]~= "" then 
		platform_idc = data["platform_idc"]
	end 
	if host == nil or port == nil or bns == nil then
		res["error_msg"] = "E_BAD_FORMAT"
		return _M.output(res)
	end

	backend = host .. ":" .. port
	http_code = tonumber(args["http_code"])

	-- grep -r "http_code=5" access.log.2024082111  | grep "pass-session" | grep -v "2024:11:5" | grep -v "2024:12:00"
	ctxs["backend"] = backend
	ctxs["bns"] = bns
	ctxs["iscallback"] = 1
	ctxs["ufc_service_name"] = ufc_service_name
	ctxs["from_service_name"] = from_service_name 

	if data["ufc_logid"] then
		ngx.var.ufc_logid = data["ufc_logid"]
	end

	ctxs["status"] = http_code

	--如果走stream代理，业务的callback不会进行计数
	if port ~= conf["ufc_stream_port"] then
		dynamic_conf.request_callback(from_service_name, ufc_service_name, bns, backend, http_code,  0, 1, platform_idc)
	end

	res["error_msg"] = "E_OK"

	return _M.output(res)
end

-- this method is used to print config value in mem
function _M.debug(headers, args)
	local c, ks, v
	local m = args["model"]
	if not m then
		c = ngx_worker_cache
	else
		c = moniter
	end

	local k = args["key"]
	if k == nil then
		ngx.say(cjson.encode(c))
	else
		ks = utils.explode("^", k)
		v = c
		for _, ck in pairs(ks) do
			if type(v) ~= type({}) then
				ngx.say(cjson.encode({[k] = nil}))
				return ngx.exit(200)
			end
			v = v[ck]
		end
		ngx.say(cjson.encode({[k] = v}))
	end
end

-- this method is used to print config value in mem
function _M.moniter_api(headers, args)
	local size = args["size"] or 10 
	local ufc_service_name = args["x-ufc-service-name"]
	if not ufc_service_name then
		ngx.say(cjson.encode({}))
		return ngx.exit(200)
	end

	size = tonumber(size)
	local data = moniter.rate_api(ufc_service_name, size)
	ngx.say(cjson.encode(data))
end

function _M.main()
	local start_time =  os.clock()
	ngx.var.ufc_time = math.floor(ngx.now())
	--旁路式只有小流量选择逻辑强依赖一些header
	--这里我们加个0强制把所有header读出来
	local headers = ngx.req.get_headers(0)

	ngx.ctx.ufc_ctxs = {}
	local ctxs = ngx.ctx.ufc_ctxs 

	local args, cmd
	args = ngx.req.get_uri_args()
	local res = utils.gen_ufc_log(headers, ctxs, args)

	if not ngx_worker_cache.is_inited() then
		res["error_msg"] = "E_RETRY"
		_M.output(res)
		return
	end

	if not headers["x-ufc-service-name"] and args["x-ufc-service-name"] then
		headers['x-ufc-service-name'] = args["x-ufc-service-name"]
	end

	if headers["x-ufc-sdk-version"] then
		ngx.var.ufc_sdk_version = headers["x-ufc-sdk-version"]
	end

	if headers["x-ufc-request-send-time"] then
		ngx.var.ufc_client_costtime = string.format("%.2f", ngx.now() - headers["x-ufc-request-send-time"])
	end
	ctxs["from_platform"] = headers["x-ufc-platform-value"] 
	ctxs["from_app"] = headers["x-ufc-app-value"]

	cmd = args["cmd"]
	ctxs["method"] = cmd
	--ngx.log(ngx.DEBUG, string.format("bypass cmd is %s", cmd))
	if not _M.cmds[cmd] then
		res["error_msg"] = "E_NO_SUPPORT"
		_M.output(res)
	else
		_M.cmds[cmd](headers, args, res, ctxs) -- args是get_uri_args()
	end

	local end_time = os.clock()
	ctxs["ufc_cost_time"] = math.floor((end_time - start_time) * 1000 * 1000) * 0.001
end

_M["cmds"] = {
	["get_backend"] = _M.get_backend,
	["callback"] = _M.callback,
	["debug"] = _M.debug,
	["moniter_api"] = _M.moniter_api
}

_M.main()
