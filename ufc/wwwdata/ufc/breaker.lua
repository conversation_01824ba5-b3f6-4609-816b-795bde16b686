--local cjson = require "cjson.safe"

local ngx_worker_cache = require "ngx_worker_cache"
local idc_map = require "idc_map"
local moniter = require "moniter"
local conf = require "conf"
local check_forbid_list = require "check_forbid_list"
local shared_memmory = require "shared_memory"

local _M = {
	["doc"] = "breaker process for ufc",
}

--[[
@comment 初始化单机数据
@param string ufc_service_name
@param string backend
@param bool force
]]
function _M.init_backend(ufc_service_name, backend, force)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	--应该不会走到这个逻辑，但是保险起见确认下
	if not dy_config["backend"] then
		dy_config["backend"] = {}
	end

	if (not force) and dy_config["backend"][backend] then
		return
	end


	

	if not dy_config["backend"][backend] then
		dy_config["backend"][backend] = {}
	end

	if dy_config["backend"][backend]["forbidden"] == nil then
        dy_config["backend"][backend]["forbidden"] = {}
    end

	local backend_info = dy_config["backend"][backend]
	backend_info["ctime"] = math.floor(ngx.now())
	backend_info["request_cnt"] = 0
	backend_info["success_cnt"] = 0
	backend_info["fail_cnt"] = 0
	backend_info["first_request_time"] = nil
	--简单粗暴的将所有状态码范围置成0即可
	backend_info["1xx"] = 0
	backend_info["2xx"] = 0
	backend_info["3xx"] = 0
	backend_info["4xx"] = 0
	backend_info["5xx"] = 0
	backend_info["other_status"] = 0
	--读写共享内存和luavm内存此处处理逻辑不同
	if conf["meta_table"] then
		shared_memmory.clear_backend_response_time(ufc_service_name, backend)
	else
		backend_info["response_times"] = {}
	end
	backend_info["ratio_request_cnt"] = 0
end

--[[
@comment 重置单机计数数据(不包括环比以及耗时)
@param string ufc_service_name
@param string backend
@param bool force
]]
function _M.reset_backend_count_data(ufc_service_name, backend)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]

	if not dy_config["backend"][backend] then
		dy_config["backend"][backend] = {}
	end

	if dy_config["backend"][backend]["forbidden"] == nil then
        dy_config["backend"][backend]["forbidden"] = {}
    end

	local backend_info = dy_config["backend"][backend]
	backend_info["request_cnt"] = 0
	backend_info["success_cnt"] = 0
	backend_info["fail_cnt"] = 0
	backend_info["first_request_time"] = nil
end

--[[
@comment 重置单机环比计数数据(不包括请求计数，成功计数，失败次数)
@param string ufc_service_name
@param string backend
]]
function _M.reset_backend_ratio_count_data(ufc_service_name, backend)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]

	if not dy_config["backend"][backend] then
		dy_config["backend"][backend] = {}
	end

	if dy_config["backend"][backend]["forbidden"] == nil then
        dy_config["backend"][backend]["forbidden"] = {}
    end

	local backend_info = dy_config["backend"][backend]
	backend_info["1xx"] = 0
	backend_info["2xx"] = 0
	backend_info["3xx"] = 0
	backend_info["4xx"] = 0
	backend_info["5xx"] = 0
	backend_info["other_status"] = 0
	--读写共享内存和luavm内存此处处理逻辑不同
	if conf["meta_table"] then
		shared_memmory.clear_backend_response_time(ufc_service_name, backend)
	else
		backend_info["response_times"] = {}
	end
	backend_info["ratio_request_cnt"] = 0
end

--[[
@comment 初始化service数据
@param string ufc_service_name
@param bool force
]]
function _M.init_service(ufc_service_name, force)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if (not force) and dy_config["service"] then
		return
	end

	if not dy_config["service"] then
		dy_config["service"] = {}
	end

	dy_config["service"]["ctime"] = math.floor(ngx.now())

	if not dy_config["service"]["forbidden_cnt"] then
		dy_config["service"]["forbidden_cnt"] = {} 
	end

	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	for bns, _ in pairs(service_config["bns_list"]) do
		dy_config["service"][bns] = {}
	end

	--small flow
	if service_config["small_flow"] then
		for _, small in pairs(service_config["small_flow"]) do
			if small["bns_list"] then
				for bns, _ in pairs(small["bns_list"]) do
					dy_config["service"][bns] = {}
				end
			end
		end
	end
end

--判断backend request_cnt计数是否到达最大值，达到就重置
--主要是为了防止旁路式业务不callback， request cnt无限增长（其实无限增长也不会有啥问题）
function _M.max_request_cnt_check(ufc_service_name, backend)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		 return
	end

	local backend_info = dy_config["backend"][backend]
	if not backend_info then
		return
	end

	if backend_info["request_cnt"] and backend_info["request_cnt"] >= conf["max_request_cnt_number"] then
		_M.reset_backend_count_data(ufc_service_name, backend)
		ngx.log(ngx.WARN, string.format("service: %s, backend: %s, request_cnt:%d reach max count", ufc_service_name, backend, backend_info["request_cnt"]))
	end

	return
end

--[[
@comment backend计数 
@param string ufc_service_name
@param string backend
@param string service_type
@param int delta
@return bool
]]


function _M.change_backend_cnt(ufc_service_name, backend, cnt_type, delta, bypass)
	if cnt_type == "499" then
		--如果是499状态码就直接返回不计数了
		return
	end
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]

	if not dy_config["backend"][backend] then
		dy_config["backend"][backend] = {}
	end

	local backend_info = dy_config["backend"][backend]
	--读写共享内存和luavm内存此处处理逻辑不同
	if conf["meta_table"] then
		shared_memmory.add_backend_cnt(ufc_service_name, "backend", backend, cnt_type, delta)
	else
		if not backend_info[cnt_type] then
			backend_info[cnt_type] = 0
		end
		backend_info[cnt_type] = backend_info[cnt_type] + delta
	end

	--backend计数不重置收益统计
	if not backend_info["first_request_time"] then
		backend_info["first_request_time"] = ngx.now()
	end

	--http_code范围计数和success，fail计数是并行进行的，增加request的时候不要重复计算
	--如果是http_code范围计数，temp_str=x
	local temp_str = string.sub(cnt_type, 2, 2) or "a"
	if cnt_type ~= "request_cnt" and temp_str ~= "x" then
		--读写共享内存和luavm内存此处处理逻辑不同
		if conf["meta_table"] then
			shared_memmory.add_backend_cnt(ufc_service_name, "backend", backend, "request_cnt", delta)
		else
			if not backend_info["request_cnt"] then
				backend_info["request_cnt"] = 0
			end
	
			if not backend_info["ratio_request_cnt"] then
				backend_info["ratio_request_cnt"] = 0
			end
	
			backend_info["request_cnt"] = backend_info["request_cnt"] + delta
		end
		--旁路式服务不对状态码分布以及耗时数据进行统计
		if bypass == 0 then
			--读写共享内存和luavm内存此处处理逻辑不同
			if conf["meta_table"] then
				shared_memmory.add_backend_cnt(ufc_service_name, "backend", backend, "ratio_request_cnt", delta)
			else
				backend_info["ratio_request_cnt"] = backend_info["ratio_request_cnt"] + delta
			end
		end
	end

	if delta == 0 then
		backend_info[cnt_type] = 0
	end

	--moniter
	moniter.backend_request_cnt(ufc_service_name, cnt_type, backend)

	--ngx.log(ngx.DEBUG, string.format("[logid=%s] [backend=%s] [type=%s] [backend=%s]", ngx.var.ufc_logid, backend, cnt_type, cjson.encode(dy_config["backend"][backend])))
end

--[[
@comment service计数
@param string ufc_service_name
@param string bns
@param string idc
@param string service_type
@param int delta
]]
function _M.change_service_cnt(ufc_service_name, bns, idc, service_type, delta, bypass)
	local bypass_value = bypass or 1
	-- ngx_worker_cache["dynamic_cache"][ufc_service_name]["service"] 为读写共享内存
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config["service"][bns] then
		dy_config["service"][bns] = {}
	end

	if not dy_config["service"][bns][idc] then
		dy_config["service"][bns][idc] = {}
	end

	
	local idc_info = dy_config["service"][bns][idc]
	if delta == 0 then
		-- reset dy_config["service"][bns][idc][service_type], 其中 service_type == "success_cnt", "failed_cnt", "request_cnt"
		idc_info[service_type] = 0
		return
	end
	--旁路式不对service进行计数
	if bypass_value == 1 then
		return
	end
	--读写共享内存和luavm内存此处处理逻辑不同
	if conf["meta_table"] then
		shared_memmory.add_service_cnt(ufc_service_name, "service", bns, idc, service_type, delta)
	else
		if not idc_info[service_type] then
			idc_info[service_type] = 0
		end
		idc_info[service_type] = idc_info[service_type] + delta
	end

	if service_type ~= "request_cnt" then
		--读写共享内存和luavm内存此处处理逻辑不同
		if conf["meta_table"] then
			shared_memmory.add_service_cnt(ufc_service_name, "service", bns, idc, "request_cnt", delta)
		else
			if not idc_info["request_cnt"] then
				idc_info["request_cnt"] = 0
			end
			idc_info["request_cnt"] = idc_info["request_cnt"] + delta
		end
	end
end

--[[
@comment 计数器是否超时
@param string ufc_service_name
@param string backend
]]
function _M.check_service_cnt_timeout(ufc_service_name)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	local request_reset_interval = service_config["request_reset_interval"]

	if not dy_config["service"] then
		dy_config["service"] = {}
	end

	if not dy_config["service"]["ctime"] then
		dy_config["service"]["ctime"] = ngx.now()
	end

	if math.floor(ngx.now()) - dy_config["service"]["ctime"] >= request_reset_interval then
		_M.init_service(ufc_service_name, true)
	end
end

--[[
@comment 计数器是否超时
@param string ufc_service_name
@param string backend
]]
function _M.check_backend_cnt_timeout(ufc_service_name, backend)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	local request_reset_interval = service_config["request_reset_interval"]

	if backend and math.floor(ngx.now()) - dy_config["backend"][backend]["ctime"] >= request_reset_interval then
		_M.init_backend(ufc_service_name, backend, true)
	end
end


--[[
@comment 单机封禁
@param string ufc_service_name
@param string bns
@param string backend
]]
function _M.forbid_backend(ufc_service_name, bns, backend, platform_idc, forbid_timeout, check_ok_times)
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]

	----------------------------------------------------------------------------------------------------
	--backend计数不重置收益统计,后面去掉
	if dy_config["backend"] and dy_config["backend"][backend] and dy_config["backend"][backend]["first_request_time"] then
		local request_reset_interval = service_config["request_reset_interval"]
		if ngx.now() - dy_config["backend"][backend]["first_request_time"] > request_reset_interval then
			ngx.log(ngx.NOTICE, string.format("UFC_PROFIT ufc_service_name %s backend %s is forbidden", ufc_service_name, backend))
		end
	end
	----------------------------------------------------------------------------------------------------

	if dy_config["backend"][backend] ~= nil and dy_config["backend"][backend]["forbidden"] ~= nil then
		local logid = ngx.var.ufc_logid or "-"
		if dy_config["backend"][backend]["forbidden"][bns] then
			ngx.log(ngx.DEBUG, string.format("logid %s ufc_service_name %s backend %s has forbidden", logid, ufc_service_name, backend))
			return true
		end
		
		local now = math.floor(ngx.now())

		dy_config["backend"][backend]["forbidden"][bns] = true
		dy_config["backend"][backend]["ftime"] = now
		dy_config["backend"][backend]["check_ok_times"] = 0
		--增加单机封禁日志
		ngx.log(ngx.NOTICE, string.format("logid %s ufc_service_name %s backend %s is forbidden forbid timeout is %ds", logid, ufc_service_name, backend, forbid_timeout))

		if not dy_config["service"]["forbidden_cnt"][bns] then
			dy_config["service"]["forbidden_cnt"][bns] = {} 
		end

		local forbidden_cnt_map = dy_config["service"]["forbidden_cnt"][bns]

		--全局纬度统计
		if not forbidden_cnt_map["all_idc"] then
			forbidden_cnt_map["all_idc"] = 0 
		end

		forbidden_cnt_map["all_idc"] = forbidden_cnt_map["all_idc"] + 1

		--（单个平台，逻辑idc) 纬度统计
		if platform_idc and platform_idc ~= "all_idc" then
			if not forbidden_cnt_map[platform_idc] then
				forbidden_cnt_map[platform_idc] = 0 
			end

			forbidden_cnt_map[platform_idc] = forbidden_cnt_map[platform_idc] + 1
		end

		--ngx.log(ngx.DEBUG, string.format("[logid=%s] [ufc_service_name=%s] [bns=%s] [backend=%s] [message='forbid backend']", logid, ufc_service_name, bns, backend))
		
		shared_memmory.append_node(ufc_service_name, bns, now, now, backend, forbid_timeout, platform_idc, check_ok_times)

		return true
	end

	return false
end


--[[
@comment 解封确认
@param string ufc_service_name
@param string backend
@return bool, true标识封禁，false标识不封禁
]]
function _M.unforbid_backend_check(ufc_service_name, backend, is_fail)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]

	if not dy_config["backend"][backend] then
		return false
	end

	-- dy_config["backend"][backend]
	-- ngx_worker_cache["dynamic_cache"][ufc_service_name]["backend"][backend]["check_ok_times"]


	local check_times = dy_config["backend"][backend]["check_ok_times"] or 0
	if check_times < 1 then
		--不需要确认
		return false
	end

	--ngx.log(ngx.DEBUG, string.format("[logid=%s] [service_name=%s] [backend=%s] [times=%d]", ngx.var.ufc_logid, ufc_service_name, backend, check_times))
	if is_fail == true then
		-- 即 check_times == 1 时，会封禁，
		if check_times < 2 then
			--连续n次失败，则立即封禁
			return true
		end
		--连续 n次失败
		dy_config["backend"][backend]["check_ok_times"] = check_times - 1
	else
		--不是失败，则立即解除确认
		dy_config["backend"][backend]["check_ok_times"] = 0
	end

	return false
end


--[[
@comment 全量解封
@param string ufc_service_name
@param string bns
@param string idc
@return bool
]]
function _M.reset_backends(ufc_service_name, bns, platform_idc)
	local logid = ngx.var.ufc_logid or "-"

	ngx.log(ngx.NOTICE, string.format("logid %s, check forbid, reset_backends now [service:%s] [idc:%s]", logid, ufc_service_name, platform_idc))

	local backends, backend
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]

	if ngx_worker_cache["static_cache"]["bnss"][bns] == nil then
		return false
	end

	local platforms_idcs
	if platform_idc and platform_idc ~= "all_idc" then
		platforms_idcs = ngx.ctx.platforms_idcmaps
	else
		platforms_idcs = {"all_idc"}
	end

	for _, idc_key in pairs(platforms_idcs) do
		if idc_key == "all_idc" then
			backends = ngx_worker_cache["static_cache"]["bnss"][bns]["bns_nodes"]
		else
			backends = ngx_worker_cache["static_cache"]["bnss"][bns][idc_key]
		end

		if backends and #backends > 0 then
			for _, v in pairs(backends) do
				--每一个后端都解封
				backend = v["host"] .. ":" .. v["port"]
				_M.unforbid_backend(ufc_service_name, backend, bns, platform_idc)
			end
		end
	end

	return true
end



--[[
@comment 单机解封
@param string ufc_service_name
@param string backend
@param string bns
@return bool
]]
function _M.unforbid_backend(ufc_service_name, backend, bns, platform_idc)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if dy_config["backend"][backend] == nil then
		return false
	end

	if dy_config["backend"][backend]["forbidden"] == nil then
		return false
	end

	if not dy_config["backend"][backend]["forbidden"][bns] then
		return false
	end

	-- 直接解封了
	dy_config["backend"][backend]["forbidden"][bns] = false
	--增加解封日志, 这个日志不打了，这个函数只有在熔断解封所有的后端的时候才会调用
	--local logid = ngx.var.ufc_logid or "-"
	--ngx.log(ngx.NOTICE, string.format("logid %s ufc_service_name %s backend %s is unforbidden", logid, ufc_service_name, backend))
	local forbidden_cnt_map = dy_config["service"]["forbidden_cnt"][bns]

	--ngx.log(ngx.DEBUG, string.format("logid %s, begin to unforbid backend, [forbid_info=%s]", ngx.var.ufc_logid, cjson.encode(forbidden_cnt_map)))

	--全量解封
	if forbidden_cnt_map["all_idc"] and forbidden_cnt_map["all_idc"] > 0 then
		forbidden_cnt_map["all_idc"] = forbidden_cnt_map["all_idc"] - 1
	end

	--idc纬度解封 get_backend_platform_and_logic_idc
	if platform_idc and platform_idc ~= "all_idc" then
		if forbidden_cnt_map[platform_idc] and forbidden_cnt_map[platform_idc] > 0 then
			forbidden_cnt_map[platform_idc] = forbidden_cnt_map[platform_idc] - 1
		end
	end

	--ngx.log(ngx.DEBUG, string.format("logid %s, unforbid backend finish, [forbid_info=%s]", ngx.var.ufc_logid, cjson.encode(forbidden_cnt_map)))
	
	--backend数据重置
	_M.init_backend(ufc_service_name, backend, true)

	return true
end

--触发熔断之后所有实例解封
--需要check一下forbidden_cnt是不是0
--如果不是0打日志报警并且强制置0
function _M.check_service_forbid_cnt(ufc_service_name, bns, platforms_idc)
	--最外层已经对idc做遍历了
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end

	if not dy_config["service"] or not dy_config["service"]["forbidden_cnt"] or not dy_config["service"]["forbidden_cnt"][bns] then
		return false
	end

	local cnt_table =  dy_config["service"]["forbidden_cnt"][bns]
	if cnt_table[platforms_idc] and cnt_table[platforms_idc] ~= 0 then
		ngx.log(ngx.WARN, string.format("UFC_BUG, service:%s, bns:%s, idc:%s forbid_cnt:%d is not zero", ufc_service_name, bns, platforms_idc, cnt_table[platforms_idc]))
		cnt_table[platforms_idc] = 0
	end
end

--[[
@comment 获取全局成功率
@param string ufc_service_name
@param string bns
@param string idc
@return bool
]]
function _M.get_service_success_ratio(ufc_service_name, bns, platforms_idcs, min_requests)
	local ratio = 1
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return ratio
	end

	if not dy_config["service"] then
		return ratio
	end

	if not dy_config["service"][bns] then
		return ratio
	end

	if not dy_config["service"][bns][platforms_idcs] then
		return ratio
	end

	local request_cnt = dy_config["service"][bns][platforms_idcs]["request_cnt"]

	--ngx.log(ngx.DEBUG, string.format("[logid=%s] [service=%s] [min_requests=%d]", ngx.var.ufc_logid, ufc_service_name, min_requests))

	if not request_cnt or request_cnt < min_requests then
		return ratio
	end

	local success_cnt = dy_config["service"][bns][platforms_idcs]["success_cnt"]
	if not success_cnt then
		-- 有可能命中该分支
		--ngx.log(ngx.DEBUG, string.format("[logid=%s] [service=%s] [bns=%s] [idc=%s] [success_percent=%s]", ngx.var.ufc_logid, ufc_service_name, bns, idc, 0))
		return 0
	end

	local success_percent = success_cnt / request_cnt

	--ngx.log(ngx.DEBUG, string.format("[logid=%s] [service=%s] [bns=%s] [idc=%s] [success_percent=%s]", ngx.var.ufc_logid, ufc_service_name, bns, idc, success_percent))

	return success_percent
end

--[[
@comment 获取全局指定范围状态码比例, 把指定后端的状态码比例给去掉
@param string ufc_service_name
@param string bns
@param string idc
@return bool
]]
function _M.get_service_status_ratio(ufc_service_name, backend, status)
	--获取该服务下除了指定后端外所有后端的平均状态码比例
	local ratio = -1
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return ratio
	end

	if not dy_config["backend"] then
		return ratio
	end
	local backends_data = dy_config["backend"]

	local backends
	--有哪些后端在dynamic cache里面
	--共享内存和luavm内存此处处理逻辑不同
	if conf["meta_table"] then
		backends = ngx_worker_cache["backend_keys"][ufc_service_name]
	else
		backends = backends_data
	end

	local service_total_request_cnt = 0
	local service_total_status_cnt = 0
	--"last_5xx"之类的
	local last_status = "last_" .. status
	for k, _ in pairs(backends) do
		local v = backends_data[k]
		if k ~= backend and v ~= nil then
			if v["last_ratio_request_cnt"] ~= nil and v[last_status] ~= nil then
				service_total_status_cnt = service_total_status_cnt + v[last_status]
				service_total_request_cnt = service_total_request_cnt + v["last_ratio_request_cnt"]
			end
		end
	end

	if service_total_request_cnt > 0 then
		ratio = service_total_status_cnt / service_total_request_cnt
	end
	return ratio
end

--[[
@comment 获取服务中位数平均耗时(排除指定单机)
@param string ufc_service_name
@param string backend, bns. idc
@param number min_request
@return number
]]
function _M.get_service_mid_average_cost_time(ufc_service_name, backend)
	--获取该服务下除了指定后端外所有后端耗时中位数的平均值
	local cost_time_avg = -1
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return cost_time_avg
	end

	if not dy_config["backend"] then
		return cost_time_avg
	end

	if not dy_config["service"] then
		return cost_time_avg
	end
	local service_info = dy_config["service"]
	if not service_info["mid_cost_time_sum"] or not service_info["mid_cnt"] then
		return cost_time_avg
	end

	if service_info["mid_cnt"] <= 1 then
		return cost_time_avg
	end

	local backends_data = dy_config["backend"][backend]
	--所有后端中位数耗时减去当前后端中位数算个平均值
	local service_mid_costtime_avg = (service_info["mid_cost_time_sum"] - backends_data["last_mid_response_times"]) / (service_info["mid_cnt"] - 1)
	return service_mid_costtime_avg
end

--[[
@comment 获取单机成功率
@param string ufc_service_name
@param string backend
@return number
]]
function _M.get_backend_success_ratio(ufc_service_name, backend)
	local ratio = 1
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return ratio
	end

	if not dy_config["backend"][backend] then
		return ratio
	end

	local request_cnt = dy_config["backend"][backend]["request_cnt"]
	if request_cnt < 2 then
		return ratio
	end

	if not dy_config["backend"][backend]["success_cnt"] then
		return 0
	end

	local success_cnt = dy_config["backend"][backend]["success_cnt"]
	local success_percent = success_cnt / request_cnt

	--ngx.log(ngx.DEBUG, string.format("[logid=%s] [service=%s] [backend=%s] [success_percent=%s]", ngx.var.ufc_logid, ufc_service_name, backend, success_percent))

	return success_percent
end

--[[
@comment 获取单机指定范围状态码比例
@param string ufc_service_name
@param string backend
@return number
]]
function _M.get_backend_status_ratio(ufc_service_name, backend, min_request, status, ratio_in_config)
	local ratio = -1
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return ratio
	end

	if not dy_config["backend"][backend] then
		return ratio
	end
	--状态访问计数用额外的变量统计
	local ratio_request_cnt = dy_config["backend"][backend]["ratio_request_cnt"]

	if not ratio_request_cnt then
		return ratio
	end

	if ratio_request_cnt < min_request then
		return ratio
	end

	

	if not dy_config["backend"][backend][status] then
		return ratio
	end

	local backend_status_cnt = dy_config["backend"][backend][status]
	local backend_status_percent = backend_status_cnt / ratio_request_cnt

	if backend_status_percent * 100 < ratio_in_config then
		--如果后端状态码占比比配置里面的比例还小后面就不用比较了，直接返回
		return ratio
	end
	--ngx.log(ngx.NOTICE, string.format("%d, %d",backend_404_cnt ,request_cnt ))
	--ngx.log(ngx.DEBUG, string.format("[logid=%s] [service=%s] [backend=%s] [success_percent=%s]", ngx.var.ufc_logid, ufc_service_name, backend, success_percent))

	return backend_status_percent
end

--[[
@comment 获取单机器耗时中位数
@param string ufc_service_name
@param string backend
@param number min_request
@return number
]]
function _M.get_backend_mid_cost_time(ufc_service_name, backend, min_request)
	local cost_time_mid = -1
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return cost_time_mid
	end

	--获取单机请求次数和总耗时
	if not dy_config["backend"] then
		return cost_time_mid
	end

	if not dy_config["backend"][backend] then 	
		return cost_time_mid
	end

	local backend_info = dy_config["backend"][backend]

	local backend_request_cnt = dy_config["backend"][backend]["ratio_request_cnt"]
	if not backend_request_cnt or backend_request_cnt < min_request then
		return cost_time_mid
	end

	--获取耗时的中位数
	table.sort(backend_info["response_times"])
	local mid = math.ceil(backend_request_cnt/2)
	if mid < 1 then
		return cost_time_mid
	end

	if not backend_info["response_times"][mid] then 
		return cost_time_mid
	end

	return backend_info["response_times"][mid]
end

--[[
@comment service熔断触发封禁 
@param string ufc_service_name
@param string bns
@param string idc
]]
function _M.forbid_service(from_service, ufc_service_name, bns, platforms_idc)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end

	--全量解封
	_M.reset_backends(ufc_service_name, bns, platforms_idc)
	if not dy_config["breaker"] then
		dy_config["breaker"] = {}
	end

	if not dy_config["breaker"][bns] then
		dy_config["breaker"][bns] = {}
	end

	if not dy_config["breaker"][bns][platforms_idc] then
		dy_config["breaker"][bns][platforms_idc] = {}
	end

	if not dy_config["breaker"][bns][platforms_idc][from_service] then
		dy_config["breaker"][bns][platforms_idc][from_service] = {}
	end

	dy_config["breaker"][bns][platforms_idc][from_service]["ftime"] = math.floor(ngx.now())
	
	-- forbid_service 触发 service 封禁，将key置为true
	dy_config["breaker"][bns][platforms_idc][from_service]["forbid"] = true 

	local logid = ngx.var.ufc_logid or "-"
	ngx.log(ngx.WARN, string.format("[logid=%s] [policy_service=%s] [to_service=%s] [bns=%s] [idc=%s]", logid, from_service, ufc_service_name, bns, platforms_idc))

end


--[[
@comment service解除熔断 
@param string ufc_service_name
@param string bns
@param string idc
]]
function _M.unforbid_service(from_service, ufc_service_name, bns, idc)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end

	if not dy_config["breaker"] then
		return false
	end

	if not dy_config["breaker"][bns] then
		return false
	end

	if not dy_config["breaker"][bns][idc] then
		return false
	end

	if not dy_config["breaker"][bns][idc][from_service] then
		return false
	end

	dy_config["breaker"][bns][idc][from_service]["forbid"] = false 

	local logid = ngx.var.ufc_logid or "-"
	ngx.log(ngx.WARN, string.format("[logid=%s] [policy_service=%s] [to_service=%s] [bns=%s] [idc=%s]", logid, from_service, ufc_service_name, bns, idc))
end


--[[
@param string ufc_service_name
@param string bns
@param string idc
@return boolean true为服务正常，false为服务封禁
]]
function _M.is_service_ok(from_service, ufc_service_name, bns, platforms_idc)
	--读写共享内存和luavm内存此处处理逻辑不同
	if conf["meta_table"] then
		if shared_memmory.get_dynamic_data6(ufc_service_name, "breaker", bns, platforms_idc, from_service, "forbid") then
			return false
		end
	else
		local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
		if not dy_config then
			return false
		end
	
		if not dy_config["breaker"] then
			return true
		end
	
		if not dy_config["breaker"][bns] then
			return true
		end
	
		if not dy_config["breaker"][bns][platforms_idc] then
			return true
		end
	
		if not dy_config["breaker"][bns][platforms_idc][from_service] then
			return true
		end
	
		if dy_config["breaker"][bns][platforms_idc][from_service]["forbid"] then
			--封禁中
			return false
		end
	end

	return true
end


--[[
@param string ufc_service_name
@param string bns
@param string idc
]]
function _M.check_service_forbid_timeout(from_service, ufc_service_name, bns, platforms_idc, timeout)
	local ftime = nil
	if conf["meta_table"] then
		ftime = shared_memmory.get_dynamic_data6(ufc_service_name, "breaker", bns, platforms_idc, from_service, "ftime")
	else
		local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
		if not dy_config then
			return true
		end
	
		if not dy_config["breaker"] then
			return true
		end
	
		if not dy_config["breaker"][bns] then
			return true
		end
	
		if not dy_config["breaker"][bns][platforms_idc] then
			return true
		end
	
		if not dy_config["breaker"][bns][platforms_idc][from_service] then
			return true
		end
		ftime = dy_config["breaker"][bns][platforms_idc][from_service]["ftime"]
	end

	if not ftime then
		return true
	end

	if not timeout then
		timeout = 30
	end

	if ftime + timeout >  math.floor(ngx.now()) then
		--未超时
		return false
	end

	return true
end

--[[
@comment service是否关闭全局熔断
@param string ufc_service_name
@param string bns
@param string idc
@return boolean true为关闭熔断，false为开启熔断
]]
function _M.is_breaker_disable(backend_cnt)
	if backend_cnt and backend_cnt == 1 then
		return true 
	end
	return false
end 

return _M
