local ngx_worker_cache = require "ngx_worker_cache"
local conf = require "conf"
local shared_memory = require "shared_memory"
local cjson = require "cjson.safe"
local string = require "string"
local http = require("resty.http") 
local gossip_utils = require "gossip_utils"
local ngx = ngx
local math = math
local table = table


local _M = {
	["doc"] = "gossip",
	["connect_timeout"] = 1000,
	["read_timeout"] = 3000,
	--要写到磁盘的数据先放到缓存中，最后一起写磁盘
	--避免磁盘满特权进程写磁盘挂掉之后后面的配置无法更新
	["cache_table"] = table.new(0, 1000)
}

--开始从远端peer同步配置
function _M.gossip_cron()
	local ip, mtime = gossip_utils.get_gossip_flag()
	if not ip or not mtime then
		return
	end

	gossip_utils.delete_gossip_flag()
	ngx.log(ngx.NOTICE, string.format("load config from peer %s start", ip))
	--lock的操作在woker进程收到扩散请求就做了
	local res = _M._load_config_from_peer(ip, mtime)
	--同步成功继续扩散
	if res ~= conf["res"]["FAIL"] then
		--更新配置更新时间戳
		shared_memory.static_data_set("static_cache", "last_update_time", ngx.time())
	end
	if res == conf["res"]["SUCCESS"] then
		_M.broadcast_conifg()
	end
	gossip_utils.config_update_unlock()
	ngx.log(ngx.NOTICE, string.format("load config from peer %s end, res is %s", ip, res))
end

--把配置向其他机器扩散
function _M.broadcast_conifg()
	if not ngx_worker_cache["static_cache"]["configs"] or not ngx_worker_cache["static_cache"]["configs"]["gossip"] then
		ngx.log(ngx.NOTICE, "gossip config miss, stop broadcast config")
		return
	end

	local gossip_config = ngx_worker_cache["static_cache"]["configs"]["gossip"]
	if not gossip_config["enable"] then
		ngx.log(ngx.NOTICE, "gossip config miss, stop broadcast config")
		return
	end
	--默认向同组一半机器进行扩散
	local rate = gossip_config["rate"] or 0.5
	--向同组其他机器进行扩散
	local peers = gossip_utils.get_peers(rate)
	if not peers then
		return
	end
	local mtime = ngx_worker_cache["static_cache"]["mtime"]
	for peer, _ in pairs(peers) do
		ngx.log(ngx.NOTICE, string.format("start broadcast config to peer %s", peer))
		_M.broadcast_to_one_peer(peer, mtime)
		--现在peer拉消息是秒级别
		--向一个peer扩散完之后sleep一秒，达到串行的效果
		ngx.sleep(1)
	end
end

function _M.broadcast_to_one_peer(ip, mtime)
	--请求peer的goosip/start接口
	local httpc = http.new()
	httpc:set_timeouts(_M["connect_timeout"], _M["read_timeout"], _M["read_timeout"])
	local ok, err = httpc:connect({
		scheme = "http",
		host = ip,
		port = conf["ufc_port"],
	})

	--连接失败，把peer从分组数据中删掉
	if not ok then
		httpc:close()
		--peer ip和本机ip在同一组
		local index
		if gossip_utils["last_index"] then
			index = gossip_utils["last_index"]
		else
			index = gossip_utils.get_peer_group_index()
		end
		if index then
			gossip_utils.delete_peer(ip, index)
		end
		ngx.log(ngx.NOTICE, string.format("broadcast config to peer %s failed, connect failed, err is %s", ip, err))
		return
	end

	local params = {}
	local ok, err, res, body, response
	local uri = string.format("/gossip?cmd=start&mtime=%s&token=%s", mtime, gossip_utils["token"])
	local headers = {}
	headers["http-x-isis-logid"] = ngx.time()
	headers["x-ufc-service-name"] = "pcs-ufc-agent"
	headers["x-ufc-self-service-name"] = "pcs-ufc-agent"

	--请求本机ufc正常
	params["method"] = "GET"
	params["path"] = uri
	params["headers"] = headers
	res, err = httpc:request(params)
	if err then
		return err
	end
	body, err = res:read_body()
	if err then
		return err
	end
	response, err = cjson.decode(body)
	if err then
		ngx.log(ngx.WARN, string.format("peer %s response %s is not json", ip, body))
		return 
	end
	if not response["token"] or response["token"] ~= gossip_utils["token"] then
		ngx.log(ngx.WARN, string.format("peer %s response token %s is invalid, broadcast failed", ip, response["token"]))
		return
	end

	if not response["res"] or response["res"] == "invalid_args" then
		ngx.log(ngx.WARN, string.format("peer %s response %s, broadcast failed", ip, response["res"]))
		return
	end

	if response["res"] == "end" then
		ngx.log(ngx.WARN, string.format("peer %s is in other load config process, stop broadcast", ip))
		return
	end 

	--被扩散的机器配置比扩散的机器新（这个概率比较小）
	--先把该机器最新的配置拉过来再继续向剩下的机器扩散
	local peer_mtime = tonumber(response["res"])
	if peer_mtime > tonumber(mtime) then
		ngx.log(ngx.NOTICE, string.format("peer %s config is newer, load config when broadcase start", ip))
		local res = _M._load_config_from_peer(ip, peer_mtime)
		if res ~= conf["res"]["FAIL"] then
			--更新配置更新时间戳
			shared_memory.static_data_set("static_cache", "last_update_time", ngx.time())
		end
		ngx.log(ngx.NOTICE, string.format("load config from peer %s when broadcase end, res is %s", ip, res))
	end
	return
end

--从peer中获取配置信息
function _M._load_config_from_peer(ip, global_mtime)
	--先和peer建立http连接
	local httpc = http.new()
	httpc:set_timeouts(_M["connect_timeout"], _M["read_timeout"], _M["read_timeout"])
	local ok, err = httpc:connect({
		scheme = "http",
		host = ip,
		port = conf["ufc_port"],
	})

	--连接失败，把peer从分组数据中删掉
	if not ok then
		httpc:close()
		--peer ip和本机ip在同一组
		local index
		if gossip_utils["last_index"] then
			index = gossip_utils["last_index"]
		else
			index = gossip_utils.get_peer_group_index()
		end
		if index then
			gossip_utils.delete_peer(ip, index)
		end
		ngx.log(ngx.NOTICE, string.format("get config from peer %s failed, connect failed, err is %s", ip, err))
		return conf["res"]["FAIL"]
	end

	local res	
	--先加载bns数据，要不然服务切bns的时候会出现service配置先更新了bns数据没更新的问题
	--之前在redis配置更新和gossip协议里面顺序不对没问题是因为最后才更新总的mtime，现在worker进程会主动拉一次配置这里要做一下修复
	res = _M._load_one_type_config_from_peer(ip, httpc, "bns")
    ngx.log(ngx.NOTICE, string.format("get config from peer %s, bnss res is %s", ip, res))
    if res == conf["res"]["FAIL"] then
        httpc:close()
        return conf["res"]["FAIL"]
    end
	
    res = _M._load_one_type_config_from_peer(ip, httpc, "service")
    ngx.log(ngx.NOTICE, string.format("get config from peer %s, services res is %s", ip, res))

    if res == conf["res"]["FAIL"] then
		httpc:close()
        return conf["res"]["FAIL"]
    end

	res = _M._load_one_type_config_from_peer(ip, httpc, "config")
	--整体配置保存在本地
	if res == conf["res"]["SUCCESS"] then
		ngx_worker_cache.save_config_local("configs", ngx_worker_cache["static_cache"]["configs"], _M["cache_table"])
	end
    ngx.log(ngx.NOTICE, string.format("get config from peer %s, configs res is %s", ip, res))
    if res == conf["res"]["FAIL"] then
		httpc:close()
        return conf["res"]["FAIL"]
    end

	res = _M._load_one_type_config_from_peer(ip, httpc, "http_ip")
	--整体配置保存在本地
	if res == conf["res"]["SUCCESS"] then
		ngx_worker_cache.save_config_local("http_ips", ngx_worker_cache["static_cache"]["http_ips"], _M["cache_table"])
	end
	ngx.log(ngx.NOTICE, string.format("get config from peer %s, http_ip res is %s", ip, res))
	if res == conf["res"]["FAIL"] then
		httpc:close()
		return conf["res"]["FAIL"]
	end
	
	res = _M._load_one_type_config_from_peer(ip, httpc, "stream_ip")
	--整体配置保存在本地
	if res == conf["res"]["SUCCESS"] then
		ngx_worker_cache.save_config_local("stream_ips", ngx_worker_cache["static_cache"]["stream_ips"], _M["cache_table"])
	end
    ngx.log(ngx.NOTICE, string.format("get config from peer %s , stream_ip res is %s", ip, res))
    if res == conf["res"]["FAIL"] then
		httpc:close()
        return conf["res"]["FAIL"]
    end
	httpc:close()

	--global mtime写到共享内存中
	shared_memory.set_global_mtime(global_mtime)

	--把当前时间戳写到共享内存，供后续 worker 进程更新 worker latency
	local now_timestamp = ngx.time()
	shared_memory.set_global_mtime_update_timestamp(now_timestamp)
	--更新 privileged_latency 时间戳
	local privileged_latency = now_timestamp - global_mtime
	shared_memory.set_privileged_latency(privileged_latency)

	--如果redis配置中有idc相关配置，替换conf
	ngx_worker_cache.update_idc_conf()
	--保存meta信息
	ngx_worker_cache.save_ufc_meta_info(_M["cache_table"])
	--之前的写磁盘操作其实只把数据写到cache_table里面，在这里把他们全部写到磁盘上面
	--即使磁盘满了特权进程挂掉，共享内存里面的数据也是新的不会影响到worker进程
	ngx_worker_cache.save_cache_table(_M["cache_table"])
    ngx_worker_cache["static_cache"]["mtime"] = global_mtime
    return conf["res"]["SUCCESS"]
end

function _M._load_one_type_config_from_peer(ip, httpc, config_type)
	local res, err, config_cnt
	local needed_update_config_names, needed_update_config_mtimes
	config_cnt, err = gossip_utils.get_config_cnt_from_peer(ip, httpc, config_type)
	if err then
		ngx.log(ngx.WARN, string.format("get config %s cnt from peer %s failed, err is %s", config_type, ip, err))
		return conf["res"]["FAIL"]
	end
	--config的cnt数据写到共享内存中去
	--idcmap，platformmap, platformidcmap不用写，它的数据和bns放在一起
	if config_type ~= "idcmap" and config_type ~= "platformmap" and config_type ~= "platformidcmap" then
		shared_memory.set_config_cnt(config_type, config_cnt)
	end

	needed_update_config_names, needed_update_config_mtimes, err = _M._batch_get_needed_update_config_names(ip, httpc, config_type, config_cnt)
	if err then
		ngx.log(ngx.WARN, string.format("batch get need update config %s from peer %s failed, err is %s", config_type, ip, err))
		return conf["res"]["FAIL"]
	end

	if not needed_update_config_names or not needed_update_config_mtimes then 
		ngx.log(ngx.WARN, string.format("needed_update_config_names or needed_update_config_mtimes is nil, config type is %s", config_type))
		return  conf["res"]["FAIL"]
	end 

	for _, config_name in ipairs(needed_update_config_names) do
		res = _M._load_one_config_from_peer(ip, httpc, config_type, config_name, needed_update_config_mtimes[config_name])
		if res == conf["res"]["FAIL"] then
			return conf["res"]["FAIL"]
		end
	end
	return conf["res"]["SUCCESS"]
end

--获取batch大小
function _M._get_batch_size()
	--默认batch大小50
	local size = 50
	if not ngx_worker_cache["static_cache"]["configs"] or not ngx_worker_cache["static_cache"]["configs"]["gossip"] then
		return size
	end

	local gossip_config = ngx_worker_cache["static_cache"]["configs"]["gossip"]
	if not gossip_config["batch_size"] then
		return size
	end

	return gossip_config["batch_size"]
end

--批量获取需要更新的config名字以及mtime
function _M._batch_get_needed_update_config_names(ip, httpc, config_type, config_cnt)
	config_cnt = tonumber(config_cnt)
	local batch_size = _M._get_batch_size()

	local configs = {}
	local config_mtimes = {}
	local last_index = 0
	local needed_update_config = {}
	local err = nil
	--获取所有的config name
	while true do
		local stop = 0
		local config_name_keys = {}
		for i = 1, batch_size do 
			if last_index >= config_cnt then
				stop = 1
				break
			end
			table.insert(config_name_keys, last_index)
			last_index = last_index + 1 
		end

		local res = {}
		if #config_name_keys > 0 then
			res, err = gossip_utils.batch_get_peer_config(ip, httpc, config_type, cjson.encode(config_name_keys))
			if err ~= nil or res == nil then
				ngx.log(ngx.WARN, string.format("batch get config names failed, config_type is %s err is %s",config_type, err))
				return nil, nil, "batch get config names failed"
			end
		end
		
		for _, v in ipairs(res) do
			--更新ufc meta信息
			if config_type == "service" or config_type == "bns" then
				if not ngx_worker_cache["ufc_meta_info"] then
					ngx_worker_cache["ufc_meta_info"] = {}
				end

				if not ngx_worker_cache["ufc_meta_info"][config_type .. "s"] then
					ngx_worker_cache["ufc_meta_info"][config_type .. "s"] = {}
				end
				ngx_worker_cache["ufc_meta_info"][config_type .. "s"][v] = 1
			end
			table.insert(configs, v)
		end

		if stop == 1 then
			break
		end
	end

	last_index = 1
	while true do
		local stop = 0
		local config_mtime_keys = {}
		for i = 1, batch_size do 
			if last_index > config_cnt then
				stop = 1
				break
			end

			
			local config_mtime_key = string.format("%s-mtime", configs[last_index])
			table.insert(config_mtime_keys, config_mtime_key)
			last_index = last_index + 1 
		end

		local res = {}
		if #config_mtime_keys > 0 then
			res, err = gossip_utils.batch_get_peer_config(ip, httpc, config_type, cjson.encode(config_mtime_keys))
			if err or res == nil then
				ngx.log(ngx.WARN, string.format("batch get config mtimes failed, config_type is %s err is %s",config_type, err))
				return nil, nil, "batch get config mtimes failed"
			end
		end
		
		for _, v in ipairs(res) do
			table.insert(config_mtimes, v)
		end
		
		if stop == 1 then
			break
		end
	end

	--对比获取需要更新的config name
	local mtime_res = {}
	local red = nil 
    red, err = conf.get_redis()
    if err ~= nil then
        ngx.log(ngx.WARN, string.format("load config, get redis fail, err is %s", err))
        return nil, nil, "get redis failed"
    end
	for i = 1, config_cnt do
		--更新config index信息
		shared_memory.set_config_name(config_type, i - 1, configs[i])
		if _M._is_peer_config_newer(red, config_type, configs[i], config_mtimes[i]) then
			table.insert(needed_update_config, configs[i])
			mtime_res[configs[i]] = config_mtimes[i]
		end
		shared_memory.set_config_name(config_type, i - 1, configs[i])
	end
	
	return needed_update_config, mtime_res, nil
end

function _M.is_body_mtime_less_than_redis_mtime(config_decode, redis_mtime)
	local body_mtime = config_decode["mtime"]
	if not body_mtime or not redis_mtime then 
		return true
	end

	if body_mtime < redis_mtime then
		return true
	end 

	return false
end 

--peer中的配置是否更新一点
function _M._is_peer_config_newer(red, config_type, config_name, config_mtime)
	local cached_config, cached_mtime
	cached_mtime = shared_memory.get_config_mtime(config_type, config_name)
	if not cached_mtime or tonumber(cached_mtime) < tonumber(config_mtime) then
		-- 小流量判断是否存在
		if gossip_utils.is_in_small_flow(config_type, config_name, red) == false then
			return false
		end 
		return  true
	end

	return false
end

function _M._load_one_config_from_peer(ip, httpc, config_type, config_name, config_mtime)
	local config_decode, err = _M._real_load_one_config_from_peer(ip, httpc, config_type, config_name)
	if not config_decode or err then
		return conf["res"]["FAIL"]
	end

	-- peer 的配置是新的，但仍需要 check 下 peer body里的 mtime 是否小于 redis-mtime，是的话先打个日志
	-- 允许拉过来更新配置(否则不好定位之前的问题），但更新 mtime时会被 check_need_set_config_mtime 拦截
	if _M.is_body_mtime_less_than_redis_mtime(config_decode, config_mtime) then 
		ngx.log(ngx.WARN, string.format("peer config body mtime is less than redis mtime, peer is %s, config_type is %s, config_name is %s, body_mtime is %s, redis_mtime is %s", 
			ip, config_type, config_name, config_decode["mtime"], config_mtime))
	end 
	
	--对于每种config_type处理方式均不同
	--这里即使某一个数据共享内存写入失败也不返回失败，否则影响后面数据更新，等下次更新就好了
	local handle_res
	if config_type == "service" then
		handle_res = _M.service_content_handle(config_type, config_name, config_decode)
		-- 从远端读取到的配置就是老的
	elseif config_type == "bns" then
		handle_res = _M.bns_content_handle(config_type, config_name, config_decode, config_mtime)
	elseif config_type == "http_ip" or config_type == "stream_ip" then
		handle_res = _M.ip_content_handle(config_type, config_name, config_decode, config_mtime)
	else 
		handle_res =_M.config_content_handle(config_type, config_name, config_decode, config_mtime)
	end
	--确认配置内容写入共享内存成功之后才在共享内存中写入mtime，这样能保证共享内存中的配置内容永远比mtime更新
	--即使我们的代码里面有一些非预期的bug，也能保证经过多轮配置更新之后，共享内存中的配置数据是最新的
	if handle_res == conf["res"]["SUCCESS"] then
		-- config 和 service 有小流量, 检查是否需要设置 config_mtime。gossip这里配置更新可能有bug只在gossip 需要加一下
		if (_M.check_need_set_config_mtime(config_decode, config_mtime) == true) then 
			shared_memory.set_config_mtime(config_type, config_name, config_mtime)
		else 
			ngx.log(ngx.WARN, string.format("gossip set config time failed, config_type is %s, config_name is %s, body_mtime is %s, redis_mtime is %s",
				config_type, config_name, config_decode["mtime"], config_mtime))
		end
	end

	return conf["res"]["SUCCESS"], nil 
end

function _M.check_need_set_config_mtime(config_decode, config_mtime)
	-- 有些配置项没有 mtime 也直接更新，比如当前的 double_ends. 如果 body_mtime 不存在，或者存在但大于或者等于 redis-key 
	-- 大于选项是因为有些mtime是在ufc-agent设置timeNow 导致的
	local body_mtime = config_decode["mtime"]
	if body_mtime == nil or body_mtime >= config_mtime then
		return true
	end

	return false
end 

--这个数据放一份到内存中（需要写磁盘）
function _M.ip_content_handle(config_type, config_name, config, config_mtime)
	if not ngx_worker_cache["static_cache"][config_type .. "s"] then
		ngx_worker_cache["static_cache"][config_type .. "s"] = {}
	end
	config["mtime"] = config_mtime
	ngx_worker_cache["static_cache"][config_type .. "s"][config_name] = config
	--数据写到共享内存中
	if not _M.save_config_in_shared_memmory(config_type, config_name, config) then
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end
	return conf["res"]["SUCCESS"]
end

--bns数据需要放一份在static_cache[bnss]中
--同样的也需要写磁盘和共享内存
function _M.bns_content_handle(config_type, config_name, config, config_mtime)
	if not ngx_worker_cache["static_cache"][config_type .. "s"] then
		ngx_worker_cache["static_cache"][config_type .. "s"] = {}
	end
	config["mtime"] = config_mtime
	ngx_worker_cache["static_cache"][config_type .. "s"][config_name] = config -- 存一份
	--存到磁盘上和共享内存中
	ngx_worker_cache.save_item_local(config_type, config_name, config, _M["cache_table"])
	if not _M.save_config_in_shared_memmory(config_type, config_name, config) then
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end
	return conf["res"]["SUCCESS"]
end

--service数据啥都不用管，处理好之后写到磁盘和共享内存就行了
function _M.service_content_handle(config_type, config_name, config)
	ngx_worker_cache.save_item_local(config_type, config_name, config, _M["cache_table"])
	if not _M.save_config_in_shared_memmory(config_type, config_name, config) then
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end
	return conf["res"]["SUCCESS"]
end

--configs数据需要放一份在static_cache[configs]中
--获取定时器配置需要用到这个
function _M.config_content_handle(config_type, config_name, config, config_mtime)
	if not ngx_worker_cache["static_cache"][config_type .. "s"] then
		ngx_worker_cache["static_cache"][config_type .. "s"] = {}
	end
	config["mtime"] = config_mtime
	ngx_worker_cache["static_cache"][config_type .. "s"][config_name] = config
	--数据写到共享内存中
	if not _M.save_config_in_shared_memmory(config_type, config_name, config) then
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end
	return conf["res"]["SUCCESS"]
end

function _M._real_load_one_config_from_peer(ip, httpc, config_type, config_name)
    local err
    local prefix
    local config_encode, config
    prefix = conf.get_redis_prefix()

    config_encode, err = gossip_utils.get_config_content_from_peer(ip, httpc, config_type, config_name)
    if err or config_encode == ngx.null then
        ngx.log(ngx.WARN, string.format("%s conf miss, err is %s, index is %s",
            config_type, err, config_name))
        return nil, err or "config null"
    end

    config, err = cjson.decode(config_encode)
    if not config or err then
        return nil, err or "decode fail"
    end
    return config, nil
end

--把处理好的配置信息写到共享内存中去
function _M.save_config_in_shared_memmory(config_type, config_name, config_content)
	return shared_memory.set_static_data(config_type, config_name, cjson.encode(config_content))
end

return _M