local utils = require "utils"
local redis = require "resty.redis"
local cjson = require "cjson.safe"
local ufc   = require "ufclib.ufc"
local ngx = ngx
local tonumber = tonumber

local _M = {
    ["version"] = "git_version",
    --标识代码是线上版本还是沙盒版本
    ["sandbox"] = "sandbox_flag",
    --是否设置meta table
    ["meta_table"] = false,
    --在内存中标识目前是http还是stream模块
    ["stream"] = 0,
    ["ufc_stream_addr"] = "127.192.0.0",
    ["ufc_stream_port"] = 8241,
    --动态分配的from service index从101开始
    ["ufc_stream_from_service_base_index"] = 100,
    ["ufc_stream_illegal_remote_addr"] = {
        ["127.0.0.1"] = 1, 
        ["127.0.0.2"] = 1
    },
    ["key_prefix"] = {
        [3] = "p-3ufc",
        [4] = "p-4ufc",
        ["sandbox"] = "p-3ufc-sandbox"
    },
    ["unimportant_config_update_interval"] = 7,
    ["batch_size"] = 50,
    ["local_location"] = nil,
    ["cache_reload_interval"] = 60, -- s
    ["redis_timeout"] = 30000,  -- ms
    ["forbid_connect_timeout"] = 500, -- ms
    ["meta_cache_dir"] = "ufc/meta_cache",
    ["random_max_tries"] = 8,
    ["clean_timeout_worker_min_interval"] = 360,
    ["max_request_cnt_number"] = 9999999,

    ["defaults"] = {
        ["connect_timeout"] = 1000, -- ms
        ["send_timeout"] = 3000, -- ms
        ["read_timeout"] = 20000, -- ms
        ["limit_qps_all"] = 0,
        ["qps_drop_rate"] = 0,
        ["bp"] = 1,
        ["max_fail"] = 1,
        ["max_forbid_percent"] = 25,
        ["forbid_timeout"] = 120,
        ["backends_max_tries"] = 3,
        ["max_global_running_cnt"] = 3,
        ["worker_timeout"] = 3600,
        ["request_reset_interval"] = 10,
        ["vpn_percent"] = 100,
        ["black_list"] = {},
        ["illegal_headers"] = {},
        ["4xx_to_406_percent"] = 0,
        ["5xx_to_507_percent"] = 0,
        ["forbid_interval_time"] = 600,
        ["longer_forbid_timeout"] = 300,
    },
    ["balance_policy"] = {
        ["ROUND_ROBIN"] = 1, -- normal round robin
        ["ROUND_ROBIN_FORBID_SUCCESS_RATE_UNNORMAL"] = 2, -- rr forbid 2xx rate too low
        ["RANDOM"] = 3,
        ["RANDOM_FORBID_SUCCESS_RATE_UNNORMAL"] = 4,
        ["IDLE"] = 5,
        ["IDLE_FORBID_SUCCESS_RATE_UNNORMAL"] = 6, -- idle forbid 2xx rate too low
        ["HASH"] = 7,
        ["GLOBAL_IDLE"] = 8,
        ["CONSISTENT_HASH"] = 9
        --[[
            bp 为1表示使用轮询策略, 包含的特性有超时封禁, 后端重试与轮询
            bp 为2表示在轮询策略之上增加后端”5xx 比例异常节点的封禁”逻辑
            bp 为3表示使用随机策略, 包含的特性有超时封禁, 后端重试与轮询
            bp 为4表示在随机策略之上增加后端”5xx 比例异常节点的封禁”逻辑
            bp 为5表示使用负载均衡策略, 按照后端响应时间分发请求
            bp 为6表示在负载均衡策略之上增加后端”5xx 比例异常节点的封禁”逻辑
            bp 为7表示使用哈希策略, 用户在 header 中传递 hash-key 字段控制返回的后端
            bp 为8只用在旁路式中, 表示全局负载均衡策略, 与其他策略不同, 全局负载均衡策略使用并依赖中心存储
            bp 为9表示使用一致性哈希策略，用户在 header 中传递 hash-key 字段控制返回的后端
         ]]
    },
    ["res"] = {
        ["SUCCESS"] = 1,
        ["FAIL"] = -1,
        ["NONEED"] = 0
    },

    ["breaker_default_policy"] = {
        ["PA"] = {
            ["policy"] = 1,
            ["regular"]= "if http_code ~= 502 and http_code ~= 504 and http_code ~= 555 then if http_code < 500 then return 1 end return 0 else return -1 end"
        },
        ["PB"] = {
            ["policy"] = 2,
            ["max_fail"] = 2
        },
        ["PC"] = {
            ["policy"]  = 1,
            ["forbid_timeout"] = 60
        },
        ["PD"] = {
            ["policy"]  = 1,
            ["ratio"]  = 20
        },
        ["PE"] = {
            ["policy"]  = 1
        },
        ["PF"] = {
            ["policy"]  = 1,
            ["status"] = {"3xx", "4xx", "5xx"},
            ["min_request"] = 20,
            ["cost_time_ratio"] = 300,
            ["status_ratio"] = 40
        }
    },  

    ["inner_ip"] = {
        ["61.135.169"] = 1,
        ["220.181.38"] = 1
    },

    ["backup_ufc"] = "************:8240",

    ["ufc_port"] = 8240,

    ["http_code"] = {
        ["E_OK"] = 200,

        ["E_RETRY"] = 312,

        ["E_BAD_FORMAT"] = 400,
        ["E_HEADERS_ILLEGAL"] = 400,
        ["E_QPS_LIMIT"] = 407,
        ["E_RATE_DROP"] = 407,

        ["E_DEGRADE_4XX"] = 406,
        ["E_DEGRADE_5XX"] = 507,

        ["E_NO_SERVICE"] = 502,
        ["E_NO_BNS"] = 502,
        ["E_NO_BACKEND"] = 502,
        ["E_NO_SUPPORT"] = 502,
        ["E_NO_RETRY"] = 507,

        ["E_SYSTEM"] = 513,
    },
    ["auto_replace_host"] = {
        [""] = 1,
        ["localhost"] = 1,
        ["127.0.0.1"] = 1,
    },

    ["idc_location"] = {
        ["default"] = "beijing",
        ["yq01"] = "yangquan",
        ["yq02"] = "yangquan",
        ["xaky"] = "xian",
        ["xakd"] = "xian",
        ["xafj"] = "xian",
    },

    ["idc_map"] = {
        ["bjyz"] = "TC",
        ["st01"] = "TC",
        ["tc"] = "TC",
        ["cq02"] = "TC",
        ["m1"] = "TC",
        ["gajl"] = "TC",
        ["bjhw"] = "HBA",
        ["yf01"] = "JX",
        ["cp01"] = "JX",
        ["cq01"] = "JX",
        ["yq01"] = "YQ",
        ["yq02"] = "YQ",
        ["nj01"] = "NJ01",
        ["nj02"] = "NJ02",
        ["nb01"] = "NB",
        ["hz01"] = "HZ",
        ["sh01"] = "SH",
        ["qd01"] = "QD",
        ["qdbh"] = "QDBH",
        ["gzhl"] = "GZHL",
        ["gzhxy"] = "GZHXY",
        ["gzns"] = "GZNS",
        ["xaky"] = "XAKY",
        ["xafj"] = "XAFJ",
        ["xakd"] = "XAKD",
        ["bddwd"] = "JX",
        ["bddx"] = "JX",
        ["bdjl"] = "JX",
    },

    ["cloud_log"] = {
        ["max_slice_num"] = 1000,
        ["ufc_service"] = "netdisk-op-jindouyou",
    },

    ["legal_config_format"] = {
        ["connect_timeout"] = function (v) return utils.judge_type(v, 0) and v > 0 end,
        ["send_timeout"] = function (v) return utils.judge_type(v, 0) and v > 0 end,
        ["read_timeout"] = function (v) return utils.judge_type(v, 0) and v > 0 end,
        ["bp"] = function (v) return utils.judge_type(v, 0) and v >= 1 and v <= 9 end,
        ["backends_max_tries"] = function (v) return utils.judge_type(v, 0) and v > 0 end,
    },

    ["redis_ufc_service"] = {
        ["qingdao"] = "pss-cloudcache-common-qd",
        ["ningbo"] = "pss-cloudcache-common-nb",
        ["nanjing02"] = "pss-cloudcache-common-nj02",
        ["yangquan"] = "pss-cloudcache-common-yq",
        ["hangzhou"] = "pss-cloudcache-common-yq",
        ["shanghai"] = "pss-cloudcache-common-yq",
        ["beijing"] = "pss-cloudcache-common-bj",
        ["guangzhou"] = "pss-cloudcache-common-gz",
        ["xian"] = "pss-cloudcache-common-xa",
    },

    ["sandbox_redis_ufc_service"] = {
        ["beijing"] = "sandbox:pss-cloudcache-sandbox-bj",
        ["yangquan"] = "sandbox:pss-cloudcache-sandbox-yq",
        ["xian"] = "sandbox:pss-cloudcache-sandbox-yq",
        ["qingdao"] = "sandbox:pss-cloudcache-sandbox-yq", 
    }
}

--获取最大重试次数
--优先级为：header里面的 > redis配置里的 > conf.lua里面的
--最大重试次数配置文件里面限制死了是四次，设置再大也不会起作用
function _M.get_max_retries(service_config)
	local default_retry = _M["defaults"]["backends_max_tries"]
	--这里ctxs不可能是nil
	local ctxs = ngx.ctx.ufc_ctxs
	--这里即使ctxs["x-ufc-retry-count"]是nil也不会panic
	local ctx_retry = tonumber(ctxs["x-ufc-retry-count"])
	if ctx_retry and ctx_retry > 0 then
		return ctx_retry
	end

	if service_config == nil then
		return default_retry
	end
	local service_config_retry = service_config["backends_max_tries"]

	if service_config_retry and service_config_retry > 0 then
		return service_config_retry
	end

	return default_retry
end

function _M.get_local_location()
	if _M["local_location"] then
		return _M["local_location"]
	end

	local local_location = _M["idc_location"]["default"]
	local t = utils.explode("-", utils.get_hostname())
	if #t ~= 0 then
		local local_idc = t[1]
		local_location = _M["idc_location"][local_idc] or local_location
	end

	_M["local_location"] = local_location
	return local_location
end

function _M.get_redis_prefix(version)
    local prefix, local_location
    if not version then
        version = 3
    end

    local_location = _M.get_local_location()
    if _M["sandbox"] == true then
        prefix = string.format("%s-%s", _M["key_prefix"]["sandbox"], local_location)
        return prefix
    end
    prefix = string.format("%s-%s", _M["key_prefix"][version], local_location)       -- p-3ufc-nanjing01

    return prefix
end

function _M.get_redis()
    local local_location = _M.get_local_location()
    local timeout = _M["redis_timeout"]

    local red, err = redis:new()
    local ok, host, port
    if red == nil then
        ngx.log(ngx.WARN, string.format("load config, new redis object failed, err:%s", err))
        red:close()
        return nil, err
    end

    local headers = {}
    headers["x-ufc-self-service-name"] = "pcs-ufc-agent"

    local logid = ngx.time()

    red:set_timeout(timeout)
    local redis_service = _M["redis_ufc_service"][local_location]

    if _M["sandbox"] == true then
        redis_service = _M["sandbox_redis_ufc_service"][local_location]
    end

    local ret = nil
    for i = 1,3 do
        
        ret, err = ufc.bypass(redis_service, logid, headers)
        ngx.log(ngx.NOTICE, string.format("load config, get ufc-backup, try_times:%d", i))

        if not err and ret and ret.host and ret.port then
            host, port = ret.host, ret.port
            ok, err = red:connect(host, tonumber(port))
            if ok then
                
                ngx.log(ngx.NOTICE, string.format("load config, connect redis %s:%s ok, try_times:%d", host, port, i))
                break
                
                
            else
                red:close()
                ngx.log(ngx.WARN, string.format("load config, connect redis %s:%s failed, err:%s, try_times:%d", host, port, err, i))
            end
        end
    end

    if ok then
        return red, nil
    end

    ngx.log(ngx.WARN, "load config, connect redis error, no target")
    return nil, err
end

function _M.exit(error_msg)
    local status = _M["http_code"][error_msg]
    if error_msg == nil or status == nil then
        error_msg = "E_SYSTEM"
    end

    ngx.status = status 
    ngx.print(cjson.encode({
        ["error"] = error_msg,
        ["errno"] = _M["http_code"][error_msg],
    }))

    ngx.exit(status)
end

function _M.fail(error_msg)
    if error_msg == nil or _M["http_code"][error_msg] == nil then
        error_msg = "E_SYSTEM"
    end

    local error_code = _M["http_code"][error_msg]
    --ngx.ctx.ufc_status = error_code
    ngx.status = error_code 
    ngx.exit(error_code)
end

return _M
