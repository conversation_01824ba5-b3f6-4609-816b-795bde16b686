local ngx_worker_cache = require "ngx_worker_cache"
local moniter = require "moniter"
local cjson = require "cjson.safe"
local utils = require "utils"
local shared_memory = require "shared_memory"
local conf = require "conf"

local _M = {
	["doc"] = "check forbid list function",
}

--链表解封相关操作抽离到这个文件中

function _M.init_list(ufc_service_name, bns, forbid_time, check_time,  backend, forbid_timeout, idc, check_ok_times)
    ngx_worker_cache["forbid_list"] = {
        value = 1,
        next = {
            value = {
                ["service"] = ufc_service_name,
                ["bns"] = bns,
                ["idc"] = idc,
            	["check_ok_times"] = check_ok_times,
                ["forbid_time"] = forbid_time,
                ["check_time"] = check_time or math.floor(ngx.now()),
                ["backend"] = backend,
                ["forbid_timeout"] = forbid_timeout or 120,
            },
            next = nil
        }
    }

    -- 将list和last关联起来, list仍然是dummy头,last是最后一个元素,不是nil
    ngx_worker_cache["forbid_list_last"] = ngx_worker_cache["forbid_list"].next
    --ngx.log(ngx.DEBUG, string.format("check forbid, init forbid list [last_len:%s] [count:%d] [list_len:%d]", string.len(cjson.encode(ngx_worker_cache["forbid_list_last"])), ngx_worker_cache["forbid_list"].value, string.len(cjson.encode(ngx_worker_cache["forbid_list"])) ))
end


-- @params string backend_platform_idc_info  单平台单logic_idc信息
function _M.append_node(ufc_service_name, bns, forbid_time, check_time,  backend, forbid_timeout, backend_platform_idc_info, check_ok_times)
    --拼接后又切割的数据都是string类型
    forbid_time = tonumber(forbid_time)
    check_time = tonumber(check_time)
    forbid_timeout = tonumber(forbid_timeout)
    check_ok_times = tonumber(check_ok_times)
    if backend_platform_idc_info == "nil" then
        backend_platform_idc_info = nil
    end

    if not ngx_worker_cache["forbid_list"] then
        _M.init_list(ufc_service_name, bns, forbid_time, check_time,  backend, forbid_timeout, backend_platform_idc_info, check_ok_times)
        return
    end

    ngx_worker_cache["forbid_list_last"].next = {
        value = {
            ["service"] = ufc_service_name,
            ["bns"] = bns,
            ["idc"] = backend_platform_idc_info,
            ["check_ok_times"] = check_ok_times,
            ["forbid_time"] = forbid_time,
            ["check_time"] = check_time or math.floor(ngx.now()),
            ["backend"] = backend,
            ["forbid_timeout"] = forbid_timeout or 120,
        },
        next = nil
    }

    -- 添加一个节点, last指向最后一个元素
    ngx_worker_cache["forbid_list_last"] = ngx_worker_cache["forbid_list_last"].next
    ngx_worker_cache["forbid_list"].value = ngx_worker_cache["forbid_list"].value + 1  -- list跟随变长

    --ngx.log(ngx.DEBUG, string.format("check forbid, add list param [count:%d] [last:%s]", ngx_worker_cache["forbid_list"].value, cjson.encode(ngx_worker_cache["forbid_list_last"])))
end

function _M.remove_head()
    -- 进入此函数, ngx_worker_cache["forbid_list"].next 一定有值
    if not ngx_worker_cache["forbid_list"].next then
        ngx.log(ngx.NOTICE, string.format("check forbid, remove head but nil [count:%d] [last:%s] [list:%s]",
            ngx_worker_cache["forbid_list"].value, cjson.encode(ngx_worker_cache["forbid_list_last"]), cjson.encode(ngx_worker_cache["forbid_list"])))
        return
    end
    local n = ngx_worker_cache["forbid_list"].next
    ngx_worker_cache["forbid_list"].next = ngx_worker_cache["forbid_list"].next.next
    n.next = nil
    ngx_worker_cache["forbid_list"].value = ngx_worker_cache["forbid_list"].value - 1

    --ngx.log(ngx.DEBUG, string.format("check forbid, remove list head [count:%d] [last:%s]", cjson.encode(ngx_worker_cache["forbid_list"].value), cjson.encode(ngx_worker_cache["forbid_list_last"]) ))
end



function _M.add_forbid_cnt_map(forbid_cnt_map, service, bns, idc)
	--对于没有idcmap信息的bns，idc是nil
	if idc == nil then
		idc = "all_idc"
	end

	if not forbid_cnt_map[service] then
		forbid_cnt_map[service] = {}
	end
	if not forbid_cnt_map[service][bns] then
		forbid_cnt_map[service][bns] = {}
	end
	if not forbid_cnt_map[service][bns][idc] then
		forbid_cnt_map[service][bns][idc] = 0
	end
	forbid_cnt_map[service][bns][idc] = forbid_cnt_map[service][bns][idc] + 1
end

function _M.check_service_forbid_cnt(ufc_service_name, bns, idc, cnt)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return -1
	end

	if not dy_config["service"] or not dy_config["service"]["forbidden_cnt"] or not dy_config["service"]["forbidden_cnt"][bns] then
		return -1
	end
	local forbidden_cnt_map = dy_config["service"]["forbidden_cnt"][bns]
	if not forbidden_cnt_map[idc] then
		return -1
	end

	if forbidden_cnt_map[idc] ~= cnt then
		return forbidden_cnt_map[idc]
	end
	return nil
end

function _M.check_forbid()
	local forbid_cnt_map = table.new(0, 200)
	-- 从共享内存中把封禁实例信息拿出来，维护worker/特权进程 ngx_worker_cache["forbid_list"] 和  ngx_worker_cache["forbid_list_last"]
	_M.get_forbid_backends()
	if ngx_worker_cache["forbid_list"] and ngx_worker_cache["forbid_list"].next then
		ngx.log(ngx.NOTICE, string.format("check forbid begin [count:%d] [list_node:%s]", ngx_worker_cache["forbid_list"].value, cjson.encode(ngx_worker_cache["forbid_list"].next.value) ))
	else
		ngx.log(ngx.NOTICE, string.format("check forbid begin, list nil [last:%s]", cjson.encode(ngx_worker_cache["forbid_list_last"]) ))
		return
	end
	if ngx_worker_cache["forbid_list"] ~= nil and ngx_worker_cache["forbid_list"].next ~= nil then
        local times = 0
		local now = math.floor(ngx.now())
		local list_count = ngx_worker_cache["forbid_list"].value --  ngx_worker_cache["forbid_list"]为节点， value 标识 forbid_list count

	
		-- 把链表从头到尾遍历一遍，检查是否超时，超时则移除，未超时则继续放回去
		while ngx_worker_cache["forbid_list"].next do  -- 除去空节点, 至少还有一个节点
			if times >= list_count then
				break
			end
			local node = ngx_worker_cache["forbid_list"].next
            local tmp_table = node.value 
			local service = tmp_table["service"]
			local bns = tmp_table["bns"]
			local backend = tmp_table["backend"]
			local idc = tmp_table["idc"]

			-- 都不在bns中
			local backend_ok = _M.check_backend_in_bns(backend, bns)

			-- 最新的backend保存数据
			local backend_info = ngx_worker_cache["dynamic_cache"][service]["backend"][backend]
			--ngx.log(ngx.DEBUG, string.format("check forbid, check backend_info [backend:%s] [service:%s] [info:%s]", cjson.encode(tmp_table["backend"]), cjson.encode(tmp_table["service"]), cjson.encode(backend_info)))
			if not backend_ok then
				--机器下线，不在bns中，直接移除
				_M.unforbid_backend(tmp_table)
			elseif not backend_info then
				_M.unforbid_backend(tmp_table)
			elseif backend_info["forbidden"] and backend_info["forbidden"][bns] then
                if backend_info["ftime"] and backend_info["ftime"] > tmp_table["forbid_time"] then
					--说明链表中封禁的节点已经不是最新的，会有重复
					goto remove
				end
				if tmp_table["check_time"] == nil or tmp_table["check_time"] + tmp_table["forbid_timeout"] < now then
					--超时处理
					local t = utils.explode(":", tmp_table["backend"])
					local ok = _M.check_backend_ok(t[1], tonumber(t[2]))
					if not ok then
						--上报异常机器
        				moniter.backend_collect(ngx_worker_cache["static_cache"]["configs"], service, bns, idc, backend, 1, nil)
						--超时了继续封禁
                        _M.forbid_backend(tmp_table, now)
						--统计一下每个service 下面封禁多少个
						_M.add_forbid_cnt_map(forbid_cnt_map, service, bns, idc)
					else
						_M.unforbid_backend(tmp_table)
					end
				else					
					--未超时
                    _M.forbid_backend(tmp_table, tmp_table["check_time"])
					--统计一下每个service 下面封禁多少个
					_M.add_forbid_cnt_map(forbid_cnt_map, service, bns, idc)
				end
			end
            :: remove ::
			--移除，同时移到下一个节点
			_M.remove_head()
			times = times + 1
        end
	else
		ngx.log(ngx.NOTICE, "check forbid nil, wait for next timer")
	end

	--检查一下封禁backend计数是否有问题
	for service, service_info in pairs(forbid_cnt_map) do
		for bns, bns_info in pairs(service_info) do
			for idc, cnt in pairs(bns_info) do
				local forbid_cnt = _M.check_service_forbid_cnt(service, bns, idc, cnt)
				if forbid_cnt then
					ngx.log(ngx.WARN, string.format("UFC_BUG, service:%s, bns:%s, idc:%s forbid_cnt:%d is not list_cnt:%d", service, bns, idc, forbid_cnt, cnt))
				end
			end
		end
		ngx.sleep(0)
	end

	if ngx_worker_cache["forbid_list"] ~= nil and ngx_worker_cache["forbid_list"].next == nil then
		-- 强制让list和last进行重置
		ngx_worker_cache["forbid_list"] = nil
		ngx_worker_cache["forbid_list_last"] = nil
	end
	if ngx_worker_cache["forbid_list"] and ngx_worker_cache["forbid_list"].next then
		ngx.log(ngx.NOTICE, string.format("check forbid end [count:%d] [list_node:%s]", ngx_worker_cache["forbid_list"].value, cjson.encode(ngx_worker_cache["forbid_list"].next.value) ))
	else
		ngx.log(ngx.NOTICE, string.format("check forbid end, list nil [last:%s]", cjson.encode(ngx_worker_cache["forbid_list_last"]) ))
	end
end

-- 还在封禁中, 需要检查连通性
function _M.check_backend_ok(host, port)
    if not host or not port then
        return false
    end
    local socket = ngx.socket.tcp()
    socket:settimeout(conf["forbid_connect_timeout"])
    local ret, err = socket:connect(host, tonumber(port))
    socket:close()
    if not ret then -- 不能解封
        return false
    else            -- 需要解封
        return true
    end
end


--封禁单机，维护非共享内存 ngx_worker_cache["forbid_list_last"] 和   ngx_worker_cache["forbid_list"]
function _M.forbid_backend(node, now)
	_M.append_node(node["service"], node["bns"], node["forbid_time"], now, node["backend"], node["forbid_timeout"], node["idc"], node["check_ok_times"])
end


--解封单机
function _M.unforbid_backend(node)
	_M._cache_unforbid_backend(node["service"], node["backend"], node["bns"], node["idc"], node["check_ok_times"])
	_M._cache_init_backend(node["service"], node["backend"], true)
end

function _M._cache_init_backend(ufc_service_name, backend, force)
    local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
    if (not force) and dy_config["backend"][backend] then
        return
    end

    if not dy_config["backend"][backend] then
        --ngx.log(ngx.DEBUG, string.format("ufc_service_name %s backend %s is reset", ufc_service_name, backend))
        dy_config["backend"][backend] = {}
    end

    if dy_config["backend"][backend]["forbidden"] == nil then
        dy_config["backend"][backend]["forbidden"] = {}
    end

    dy_config["backend"][backend]["ctime"] = math.floor(ngx.now())
    dy_config["backend"][backend]["request_cnt"] = 0
    dy_config["backend"][backend]["success_cnt"] = 0
    dy_config["backend"][backend]["fail_cnt"] = 0 -- 这里走不到
	--读写共享内存和luavm内存此处处理逻辑不同
	if conf["meta_table"] then
		shared_memory.clear_backend_response_time(ufc_service_name, backend)
	else
		dy_config["backend"][backend]["response_times"] = {}
	end
    dy_config["backend"][backend]["ratio_request_cnt"] = 0
    --这里简单粗暴的所有状态码范围都重置为0即可
    dy_config["backend"][backend]["1xx"] = 0
    dy_config["backend"][backend]["2xx"] = 0
    dy_config["backend"][backend]["3xx"] = 0
    dy_config["backend"][backend]["4xx"] = 0
    dy_config["backend"][backend]["5xx"] = 0
    dy_config["backend"][backend]["other_status"] = 0
end

-- 写 dynamic_cache, 通过memtable写到共享内存里面
function _M._cache_unforbid_backend(ufc_service_name, backend, bns, idc, check_ok_times)
    local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
    if dy_config["backend"][backend] == nil then
        return false
    end

    if dy_config["backend"][backend]["forbidden"] == nil then
        return false
    end

    if not dy_config["backend"][backend]["forbidden"][bns] then
        return false
    end

    -- 直接解封了
    dy_config["backend"][backend]["forbidden"][bns] = false   
    --增加解封日志
    ngx.log(ngx.NOTICE, string.format("ufc_service_name %s bns %s backend %s is unforbidden", ufc_service_name, bns, backend))
	if check_ok_times > 0 then
		--解封确认
    	dy_config["backend"][backend]["check_ok_times"] = check_ok_times
	end

    if not dy_config["service"]["forbidden_cnt"][bns] then
        dy_config["service"]["forbidden_cnt"][bns] = {}
        return false
    end

    if dy_config["service"]["forbidden_cnt"][bns]["all_idc"] and dy_config["service"]["forbidden_cnt"][bns]["all_idc"] > 0 then
        dy_config["service"]["forbidden_cnt"][bns]["all_idc"] = dy_config["service"]["forbidden_cnt"][bns]["all_idc"] - 1
    end

    --增加idc纬度解封
	if not idc or idc == "all_idc" then
		return false
	end

	--idc维度进行计数
	if dy_config["service"]["forbidden_cnt"][bns][idc] and dy_config["service"]["forbidden_cnt"][bns][idc] > 0 then
		dy_config["service"]["forbidden_cnt"][bns][idc] = dy_config["service"]["forbidden_cnt"][bns][idc] - 1
	end

    return true
end




--检查ip是否已被下线
function _M.check_backend_in_bns(backend, bns)
	local bnss = ngx_worker_cache["static_cache"]["bnss"]
	if not bnss[bns] then
		return false
	end

	local backends = bnss[bns]["backend_map"]
	if not backends then
		return true
	end

	if backends[backend] then
		return true
	end

	return false
end

--从共享内存中把封禁实例信息拿出来
function _M.get_forbid_backends()
	local forbid_backends, len = shared_memory.get_forbid_backends()
	ngx.log(ngx.NOTICE, string.format("get %d forbid backends from worker process", len))
	if len == 0 then
		return
	end
	for i = 1, len do
		local node = forbid_backends[i]
		local data = utils.explode("&", node)
		if #data ~= 8 then
            ngx.log(ngx.WARN, string.format("forbid data %s format error", node))
			return
		end
		-- 塞到本地内存中
		_M.append_node(data[1], data[2], data[3], data[4], data[5], data[6], data[7], data[8])
	end
end

return _M