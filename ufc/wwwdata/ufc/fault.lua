local ngx_worker_cache = require "ngx_worker_cache"
local utils = require "utils"
local cjson = require "cjson.safe"
local tonumber = tonumber

local _M = {
	["doc"] = "ufc fault inject",
	["fault_bns"] = "fault-bns.all",
	["timeout"] = "3600"
}

function _M.fault_data_cleanup(fault)
	local time_now = ngx.now()
	local faut_data = fault["fault"]
	local timeout_id = {}
	for id, content in pairs(faut_data) do
		while true do
			local match = content["match"]
			if not match then
				break
			end
			local end_time = match["end_time"]
			
			if time_now > end_time then
				--故障信息过期
				table.insert(timeout_id, id)
			end

			break
		end
	end

	for _, id in pairs(timeout_id) do
		faut_data[id] = nil
		ngx.log(ngx.INFO, string.format("fault %s is timeout, should be cleanup", id))
	end
end

--[[
	检查是否需要故障注入，以及故障注入的类型，参数
]]
function _M.fault_injection_check(from_service, to_service, from_idc, occur_ufc)
	if not from_service then
		from_service = "-"
	end

	if not to_service then
		to_service = "-"
	end

	if not from_idc then
		from_idc = "-"
	end

	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		return false, nil, nil
	end

	local configs = static_cache["configs"]
	if not configs then
		return false, nil, nil
	end
	
	local fault_config = configs["fault"]
	if not fault_config or not fault_config["enable"] or fault_config["enable"] < 1 then
		return false, nil, nil
	end

	local config_content = fault_config["fault"]
	if not config_content then
		return false, nil, nil
	end

	return _M.fault_injection_select(config_content, from_service, to_service, from_idc, occur_ufc)
end



--[[
	故障注入配置选择
]]
function _M.fault_injection_select(config_content, from_service, to_service,from_idc, occur_ufc)
	local injection_result = {}
	for id , content in pairs(config_content) do
		while true do
			local fault_enable = content["enable"]
			if not fault_enable or fault_enable < 1 then
				break
			end

			local match = content["match"]
			if not match then
				break
			end

			local fault = content["fault"]
			if not fault then
				break
			end

			local start_time = match["start_time"]
			local end_time = match["end_time"]
			local time_now = ngx.now()

			--只要当前时间不在指定时间范围内就不会触发故障注入
			--starttime 和 endtime可以全不指定或者只指定一个
			if start_time and time_now < start_time then
				break
			end

			if end_time and time_now > end_time then
				break
			end

			if match["from_service"] and match["from_service"] ~= from_service and from_service ~= "all" then
				break
			end
	
			if match["to_service"] and match["to_service"] ~= to_service and to_service ~= "all" then
				break
			end
	

			if match["from_idc"] and match["from_idc"] ~= from_idc and from_idc ~= "all" then
				break
			end
			
			--如果不指定，故障默认发生在上游ufc
			local occur_ufc_in_config = match["occur_ufc"] or "up"
			if occur_ufc_in_config and occur_ufc_in_config ~= occur_ufc and occur_ufc_in_config ~= "all" then
				break
			end
			--判断是否通过header校检的标志位
			if not _M.fault_injection_header_select(match) then
				break
			end

			--判断是否通过query string校检
			if not _M.fault_injection_query_string_select(match) then
				break
			end
			
			if fault["delay_fault"] then
				injection_result["delay_fault"] = fault["delay_fault"]
			end

			if fault["abort_fault"] then
				injection_result["abort_fault"] = fault["abort_fault"]
			end

			if fault["bypass_fault"] then
				injection_result["bypass_fault"] = fault["bypass_fault"]
			end

			if fault["direct_rewrite_fault"] then
				injection_result["direct_rewrite_fault"] = fault["direct_rewrite_fault"]
			end

			do
				local fault_key = "fault-hit-" .. id
				local ttl = end_time - time_now + tonumber(_M["timeout"])
				local shared_fault_data = ngx.shared.fault

				shared_fault_data:set(fault_key, 1, ttl)
				ngx.log(ngx.NOTICE, string.format("fault key %s, set hit 1, ttl %d", fault_key, ttl))

				return true, injection_result, id
			end

			break
		end
	end

	return false, nil, nil
end

--[[
	如果故障注入配置中有header配置，那么判断该请求是否命中了header
]]
function _M.fault_injection_header_select(content)
	local headers = ngx.req.get_headers()
	if not content["http_headers"] then
		return true
	end

	for k, v in pairs(content["http_headers"]) do 
		if not headers[k] or headers[k] ~= v then
			return false
		end
	end

	return true
end

--[[
	如果故障注入配置中有query_string配置，那么判断该请求是否包含所有query string
]]
function _M.fault_injection_query_string_select(content)
	if not content["query_string"] then
		return true
	end
	
	local args = ngx.req.get_uri_args()
	for k, v in pairs(content["query_string"]) do 
		if not args[k] or args[k] ~= v then
			return false
		end
	end

	return true
end

--[[
	延时故障处理
]]
function _M.delay_fault_deal(fault_content)
	local dealy_fault = fault_content["delay_fault"]
	if not dealy_fault then 
		return
	end

	local probability = dealy_fault["probability"] or 50
	if math.random(100) > probability then
		return
	end
	--默认延时0.5
	local delay_time = dealy_fault["delay_time"] or 0.5
	ngx.sleep(delay_time)
end

--[[
	旁路式故障处理
]]
function _M.bypass_fault_deal(fault_content, res, ctxs, ufc_service_name, from_service_name)
	local bypass_fault = fault_content["bypass_fault"]
	if not bypass_fault then
		return nil
	end

	local ip = bypass_fault["ip"]
	local port = tonumber(bypass_fault["port"])
	if not ip or not port then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s from_service %s to_service %s fault ip or port is nil", logid, from_service_name, ufc_service_name))
		return nil
	end

	local probability = bypass_fault["probability"] or 50
	if math.random(100) > probability then
		return nil
	end

	ctxs["bns"] = _M["fault_bns"]
	local backend = ip .. ":" .. port
	ctxs["backend"] = backend
	
	res["host"] = ip
	res["port"] = port
	res["bns"] = _M["fault_bns"]
	res["x-ufc-service-name"] = ufc_service_name
	res["x-ufc-self-service-name"] = from_service_name
	res["error_msg"] = "E_OK"
	return res
end

--[[
	获取dump转发的下游
]]
function _M.get_proxy_backend_with_dump(fault_content, ufc_service_name, from_service_name)
	local bypass_fault = fault_content["bypass_fault"]
	if not bypass_fault then
		return nil, nil
	end

	local ip = bypass_fault["ip"]
	local port = tonumber(bypass_fault["port"])
	if not ip or not port then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s from_service %s to_service %s fault ip or port is nil", logid, from_service_name, ufc_service_name))
		return nil, nil
	end

	local probability = bypass_fault["probability"] or 50
	if math.random(100) > probability then
		return nil, nil 
	end

	return ip, port
end

--[[
	abort 故障处理
]]
function _M.abort_fault_deal(fault_content, bypass, res)
	local abort_fault = fault_content["abort_fault"]
	if not abort_fault then
		return false, false
	end

	local rewrite = abort_fault["rewrite"]
	if rewrite and rewrite == 1 then
		return false, true
	end

	local probability = abort_fault["probability"] or 50
	if math.random(100) > probability then
		return false, false
	end

	local fault_status = abort_fault["http_code"] or 502
	local fault_body = abort_fault["fault_body"]
	--ngx.log(ngx.WARN, string.format("abort fault is occur"))
	--穿透式直接exit就行
	ngx.status = fault_status
	if fault_body ~= nil and type(fault_body) == "string" then
		ngx.print(fault_body)
	end
	ngx.exit(fault_status)
	return true, false
end

--穿透式rewrite故障处理
--把解析到的fault ip, port放到ufc ctx里面
function _M.direct_rewrite_fault(fault_content)
	local direct_rewrite_fault = fault_content["direct_rewrite_fault"]
	if not direct_rewrite_fault then
		return
	end

	local ip = direct_rewrite_fault["ip"]
	local port = direct_rewrite_fault["port"]
	port = tonumber(port)
	if ip ~= nil and port ~= nil then
		local ctxs = ngx.ctx.ufc_ctxs
		ctxs["ufc_fault_ip"] = ip
		ctxs["ufc_fault_port"] = port
	end
end

return _M
