local dynamic_conf = require "dynamic_conf"
local conf = require "conf"
local utils = require "utils"
local ngx_worker_cache = require "ngx_worker_cache"
local fault = require "fault"
local dump = require "dump"

local _M = {
	["doc"] = "this file exist just because proxy set header is available in this stage",
}

function _M.replace_host_if_necessary()
	local host = ngx.var.host
	if host == nil or conf["auto_replace_host"][host] == 1 then
		ngx.var.ufc_host = utils.get_hostname()  -- var里面几个变量,重点掌握
	else
		ngx.var.ufc_host = host
	end
end

--将heaer里面的ufc私有header去掉
function _M.remove_ufc_header(headers)
	--去掉上游添加的告知下游ufc本地port的head
	for k, v in pairs(headers) do
		if string.find(k, "x-ufc-port-", 1, true) ~= nil then
			ngx.req.set_header(k, nil)
		end
	end

	--去掉上游超时时间from—remote, from-idc头
	ngx.req.set_header("x-ufc-timeout", nil)
	ngx.req.set_header("x-ufc-request-from-remote", nil)
	ngx.req.set_header("x-ufc-from-idc", nil)
end

--请求是从远端ufc来的应该获得dealine后把请求发给本地服务
function _M.redict_to_local(ctxs, headers, from_ip, fault_injection_flag, fault_injection_result)
	local local_ip = ngx_worker_cache["local_ip"]
	if not local_ip then
		return
	end

	local from_service = ctxs["from_service_name"] 
	local to_service = ctxs["ufc_service_name"] 
	
	if dump.dump_match(headers, from_service, to_service) == true then
		-- ctxs[dump_match] 用于后续response阶段判断是否要落到 db
		ngx.var.ufc_fault_injection = "dump_db"
		ctxs["dump_match"] = 1		
		-- 用于发送给 proxy
		ctxs["dump_from_ip"] = from_ip
		ctxs["req_method"] =   ngx.var.request_method
		ctxs["req_uri"]    =   ngx.var.request_uri

		ngx.req.read_body()
		ctxs["req_body"] =  ngx.req.get_body_data()
		ngx.timer.at(0, dump.send_dump_request_to_db, ctxs, from_service, to_service)
	end
	
	local timeout_key = "x-ufc-timeout"
	local timeout = headers[timeout_key]
	_M.remove_ufc_header(headers)

	if timeout then
		--设置deadline头
		local deadline = ngx.now() + timeout/1000.0
		ngx.req.set_header("x-ufc-deadline", deadline)
	end
	--delay故障在生成deadline之后注入
	if fault_injection_flag then
		ngx.var.ufc_fault_injection = 1
		fault.delay_fault_deal(fault_injection_result)
		local abort_fault_flag = false
		abort_fault_flag= fault.abort_fault_deal(fault_injection_result)
		if abort_fault_flag then
			--触发abort故障，返回
			return
		end
	end

	--head里的超时时间
	if headers["x-ufc-connect-timeout"] then 
		ctxs["ufc-connect-timeout"] = headers["x-ufc-connect-timeout"]
	end

	if headers["x-ufc-rw-timeout"] then  
		ctxs["ufc-rw-timeout"] = headers["x-ufc-rw-timeout"]
	end
	ctxs["x-ufc-request-from-remote"] = 1
	ctxs["headers"] = headers
	ngx.ctx.ufc_ctxs = ctxs
end


function _M.main()
	--有些业务会把deadline的head透传给下游，这里把这个头去掉
	ngx.req.set_header("x-ufc-deadline", nil)
	local from_service_name, ufc_service_name, ori_service_name
	local error_msg
	--这里对于穿透式我们加个0强制把所有header读出来，openresty默认只读100个
	--service 选择，小流量选择，双端等逻辑强依赖一些header，因此我们不能漏掉
	local headers = ngx.req.get_headers(0)
	--ngx.ctx.headers = headers
	local ctxs = {}
	ngx.ctx.ufc_ctxs = ctxs
	utils.gen_ufc_log(headers, ctxs)

	if headers["x-product"] then  -- 这是什么header
		ngx.var.ufc_product = headers["x-product"]
	end

	if headers["x-ufc-sdk-version"] then
		ngx.var.ufc_sdk_version = headers["x-ufc-sdk-version"]
	end

	--some api do not permit host is "localhost", so we replace it to local hostname if necessary
	_M.replace_host_if_necessary()

	if not ngx_worker_cache.is_inited() then
		if not conf["backup_ufc"] then
			return conf.exit("E_NO_SERVICE")
		else
			return false
		end
	end

	--check whether service registered or whether service forbidden
	from_service_name, ufc_service_name, ori_service_name = dynamic_conf.get_ufc_service_name(ngx.var.uri, headers)
	--在沙盒环境中服务没有注册也可以访问
	if conf["sandbox"] == true then
		if ori_service_name ~= nil and ufc_service_name == nil then
			ufc_service_name = ori_service_name
			ctxs["not_exist_service_in_sandbox"] = true
		end
	end

	ctxs["ufc_service_name"] = ufc_service_name
	ctxs["ori_ufc_service_name"] = ori_service_name
	ctxs["from_service_name"] = from_service_name

	--地域合法性校检查
	local verification_flag = ngx_worker_cache.request_verification(from_service_name, ufc_service_name)
	if verification_flag == false then
		return
	end
	
	local occur_ufc = "up"
	local should_redict_to_local = false
	--后面做了兼容，即使local_ip没获取到为空也没关系
	local from_ip = ngx_worker_cache["local_ip"] 
	local to_ip = "all"
	local from_idc 
	if headers["x-ufc-idc"] then
		from_idc = headers["x-ufc-idc"]
	elseif ngx_worker_cache["local_idc"] then
		from_idc = ngx_worker_cache["local_idc"]
	end

	ctxs["from_platform"] = headers["x-ufc-platform-value"] 
	ctxs["from_app"] = headers["x-ufc-app-value"]
	
	if headers["x-ufc-request-from-remote"] then
		--请求是从远端ufc来的应该获得dealine后把请求发给本地服务
		ngx.var.ufc_request_from = "remote"
		should_redict_to_local = true
		occur_ufc = "down"
		--此时ufc处于下游，from_ip用远端ip
		from_ip = ngx.var.remote_addr 
		--此时ufc处于下游，from_idc用head里面传过来的上游idc
		from_idc = headers["x-ufc-from-idc"]
	end


	--故障注入判断
	local fault_injection_result, fault_injection_flag, fault_id

	fault_injection_flag, fault_injection_result, fault_id = fault.fault_injection_check(from_service_name, ufc_service_name, from_idc, occur_ufc)
	
	if should_redict_to_local == true then
			-- fromip: hostname -i 获取到的ip
		ngx.req.set_header("X-UFC-Forward-IP", from_ip)
		return _M.redict_to_local(ctxs, headers, from_ip, fault_injection_flag, fault_injection_result)
	end


	if fault_injection_flag then
		ngx.var.ufc_fault_injection = fault_id
		fault.delay_fault_deal(fault_injection_result)
		--穿透式流量发送到指定实例故障发生在延时故障之后
		--如果同时有abort故障，该故障不会生效
		fault.direct_rewrite_fault(fault_injection_result)

		local abort_fault_flag = false
		local rewrite_flag = false

		abort_fault_flag, rewrite_flag = fault.abort_fault_deal(fault_injection_result, 0)
		if rewrite_flag == true then
			dynamic_conf.send_request_to_chaos(ctxs, headers, fault_id)
		end

		if abort_fault_flag then
			return
		end

		-- 故障注入模式开始时，先提前获取 ip:port 
		local ip, port = fault.get_proxy_backend_with_dump(fault_injection_result, ufc_service_name, from_service_name)
		if ip and port then
			ngx.var.ufc_fault_injection = fault_id
			ctxs["to_dump_proxy"] = 1
			ctxs["dump_proxy_ip"] = ip
			ctxs["dump_proxy_port"] = port
		end
	end

	if not ufc_service_name then
		error_msg = "E_NO_SERVICE"
	else
		error_msg = dynamic_conf.check_flow_limit(from_service_name, ufc_service_name, headers)
	end
	if error_msg then
		return conf.exit(error_msg)
	end

	--head里的超时时间
	if headers["x-ufc-connect-timeout"] then 
		ctxs["ufc-connect-timeout"] = headers["x-ufc-connect-timeout"]
	end

	if headers["x-ufc-rw-timeout"] then  
		ctxs["ufc-rw-timeout"] = headers["x-ufc-rw-timeout"]
	end

	--header里面的重试次数覆盖配置里面的重试次数
	if headers["x-ufc-retry-count"] then
		ctxs["x-ufc-retry-count"] = headers["x-ufc-retry-count"]
	end

	--ngx.req.set_header("X-Ufc-Service-Name", ufc_service_name)
	--ngx.ctx.ufc_service_name = ufc_service_name
	--ngx.ctx.ori_ufc_service_name = ori_service_name
	--ngx.ctx.from_service_name = from_service_name
	--双端模式需要提前选backends便于告诉下游ufc本机端口号
	--沙盒中的双端模式不存在不注册服务也让使用的问题
	local double_ends_backends = dynamic_conf.select_backends_for_double_ends(from_service_name, ufc_service_name, from_ip, headers, ctxs)
	--返回值不为空启动双端模式
	if double_ends_backends ~= nil then
		--此请求由双端模式的上游发出
		ctxs["double_ends"] = 0
	end

	ctxs["double_ends_backends"] = double_ends_backends
	ctxs["headers"] = headers
	ngx.ctx.ufc_ctxs = ctxs
end

_M.main()
