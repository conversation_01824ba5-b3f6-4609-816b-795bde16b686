local utils = require "utils"
local dump = require "dump"
local trace = require "trace"

--local cjson = require "cjson.safe"
local ngx_worker_cache = require "ngx_worker_cache"

local conf = require "conf"
local dynamic_conf = require "dynamic_conf"
local tonumber = tonumber
local table = table

local ctxs = ngx.ctx.ufc_ctxs or {}
local shared_memory = require "shared_memory"
local ngx_wroker_process_cron = require "ngx_worker_process_cron"

--[[
local ufc_service_name = ngx.ctx.ufc_service_name or "-"
local ori_ufc_service_name = ngx.ctx.ori_ufc_service_name or "-"
local from_service_name = ngx.ctx.from_service_name or "-"
--]]

local ufc_service_name = ctxs["ufc_service_name"] or "-"
local ori_ufc_service_name = ctxs["ori_ufc_service_name"] or "-"
local from_service_name = ctxs["from_service_name"] or "-"
local ufc_time = ctxs["ufc_time"]

local last_update_time = shared_memory.get("static_cache", "last_update_time")
local update_lantency = nil
if last_update_time then 
	local time_now = ngx.time()
	update_lantency = time_now - last_update_time
	ngx_wroker_process_cron.update_lantency_upload(update_lantency, time_now)
	--默认5分钟配置没有更新就报警
	if update_lantency >= utils["max_update_interval"] then
		utils.update_error_log(update_lantency)
	end
end
local callid = ngx.var.ufc_callid

--local bnss = ngx.ctx.bnss or {"-"}
local bnss = ctxs["bnss"] or {"-"}

--local from_idc = ngx.ctx.from_idc
local from_idc = ctxs["from_idc"] or "-"

local from_logic_idc = "-"
if from_idc and conf["idc_map"][from_idc] then
	from_logic_idc = conf["idc_map"][from_idc]
end

local from_platform = ctxs["from_platform"]
if from_platform and from_platform ~= "-" then 
	-- 虽然不一定开发 platformmap, 但是打日志这里还是加一下
	if from_logic_idc == "-" then
		from_logic_idc = from_platform
	else 
		from_logic_idc = from_platform .. from_logic_idc
	end
end 

--local to_logic_idc = ngx.ctx.idc or "-"
local platforms_idc = ctxs["idc"] or "-"
local to_physics_idc = ctxs["to_physics_idc"] or "-"

--local method = ngx.ctx.method
local method = ctxs["method"] or ""
if type(method) == "table" then
	method = method[#method]
end

if method ~= "-" then
	ngx.var.ufc_uri = ngx.var.ufc_uri .. "?method=" .. method
end

-- send dump response begin
if ctxs["dump_match"] == 1 then	
	ctxs["resp_status"] =  ngx.status
	ctxs["resp_header"] =  ngx.resp.get_headers()
	ngx.timer.at(0, dump.send_dump_response_to_db, ctxs, from_service_name, ufc_service_name)
end -- send dump response end

local var_upstream_status = ngx.var.upstream_status
local var_upstream_addr = ngx.var.upstream_addr
local var_upstream_response_time = ngx.var.upstream_response_time
local upstream_status, upstream_addr, upstream_response_time

local ufc_cost_time = ctxs["ufc_cost_time"] or 0
ngx.var.ufc_cost_time = ufc_cost_time
ngx.var.ufc_service_name = ufc_service_name
ngx.var.update_lantency = update_lantency or "-"
ngx.var.ori_from_service_name = from_service_name
-- 如果有传递app和平台信息，from_service 打印app-platform
if type(ctxs["from_app"]) == "string" and ctxs["from_app"] ~= "" and type(ctxs["from_platform"]) == "string" and ctxs["from_platform"] ~= "" then
	ngx.var.from_service_name = ctxs["from_app"] .. "-" .. ctxs["from_platform"]
else
	ngx.var.from_service_name = from_service_name
end
ngx.var.ori_ufc_service_name = ori_ufc_service_name
ngx.var.method = method
ngx.var.from_logic_idc = from_logic_idc

--最多重试4次，用8个变量就够
local ufc_log = ngx_worker_cache["ufc_log"]
ufc_log["ufc_log1"] = ""
ufc_log["ufc_log2"] = ""
ufc_log["ufc_log3"] = ""
ufc_log["ufc_log4"] = ""
ufc_log["ufc_log5"] = ""
ufc_log["ufc_log6"] = ""
ufc_log["ufc_log7"] = ""
ufc_log["ufc_log8"] = ""
local upstream_addr_len = 0 

if var_upstream_addr then
	if string.sub(var_upstream_status, -1, -1) == " " then
		upstream_status = utils.explode(", ", string.sub(var_upstream_status, 1, -4))
		upstream_addr = utils.explode(", ", string.sub(var_upstream_addr, 1, -4))
		upstream_response_time = utils.explode(", ", string.sub(var_upstream_response_time, 1, -4))
	else
		upstream_status = utils.explode(", ", var_upstream_status)
		upstream_addr = utils.explode(", ", var_upstream_addr)
		upstream_response_time = utils.explode(", ", var_upstream_response_time)
	end

	--ngx.log(ngx.DEBUG, "var_upstream_status is " .. var_upstream_status .. " upstream_addr is " .. var_upstream_addr .. " upstream_response_time is " .. var_upstream_response_time)
	
	--ngx.log(ngx.NOTICE, "logid " ..logid.." var_upstream_status is " .. cjson.encode(upstream_status) .. " upstream_addr is " .. cjson.encode(upstream_addr) .. " upstream_response_time is " .. cjson.encode(upstream_response_time))
	if upstream_addr then
		upstream_addr_len = #upstream_addr
		for i=1,upstream_addr_len do
			--对于双端模式log穿透式阶段做额外的处理，双端模式的上游ufc端口替换成真实端口，下游不进行request_call back计数
			--如果走混沌工程proxy，即使是双端就不应替换端口
			local backend = upstream_addr[i]
			if ctxs["double_ends"] and ctxs["double_ends"] == 0 and not ctxs["to_dump_proxy"] then
				--双端模式的上游替换
				backend = dynamic_conf.port_replace(backend, ctxs)
			end


			local double_ends_down = ctxs["double_ends"] or -1

			ufc_log["ufc_log" .. (1 + (i -1) * 2 )] = "[callid=" .. callid  .. " cost_time=" .. upstream_response_time[i] .. " to_module=" .. ufc_service_name
			ufc_log["ufc_log" .. (2 * i)] = " to_bns=" .. bnss[i] .. " to_ip=" .. backend .. " to_idc=" .. to_physics_idc .. " upstream_status=" .. upstream_status[i] .."]"
			--双端模式的下游不进行request计数
			if conf["backup_ufc"] ~= backend and backend ~= "-" and double_ends_down ~= 1 then
				if platforms_idc and platforms_idc == "-" then
					dynamic_conf.request_callback(from_service_name, ufc_service_name, bnss[i], backend, tonumber(upstream_status[i]), upstream_response_time[i], 0, nil)
				else
					dynamic_conf.request_callback(from_service_name, ufc_service_name, bnss[i], backend, tonumber(upstream_status[i]), upstream_response_time[i], 0, platforms_idc)
				end
			end
		end
	end
end

if upstream_addr_len == 0 then
	ufc_log["ufc_log1"] = "]"
else
	local temp = "ufc_log" .. (upstream_addr_len * 2)
	ufc_log[temp] = ufc_log[temp] .. "]"
end

ngx.var.ufc_log1 = ufc_log["ufc_log1"]
ngx.var.ufc_log2 = ufc_log["ufc_log2"]
ngx.var.ufc_log3 = ufc_log["ufc_log3"]
ngx.var.ufc_log4 = ufc_log["ufc_log4"]
ngx.var.ufc_log5 = ufc_log["ufc_log5"]
ngx.var.ufc_log6 = ufc_log["ufc_log6"]
ngx.var.ufc_log7 = ufc_log["ufc_log7"]
ngx.var.ufc_log8 = ufc_log["ufc_log8"]
