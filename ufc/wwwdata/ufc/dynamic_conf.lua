local conf = require "conf"
local utils = require "utils"
local cjson = require "cjson.safe"
--local balancer = require "ngx.balancer"
local ngx_worker_cache = require "ngx_worker_cache"
local breaker = require "breaker"
local idc_map = require "idc_map"
local moniter = require "moniter"
local breaker_policy = require "breaker_policy"
local http = require "ufclib.Http"
local stream_utils = require "stream_utils"
local sandbox = require "sandbox"
local shared_memmory = require "shared_memory"
local lock = require "lock"
local ngx = ngx
local worker_process_cache = require "worker_process_cache"
local resty_chash = require "resty.chash"


--local global_running_cnt_balancer = require "global_running_cnt_balancer"

local _M = {
	["doc"] = "nginx worker config, including static config such as \
	all backends for all services... \
	and dynamic config, including forbidden node and \
	node request result...",
	["quantile_vaule"] = 99,
	["quantile_time_interval"] = 300
}

-- when backend was forbidden and forbid not time out, return false
function _M._is_backend_can_connect(ufc_service_name, backend, bns)
	--读写共享内存和luavm内存此处处理逻辑不同
	if conf["meta_table"] then
		if not shared_memmory.get_dynamic_data5(ufc_service_name, "backend", backend, "forbidden", bns) then
			return true
		end
	else
		local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
		if not dy_config["backend"][backend] then  -- 说明还不存在dynamic里面
			return true
		end
	
		if not dy_config["backend"][backend]["forbidden"] then -- 不封禁
			return true
		end
	
		if not dy_config["backend"][backend]["forbidden"][bns] then -- 不封禁
			return true
		end
	end
	return false
end

-- entrance: judge whether a backend is able to use, consider a lot of results, such as:
-- forbidden, in blacklist, 5xx normal or not
function _M._is_backend_ok(ufc_service_name, bns, backend)
	local config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	--local inner_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	--local bp = inner_config["bp"]

	--local request_reset_interval = config["request_reset_interval"]

	if not _M._is_backend_can_connect(ufc_service_name, backend, bns) then
		return false
	end

	if config["black_list"][backend] == 1 then
		return false
	end

	return true
end

function _M._round_robin_select(ufc_service_name, bns, bypass, platforms_idc)
	local dy_config =ngx_worker_cache["dynamic_cache"][ufc_service_name]
	--ngx.log(ngx.DEBUG, "ufc service name is " .. ufc_service_name .. ", dy_config is " .. cjson.encode(ngx_worker_cache["dynamic_cache"]))
	local backends_cnt = idc_map.get_backends_cnt(bns, platforms_idc)

	local default_index = math.random(1, backends_cnt)
	local current_index = dy_config["current_index"][bns] or default_index
	if platforms_idc and platforms_idc ~= "all_idc" then
		if not dy_config["current_index"][platforms_idc] then
			current_index = default_index
		else
			current_index = dy_config["current_index"][platforms_idc][bns] or default_index
		end
	end

	--ngx.log(ngx.DEBUG, string.format("for bns %s current_index is %s", bns, current_index))
	local host, port, backend, index
	--ngx.log(ngx.DEBUG, string.format("for bns %s all backends is %s", bns, cjson.encode(backends)))

	local info
	for k=0, backends_cnt - 1 do
		index = (current_index + k) % backends_cnt + 1
		info = _M._get_backend(bns, index)
		if info then
			host = info["host"]
			port = info["port"]
			backend = host .. ":" ..  port
			--ngx.log(ngx.DEBUG, string.format("for bns %s index is %s node is %s", bns, index, backend))
			breaker.init_backend(ufc_service_name, backend)  -- 为dynamic中初始化这个backend
			-- 如果 backend_cnt 下只有一个实例，则不做封禁检测
			if _M.is_forbid_disable(backends_cnt) or _M._is_backend_ok(ufc_service_name, bns, backend) then
				if platforms_idc then
					if not dy_config["current_index"][platforms_idc] then
						dy_config["current_index"][platforms_idc] = {}
					end
					dy_config["current_index"][platforms_idc][bns] = index
				else
					dy_config["current_index"][bns] = index
				end

				return host, port
			end
		end
	end

	ngx.log(ngx.WARN, string.format("round_robin_select error, bns %s current_index is %s backends %d", bns, current_index, backends_cnt))
	return nil, nil
end

function _M.is_forbid_disable(backend_cnt)
	if backend_cnt and backend_cnt == 1 then
		local ctxs = ngx.ctx.ufc_ctxs
		ctxs["is_forbid_disable"] = true		
	end
end 


function _M._get_backend(bns, index)
	local bnss = ngx_worker_cache["static_cache"]["bnss"][bns]
	if not bnss then
		return nil
	end

	-- ngx.ctx.idc_maps 保存的是选择物理idc后的数组，调整后变成 idc-platform 的数组
	local platforms_idcmaps = ngx.ctx.platforms_idcmaps
	if platforms_idcmaps and type(platforms_idcmaps) == "table" then
		local max_index = 0
		local min_index = 0
		for _, platforms_physical_idc in pairs(platforms_idcmaps) do
			if bnss[platforms_physical_idc] and #bnss[platforms_physical_idc] > 0 then
				max_index = max_index + #bnss[platforms_physical_idc]

				if index <= max_index then
					local real_index = index - min_index
					if bnss[platforms_physical_idc][real_index] then
						--日志上打的to_idc应该是真是的物理idc
						local ctxs = ngx.ctx.ufc_ctxs
						ctxs["to_physics_idc"] = platforms_physical_idc
						return bnss[platforms_physical_idc][real_index]
					end
				end

				min_index = max_index
			end
		end
	end

	local backends = bnss["bns_nodes"]
	if not backends then
		return nil
	end

	return backends[index]
end


function _M._random_select(ufc_service_name, bns, bypass, platforms_idc)
	if bypass == 1 then
		return _M._round_robin_select(ufc_service_name, bns, bypass, platforms_idc)
	end

	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	local backends_cnt = idc_map.get_backends_cnt(bns, platforms_idc)

	local host, port, backend, index
	local max_tries = conf["random_max_tries"] or backends_cnt

	local info
	for i = 1, max_tries do
		index = math.random(1, backends_cnt)
		info = _M._get_backend(bns, index)
		if info then
			host = info["host"]
			port = info["port"]
			backend = host .. ":" .. port
			breaker.init_backend(ufc_service_name, backend)  -- 为dynamic中初始化这个backend
			if _M.is_forbid_disable(backends_cnt) or _M._is_backend_ok(ufc_service_name, bns, backend) then
				return host, port
			end
		end
	end

	return nil, nil
end

function _M._clean_timeout_worker(service_name)
	local redis_cli, err = conf.get_redis()
	if err ~= nil then
		return false
	end
	local prefix = conf.get_redis_prefix(4)

	local service_config = ngx_worker_cache["static_cache"]["services"][service_name]
	local bns_list = service_config["bns_list"]
	local hash_pool_size = service_config["hash_pool_size"]
	local worker_timeout = service_config["worker_timeout"]
	for hash_pool_index = 0, hash_pool_size - 1 do
		--ngx.log(ngx.DEBUG, string.format("cleaning %s hash pool %s", service_name, hash_pool_index))
		for bns, _ in pairs(bns_list) do
			local hash_key = string.format("%s-%s-%s-hash-pool-%d", prefix, service_name, bns, hash_pool_index)
			local all_backends, err = redis_cli:zrange(hash_key, 0, -1)
			--ngx.log(ngx.DEBUG, string.format("cleaning %s %s hash pool %s", service_name, bns, hash_pool_index))
			if not err then
				for _, backend in pairs(all_backends) do
					--ngx.log(ngx.DEBUG, string.format("checking bns %s backend %s", bns, backend))
					local timestamp_key = string.format("%s-%s", hash_key, backend)
					local timestamps, err = redis_cli:hgetall(timestamp_key)
					if not err then
						timestamps = utils.format_redis_hash_res(timestamps)
						--ngx.log(ngx.DEBUG, string.format("timestamps is %s", cjson.encode(all_alloc_times)))
						for timestamp, _ in pairs(timestamps) do
							if ngx.now() - timestamp > worker_timeout then
								--ngx.log(ngx.DEBUG, string.format("pool %s backend %s timeout", hash_pool_index, backend))
								redis_cli:hdel(timestamp_key, timestamp)
								--ngx.log(ngx.DEBUG, string.format("key is %s, field is %s", key, timestamp))
								redis_cli:zincrby(hash_key, -1, backend)
							end
						end
					end
				end
			end
		end
	end
	redis_cli:close()
end

function _M._cron_clean_timeout_worker()
	local redis_cli, err
	local prefix, last_clean_time
	ngx.timer.at(conf["clean_timeout_worker_min_interval"], _M._cron_clean_timeout_worker)
	redis_cli, err = conf.get_redis()
	if err then
		return false
	end

	prefix = conf.get_redis_prefix(4)
	last_clean_time, err = redis_cli:get(string.format("%s-last-clean-timeout-worker-time", prefix))
	if err then
		redis_cli:close()
		return false
	end
	if last_clean_time ~= ngx.null and ngx.now() - tonumber(last_clean_time) < conf["clean_timeout_worker_min_interval"] then
		redis_cli:close()
		return false
	end

	local services = ngx_worker_cache["static_cache"]["services"]
	for service_name, service_config in pairs(services) do
		if service_config["bp"] == conf["balance_policy"]["GLOBAL_IDLE"] then
			--ngx.log(ngx.DEBUG, string.format("start cleaning %s timeout worker", service_name))
			_M._clean_timeout_worker(service_name)
		end
	end

	redis_cli:set(string.format("%s-last-clean-timeout-worker-time", prefix), ngx.now())
	redis_cli:close()
end

function _M._idle_select(ufc_service_name, bns, bypass, idc)
	if bypass == 1 then
		return _M._round_robin_select(ufc_service_name, bns, bypass, idc)
	end

	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]

	local backends = _M._get_backends(bns, idc)

	local host, port, backend, index, avg_response_time
	local idle_host, idle_port
	local request_cnt, response_times
	local max_tries = conf["random_max_tries"] or #backends

	for i = 1, max_tries do
		index = math.random(1, #backends)
		host = backends[index]["host"]
		port = backends[index]["port"]
		backend = host .. ":" .. port
		breaker.init_backend(ufc_service_name, backend)
		request_cnt = dy_config["backend"][backend]["request_cnt"]
		response_times = dy_config["backend"][backend]["response_times"]
		if _M._is_backend_ok(ufc_service_name, bns, backend) then
			if request_cnt == nil or response_times == nil or request_cnt == 0 or response_times == 0 then
				--ngx.log(ngx.DEBUG, string.format("host %s port %d idle is %d", host, port, 0))
				return host, port
			end

			if avg_response_time == nil or
				response_times / request_cnt < avg_response_time then
				avg_response_time = response_times / request_cnt
				--ngx.log(ngx.DEBUG, string.format("host %s port %d avg_response_time is %d", host, port, avg_response_time))
				idle_host = host
				idle_port = port
			end
		end
	end

	return idle_host, idle_port
end

function _M._hash_select(hash_key, service_meta, ufc_service_name, bypass, idc)
	local bns_list = service_meta["bns_list"]
	local crc32 = ngx.crc32_short(hash_key)
	local bns_list_len = 0
	for bns, p in pairs(bns_list) do
		bns_list_len = bns_list_len + 1
	end

	local i = crc32 % bns_list_len + 1
	local selected_bns
	local s = 1
	for bns, p in pairs(bns_list) do
		if s == i then
			selected_bns = bns
			break
		end
		s = s + 1
	end
	--ngx.log(ngx.DEBUG, string.format("crc32 is %s, i is %s, s is %s, bns_list is %s", crc32, i, s, cjson.encode(bns_list)))
	--ngx.log(ngx.DEBUG, string.format("hash select bns name is %s", selected_bns))

	service_meta["bns"] = selected_bns
	-- 检查一下内存里面的bns数据
	if not _M.check_bns_in_cache(selected_bns) then
		return service_meta, nil, nil
	end
	local backends_cnt = idc_map.get_backends_cnt(selected_bns, idc)

	local j, host, port, backend
	local n = 0
	local info
	while n <= conf["random_max_tries"] do
		j = crc32 % backends_cnt + 1
		info = _M._get_backend(selected_bns, j)
		if info then
			host = info["host"]
			port = info["port"]

			backend = host .. ":" .. port
			breaker.init_backend(ufc_service_name, backend)
			if _M.is_forbid_disable(backends_cnt) or _M._is_backend_ok(ufc_service_name, selected_bns, backend) then
				return service_meta, host, port
			end
		end

		crc32 = ngx.crc32_short(crc32)
		n = n + 1
	end

	return service_meta, nil, nil
end

--- 一致性哈希实现begin
function _M._consistent_hash_select(hash_key, service_meta, ufc_service_name, bypass, platforms_idc)
	if platforms_idc == nil then 
		platforms_idc = "all_idc"
	end

	-- 选择bns用hash即可
	local bns_name = _M._select_bns_by_hash(hash_key, service_meta)
	-- 检查一下内存里面的bns数据
	if not _M.check_bns_in_cache(bns_name) then
		return service_meta, nil, nil
	end
	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		return service_meta, nil, nil
	end

	local chash_cache, cur_mtime, is_bns_changed = _M.is_bns_changed(bns_name, platforms_idc) 
	if is_bns_changed and chash_cache then 
		-- 重新建立一致性 hash 环
		local ok = _M.init_consistent_hash_ring(bns_name, platforms_idc)
		if not ok then 
			local last_mtime = chash_cache[bns_name][platforms_idc]
			ngx.log(ngx.WARN, string.format("init consistent hash ring failed, ufc service name: %s, bns: %s, idc: %s,last mtime: %s", ufc_service_name, bns_name, platforms_idc, last_mtime))
		else 
			ngx.log(ngx.WARN, string.format("init consistent hash ring success, ufc service name: %s, bns: %s, idc: %s, cur time: %s", ufc_service_name, bns_name, platforms_idc, cur_mtime))
		end 
		-- 更新 bns_name 的 mtime
		chash_cache[bns_name][platforms_idc] = cur_mtime
		ngx_worker_cache["static_cache"]["chash_mtime"] = chash_cache
	end 

	service_meta["bns"] = bns_name

	local chash_pickers = static_cache["chash_picker"]
	if not chash_pickers then
		ngx.log(ngx.WARN, string.format("logid %s service_name %s bns message has not chash_pickers", ngx.var.ufc_logid, ufc_service_name, bns_name))
		return service_meta, nil, nil
	end

	if not chash_pickers[bns_name] then 
		-- 理论不会走到这些 nil， 除非有bug
		ngx.log(ngx.WARN, string.format("logid %s service_name %s bns message has not chash_pickers bnss", ngx.var.ufc_logid, ufc_service_name, bns_name))
		return service_meta, nil, nil
	end 
	
	local picker = chash_pickers[bns_name][platforms_idc] 
	if not picker then 
		return service_meta, nil, nil
	end 
	
	local backends_cnt = idc_map.get_backends_cnt(bns_name, platforms_idc)
	local host, port, backend, slice, index
	-- 一致性哈希，选择后端实例，当实例不可用时重新选择
	local n = 0
	while n < conf["random_max_tries"] do
		if n == 0 or not index then 
			backend, index = picker:find(hash_key)
			-- ngx.log(ngx.DEBUG, string.format("consistent hash find backend: %s, index: %d", backend, index))
		else
			backend, index = picker:next(index) 
			-- ngx.log(ngx.DEBUG, string.format("consistent hash next backend: %s, index: %d", backend, index))
		end 
		
		slice = utils.explode(":", backend)
		if slice and #slice == 2 then
			host = slice[1]
			port = slice[2]

			breaker.init_backend(ufc_service_name, backend)
			if _M.is_forbid_disable(backends_cnt) or _M._is_backend_ok(ufc_service_name, bns_name, backend) then
				-- 用于一致性hash时输出to_idc
				local platform
				local idc 
				local bns_config = ngx_worker_cache["static_cache"]["bnss"][bns_name]

				local ctxs = ngx.ctx.ufc_ctxs
				if bns_config and bns_config["ip2platform"] and ctxs["platformmap_enabled"] == true then 
					platform = bns_config["ip2platform"][backend]
				end
				if bns_config and bns_config["ip2platform"] and ctxs["idcmap_enabled"] == true then 
					idc = bns_config["ip2idc"][backend]
				end 

				if platform and idc then 
					ctxs["to_physics_idc"] = platform .. "-" .. idc 
				elseif platform then 
					ctxs["to_physics_idc"] = platform
				elseif idc then 
					ctxs["to_physics_idc"] = idc
				end 

				return service_meta, host, port
			end
		end
		n = n + 1
	end
	return service_meta, nil, nil
end

function _M._select_bns_by_hash(hash_key, service_meta)
	local bns_list = service_meta["bns_list"]
	local crc32 = ngx.crc32_short(hash_key)
	local bns_list_len = 0
	for bns, p in pairs(bns_list) do
		bns_list_len = bns_list_len + 1
	end
	-- 选取 bns 用 hash 即可
	local i = crc32 % bns_list_len + 1
	local selected_bns
	local s = 1
	for bns, p in pairs(bns_list) do
		if s == i then
			selected_bns = bns
			break
		end
		s = s + 1
	end
	
	return selected_bns
end 


function _M.is_bns_changed(bns, platforms_idc)
	-- 如果开启了 idcmap，每个 bns 需要对每个 idc 建立一致性哈希环，判断变更需要将 bns_name 和 idc 一起进行判断
	-- 获取最新的 mtime
	local bnss = ngx_worker_cache["static_cache"]["bnss"]
	if not bnss then
		-- 这时候还没有实例，返回false即可，返回true会走创建 hash环的逻辑没有必要也创建不成功
		return nil, nil, false 
	end 
	
	local bns_cache = bnss[bns]
	if not bnss[bns] then
		return nil, nil, false
	end

	local cur_mtime =  bns_cache["mtime"]

	local is_changed = false
	-- 建立哈希环的时间
	local chash_mtime_cache = ngx_worker_cache["static_cache"]["chash_mtime"]
	if not chash_mtime_cache then
		chash_mtime_cache = {}
		is_changed = true 
	end 

	if not chash_mtime_cache[bns] then 
		chash_mtime_cache[bns] = {}
		is_changed = true 
	end 

	local bns_idc_mtime = chash_mtime_cache[bns][platforms_idc]
	if not bns_idc_mtime then 
		is_changed = true 
	elseif bns_idc_mtime < cur_mtime then 
		is_changed = true 
	else 
		is_changed = false
	end 

	return chash_mtime_cache, cur_mtime, is_changed
end



function _M.init_consistent_hash_ring(bns_name, idc)
    local bnss = ngx_worker_cache["static_cache"]["bnss"][bns_name]
    if not bnss then
        return false 
    end

	local nodes = {}
	local instance_list = {}
	if idc == "all_idc" then
		-- 非platform/非idc路由
		instance_list = bnss["bns_nodes"]
		if #instance_list > 0 then 
			for _, instance in pairs(instance_list) do
				local host = instance["host"]
				local port = instance["port"]
				local backends  = host .. ":" .. port
				nodes[backends] = 1 -- 所有实例权重均等，都为1
			end
		end  
	else 
		--平台idc路由
		local platforms_idcmaps = ngx.ctx.platforms_idcmaps
		for _, key in pairs(platforms_idcmaps) do
			if bnss[key] and #bnss[key] > 0 then
				for _, instance in pairs(bnss[key]) do
					local host = instance["host"]
					local port = instance["port"]
					local backends  = host .. ":" .. port
					nodes[backends] = 1 -- 所有实例权重均等，都为1
				end 
			end
		end
	end 

	local picker = resty_chash:new(nodes)
	if not ngx_worker_cache["static_cache"]["chash_picker"] then 
		ngx_worker_cache["static_cache"]["chash_picker"] = {}
	end 
	if not ngx_worker_cache["static_cache"]["chash_picker"][bns_name] then 
		ngx_worker_cache["static_cache"]["chash_picker"][bns_name] = {}
	end

	ngx_worker_cache["static_cache"]["chash_picker"][bns_name][idc] = picker
	return true 
end

--- 一致性hash实现end

function _M._get_compatible_old_service_name(uri, host)
	local ufc_service_name, compatible_uri

	if string.sub(uri, -1, -1) == "/" then
		compatible_uri = string.sub(uri, 1, -2)
	else
		compatible_uri = uri .. "/"
	end
	ufc_service_name = _M._get_old_ufc_service_name(compatible_uri, host)
	if ufc_service_name then
		return ufc_service_name
	end

	for i = -1, -#uri, -1 do
		if string.sub(uri, i, i) == "/" then
			ufc_service_name = _M._get_old_ufc_service_name(string.sub(uri, 1, i), host)
			if ufc_service_name then
				return ufc_service_name
			end
		end
	end
end

function _M._get_old_ufc_service_name(uri, host)
	local use_host
	local compatible_uri
	local old_service_name
	local old_service_config
	--ngx.log(ngx.DEBUG, "request uri is" .. uri .. " host is" .. host)

	old_service_config = ngx_worker_cache["static_cache"]["old"][uri]
	if old_service_config then
		use_host = old_service_config["use_host"]
		if use_host then
			old_service_name = uri .. "_" .. host
		else
			old_service_name = uri
		end

		if old_service_name
			and ngx_worker_cache["static_cache"]["old"][old_service_name] then
			--ngx.log(ngx.DEBUG, "old_service_name is " .. old_service_name)
			return ngx_worker_cache["static_cache"]["old"][old_service_name]["service_name"]
		end
	end
end

function _M._get_standard_ufc_service_name(ufc_service_name_in_header)
	if not ufc_service_name_in_header then
		return nil
	end

	if not ngx_worker_cache["ufc_meta_info"] or not ngx_worker_cache["ufc_meta_info"]["services"] then
		return nil
	end

	if ngx_worker_cache["ufc_meta_info"]["services"][ufc_service_name_in_header] then
		return ufc_service_name_in_header
	end

	return nil
end

function _M._get_default_ufc_service_name(ufc_service_name_in_header)
	local tmp
	local prefix
	local default_service_name
	--ngx.log(ngx.DEBUG, string.format("service name in header is %s", ufc_service_name_in_header))
	if not ufc_service_name_in_header then
		return nil
	end

	if not ngx_worker_cache["ufc_meta_info"] or not ngx_worker_cache["ufc_meta_info"]["services"] then
		return nil
	end

	if ngx_worker_cache["ufc_meta_info"]["services"][ufc_service_name_in_header] then
		return ufc_service_name_in_header
	end

	if not string.find(ufc_service_name_in_header, "-") then
		return
	end

	tmp = utils.explode("-", ufc_service_name_in_header)
	for i = 0, #tmp - 1 do
		prefix = nil
		for j = 1, #tmp - i do
			if not prefix then
				prefix = tmp[j]
			else
				prefix = prefix .. "-" .. tmp[j]
			end
		end

	
		default_service_name = prefix .. "-default"		
		if ngx_worker_cache["ufc_meta_info"]["services"][default_service_name] then  -- TODO 逐个向default回退
			return default_service_name
		end
	end
end


function _M.get_random_service_name()
	if not ngx_worker_cache["static_cache"] then
		return nil
	end

	local services = ngx_worker_cache["static_cache"]["services"]

	if not services then
		return nil
	end

	local return_service = nil

	--lua 没有办法获取table大小，现在注册服务大概有900个
	local random_cnt = math.random(900)
	local i = 0
	for service, _ in pairs(services) do
		return_service = service
		i = i + 1
		if i >= random_cnt then
			break
		end
	end

	if return_service ~= nil then 
		breaker.init_service(return_service)
	end

	return return_service
end

function _M.get_chaos_config()
    local configs = ngx_worker_cache["static_cache"]["configs"]
    if not configs then
        return nil
    end

    if not configs["chaos"] then
        return nil
    end

    return configs["chaos"]
end

function _M._send_request_to_chaos(premature, chaos_args)
    if not (chaos_args["uri"] and chaos_args["target"]) then
        ngx.log(ngx.ERR, string.format("_send_request_to_chaos chaos_args error "))
        return
    end

    local chaos_config = _M.get_chaos_config()
    
    if chaos_config == nil then
        ngx.log(ngx.ERR, string.format("_send_request_to_chaos chaos_config error [to_service:%s] [uri:%s] ", chaos_args["target"], chaos_args["uri"]))
        return
    end

    if not (chaos_config["domain"] and chaos_config["port"]) then
        ngx.log(ngx.ERR, string.format("_send_request_to_chaos domain or port error [to_service:%s] [uri:%s] ", chaos_args["target"], chaos_args["uri"]))
        return
    end

    local addr = {
        ["domain"]  = chaos_config["domain"],
        ["port"]    = chaos_config["port"],
    }

    local args_str = cjson.encode(chaos_args)
    local args_len = string.len(args_str)

    local header = {
        ["Content-Type"]    = "application/json",
        ["Content-Length"]  = args_len,
    }

    local timeout = {
        ["connect_timeout"] = 1000,
        ["read_timeout"]    = 3000,
    }
        
    local ret, err = http.post(addr, "/rest/2.0/pvcp/chaos?method=unexpection", header, args_str, timeout)
    ngx.log(ngx.NOTICE, string.format("_send_request_to_chaos success [args:%s] [header:%s] [ret:%s] [err:%s]", args_str, cjson.encode(header), cjson.encode(ret), cjson.encode(err)))
end

function _M.send_request_to_chaos(ctxs, headers, fault_id)
    ngx.req.read_body()

    local chaos_args = {
        ["fault_id"]        = fault_id,
        ["from"]            = ctxs["from_service_name"],
        ["target"]          = ctxs["ufc_service_name"],
        ["uri"]             = ngx.var.request_uri,
        ["header"]          = headers,
        ["method"]          = ngx.var.request_method,
        ["body"]            = ngx.req.get_body_data(),
    }
	
    ngx.timer.at(2, _M._send_request_to_chaos, chaos_args)
end

--更新需要用到的service配置
--为了避免进程启动阻塞，在进程重启的时候只从磁盘上加载需要用到的service的配置
--这个函数调用永远跟在check_service_in_cache 后面
function _M.update_needed_service_map(from_service, to_service)
	local time_now = ngx.now()
	ngx_worker_cache["need_services"][to_service] = time_now

	if ngx_worker_cache and ngx_worker_cache["static_cache"] and ngx_worker_cache["static_cache"]["services"] 
	and ngx_worker_cache["static_cache"]["services"][from_service] then
		ngx_worker_cache["need_services"][from_service] = time_now
	end
end

--更新需要用到的bns配置
--为了避免进程启动阻塞，在进程重启的时候只从磁盘上加载需要用到的bns配置
--为了避免大bns 反序列化阻塞nginx 线程，worker进程对于需要用到的bns配置每次都更新
--不需要用到的bns配置每五次更新一次（沙盒没有这个限制）
function _M.update_needed_bns_map(bns_name)
	local time_now = ngx.now()
	ngx_worker_cache["need_bnss"][bns_name] = time_now
end

--检查一下bns信息
--（1）bns是否在内存里面，没有就从磁盘上加载，只有重启的时候可能出现这种情况
--后面只要经过一次配置更新，内存里面肯定有数据
--（2）bns是否在need_bnss map里面，如果不在他的数据可能不是最新的（worker进程对用不到的bns每8次更新一次的策略）
--此时我们强制从共享内存里面读一次数据就行了（共享内存每次都会更新数据，肯定是最新的）
function _M.check_bns_in_cache(bns)
	-- 检查内存里面有没有数据
	if not ngx_worker_cache["static_cache"]["bnss"] or not ngx_worker_cache["static_cache"]["bnss"][bns] then
		--更新need map
		_M.update_needed_bns_map(bns)
		if not ngx_worker_cache.load_one_local_config("bns", bns) then
			local logid = ngx.var.ufc_logid or "-"
			ngx.log(ngx.NOTICE, string.format("logid:%s bns name:%s, load data from disk failed", logid, bns))
			return false
		end
	end
	--检查是否在need_bnss map里面
	if not ngx_worker_cache["need_bnss"][bns] then
		--更新need map
		_M.update_needed_bns_map(bns)
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.NOTICE, string.format("logid:%s bns name:%s, need bnss map miss, load from shared memory", logid, bns))
		--强制从共享内存读一份数据，这里不对返回值做判断(因为无论如何内存里面现在有数据)，也不对mtime做比较，无脑更新
		--只会影响一个请求，对性能影响忽略不计
		local config, err = worker_process_cache._real_load_one_shared_memmory_config("bns", bns)
		if not config or err then
			return true
		end
		if not ngx_worker_cache["static_cache"]["bnss"] then
			ngx_worker_cache["static_cache"]["bnss"] = {}
		end
	
		ngx_worker_cache["static_cache"]["bnss"][bns] = config
	end
	return true
end

--check一下service 是否在内存中，没有就需要从磁盘上面加载
--加载失败返回false，成功返回true
function _M.check_service_in_cache(ufc_service_name)
	if not ngx_worker_cache["static_cache"]["services"] then
		ngx_worker_cache["static_cache"]["services"] = {}
	end
	--这里只check service配置有没有，如果bns配置没有他会从磁盘或者共享内存加载
	if ngx_worker_cache["static_cache"]["services"][ufc_service_name] then
		return true
	end

	ngx.log(ngx.NOTICE, string.format("service name: %s, is not exist in cache, need to load from disk", ufc_service_name))
	--不在内存里面需要从磁盘加载
	if not ngx_worker_cache.load_one_local_config("service", ufc_service_name) then
		ngx.log(ngx.NOTICE, string.format("service name: %s, load data from disk failed", ufc_service_name))
		return false
	end
	--加载完后初始化相应的dynamic_cache
	ngx_worker_cache._init_one_dynamic_cache(ufc_service_name)

	--加载service 中所有bns信息
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	local bns_list = service_config["bns_list"]

	if bns_list then
		for k, v in pairs(bns_list) do
			if not ngx_worker_cache.load_one_local_config("bns", k) then
				ngx.log(ngx.NOTICE, string.format("bns name: %s, load data from disk failed", k))
			end
		end
	end

	--小流量配置
	local small_flow_configs = service_config["small_flow"]
	if not small_flow_configs then
		return true
	end

	for _, small_flow_config in pairs(small_flow_configs) do
		bns_list = small_flow_config["bns_list"]
		if not bns_list then
			goto done
		end
	
		for k, v in pairs(bns_list) do
			if not ngx_worker_cache.load_one_local_config("bns", k) then
				ngx.log(ngx.NOTICE, string.format("bns name: %s, load data from disk failed", k))
			end
		end
		:: done ::
	end
	return true
end


function _M.get_ufc_service_name(uri, headers)  -- TODO 会有回退的service返回
	local ufc_service_name
	local host
	local ufc_service_name_in_header
	if type(headers["x-ufc-service-name"]) == type({}) then
		ufc_service_name_in_header = headers["x-ufc-service-name"][1]
	else
		ufc_service_name_in_header = headers["x-ufc-service-name"]
	end

	local from_service_name_in_header
	local from_service_name
	if type(headers["x-ufc-self-service-name"]) == type({}) then
		from_service_name_in_header = headers["x-ufc-self-service-name"][1]
	else
		from_service_name_in_header = headers["x-ufc-self-service-name"]
	end
	--用来上报的from_service_name
	local upload_from_service_name = nil
	if from_service_name_in_header == nil then
		upload_from_service_name =  "-"
	else
		upload_from_service_name = from_service_name_in_header
	end

	from_service_name = _M._get_standard_ufc_service_name(from_service_name_in_header)
	--标准的服务名找不到就找default的
	if from_service_name == nil then
		from_service_name = _M._get_default_ufc_service_name(from_service_name_in_header)
	end
	--都找不到就上报
	if from_service_name == nil then
		local ufc_service_name_in_header_temp = ufc_service_name_in_header
		if ufc_service_name_in_header_temp == nil then
			ufc_service_name_in_header_temp = "-"
		end
		local message = "nonstandard_from_service:" .. upload_from_service_name .. ":" .. ufc_service_name_in_header_temp
		moniter.warn_collect(ngx_worker_cache["static_cache"]["configs"], "nonstandard_from_service", "nonstandard_from_service", message)
		--找不到打日志的时候就用原始的就行了
		from_service_name = from_service_name_in_header
	end

	

	ufc_service_name = _M._get_standard_ufc_service_name(ufc_service_name_in_header)  -- 只查看是否在配置services中
	if ufc_service_name then
		goto done
	end

	ufc_service_name = _M._get_default_ufc_service_name(ufc_service_name_in_header)
	if ufc_service_name then
		goto done
	end

	if uri == nil then
		goto done
	end

	if conf["sandbox"] == false and ufc_service_name_in_header ~= nil and string.find(ufc_service_name_in_header, "sandbox:", 1, true) == nil then 
		-- 线上ufc中带有 sandbox: 字符串的不能走该逻辑
		host = headers["Host"]
		ufc_service_name = _M._get_old_ufc_service_name(uri, host)
		if ufc_service_name then
			goto done
		end
		ufc_service_name = _M._get_compatible_old_service_name(uri, host)
		if ufc_service_name then
			goto done
		end
	end 


	:: done ::
	--看一下内存里面有没有相应的的配置信息，没有就从磁盘里面读
	if not _M.check_service_in_cache(ufc_service_name) then
		--内存里面没有，从磁盘加载失败，按没有注册处理，等待云端更新
		return from_service_name, nil, ufc_service_name_in_header
	end
	_M.update_needed_service_map(from_service_name, ufc_service_name)
	
	--做高级路由策略选择, 如果命中高级路由策略, service name会变
	ufc_service_name = _M.advanced_route_select(ufc_service_name, headers)
	--这里有个不容易注意的竞争条件，云端加载在结束后才会初始化dynamic cache
	--从磁盘加载相应的service后也会进行dynamic cache初始化
	--目前不是一下子把所有配置信息从磁盘读进来，有可能正好一个service配置更新完后请求过来了，此时dynamic cache没有初始化
	if not ngx_worker_cache["dynamic_cache"] or not ngx_worker_cache["dynamic_cache"][ufc_service_name] or not ngx_worker_cache["not_shared_dynamic_cache"] or not ngx_worker_cache["not_shared_dynamic_cache"][ufc_service_name] then
		ngx_worker_cache._init_one_dynamic_cache(ufc_service_name)
	end
	breaker.init_service(ufc_service_name)

	return from_service_name, ufc_service_name, ufc_service_name_in_header
end



function _M._is_request_from_inner(headers)
	local ips, first_ip, first_ips, first_ip_prefix

	if headers["X-Forwarded-For"] == nil then
		return false
	end

	ips = utils.explode(",", headers["X-Forwarded-For"])
	first_ip = ips[1]
	first_ips = utils.explode(".", first_ip)
	if #first_ips ~= 4 then
		return false
	end

	first_ip_prefix = first_ips[1] .. "." .. first_ips[2] .. "." .. first_ips[3]
	if conf["inner_ip"][first_ip_prefix] ~= nil then
		return true
	end

	return false
end

function _M._select_service_meta(service_config, headers)
	local res = {}

	--标示是否小流量
	service_config["flow"] = "main" 

	local small_flow_policies = service_config["small_flow"]
	if not small_flow_policies then
		return service_config
	end

	local small_flow_value = headers["x-http-flow-control"]
	local is_request_from_inner = _M._is_request_from_inner(headers)
	if (not small_flow_value) and (not is_request_from_inner) then
		return service_config
	end

	for _, small_flow_policy in pairs(small_flow_policies) do
		if small_flow_value then
			if small_flow_policy["small_list"] then
				if small_flow_policy["small_list"][small_flow_value] == 1 then
					small_flow_policy["flow"] = "small"
					return small_flow_policy
				end
			end

			if small_flow_policy["hash_range"] then
				local min = small_flow_policy["hash_range"]["min"]
				local max = small_flow_policy["hash_range"]["max"]
				local size = small_flow_policy["hash_range"]["size"]
				local hash_value = utils.hash(small_flow_value)
				if hash_value % size >= min and hash_value % size < max then
					small_flow_policy["flow"] = "small"
					return small_flow_policy
				end
			end
		end

		if small_flow_policy["inner"] == 1 and is_request_from_inner then
			small_flow_policy["flow"] = "small"
			return small_flow_policy
		end
	end

	return service_config
end

--高级路由策略选择
function _M.advanced_route_select(service_name, headers)
	local service_config = ngx_worker_cache["static_cache"]["services"][service_name]
	if not service_config then
		return service_name
	end
	--没有高级路由策略配置
	if not service_config["advanced_route"] then
		return service_name
	end

	local route_rule = service_config["advanced_route"]["route_rule"]
	if not route_rule or type(route_rule) ~= "table" then
		return service_name
	end

	local route_app = service_config["advanced_route"]["route_app"]
	if not route_app or type(route_app ) ~= "table" then
		return service_name
	end


	--先匹配url path
	local url_path = ngx.var.uri
	local url_path_rule = route_rule[url_path]
	if not url_path_rule or type(url_path_rule) ~= "table" then
		-- url path 没有匹配到规则
		return service_name
	end
	local logid = ngx.var.ufc_logid or "-"
	local args, err = ngx.req.get_uri_args()
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("logid:%s, service name:%s get args failed, err:%s", logid, service_name, err))
		return service_name
	end

	for app, rules in pairs(url_path_rule) do
		--匹配query string
		local matched = true
		if rules["query_string"] and type(rules["query_string"]) == "table" then
			for k, v in pairs(rules["query_string"]) do
				--这里args[k]可能是个table，说明query string有重复的key，这种情况视为没匹配上
				if args[k] ~= v then
					--有一个不匹配就是没匹配上
					matched = false
					goto continue
				end
			end
		end

		--匹配header
		if rules["header"] and type(rules["header"]) == "table" then
			for k, v in pairs(rules["header"]) do
				--这里headers[k]可能是个table，说明heade里面有重复的key，这种情况视为没匹配上
				if headers[k] ~= v then
					--有一个不匹配就是没匹配上
					matched = false
					goto continue
				end
			end
		end
		:: continue ::

		if matched == true then
			--匹配上了先找有没有app 配置
			if not route_app[app] then
				return service_name
			end

			local new_service_name = string.format("%s:%s", service_name, app)
			--后面一些逻辑会通过service name 从static cache获取service相关配置
			--额外在内存里面添加一下新的service的配置，这个配置会跟着原始service的配置一起更新
			worker_process_cache.add_one_advanced_route_service_config(new_service_name, service_config, route_app[app])
			ngx.log(ngx.WARN, string.format("logid:%s service name:%s app:%s hit advanced route", logid, service_name, app))
			return new_service_name
		end
	end

	--没有匹配上, 返回原来的service config 和service name 就行了
	return service_name
end

function _M._select_bns(bns_list)
	local selected_bns
	local r = math.random(100)
	local s = 0

	for bns, p in pairs(bns_list) do
		if s < r then
			s = s + p
			selected_bns = bns
		end
	end
	return selected_bns
end

function _M.get_entire_config(ufc_service_name)
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	return service_config
end

function _M.get_backup(ufc_service_name)
	local backup, backup_config
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	if service_config == nil then
		return nil
	end

	backup_config = service_config["backup"]
	if backup_config == nil then
		return nil
	end

	backup = {}
	for backup_ufc_service_name, backup_config in pairs(backup_config) do
		if backup_config["enable"] == 1 then
			--ngx.log(ngx.DEBUG, string.format("backup_config is %s", cjson.encode(backup_config)))
			local r = {}
			local service_meta, host, port = _M.select_backend(backup_ufc_service_name, {}, 1)
			if service_meta ~= nil and host ~= nil and port ~= nil then
				r = {
					["host"] = host,
					["port"] = port,
					["bns"] = service_meta["bns"],
					["protocol"] = service_meta["protocol"],
					["options"] = backup_config["options"]
				}
				table.insert(backup, r)
			end
		end
	end

	if #backup == 0 then
		return nil
	end

	return backup
end

--[[
	检查ufc 定时器线程是否失效
]]--
function _M.timer_thread_check()
	--定时器超过间隔时间的两倍没有更新就报警
	--版本上报这个间隔时间过长并且开关不一定开，不进行报警
	local invalid_timer = moniter["invalid_timer"]
	local time_now = ngx.now()
	local worker_check_time = moniter["worker_check_time"]
	moniter["last_worker_check_time"] = time_now
	local pid = ngx.worker.pid()
	local module = "http"

	if conf["stream"] == 1 then
		module = "stream"
	end

	local invalid_timeout = moniter["timer_interval"]["moniter_interval"] or 600
	if time_now - worker_check_time["last_moniter_time"] > invalid_timeout * 2 then
		local content = string.format("%s:%s:moniter_timer:is_invalid", pid, module)
		table.insert(invalid_timer, content)
	end

	invalid_timeout = moniter["timer_interval"]["reload_config_interval"] or 600
	if time_now - worker_check_time["last_reload_config_time"] > invalid_timeout * 2 then
		local content = string.format("%s:%s:load_config_timer:is_invalid", pid, module)
		table.insert(invalid_timer, content)
	end

	invalid_timeout = moniter["timer_interval"]["clear_invalid_interval"] or 600
	if time_now - worker_check_time["last_clear_invalid_time"] > invalid_timeout * 2 then
		local content = string.format("%s:%s:clear_invalid_backend_timer:is_invalid", pid, module)
		table.insert(invalid_timer, content)
	end

	--这个定时器只有http模式有
	if conf["stream"] == 0 then
		invalid_timeout = moniter["timer_interval"]["fault_data_update_interval"] or 600
		if time_now - worker_check_time["last_fault_data_update_time"] > invalid_timeout * 2 then
			local content = string.format("%s:%s:fault_data_update_timer:is_invalid", pid, module)
			table.insert(invalid_timer, content)
		end
	end

	if invalid_timer == nil or next(invalid_timer) == nil then
		return
	end

	local configs = ngx_worker_cache["static_cache"]["configs"]
	if configs and configs["moniter"] then
		moniter.cleanup_timer(configs["moniter"])
	end

end

--判断是否需要检查定时器是否失效，true 需要，flase不需要
function _M.timer_thread_check_judge()
	local configs = ngx_worker_cache["static_cache"]["configs"]
	if configs and configs["timer"] then
		local timer_check = configs["timer"]["timer_check"] 
		if not timer_check or not timer_check["enable"] or timer_check["enable"] < 1 then 
			return false
		end
		--半个小时检查一次
		local check_timeout = timer_check["check_timeout"] or 1800
	
		if not moniter["last_worker_check_time"] then
			moniter["last_worker_check_time"] = ngx.now()
			return false
		end
	
		if ngx.now() - moniter["last_worker_check_time"] < check_timeout then
			return false
		end
		return true
	end
	return false
end

--是否返回指定后端（这个后端通常是个不在bns里面的假后端）
--目前会有2中情况返回指定后端
--（1）触发沙盒的链路分支逻辑
--（2）触发了穿透式指定请求故障
function _M.select_specific_backend(from_service, to_service, ctxs)
	--如果是沙盒环境并且uid命中小流量配置就从小流量实例中选择后端
	if conf["sandbox"] == true then
		local sandbox_ip, sandbox_port = sandbox.sandbox_check(from_service, to_service, ngx.var.ufc_logid)
		if sandbox_ip and sandbox_port then
			--用access日志中的fault字段标识触发了沙盒小流量策略
			ngx.var.ufc_fault_injection = "sandbox"
			return sandbox_ip, sandbox_port
		end
	end

	--ufc穿透式指定流量故障注入
	if ctxs["ufc_fault_ip"] and ctxs["ufc_fault_port"] then
		return ctxs["ufc_fault_ip"], ctxs["ufc_fault_port"]
	end
	return nil, nil
end

function _M.select_backend(ufc_service_name, headers, bypass, double_ends_tries, not_exist_service_in_sandbox)
	--检查定时器是否失效
	if _M.timer_thread_check_judge() then
		--利用ngx.timer.at 避免序列化耗时
		ngx.timer.at(0, _M.timer_thread_check)
	end

	local ctxs = ngx.ctx.ufc_ctxs
	local from_service_name = ctxs["from_service_name"]
	--在沙盒里，服务没有注册
	if not_exist_service_in_sandbox then
		return sandbox.service_not_exist_sandbox_check(from_service_name, ufc_service_name, ngx.var.ufc_logid)
	end

	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	--ngx.log(ngx.DEBUG, cjson.encode(service_config))
	local bp = service_config["bp"]   -- TODO 这个字段需要必须显示的带
	--这里代码做一下限制，如果是stream模块，并且选择了hash负载均衡策略默认换成轮询
	if conf["stream"] == 1 and bp == conf["balance_policy"]["HASH"] then
		bp = conf["balance_policy"]["RANDOM"]
	end

	local service_meta = _M._select_service_meta(service_config, headers) -- 决定是否走小流量配置
	local bns_list = service_meta["bns_list"]
	local bns = _M._select_bns(bns_list)
	--check 一下bns数据是否不在内存里面或者不是最新的
	if not _M.check_bns_in_cache(bns) then
		return service_meta, nil, nil
	end

	service_meta["bns"] = bns
	local tries = double_ends_tries or ctxs["tries"]

	local max_tries = conf.get_max_retries(service_meta)

	if bypass == 0 and tries > max_tries then
		return service_meta, nil, nil
	end

	--ngx.log(ngx.DEBUG, string.format("for service bp is %s bns_list is %s bns is %s", bp, cjson.encode(bns_list), bns))

	local local_idc = "-"
	if headers["x-ufc-idc"] then
		local_idc = headers["x-ufc-idc"]
	elseif ngx_worker_cache["local_idc"] then
		local_idc = ngx_worker_cache["local_idc"]
	end

	local from_platform = "-"
	if headers["x-ufc-platform-value"] then
		from_platform = headers["x-ufc-platform-value"]
		ctxs["from_platform"] = from_platform
	end 
		

	--ngx.ctx.from_idc = local_idc
	ctxs["from_idc"] = local_idc 
	ctxs["bypass"] = bypass

	local idcs = {"all_idc"} 

	local platforms_idc = idc_map.select_platforms_idc(service_meta, from_platform, local_idc, from_service_name)
	if platforms_idc then
		ctxs["idc"] = platforms_idc
		table.insert(idcs, platforms_idc)
	end

	--定时check
	breaker.check_service_cnt_timeout(ufc_service_name)

	--上报proxy上部署服务
	--moniter.service_proxy_regiester(ufc_service_name, idc)

	--全局检查
	--local from_service_name = ngx.ctx.from_service_name
	for _, platforms_idc in pairs(idcs) do
		local ok = breaker_policy.check_service(from_service_name, ufc_service_name, bns, platforms_idc, bypass)
		if ok then
			--雪崩
			if breaker_policy.service_breaker(from_service_name, ufc_service_name, bns, platforms_idc) then
				--不允许访问，直接返回
				return service_meta, nil, nil, true
			end
		end
	end

	if bypass == 0 and tries > 1 and not breaker_policy.is_retry_ok(from_service_name, ufc_service_name, bns, platforms_idc) then
		--穿透式，雪崩情况下，取消重试
		if double_ends_tries == nil then
			--只有在不开双端的时候才设置ctxs["tries"]
			ctxs["tries"] = max_tries + 1
		end
		return service_meta, nil, nil, false
	end

	--在线、离线隔离审查
	_M.online_isolation(from_service_name, ufc_service_name, service_meta, headers)

	--返回的是指定的后端就不走后面的负载均衡逻辑
	local specific_ip, specific_port = _M.select_specific_backend(from_service_name, ufc_service_name, ctxs)
	if specific_ip ~= nil and specific_port ~= nil then
		return service_meta, specific_ip, specific_port
	end

	--旁路式情况下如果开启了stream 模式返回假的ip端口
	--暂时关闭stream逻辑
	--[[
	if bypass == 1 then
		local loopback_ip = stream_utils.get_stream_host(from_service_name, ufc_service_name)
		if loopback_ip ~= nil then
			return service_meta, tostring(loopback_ip), conf["ufc_stream_port"]
		end
	end
	]]
	if bp == conf["balance_policy"]["ROUND_ROBIN"]
		or bp == conf["balance_policy"]["ROUND_ROBIN_FORBID_SUCCESS_RATE_UNNORMAL"] then
		return service_meta, _M._round_robin_select(ufc_service_name, bns, bypass, platforms_idc)
	end

	if bp == conf["balance_policy"]["IDLE"]
		or bp == conf["balance_policy"]["IDLE_FORBID_SUCCESS_RATE_UNNORMAL"] then
		--ngx.log(ngx.DEBUG, string.format("service %s now using idle select policy selecting backend", ufc_service_name))
		--return service_meta, _M._idle_select(ufc_service_name, bns, bypass, idc)
		return service_meta, _M._round_robin_select(ufc_service_name, bns, bypass, platforms_idc)
	end

	if bp == conf["balance_policy"]["RANDOM"]
		or bp == conf["balance_policy"]["RANDOM_FORBID_SUCCESS_RATE_UNNORMAL"] then
		return service_meta, _M._random_select(ufc_service_name, bns, bypass, platforms_idc)
	end

	if bp == conf["balance_policy"]["HASH"] then
		local hash_key = headers["hash-key"]
		if not hash_key then
			-- 没有传这个 header 默认走轮询, 上传到 ufc-admin-easy 进行 hi 报警
			ngx.log(ngx.WARN, string.format("loadid %s, service %s, from_service %s, message header not found hash-key and use hash balance_policy", ngx.var.ufc_logid, ufc_service_name, from_service_name))
			moniter.bp_hash_err_service_collect(from_service_name, ufc_service_name)
			return service_meta, _M._round_robin_select(ufc_service_name, bns, bypass, platforms_idc)
		end 

		return _M._hash_select(hash_key, service_meta, ufc_service_name, bypass, platforms_idc)
	end

	if bp == conf["balance_policy"]["GLOBAL_IDLE"] then
		--return service_meta, _M._global_idle_select(ufc_service_name, bns, bypass, idc)
		return service_meta, _M._round_robin_select(ufc_service_name, bns, bypass, platforms_idc)
	end

	if bp == conf["balance_policy"]["CONSISTENT_HASH"] then
		local hash_key = headers["hash-key"]
		if not hash_key then
			-- 没有传这个 header 默认走轮询, 上传到 ufc-admin-easy 进行 hi 报警
			ngx.log(ngx.WARN, string.format("loadid %s, service %s, from_service %s, message header not found hash-key and use consistent hash balance_policy", ngx.var.ufc_logid, ufc_service_name, from_service_name))
			moniter.bp_hash_err_service_collect(from_service_name, ufc_service_name)
			return service_meta, _M._round_robin_select(ufc_service_name, bns, bypass, platforms_idc)
		end		
		return _M._consistent_hash_select(hash_key, service_meta, ufc_service_name, bypass, platforms_idc)
	end

	local logid = ngx.var.ufc_logid or "-"
	ngx.log(ngx.WARN, string.format("logid %s service_name %s message select backend failed", logid, ufc_service_name))
	return bns, nil, nil
end


--[[
@comment 在线离线流量隔离 
@param string from_service_name
@param string to_service_name
@param meta service_config
@param meta headers
]]
function _M.online_isolation(from_service_name, to_service_name, service_meta, headers)
	if not service_meta then
		return
	end

	--在线、离线隔离审查
	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		return
	end

	local configs = static_cache["configs"]
	if not configs then
		return
	end

	local online_isolation = configs["online_isolation"]

	--开关是否开启, 1在线、0离线
	if not online_isolation or not online_isolation["enable"] or online_isolation["enable"] < 1 then
		ngx.log(ngx.DEBUG, "online_isolation enable is close")
		return
	end

	--优先header获取属性标示
	local from_is_online = headers["x-ufc-online"]
	if not from_is_online then
		--从from service配置获取
		local from_service_meta = static_cache["services"][from_service_name]
		if not from_service_meta then
			return
		end

		from_is_online = from_service_meta["is_online"]
		if not from_is_online then
			--中心配置: 1在线、0离线
			local onlineTmp = online_isolation[from_service_name] 
			if not onlineTmp then
				return
			end

			-- {"cloudui-bj":{"main":1, "flow":0}}
			from_is_online = onlineTmp["main"]
			if not from_is_online then
				return
			end
		end
	end

	local to_is_online = service_meta["is_online"]
	if not to_is_online then
		-- {"cloudui-bj":{"main":1, "flow":0}}
		local flow_type = service_meta["flow"]
		if not flow_type then
			return
		end

		local onlineTmp = online_isolation[to_service_name] 
		if not onlineTmp then
			return
		end

		to_is_online = onlineTmp[flow_type]
		if not to_is_online then
			return
		end
	end

	if tonumber(from_is_online) ~= tonumber(to_is_online) then
		local action = "online_isolation"
		local key = from_service_name .. ":" .. to_service_name
		local message = action .. ":" .. from_service_name .. ":" .. from_is_online .. ":" .. to_service_name .. ":" .. to_is_online

		ngx.log(ngx.DEBUG, "online_isolation "..message)
		moniter.warn_collect(configs, action, key, message)
	end
end

--判断此次请求是否需要计数
--目前4种情况不需要对请求进行计数
--（1）在沙盒环境触发了链路分支逻辑，拿到的是个不存在的后端
--（2）触发了穿透式指定请求故障，拿到的是个不存在的后端
--（3）多进程情况下为了避免对共享内存的操作出现竞争，抢锁失败也不会进行计数
-- （3）需要依赖backend数据 不在这里做额外处理
-- (4) platform_idc下，只有单个实例
function _M.is_this_request_need_count(bypass, bns, idc)
	--沙盒环境下触发链路分支逻辑不进行计数
	if conf["sandbox"] == true and ngx.var.ufc_fault_injection ~= nil then
		return false
	end

	-- 穿透式
	if bypass == 0 then
		--触发了穿透式流量发送到指定实例故障不进行计数
		if  ngx.ctx.ufc_ctxs["ufc_fault_ip"] ~= nil then 
			return false
		end 
	
		-- 穿透式platform_idc下只有单实例不进行计数，用标记判断即可
		local ctxs = ngx.ctx.ufc_ctxs or {}
		if ctxs["is_forbid_disable"] == true then
			return false
		end
	end

	-- 旁路式时没有标记，需要判断实例数
	if bypass == 1 then 
		local backends_cnt = idc_map.get_backends_cnt(bns, idc)
		if _M.is_forbid_disable(backends_cnt) == true then
			return false 
		end
	end

	return true
end

function _M.request_before(ufc_service_name, bns, idc, backend, bypass)
	if _M.is_this_request_need_count(bypass, bns, idc) == false then
		return
	end

	if bypass == 1 then
		--这里bypass参数传1，避免ratio_request_cnt进行计数
		breaker.change_backend_cnt(ufc_service_name, backend, "request_cnt", 1, bypass)
		--有些业务不会callback，所以request cnt会一直增长下去，虽然lua number不会溢出，最好还是限制一个最大值
		breaker.max_request_cnt_check(ufc_service_name, backend)
		--旁路式service不计数了
		--moniter
		moniter.service_request_cnt(ufc_service_name, "request_cnt")
	end
	--对backend计数不再重置
	--breaker.check_backend_cnt_timeout(ufc_service_name, backend)
end


function _M.global_idle_request_callback(ufc_service_name, bns, backend, timestamp, http_code)
	--ngx.log(ngx.DEBUG, string.format("%s %s %s %s %s now callbacking", ufc_service_name, bns, backend, timestamp, http_code))
	local res
	local redis_cli, err = conf.get_redis()
	if err then
		return false
	end

	local prefix = conf.get_redis_prefix(4)
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	local hash_pool_size = service_config["hash_pool_size"]
	local hash_pool_index = ngx.crc32_short(backend) % hash_pool_size

	local hash_key = string.format("%s-%s-%s-hash-pool-%d", prefix, ufc_service_name, bns, hash_pool_index)
	local timestamp_key = string.format("%s-%s", hash_key, backend)

	--ngx.log(ngx.DEBUG, string.format("key %s hash_pool_index is %d, hash_key is %s, timestamp_key is %s", backend, hash_pool_index, hash_key, timestamp_key))

	res, err = redis_cli:hdel(timestamp_key, timestamp)
	if err then
		redis_cli:close()
		return false
	end

	if tonumber(res) == 0 then
		redis_cli:close()
		return true
	end

	res, err = redis_cli:zincrby(hash_key, -1, backend)
	if err then
		redis_cli:close()
		return false
	end

	redis_cli:close()
	if http_code == 555 then
		if not ngx_worker_cache["dynamic_cache"][ufc_service_name]["black_list"] then
			ngx_worker_cache["dynamic_cache"][ufc_service_name]["black_list"] = {}
		end
		ngx_worker_cache["dynamic_cache"][ufc_service_name]["black_list"][backend] = 1
	end
	return true
end

--log backend http code, healthy, response time etc...
function _M.request_callback(from_service_name, ufc_service_name, bns, backend, http_code, cost_time, bypass, platforms_idc)
	if _M.is_this_request_need_count(bypass, bns, platforms_idc) == false then
		return
	end

	local cost_time_number = tonumber(cost_time)

	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	local max_tries = conf.get_max_retries(service_config)
	--如果重试次数为1，状态码又是499这种可以判定这个后端就是执行超时了，计数按照504处理
	if max_tries == 1 and http_code == nil then
		http_code = 504
	end

	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	-- BUG here, request and callback may come from different workers
	-- we have to use cache cross workers to solve it
	if dy_config["backend"][backend] ~= nil then
		--只对穿透式统计后端耗时
		--尚不清楚非http协议长耗时后端是否应该封禁，保险起见不封
		if bypass == 0 and conf["stream"] == 0 then
			_M.add_backend_response_time(from_service_name, ufc_service_name, backend, cost_time_number)
		end
		--返回是否触发封禁以及触发封禁的策略
		--抢锁如果没抢到此次请求不参与计数
		if not lock.lock_backend(ufc_service_name, backend) then
			return
		end
		local f, reason = breaker_policy.check_backend(from_service_name, ufc_service_name, bns, platforms_idc, backend, http_code, bypass)
		if f == true then
			breaker_policy.backend_breaker(from_service_name, ufc_service_name, bns, backend, platforms_idc, reason)
		end
		lock.unlock_backend(ufc_service_name, backend)
	end

end

--后端耗时统计
function _M.add_backend_response_time(from_service_name, ufc_service_name, backend, response_time)
	if response_time == nil then
		--如果upstream耗时没有就直接返回
		return
	end
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config["backend"][backend] then
		dy_config["backend"][backend] = {}
	end
	--读写共享内存和luavm内存此处处理逻辑不同
	if conf["meta_table"] then
		shared_memmory.insert_backend_response_time(ufc_service_name, backend, response_time)
	else
		local backend_info = dy_config["backend"][backend]
		if not backend_info["response_times"] then
			backend_info["response_times"] = {}
		end
		table.insert(backend_info["response_times"], response_time)
	end
	
	--耗时分位值计数
	if not dy_config["response_times"] then
		dy_config["response_times"] = {}
	end

	if not dy_config["response_times"][from_service_name] then
		dy_config["response_times"][from_service_name] = {}
	end

	local service_response_times = dy_config["response_times"][from_service_name]
	local time_interval, quantile_vaule = _M.quantile_param_get(from_service_name, ufc_service_name)
	_M.quantile_value_get(service_response_times, response_time, time_interval, quantile_vaule)
end

--根据上下游获取分位值计算参数
--默认返回5分钟，99分位
function _M.quantile_param_get(from_service, to_service)
	local configs = ngx_worker_cache["static_cache"]["configs"]
	local time_interval = _M["quantile_time_interval"]
	local quantile_vaule = _M["quantile_vaule"]
	if not configs then
		return time_interval, quantile_vaule
	end

	local quantile = configs["quantile"]
	if not quantile then
		return time_interval, quantile_vaule
	end

	local temp = from_service .. "-" .. to_service

	--每个上下游参数是可配置的，也要有个整体的默认值
	local service_config = configs["quantile"][temp]
	if service_config then
		time_interval = service_config["time_interval"] or time_interval
		quantile_vaule = service_config["quantile_vaule"] or quantile_vaule
		return time_interval, quantile_vaule
	end

	local default_config = configs["quantile"]["default"]
	if default_config then
		time_interval = default_config["time_interval"] or time_interval
		quantile_vaule = default_config["quantile_vaule"] or quantile_vaule
		return time_interval, quantile_vaule
	end

	return time_interval, quantile_vaule
end

--获取耗时分位值
--这个耗时分位值数组就别定时重置了
function _M.quantile_value_get(service_response_times, response_time, time_interval, quantile_vaule)
	if service_response_times["last_time"] == nil then
		_M.quantile_buckets_init(service_response_times)
	end

	local last_time = service_response_times["last_time"]

	_M.quantile_data_insert(service_response_times, response_time)
	
	if ngx.now() -  last_time > time_interval then
		--时间到了，算一下分位值 
		local quantile_value = _M.quantile_value_compute(service_response_times, quantile_vaule, service_response_times["num"])
		--把所有桶重置一下·
		_M.quantile_buckets_init(service_response_times)
		if quantile_value and quantile_value >= 0 then
			service_response_times["quantile_value"] = quantile_value
		end
	end
	--把算好的分位置值放在var中，默认是 - 就不会被采样计数
	ngx.var.timeout_quantile = service_response_times["quantile_value"] or "-"
	--业务和ufc之间执行超时
	local ctxs = ngx.ctx.ufc_ctxs or {}
	ngx.var.client_timeout = ctxs["ufc-client-timeout"] or "-"
end

--耗时分位值计数初始化
function _M.quantile_buckets_init(service_response_times)
	service_response_times["last_time"] = ngx.now()
	service_response_times["num"] = 0
	service_response_times["buckets"] = {}
	--每个桶增加的数据(因为会有淘汰)
	service_response_times["added_num"] = {}
	--初始化16个桶
	for i = 1, 16, 1 do
		service_response_times["buckets"][i] = {}
		service_response_times["added_num"][i] = 0
	end
end

--耗时分位值插入
function _M.quantile_data_insert(service_response_times, cost_time)
	local buckets = service_response_times["buckets"]
	if not buckets or not service_response_times["num"] then
		_M.quantile_buckets_init(service_response_times)
	end

	service_response_times["num"] = service_response_times["num"] + 1

	--换算成毫秒
	local cost_time_ms = cost_time * 1000
	--保险起见对耗时取整数
	--lua 的数组从1开始，log2的值最小为0
	local index = utils.log2(math.ceil(cost_time_ms)) + 1

	if index  < 1 then
		index = 1
	end

	if index > 16 then
		index = 16
	end

	if not service_response_times["added_num"] or not service_response_times["added_num"][index] then
		_M.quantile_buckets_init(service_response_times)
	end
	service_response_times["added_num"][index] = service_response_times["added_num"][index] + 1

	--把数据插到相应的桶里面
	local bucket = buckets[index]
	--一个桶最多放254个数，超了就随机替换
	if #bucket >= 254 then
		local replace_index = math.random(254)
		bucket[replace_index] = cost_time_ms
	else
		table.insert(bucket, cost_time_ms)
	end
end

--耗时分位值计算
function _M.quantile_value_compute(service_response_times, quantile, num_total)
	local buckets = service_response_times["buckets"]
	local added_nums = service_response_times["added_num"]
	if not buckets or not added_nums or not quantile or not num_total then
		return
	end

	local n = math.ceil((num_total / 100) * quantile)
	if n < 1 or n > num_total then
		return -1
	end

	--先找到是哪个桶
	for i = 1, 16, 1 do
		local bucket = buckets[i]
		--这个桶里面现在有多少个数据
		local sample_num = #bucket
		--这个桶里面一共添加过多少数据
		local added_num = added_nums[i]
		if n <= added_num and n > 0 then
			--随机替换保持桶内数据分布不变，继续按比例来就行
			local real_index = math.ceil(n * sample_num / added_num)
			return bucket[real_index]
		else
			n = n - added_num
		end
	end
	return -1
end

--双端模式backends选择
function _M.select_backends_for_double_ends(from_service, to_service, from_ip, headers, ctxs)
	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		return nil
	end
	local configs = static_cache["configs"]
	if not configs then
		return
	end

	local double_ends_config = configs["double_ends"]
	--开关没开启直接返回
	if not double_ends_config or not double_ends_config["enable"] or double_ends_config["enable"] < 1 then
		return nil 
	end

	--对所有服务都开启双端模式
	if double_ends_config["all_service"] and double_ends_config["all_service"] == 1 then
		return _M.double_ends_deal(to_service, headers)
	end

	--from_ip为空
	if not from_ip then
		from_ip = "-"
	end

	--如果不是所有服务都开启那么就看下访问的下游服务有没有开启添加超时头的选项
	local services = double_ends_config["services"]
	for _, content in pairs(services) do
		while true do
			if content["from_service"] and content["from_service"] ~= from_service and content["from_service"] ~= "all" then
				break
			end
	
			if content["to_service"] and content["to_service"] ~= to_service and content["to_service"] ~= "all" then
				break
			end

			if content["from_ip"] and content["from_ip"] ~= from_ip and content["from_ip"] ~= "all" then
				break
			end

			do
				return _M.double_ends_deal(to_service, headers)
			end
			
			break
		end
	end

	return nil
end

--双端模式处理，主要是添加http头，提前把后端选好
function _M.double_ends_deal(ufc_service_name, headers)
	local res = {}
	local ctxs = ngx.ctx.ufc_ctxs
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]

	if not service_config then
		return nil
	end

	local max_tries = conf.get_max_retries(service_config)
	if not max_tries then
		return nil
	end

	for i = 1, max_tries do
		local temp = {}
		local service_meta, host, port, pass = _M.select_backend(ufc_service_name, headers, 0, i)
		temp["service_meta"] = service_meta
		temp["host"] = host
		temp["port"] = port
		temp["pass"] = pass
		table.insert(res, temp)
		--设置head告诉下游ufc本地服务端口
		--设置ctx在log阶段告知真实的后端端口
		if host and port then
			--host需要把.换成-，要不head发不出去
			local host_temp = string.gsub(host,"%.","-")
			local port_key = "x-ufc-port-" .. host_temp
			ngx.req.set_header(port_key, port)
			ctxs[host] = port
		end
		--设置超时时间头
		local bns = service_meta["bns"]
		local platforms_idc = ctxs["idc"] or "all_idc"
		local connect_timeout, send_timeout, read_timeout, try_times, is_divided = _M.get_service_timeout(ufc_service_name, bns, platforms_idc)
		ngx.req.set_header("x-ufc-connect-timeout", connect_timeout)
		ngx.req.set_header("x-ufc-rw-timeout", send_timeout)
		--添加相对超时时间
		local timeout_key = "x-ufc-timeout"
		ngx.req.set_header(timeout_key, send_timeout)
	end

	--注意最终确保全部成功后再设置x-ufc-request-from-remote 头
	ngx.req.set_header("x-ufc-request-from-remote", "1")
	--添加from idc头
	ngx.req.set_header("x-ufc-from-idc", ctxs["from_idc"])
	return res
end

--双端模式backend替换真实端口
function _M.port_replace(backend, ctxs)
	local host, port = utils.split_backend(backend)
	if not host or not port then 
		return backend
	end

	local real_port = ctxs[host]
	if not real_port then
		return backend
	end

	local new_backend = host .. ":" .. real_port
	return new_backend
end

--请注意这个函数一定会被调用多次，修改的时候一定要慎重，不然可能设置错误的超时时间
function _M.get_service_timeout(ufc_service_name, bns, platforms_idc)   
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	local connect_timeout, send_timeout, read_timeout, try_times
	local lawine_connect_timeout, lawine_rw_timeout
	local is_divided = false
	--conf.get_max_retries里面考虑到了沙盒service没有注册的情况
	--直接使用的是conf.lua里面的 backends_max_tries
	try_times = conf.get_max_retries(service_config)

	--在沙盒里service可能没有注册
	if conf["sandbox"] == true and service_config == nil then
		connect_timeout = conf["defaults"]["connect_timeout"]
		send_timeout = conf["defaults"]["send_timeout"]
		read_timeout = conf["defaults"]["read_timeout"]
	else
		connect_timeout = service_config["connect_timeout"]
		send_timeout = service_config["send_timeout"]
		read_timeout = service_config["read_timeout"]
		--熔断情况下的超时时间
		lawine_connect_timeout = service_config["lawine_connect_timeout"]
		lawine_rw_timeout = service_config["lawine_rw_timeout"]

	end
	
	--如果没有配置熔断超时时间就不用额外做熔断判断了
	if lawine_connect_timeout and lawine_rw_timeout and bns and platforms_idc  then
		--熔断状态下ufc是不会重试的，所以直接返回超时时间就好，不用除以重试次数
		if breaker_policy.is_lawine("default", ufc_service_name, bns, platforms_idc) then
			return lawine_connect_timeout, lawine_rw_timeout, lawine_rw_timeout, try_times, is_divided
		end
	end

	-- 如果head头里有超时时间就用head里的
	local ctxs = ngx.ctx.ufc_ctxs
	if ctxs["ufc-connect-timeout"] and tonumber(ctxs["ufc-connect-timeout"]) and tonumber(ctxs["ufc-connect-timeout"]) > 0 then
		ngx.log(ngx.DEBUG, string.format("now ufc-connect-timeout is %d", ctxs["ufc-connect-timeout"]))
		connect_timeout = ctxs["ufc-connect-timeout"]
	end

	if ctxs["ufc-rw-timeout"] and tonumber(ctxs["ufc-rw-timeout"]) and tonumber(ctxs["ufc-rw-timeout"]) > 0 then 
		local timeout_sdk = ctxs["ufc-rw-timeout"]
		if try_times < 1 then
			try_times = 1
		end
		local real_timeout = math.floor(timeout_sdk/try_times)
		is_divided = true 
		--执行超时不能太小
		if real_timeout < 500 then
			--local logid = ngx.var.ufc_logid or "-"
			--ngx.log(ngx.WARN, string.format("[logid=%s] [service=%s] [original timeout=%f] rw timout is too small after division", logid, ufc_service_name, real_timeout))
			real_timeout = 500
		end
		send_timeout = real_timeout
		read_timeout = real_timeout
		ngx.log(ngx.DEBUG, string.format("now ufc-rw-timeout is %d", send_timeout))
	end
--此逻辑无用直接去掉
--[[
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if dy_config["service"][bns] and dy_config["service"][bns][idc] then
		local timeout = dy_config["service"][bns][idc]["execute_timeout"]
		if timeout and timeout > 0 then
			--熔断触发，调整执行超时时间
			local logid = ngx.var.ufc_logid or "-"
			ngx.log(ngx.WARN, string.format("[logid=%s] [bns=%s] [idc=%s] [timeout=%s] [message='is lawine']", logid, bns, idc, timeout))
			return timeout, timeout, timeout
		end
	end
]]--

	--这里设置一下ctx中的值，在穿透式log阶段把这个值放到var中去
	ctxs["ufc-client-timeout"] = read_timeout
	return connect_timeout, send_timeout, read_timeout, try_times, is_divided
end

--check whether this service is permited to request
--qps/random drop/manual header rule and so on
function _M.check_flow_limit(from_service_name, ufc_service_name, headers)   -- 调用方在access和bypass中
	local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	if not service_config then
		return nil
	end
	--local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	--ngx.log(ngx.DEBUG, "ufc service name is " .. ufc_service_name .. " conf is " .. cjson.encode(service_config))

	--丢弃指定上游service的流量, 优先级高于全局的。和OP沟通确认，如果在from_module_drop_rate里面配置，这个服务就不会走后面的全局丢弃流量
	local exist_in_sub_config = false
	local qps_drop_rate_table = service_config["from_module_drop_rate"]

	local ctxs = ngx.ctx.ufc_ctxs
	local from_app_name = "" 
	if type(ctxs["from_app"]) == "string" and ctxs["from_app"] ~= "" and type(ctxs["from_platform"]) == "string" and ctxs["from_platform"] ~= "" then 
		from_app_name = ctxs["from_app"] .. "-" .. ctxs["from_platform"]
	end 

	if qps_drop_rate_table and type(qps_drop_rate_table) == "table" then
		for drop_from_service, drop_rate in pairs(qps_drop_rate_table) do 
			if drop_from_service == from_service_name then
				-- 表示命中内部的配置，直接返回
				exist_in_sub_config = true
				if drop_rate and drop_rate > 0 and math.random(100) < drop_rate then
					return "E_DEGRADE_4XX"
				end 
			end 

			if from_app_name ~= "" and drop_from_service == from_app_name then
				-- 表示命中内部的配置，直接返回
				exist_in_sub_config = true
				if drop_rate and drop_rate > 0 and math.random(100) < drop_rate then
					return "E_DEGRADE_4XX"
				end 	
			end
		end 
	end 
		

	--random drop
	local qps_drop_rate = service_config["qps_drop_rate"]
	if not exist_in_sub_config and qps_drop_rate and qps_drop_rate > 0 and math.random(100) < qps_drop_rate then
		return "E_DEGRADE_4XX"
	end

	return nil
end

function _M.is_degrade(ufc_service_name)
	local dy_config
	if ngx_worker_cache and ngx_worker_cache["dynamic_cache"] and ngx_worker_cache["dynamic_cache"][ufc_service_name] then
		dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	else
		if not ufc_service_name then
			ufc_service_name = "-"
		end

		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s server %s message dy_config is null", logid, ufc_service_name))
		return false
	end

	local is_degrade_val = dy_config["is_degrade"]  -- TODO 可以不设置 但是很管用

	if is_degrade_val and is_degrade_val > 0 then
		return true
	end

	return false
end

return _M