local utils = require "utils"
local bit = require("bit")
local conf = require("conf")
local ngx_worker_cache = require "ngx_worker_cache"
local cjson = require "cjson.safe"
local lock = require "lock"

local _M = {
	["doc"] = "stream utils",
}

--字符串ip转数字
function _M.strIp_to_num(ip)
	local split_addrs = utils.explode(".", ip)
	--把ip变成数字，方便后面做位操作
	local ip_nums = 0
	for i = 0, 3 do
		local temp = tonumber(split_addrs[4 - i])
		--左移8位
		temp = bit.lshift(temp, i * 8)
		ip_nums = bit.bor(ip_nums, temp)
	end
	return ip_nums
end

--数字ip转字符串
function _M.numIp_to_str(ip)
	local res = ""
	for i = 3, 0, -1 do
		local num_temp = bit.band(ip, bit.lshift(255, i * 8))
		num_temp = bit.rshift(num_temp, i * 8)
		res = res .. tostring(num_temp)
		if i ~= 0 then
			res = res .. "."
		end
		
	end
	return res
end


--发送动态生成的映射关系到stream模块
function _M.send_data_to_stream(premature, data, connect_timeout, send_timeout)
	local sock = ngx.socket.tcp()
	sock:settimeout(connect_timeout)
	local ok, err = sock:connect(conf["ufc_stream_addr"], conf["ufc_stream_port"])
	if not ok then
		ngx.log(ngx.WARN, string.format("connect to ufc stream moudle failed, error is %s", err))
		return nil, err
	end
	
	ok, err = sock:send(data)
	if not ok then
		sock:close()
		ngx.log(ngx.WARN, string.format("send data to ufc stream moudle failed, error is %s", err))
		return nil, err
	end
	--默认行读取模式，读取一行，需要stream模块发送数据的时候加上\r\n
	local data, err, partial = sock:receive()
	if not err then
		sock:close()
		ngx.log(ngx.WARN, string.format("read data from ufc stream moudle failed, error is %s", err))
		return nil, err
	end

	sock:close()
	return data, nil
end

--云端stream模式开关，默认打开
function _M.check_stream_enable()
	if not ngx_worker_cache["static_cache"] then
		return true
	end

	if not ngx_worker_cache["static_cache"]["configs"] then
		return true
	end
	
	

	local stream_config = ngx_worker_cache["static_cache"]["configs"]["stream"]
	if not stream_config then
		return true
	end

	if stream_config["enable"] and stream_config["enable"] == 0 then
		return false
	end

	return true
end


--判断是否需要走stream模式
function _M.get_stream_host(from_service, to_service)
	--判断一下云端开关
	if _M.check_stream_enable() == false then
		return nil
	end

	--优先看链路映射，链路映射没有看下游服务映射，都没有说明不需要走stream
	local str_temp 
	if from_service == nil then 
		str_temp = to_service
	else
		str_temp = from_service .. "|" .. to_service
	end
	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		return nil
	end

	local ips = static_cache["http_ips"]
	if not ips then
		return nil
	end

	local host = nil

	if ips[str_temp] and ips[str_temp]["content"] then
		host = ips[str_temp]["content"]
		if host ~= nil then
			return tostring(host)
		end
	end

	--链路映射没有, 看to service的映射
	if from_service == nil then
		--没有传from service就不用看了
		return nil
	end

	if not ips[to_service] then
		return nil
	end

	host = ips[to_service]["content"]
	if host ~= nil then
		--to service注册了，只是from service还没动态分配
		--在动态分配映射之前先从共享内存中同步一下数据，避免两个进程数据冲突
		--涉及到对共享内存的写操作，从一开始就加锁
		local ok = lock.stream_lock()
		if not ok then
			--抢锁失败，停止动态分配直接返回to service 对应的ip
			ngx.log(ngx.WARN, string.format("from_service: %s, to_service: %s, worker id: %d try to get lock failed, stop assign IP", from_service, to_service, ngx.worker.pid()))
			return host
		end

		ngx_worker_cache.load_ip_mapping_data()
		--同步共享内存之后再检查一下内存里面有没有from service的映射
		if ips[str_temp] and ips[str_temp]["content"] then
			host = ips[str_temp]["content"]
			if host ~= nil then
				lock.stream_unlock()
				return tostring(host)
			end
		end

		--动态分配一个链路映射
		local link_host = _M.get_link_ip_mapping(host, to_service)
		--如果因为超出index动态分配ip失败就返回to_service对应的ip
		if link_host == nil then
			lock.stream_unlock()
			return host
		end
		--把分配好的动态映射写到共享内存中
		_M.add_ip_mapping(from_service .. "|" .. to_service, link_host, 0)
		--写完共享内存就可以解锁了
		lock.stream_unlock()
		--将映射信息发送给stream模块,后面需要加个换行符
		local data = from_service .. "|" .. to_service .. "|" .. link_host .. "\r\n"
		ngx.timer.at(0, _M.send_data_to_stream, data, 1000, 3000)
		return link_host
	end
	--说明to service没有注册
	return nil
end


--动态分配链路映射
function _M.get_link_ip_mapping(ip, to_service)
	--首先从共享内存中找到上次分配的index
	local shared_data 
	--其实只有http模块会调用这个函数
	if conf["stream"] == 0 then
		shared_data = ngx.shared.http_ip
	else
		shared_data = ngx.shared.stream_ip
	end
	local index = shared_data:get("index")

	--没有动态分配 from service的记录
	if not index then
		--为了避免和云端分配的ip冲突，1～100 预留给云端分配的from service， 剩下的给动态图分配的
		index = conf["ufc_stream_from_service_base_index"]
	end

	index = index + 1
	--只有11位用来分配给from service，如果index > 0x7ff说明分配完了
	if index > 0x7ff then
		ngx.log(ngx.WARN, "allocate dynamic ip failed, index is more than 0x7ff")
		return nil
	end

	--根据index生成分配的回环ip
	--基础ip *********
	local base_num = 0x7f000000
	local ip_num = _M.strIp_to_num(ip)
	--取下游ip的低11位
	local low_11_bits = bit.band(ip_num, 0x7ff)
	--index 左移11位得到高11位
	local upper_11_bits =  bit.lshift(index, 11)
	--高11位 + 低11位 + 基础ip = 最终分配的ip
	local service_ip_num = bit.bor(bit.bor(low_11_bits, base_num), upper_11_bits)
	--数字转换成string
	local ip_str = _M.numIp_to_str(service_ip_num)
	--更新index
	shared_data:set("index", index)
	return ip_str
end

--向内存中添加动态生成的映射信息
--动态生成的映射信息只会存在内存中不会落盘
--为了保证http和stream模块内存数据是一致的，两部分数据都需要添加
function _M.add_ip_mapping(key, value, stream)
	--首先数据写到内存里面
	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache] is nil"))
		return false
	end

	local http_ips = static_cache["http_ips"]
	if not http_ips then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache][http_ips] is nil"))
		return false
	end

	local stream_ips = static_cache["stream_ips"]
	if not stream_ips then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache][stream_ips] is nil"))
		return false
	end

	local timenow = ngx.now()
	--stream:ip->链路， http:链路->ip
	if stream == 1 then
		--先添加stream模块需要的映射
		if not stream_ips[key] then
			stream_ips[key] = {}
		end
		stream_ips[key]["content"] = value
		stream_ips[key]["mtime"] = timenow

		--http模块的映射正好是相反的
		if not http_ips[value] then
			http_ips[value] = {}
		end
		http_ips[value]["content"] = key
		http_ips[value]["mtime"] = timenow
	else
		--先添加http模块需要的映射
		if not http_ips[key] then
			http_ips[key] = {}
		end
		http_ips[key]["content"] = value
		http_ips[key]["mtime"] = timenow

		--stream模块的映射正好是相反的
		if not stream_ips[value] then
			stream_ips[value] = {}
		end
		stream_ips[value]["content"] = key
		stream_ips[value]["mtime"] = timenow
	end

	--将动态添加的内存信息写到共享内存里面
	local shared_memory_name
	local shared_ip_data 
	if conf["stream"] == 0 then
		shared_memory_name = "http_ip"
		shared_ip_data = ngx.shared.http_ip
	else
		shared_memory_name = "stream_ip"
		shared_ip_data = ngx.shared.stream_ip
	end
	
	local ip_str = shared_ip_data:get(shared_memory_name)
	local ip_json = {}
	local err = nil
	if ip_str ~= nil then
		ip_json, err = cjson.decode(ip_str)
	end

	if err ~= nil then
		ip_json = {}
	end

	if not ip_json["http_ips"] then
		ip_json["http_ips"] = {}
	end

	if not ip_json["stream_ips"] then
		ip_json["stream_ips"] = {}
	end

	if stream == 1 then
		ip_json["stream_ips"][key] = stream_ips[key]
		ip_json["http_ips"][value] = http_ips[value]
	else
		ip_json["stream_ips"][value] = stream_ips[value]
		ip_json["http_ips"][key] = http_ips[key]
	end

	ip_str = cjson.encode(ip_json)
	shared_ip_data:set(shared_memory_name, ip_str)
end

function _M.gen_ufc_log()
	local ufc_time = math.floor(ngx.now())
	ngx.var.ufc_time = ufc_time 

	local logid = _M.gen_logid()
	ngx.var.ufc_logid = logid
	ngx.var.ufc_callid = logid
end

function _M.gen_logid()
	return ngx.crc32_long(ngx.now() .. ngx.worker.pid() .. math.random(1, 10240))
end

return _M
