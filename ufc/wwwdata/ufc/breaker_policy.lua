local _M = {
	["doc"] = "main policy process for ufc",
	["request_status"] = {
		["ok"]    = 1,
		["bad"]   = -1,
		["other"] = 0
	},
	["func"] = {},
	["http_status_map"] = {
		[1] = "1xx",
		[2] = "2xx",
		[3] = "3xx",
		[4] = "4xx",
		[5] = "5xx"
	}
}

local ngx_worker_cache = require "ngx_worker_cache"
local idc_map = require "idc_map"
local conf = require "conf"
local breaker = require "breaker"
local moniter = require "moniter"
local shared_memmory = require "shared_memory"
--local cjson = require "cjson.safe"

--[[
@comment 判断是否处于熔断中
@param string policy
@param string ufc_service_name
@param string bns
@param string idc
@return bool
]]
function _M.is_lawine(policy_service, ufc_service_name, bns, platforms_idc)
	if not breaker.is_service_ok(policy_service, ufc_service_name, bns, platforms_idc) then
		--熔断中
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("[logid=%s] [to_service=%s] [policy_service=%s] [bns=%s] [idc=%s]", logid, ufc_service_name, policy_service, bns, platforms_idc))
		return true
	end

	return false
end


--[[
@comment 全局熔断触发后，取消所有重试
@param string from_service_name
@param string ufc_service_name
@param string bns
@param string idc
@param table policy_config 
@param bool
]]
function _M.is_retry_ok(from_service_name, ufc_service_name, bns, platforms_idc)
	if not platforms_idc then
		platforms_idc = "all_idc"
	end 
	local config = _M._get_breaker_policy(from_service_name, ufc_service_name, "PE")
	if not config or not config["policy"] then
		return true
	end

	--check forbid timeout
	local policy_service = _M._get_policy_service(from_service_name, ufc_service_name)
	local forbid_timeout = config["forbid_timeout"] or 30

	local is_timeout = breaker.check_service_forbid_timeout(policy_service, ufc_service_name, bns, platforms_idc, forbid_timeout)
	if not is_timeout or _M.is_lawine(policy_service, ufc_service_name, bns, platforms_idc) then
		--未超时
		--ngx.log(ngx.DEBUG, string.format("[logid=%s] [is_retry_ok=false] [policy_service=%s] [from_service=%s] [to_service=%s] [bns=%s] [idc=%s] [timeout=%d] [is_timeout]", ngx.var.ufc_logid, policy_service, from_service_name, ufc_service_name, bns, idc, forbid_timeout))

		return false
	end

	return true
end


--[[
@comment 获取策略
@param string from_service_name
@param string ufc_service_name
@param string key
@return table
]]
function _M._get_breaker_policy(from_service_name, ufc_service_name, key)
	local policy

	local config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	if config and config["breaker_policy"] then
		local policy_service = _M._get_policy_service(from_service_name, ufc_service_name)
		policy = config["breaker_policy"][policy_service][key]

		if not policy then
			policy = config["breaker_policy"]["default"][key]
		end
	end

	if not policy then
		local default_config = _M._get_breaker_default_policy()
		policy = default_config[key]
	end

	--ngx.log(ngx.DEBUG, string.format("[logid=%s] [from_service=%s] [to_service=%s] [%s_policy=%s]", ngx.var.ufc_logid, from_service_name, ufc_service_name, key, cjson.encode(policy)))

	return policy
end


--[[
@comment 获取默认熔断策略命中service，默认返回default
@return string
]]
function _M._get_policy_service(from_service, ufc_service_name)
	local hit_service = "default"
	local config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	if not config then
		return hit_service 
	end

	if not config["breaker_policy"] then
		return hit_service 
	end

	if not from_service or from_service == "-" then
		return hit_service 
	end

	if config["breaker_policy"][from_service] then
		return from_service 
	end

	if config["breaker_policy"]["quote_group"] and config["breaker_policy"]["quote_group"][from_service] then
		return config["breaker_policy"]["quote_group"][from_service]
	end

	return hit_service
end


--[[
@comment 获取默认策略
@return table
]]
function _M._get_breaker_default_policy()
	return conf["breaker_default_policy"]
end

--[[
@comment 判断一个请求是否属于异常
@param string from_service_name
@param string ufc_service_name
@param int http_code
@return 0, 1, -1，其中：-1表示失败，1表示成功，0表示其他
]]
function _M.is_bad_request(from_service_name, ufc_service_name, http_code) 
	local config = _M._get_breaker_policy(from_service_name, ufc_service_name, "PA")
	if not config then
		return false
	end

	if not config or not config["policy"] then
		return false
	end

	local regular = config["regular"]
	local status = _M.PA1(regular, http_code)

	--ngx.log(ngx.DEBUG, string.format("[logid=%s] [http_code=%d] [status=%d] [message='determine request status']", ngx.var.ufc_logid , http_code, status))

	return status
end

--[[
@comment 根据表达式，判断一个请求是否属于异常
@param string regular
@param int status
@return bool
]]
function _M.PA1(regular, http_code)
	if not http_code then
		return false
	end

	local key = ngx.md5(regular)
	if not _M["func"][key] then
		local str = "return function (http_code) "..regular.." end"
		_M["func"][key] = assert(loadstring(str))()
	end

	return _M["func"][key](http_code) 
end


--[[
@comment 单机熔断触发条件
@param string from_service_name
@param string backend
@param table service_config
@return true表示需要封禁，false不需要封禁
]]
function _M.check_backend(from_service_name, ufc_service_name, bns, platforms_idc, backend, http_code, bypass)
	local policy_config = _M._get_breaker_policy(from_service_name, ufc_service_name, "PB")
	if not policy_config then
		return false
	end


	if not policy_config or not policy_config["policy"] then
		return false
	end

	local policy = policy_config["policy"]
	if policy < 1 then
		return false
	end

	local is_fail = false
	local cnt_type = "request_cnt"
	local request_status = _M.is_bad_request(from_service_name, ufc_service_name, http_code)
	if request_status == _M["request_status"]["bad"] then
		--请求异常，需要封禁，目前：502、504、555
		cnt_type = "fail_cnt"
		is_fail = true

	elseif request_status == _M["request_status"]["ok"] then
		--请求成功，目前小于500
		cnt_type = "success_cnt"
		is_fail = false
	end

	--解除封禁确认
	-- unforbid_backend_check 其实是要检测是否封禁，不封禁的话是否重置 check_ok_times
	if bypass ~= 1 and breaker.unforbid_backend_check(ufc_service_name, backend, is_fail) then
		--ngx.log(ngx.DEBUG, string.format("[logid=%s] [service_name=%s] [backend=%s] [message='continute to forbid']", ngx.var.ufc_logid, ufc_service_name, backend))
		_M.backend_breaker(from_service_name, ufc_service_name, bns, backend, platforms_idc)
		return false
	end

	--计数
	breaker.change_backend_cnt(ufc_service_name, backend, cnt_type, 1, bypass)

	--状态码范围计数，只有穿透式参与计数
	--同样的，stream模式只有5xx和200两种，也不参与状态码计数
	if bypass ~= 1 and conf["stream"] == 0 then
		local http_range = _M.get_http_status_range(http_code)
		breaker.change_backend_cnt(ufc_service_name, backend, http_range, 1, bypass)
	end

	local idcs = {"all_idc"}
	if platforms_idc and platforms_idc ~= "all_idc" then
		table.insert(idcs, platforms_idc)
	end

	for _, v in pairs(idcs) do
		breaker.change_service_cnt(ufc_service_name, bns, v, cnt_type, 1, bypass)
	end

	--moniter
	moniter.service_request_cnt(ufc_service_name, cnt_type)

	--PB策略要是能够生效就不用判断PF策略了
	local PB_result = _M["fuc"]["_PB"..policy](ufc_service_name, bns, platforms_idc, backend, policy_config, request_status)
	if PB_result == true then
		return PB_result, "PB" .. policy
	end
	--对所有的service都有一个默认的PF策略，PF策略对所有service都生效
	local policy_config_PF = _M._get_breaker_policy(from_service_name, ufc_service_name, "PF")
	if not policy_config_PF then
		return PB_result, "PB" .. policy
	end

	if not policy_config_PF["policy"] then
		return PB_result, "PB" .. policy
	end

	local policy_PF = policy_config_PF["policy"]
	if policy_PF < 1 then
		return PB_result, "PB" .. policy
	end

	local PF_result, PF_policy = _M["fuc"]["_PF"..policy_PF](ufc_service_name, bns, platforms_idc, backend, policy_config_PF, request_status, http_code)
	--走到这里说明之前没有触发PB的单机封禁，返回的应该是PF策略的结果
	return PF_result, PF_policy
end


--[[
@comment m次有n次请求失败
@param string backend
@param table policy_config
@param string ufc_service_name
@return true表示需要封禁，false不需要封禁
]]
function _M._PB1(ufc_service_name, bns, platforms_idc, backend, policy_config, request_status) 
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end

	if not dy_config["backend"][backend] or not dy_config["backend"][backend]["fail_cnt"] then
		return false
	end

	--有些业务就没有5xx
	--[[
	if dy_config["backend"][backend]["fail_cnt"] == 0 then
		return false
	end
	]]
	local max_fail = policy_config["max_fail"] or 2
	local total_request_cnt = policy_config["request_cnt"] or max_fail

	if dy_config["backend"][backend]["fail_cnt"] >= max_fail then
		return true
	end 


	if dy_config["backend"][backend]["request_cnt"] > total_request_cnt and dy_config["backend"][backend]["fail_cnt"] < max_fail then
		--只要请求次数超过m次，但是失败次数没有达到n次则重置计数数据
		breaker.reset_backend_count_data(ufc_service_name, backend)
	end

	return false
end

--[[

@comment 某个后端某个范围状态码的比例高于整个业务比例的xxx%或者平均耗时高于整个业务比例的xxx%
@param string backend
@param table policy_config
@param string ufc_service_name
@return true表示需要封禁，false不需要封禁
]]
function _M._PF1(ufc_service_name, bns, platforms_idc, backend, policy_config, request_status, http_code) 
	local min_request = policy_config["min_request"] or 20
	local cost_time_ratio = policy_config["cost_time_ratio"] or 100
	local status_ratio = policy_config["status_ratio"] or 40

	--判断一下backend计数是否达到阈值，达到阈值后更新一下last的值
	--这个函数直接返回后端耗时中位数，避免再计算一次
	local backend_cost_time_mid = _M.is_backend_count_reach_threshold(ufc_service_name, backend, min_request)
	if not backend_cost_time_mid then
		--单机计数没有达到阈值，直接返回
		return false, "PF1"
	end

	local service_cost_time_mid_avg = breaker.get_service_mid_average_cost_time(ufc_service_name, backend)
	if backend_cost_time_mid > 0 and service_cost_time_mid_avg >= 0 then
		--中位数服务整体耗时中位数平均值是可能为0的
		if service_cost_time_mid_avg == 0 or (backend_cost_time_mid - service_cost_time_mid_avg) / service_cost_time_mid_avg >= (cost_time_ratio / 100) then
			--返回之前重置一下环比计数值还有耗时数据
			breaker.reset_backend_ratio_count_data(ufc_service_name, backend)
			--打个日志，说明是因为耗时过长被封禁了
			local logid = ngx.var.ufc_logid or "-"
			ngx.log(ngx.NOTICE, string.format("logid %s ufc_service_name %s backend %s cost time is too large and should be forbiden", logid, ufc_service_name, backend))
			return true, "PF1"
		end
	end  

	local status = policy_config["status"] or {"3xx", "4xx", "5xx"}

	for _, v in pairs(status) do
		--状态码在配置范围内
		if _M.is_in_status(http_code, v) then
			local backend_status_ratio = breaker.get_backend_status_ratio(ufc_service_name, backend, min_request, v, status_ratio)

			if backend_status_ratio < 0 then
				breaker.reset_backend_ratio_count_data(ufc_service_name, backend)
				return false, "PF2"
			end
			--整体status比例对比，service的status比例要把单机的去掉
			local service_status_ratio = breaker.get_service_status_ratio(ufc_service_name, backend, v)
			if service_status_ratio < 0 then
				breaker.reset_backend_ratio_count_data(ufc_service_name, backend)
				return false, "PF2"
			end
			if (backend_status_ratio - service_status_ratio) * 100 > status_ratio then
				breaker.reset_backend_ratio_count_data(ufc_service_name, backend)
				--打个日志，说明是因为状态码异常被封禁掉
				local logid = ngx.var.ufc_logid or "-"
				ngx.log(ngx.NOTICE, string.format("logid %s ufc_service_name %s backend %s http code %d is too much and should be forbiden", logid, ufc_service_name, backend, http_code))
				return true, "PF2"
			end
		end
	end
	--返回之前重置一下环比计数值还有耗时数据
	breaker.reset_backend_ratio_count_data(ufc_service_name, backend)
	return false
end

--[[
@comment 判断后端计数是否达到阈值
@return 达到阈值返回后端耗时中位数
]]
function _M.is_backend_count_reach_threshold(ufc_service_name, backend, min_request) 
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return nil
	end

	if not dy_config["backend"][backend] then
		return nil
	end

	--初始化一下service中位数耗时
	if not dy_config["service"] then
		dy_config["service"] = {}
	end

	if not dy_config["service"]["mid_cost_time_sum"] or not dy_config["service"]["mid_cnt"] then
		dy_config["service"]["mid_cost_time_sum"] = 0
		dy_config["service"]["mid_cnt"] = 0
	end
	local service_info = dy_config["service"]
	--状态访问计数用额外的变量统计
	local ratio_request_cnt = dy_config["backend"][backend]["ratio_request_cnt"]
	--达到阈值，不要置0，后面还会用
	if ratio_request_cnt and ratio_request_cnt >= min_request then
		local backend_info = dy_config["backend"][backend]
		backend_info["last_ratio_request_cnt"] = ratio_request_cnt
		backend_info["last_1xx"] = backend_info["1xx"]
		backend_info["last_2xx"] = backend_info["2xx"]
		backend_info["last_3xx"] = backend_info["3xx"]
		backend_info["last_4xx"] = backend_info["4xx"]
		backend_info["last_5xx"] = backend_info["5xx"]
		backend_info["last_other_status"] = backend_info["other_status"]
		--获取耗时的中位数
		--读写共享内存和luavm内存此处处理逻辑不同
		local response_times, cnt
		if conf["meta_table"] then
			response_times, cnt = shared_memmory.get_backend_response_time(ufc_service_name, backend)
		else
			response_times = backend_info["response_times"]
			cnt = #response_times
		end
		if not response_times then
			return nil
		end
		table.sort(response_times)
		--不需要调用len函数
		local mid = math.ceil(cnt / 2)
		if mid < 1 then
			return nil
		end
		local mid_response_time = response_times[mid]

		--之前没有算过中位数
		if not backend_info["last_mid_response_times"] then
			service_info["mid_cnt"] = service_info["mid_cnt"] + 1
			service_info["mid_cost_time_sum"] = service_info["mid_cost_time_sum"] + mid_response_time
		else
			--这个后端之前已经有算过中位数
			service_info["mid_cost_time_sum"] = service_info["mid_cost_time_sum"] - backend_info["last_mid_response_times"] + mid_response_time
		end
		
		backend_info["last_mid_response_times"] = mid_response_time
		return mid_response_time
	end
	return nil
end


--[[
@comment 获取某个状态码属于哪个区间
@param int http_code
@return string (5xx,3xx ...)
]]
function _M.get_http_status_range(http_code) 
	if http_code == nil then
		--如果http_code是空的，返回request_cnt后端总的计数就不会增加
		return "499"
	end
	local http_code_prefix = math.floor(http_code / 100)
	local res = _M["http_status_map"][http_code_prefix]
	return res or "other_status"
end


--[[
@comment http状态码是否属于某个区间
@param int http_code
@param string http_code_range
@return bool
]]
function _M.is_in_status(http_code, http_range)
	if type(http_code) ~= "number" then
		return false
	end 

	local http_code_prefix = math.floor(http_code / 100)
	local temp = _M["http_status_map"][http_code_prefix]
	if temp and temp == http_range then 
		return true
	else
		return false
	end
end


--[[
@comment 累计n次请求失败
@param string backend
@param table policy_config
@param string ufc_service_name
@return true表示需要封禁，false不需要封禁
]]
function _M._PB2(ufc_service_name, bns, idc, backend, policy_config, request_status)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end
	-- 但是这里直接读 dynamic_cache 
	local backend_info = dy_config["backend"][backend]
	if not backend_info or not backend_info["fail_cnt"] then
		return false
	end

	local max_fail = policy_config["max_fail"] or 2
	if backend_info["fail_cnt"] >= max_fail then
		return true
	end

	local total_request_cnt = policy_config["request_cnt"] or 100

	if backend_info["request_cnt"] >= total_request_cnt then
		--旁路式累计封禁策略默认每100次重置一下请求计数数据
		breaker.reset_backend_count_data(ufc_service_name, backend)
	end


	return false
end


--[[
@comment 成功率（2xx+3xx+4xx）比例低于整个业务成功率的xxx%
@param string backend
@param table policy_config
@param string ufc_service_name
@return true表示需要封禁，false不需要封禁
]]
function _M._PB3(ufc_service_name, bns, idc, backend, policy_config, request_status)
	if request_status ~= _M["request_status"]["bad"] then
		return false
	end

	local backend_ratio = breaker.get_backend_success_ratio(ufc_service_name, backend)
	if backend_ratio == 1 then
		return false
	end

	--整体成功率对比
	local min_requests = 1
	local backends_ratio = breaker.get_service_success_ratio(ufc_service_name, bns, idc, min_requests)
	if backends_ratio == 1 then
		return false
	end

	local ratio = policy_config["ratio"] or 50

	--ngx.log(ngx.DEBUG, string.format("[logid=%s] [ufc_service_name=%s] [backend_ratio=%s] [backends_ratio=%s]", ngx.var.ufc_logid, ufc_service_name, backend_ratio, backends_ratio))

	if backend_ratio < backends_ratio * (ratio / 100) then
		return true
	end

	return false
end


--[[
@comment 单机熔断触发后的操作(处理方案以及解封条件)
@comment 0、1、-1  //0 半断开，1 闭合，-1 断开
@param string ufc_service_name
]]
function _M.backend_breaker(from_service_name, ufc_service_name, bns, backend, platform_idc, reason) 
	local config = _M._get_breaker_policy(from_service_name, ufc_service_name, "PC")
	if not config or not config["policy"] then
		return false
	end

	local policy = config["policy"]
	if policy < 1 then
		return false
	end

	if platform_idc == nil or platform_idc == "-" then
		platform_idc = "all_idc"
	end
	--如果正处于全局熔断确认中就不要再封单机了
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name] -- ("service", ${bns}), {"backend", ${backend}}
	if not dy_config["service"][bns] then
		dy_config["service"][bns] = {}
	end
	-- logic_idc 其实是 platform_idc, 这里先不改名称，要改的太多了
	if not dy_config["service"][bns][platform_idc] then
		dy_config["service"][bns][platform_idc] = {}
	end

	local idc_info = dy_config["service"][bns][platform_idc]
	if idc_info["start_count_flag"] then
		--正处于全局熔断确认中
		moniter.bug_service_collect(ngx_worker_cache["static_cache"]["configs"], ufc_service_name, bns, platform_idc, "forbid_backend_in_lawine")
		--ngx.log(ngx.NOTICE, string.format("now %s can not forbid and idc is %s", backend, logic_idc))
		return false
	end
	--ngx.log(ngx.NOTICE, string.format("now lawine_check_flag is %s ", lawine_check_flag))
	--监控上报 
	moniter.backend_collect(ngx_worker_cache["static_cache"]["configs"], ufc_service_name, bns, platform_idc, backend, 0, reason)

	--如果是因为PF策略被封禁的，就根据配置判断下是否触发封禁，没配置默认是不封禁的
	local configs = ngx_worker_cache["static_cache"]["configs"]
	if reason == "PF1" or reason == "PF2" then
		if configs and configs["PF"] and configs["PF"]["enable"] and configs["PF"]["enable"] == 1 then
			ngx.log(ngx.WARN, string.format("backend %s is forbidden, forbid reason is %s", backend, reason))
			return _M["fuc"]["_PC"..policy](ufc_service_name, platform_idc, bns, backend, config)
		else
			return false
		end
	end

	ngx.log(ngx.WARN, string.format("backend %s is forbidden, forbid reason is %s", backend, reason))
	return _M["fuc"]["_PC"..policy](ufc_service_name, platform_idc, bns, backend, config)
end


--[[
@comment 单机熔断触发后的操作(处理方案以及解封条件)
@comment 封禁ip，封禁时间xxx分钟后，解除封禁
@param string backend
@param table policy_config
@param string ufc_service_name
]]
function _M._PC1(ufc_service_name, platform_idc, bns, backend, config) 
	local check_ok_times = 0

	local forbid_timeout = config["forbid_timeout"]
	if not forbid_timeout then
		local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
		forbid_timeout = service_config["forbid_timeout"]
	end
	--十分钟之内封禁第二次封禁时间变成5分钟
	local forbid_interval_time = config["forbid_interval_time"] or conf["defaults"]["forbid_interval_time"]

	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	local now = math.floor(ngx.now())
	--上次被封的时间
	local last_forbid_time = dy_config["backend"][backend]["last_forbid_time"] 

	if last_forbid_time then
		--十分钟之内封禁第二次封禁时间变成5分钟
		if now - last_forbid_time < forbid_interval_time then
			forbid_timeout = config["longer_forbid_timeout"] or conf["defaults"]["longer_forbid_timeout"]
		end
	end

	--更新被封禁的时间
	dy_config["backend"][backend]["last_forbid_time"] = now
	--ngx.log(ngx.WARN, string.format("service name is %s bns is %s backend is %s forbid timeout is %ds", ufc_service_name, bns, backend, forbid_timeout))
	return breaker.forbid_backend(ufc_service_name, bns, backend, platform_idc, forbid_timeout, check_ok_times)
end


--[[
@comment 单机熔断触发后的操作(处理方案以及解封条件)
@comment 封禁ip，封禁时间xxx分钟后，进入半熔断状态，前n请求成确认，如果有请求成功，则立即解除，否则继续封禁
@param string backend
@param table policy_config
@param string ufc_service_name
]]
function _M._PC2(ufc_service_name, platform_idc, bns, backend, config) 
	local check_ok_times = config["check_ok_times"] or 2

	local forbid_timeout = config["forbid_timeout"]
	if not forbid_timeout then
		local service_config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
		forbid_timeout = service_config["forbid_timeout"]
	end
	--十分钟之内封禁第二次封禁时间变成5分钟
	local forbid_interval_time = config["forbid_interval_time"] or conf["defaults"]["forbid_interval_time"]

	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	local now = math.floor(ngx.now())
	--上次被封的时间
	local last_forbid_time = dy_config["backend"][backend]["last_forbid_time"] 
	if last_forbid_time then
		--十分钟之内封禁第二次封禁时间变成5分钟
		if now - last_forbid_time < forbid_interval_time then
			forbid_timeout = config["longer_forbid_timeout"] or conf["defaults"]["longer_forbid_timeout"]
		end
	end

	--更新被封禁的时间
	dy_config["backend"][backend]["last_forbid_time"] = now
	ngx.log(ngx.WARN, string.format("service name is %s bns is %s backend is %s forbid timeout is %ds", ufc_service_name, bns, backend, forbid_timeout))
	return breaker.forbid_backend(ufc_service_name, bns, backend, platform_idc, forbid_timeout, check_ok_times)
end


--[[
@comment 全局熔断触发条件
@param string backend
@param table policy_config
@param string ufc_service_name, true标识全局熔断， false表示没有
]]
function _M.check_service(from_service_name, ufc_service_name, bns, platforms_idc, bypass) 
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end

	local policy_service = _M._get_policy_service(from_service_name, ufc_service_name)
	if _M.is_lawine(policy_service, ufc_service_name, bns, platforms_idc) then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("[logid=%s] [model=PD] [is_lawine=true] [bns=%s] [idc=%s]", logid, bns, platforms_idc))
		return true
	end

	local policy_config = _M._get_breaker_policy(from_service_name, ufc_service_name, "PD")
	if not policy_config or not policy_config["policy"] then
		return false
	end

	local policy = policy_config["policy"]
	if policy < 1 then
		return false
	end

	local is_down = _M["fuc"]["_PD"..policy](ufc_service_name, bns, platforms_idc, policy_config, bypass)
	if is_down then
		local backends_cnt = idc_map.get_backends_cnt(bns, platforms_idc)
		if breaker.is_breaker_disable(backends_cnt) then
			-- 基本不会走到这一步，因为满足这个条件，是不会触发封禁计数的
			-- 边界case如业务多实例缩容至1个实例，有可能命中该case，预防万一 return false 兜底下, 监控 is breaker disable match 字段
			local logid = ngx.var.ufc_logid or "-"
			ngx.log(ngx.WARN, string.format('[is breaker disable match] [logid=%s] [model=PD] [backends_cnt=%d] [bns=%s] [idc=%s]', logid, backends_cnt, bns, platforms_idc))
			return false 
		end

		--触发全局熔断
		_M._forbid_service(ufc_service_name, bns, platforms_idc) 
	end

	return is_down
end


--[[
@comment 全局熔断触发条件
@comment 封禁比例达到xxx%
@comment true 触发， false未触发
@param table policy_config
@param string bns
@param string idc
]]
function _M._PD1(ufc_service_name, bns, platforms_idcs, policy_config, bypass) 
	local backends_cnt, max_forbid_percent

	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end

	

	if not dy_config["service"]["forbidden_cnt"][bns] then
		return false
	end

	local forbidden_backends_cnt = dy_config["service"]["forbidden_cnt"][bns][platforms_idcs]
	if not forbidden_backends_cnt then
		return false
	end

	if not ngx_worker_cache["static_cache"]["bnss"][bns] then
		-- 这里有没有可能命中熔断
		return true
	end

	backends_cnt = idc_map.get_backends_cnt(bns, platforms_idcs)
	-- 全局熔断条件，加了 platform-idc 以后，这里每个 platform-idc 可能实例数很少
	-- 很容易打到封禁熔断比例
	if backends_cnt < 1 then
		return true
	end

	max_forbid_percent = policy_config["ratio"] or 10
	--判断是否是第一次触发，如果第一次触发的是旁路式那么认为该服务只有旁路式，不用二次验证
	local first_request_flag = false
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config["service"][bns] then
		dy_config["service"][bns] = {}
	end

	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]

	if not dy_config["service"][bns][platforms_idcs] then
		dy_config["service"][bns][platforms_idcs] = {}
	end

	local idc_info = dy_config["service"][bns][platforms_idcs]
	if not idc_info["start_count_flag"] then
		--第一次触发PD1策略
		first_request_flag = true
	end

	-- dy_config
	if forbidden_backends_cnt / backends_cnt * 100 >= max_forbid_percent  then
		--穿透式才会进行二次确认
		if bypass == 1 and first_request_flag == true then 
			--第一次触发的是旁路式直接熔断就行了
			--ngx.log(ngx.NOTICE, string.format("tuichu"))
			return true
		else
			--统计一定次数内穿透式成功率，低于指定值返回true		
			local count_flag = _M.PD1_count(ufc_service_name, bns, platforms_idcs, policy_config)
			if count_flag == true then
				local min_success_ratio = policy_config["success_ratio"] or 80
				local pass_success_ratio = breaker.get_service_success_ratio(ufc_service_name, bns, platforms_idcs, 1)
				--计数结束后还是重置一下吧
				_M.reset_service_cnt(ufc_service_name, bns, platforms_idcs)
				ngx.log(ngx.DEBUG, string.format("forbiden ratio is %f, pass success raito is %f",(forbidden_backends_cnt / backends_cnt * 100), pass_success_ratio))
				if pass_success_ratio * 100 <= min_success_ratio then
					return true
				end
			end
		end
	end

	return false
end

function _M.PD1_count(ufc_service_name, bns, idc, policy_config)
	--默认统计200次成功率
	local count_number = policy_config["count_number"] or 200
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config["service"][bns] then
		dy_config["service"][bns] = {}
	end

	if not dy_config["service"][bns][idc] then
		dy_config["service"][bns][idc] = {}
	end

	local idc_info = dy_config["service"][bns][idc]

	--说明是第一次触发
	if not idc_info["start_count_flag"] then
		idc_info["start_count_flag"] = true
		--重置service计数
		--这里初始值为2，用来判断在计数期间，计数有没有被重置
		idc_info["request_cnt"] = 2
		idc_info["fail_cnt"] = 0
		idc_info["success_cnt"] = 0
		return false
	end 
	--说明在计数期间没有到达计数值，计数就被重置了，直接熔断
	if idc_info["request_cnt"] == 0 then
		idc_info["start_count_flag"] = false
		return true
	end
	--now

	if idc_info["request_cnt"] >= count_number then
		idc_info["start_count_flag"] = false
		ngx.log(ngx.DEBUG, string.format("now pass request_cnt is %d", idc_info["request_cnt"]))
		return true
	end
	return false
end


--[[
@comment 全局熔断触发条件
@comment 整体成功率低于xxx比例
@comment true 触发， false未触发
@param table policy_config
@param string bns
@param string idc 
]]

function _M._PD2(ufc_service_name, bns, platforms_idcs, policy_config)
	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end

	local min_requests = policy_config["min_requests"] or 1

	local success_ratio = breaker.get_service_success_ratio(ufc_service_name, bns, platforms_idcs, min_requests)
	local ratio = policy_config["ratio"] or 50
	if success_ratio < (ratio / 100) then
		--熔断触发
		--ngx.log(ngx.DEBUG, string.format("logid %s bns %s idc %s, s %s forbidden percent is %s", ngx.var.ufc_logid, bns, idc, success_ratio, (ratio/100)))
		return true
	end

	return false
end


--[[
@comment 全局熔断触发后的操作(处理方案以及解封条件)
@param string from_service_name
@param string ufc_service_name
]]
function _M.service_breaker(from_service_name, ufc_service_name, bns, idc) 
	local policy_config = _M._get_breaker_policy(from_service_name, ufc_service_name, "PE")
	if not policy_config or not policy_config["policy"] then
		return false
	end

	local policy = policy_config["policy"]
	if policy < 1 then
		return false
	end

	if policy_config["policy_temp"] then
		policy = policy_config["policy_temp"]
	end
	--ngx.log(ngx.DEBUG, string.format("[logid=%s] [model=PE] [policy=%s] [idc=%s]", ngx.var.ufc_logid, policy, idc))

	return _M["fuc"]["_PE"..policy](from_service_name, ufc_service_name, bns, idc, policy_config)
end


--[[
@comment 全局熔断触发后的操作(处理方案以及解封条件)
@comment 全部解封
@param string from_service_name
@param string ufc_service_name
@param string bns
@param string idc
@param table policy_config
@return bool
]]
function _M._PE1(from_service_name, ufc_service_name, bns, idc, policy_config) 
	local policy_service = _M._get_policy_service(from_service_name, ufc_service_name)

	breaker.unforbid_service(policy_service, ufc_service_name, bns, idc)
	return false
end


--[[
@comment 全局熔断触发后的操作(处理方案以及解封条件)
@comment 进入断开状态，封禁时间xxx分钟后，进入闭合状态，全部解封
@param string ufc_service_name
@param string bns
@param string idc
@param table policy_config 
@param bool
]]
function _M._PE2(from_service_name, ufc_service_name, bns, idc, policy_config) 
	--check forbid timeout
	local policy_service = _M._get_policy_service(from_service_name, ufc_service_name)
	local forbid_timeout = policy_config["forbid_timeout"] or 30

	local is_timeout = breaker.check_service_forbid_timeout(policy_service, ufc_service_name, bns, idc, forbid_timeout)
	if is_timeout then
		--超时,直接解封，并允许访问
		breaker.unforbid_service(policy_service, ufc_service_name, bns, idc)

		--ngx.log(ngx.DEBUG, string.format("[logid=%s] [PE2] [policy_service=%s] [from_service=%s] [to_service=%s] [bns=%s] [idc=%s] [timeout=%d] [is_timeout]", ngx.var.ufc_logid, policy_service, from_service_name, ufc_service_name, bns, idc, forbid_timeout))

		return false
	end

	return true
end


--[[
@comment 全局熔断触发后的操作(处理方案以及解封条件)
@comment 进入断开状态，封禁时间xxx分钟后，进入半熔断状态，前n请求成功率高于xxx%，表示已恢复，否则继续断开封禁
@param string ufc_service_name
@param string bns
@param string idc
@param table policy_config 
]]
function _M._PE3(from_service_name, ufc_service_name, bns, idc, policy_config) 
	--check forbid timeout
	local policy_service = _M._get_policy_service(from_service_name, ufc_service_name)
	local forbid_timeout = policy_config["forbid_timeout"] or 30

	local is_timeout = breaker.check_service_forbid_timeout(policy_service, ufc_service_name, bns, idc, forbid_timeout)
	if not is_timeout then
		--未超时，还处于熔断
		return true
	end

	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end

	if not dy_config["service"][bns] then
		return false
	end

	if not dy_config["service"][bns][idc] then
		return false
	end

	if not dy_config["service"][bns][idc]["request_cnt"] then
		return false
	end

	--前n次请求确认
	local max_request_number = policy_config["number"] or 5
	if dy_config["service"][bns][idc]["request_cnt"] < max_request_number then
		local min_requests = 1 
		local success_ratio = breaker.get_service_success_ratio(ufc_service_name, bns, idc, min_requests)
		local ratio = policy_config["ratio"] or 40
		if success_ratio > (ratio / 100) then
			--恢复
			breaker.unforbid_service(policy_service, ufc_service_name, bns, idc)

			--ngx.log(ngx.DEBUG, string.format("logid %s, service %s bns %s idc %s PE3 is ok", ngx.var.ufc_logid, ufc_service_name, bns, idc))
		end

		return false
	else
		--直接熔断
		breaker.forbid_service(policy_service, ufc_service_name, bns, idc)
		_M.reset_service_cnt(ufc_service_name, bns, idc)
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s, service %s bns %s idc %s is lawine", logid, ufc_service_name, bns, idc))
		return true
	end
end


--[[
@comment 全局熔断触发后的操作(处理方案以及解封条件)
@comment 进入半断开状态, 按一定的比例xxx%丢弃请求，丢弃请求返回设置的信息，超过xxx分钟后，进入闭合状态
@comment 调整超时时间为xxx（调小，默认500ms），避免任务堆积
@param string ufc_service_name
@param string bns
@param string idc
@param table policy_config 
]]
function _M._PE4(from_service_name, ufc_service_name, bns, idc, policy_config) 
	--check forbid timeout
	local policy_service = _M._get_policy_service(from_service_name, ufc_service_name)
	local forbid_timeout = policy_config["forbid_timeout"] or 30
	local is_timeout = breaker.check_service_forbid_timeout(policy_service, ufc_service_name, bns, idc, forbid_timeout)
	if is_timeout then
		--超时，解除熔断
		breaker.unforbid_service(policy_service, ufc_service_name, bns, idc)

		--ngx.log(ngx.DEBUG, string.format("logid %s, service %s bns %s idc %s PE4 service unforbid", ngx.var.ufc_logid, ufc_service_name, bns, idc))
		return false
	end

	local drop_rate = policy_config["ratio"] or 10 
	if drop_rate and math.random(100) < drop_rate then
		--命中丢弃
		--ngx.log(ngx.DEBUG, string.format("logid %s, service %s bns %s idc %s PE4 request is drop", ngx.var.ufc_logid, ufc_service_name, bns, idc))
		return true
	end

	return false
end


--[[
@comment 全局熔断触发后的操作(处理方案以及解封条件)
@comment 进入半断开状态, 从一定比例开始按xxx%通过流量，超时时间到后增大通过流量比例。最终到达指定比例后解除封禁。
@param string ufc_service_name
@param string bns
@param string idc
@param table policy_config 
]]
function _M._PE5(from_service_name, ufc_service_name, bns, idc, policy_config) 
	local policy_service = _M._get_policy_service(from_service_name, ufc_service_name)

	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end
	--先设一个封禁时长为更新时长，如果是在封禁第一个阶段后面会将其更新为封禁时长
	local forbid_timeout = policy_config["update_interval"] or 30
	local now_ratio = nil
	local min_ratio = policy_config["min_ratio"] or 0
	if not dy_config["breaker"][bns][idc][policy_service]["last_ratio"] then
		--说明是刚被封，设置初始比例，设置新的封禁时长
		forbid_timeout = policy_config["forbid_timeout"] or 30
		--dy_config["breaker"][bns][idc][policy_service]["last_ratio"] = min_ratio
		now_ratio = min_ratio
	end
	--ngx.log(ngx.NOTICE, string.format("forbid_timeout is  %d", forbid_timeout))
	local is_timeout = breaker.check_service_forbid_timeout(policy_service, ufc_service_name, bns, idc, forbid_timeout)

	if is_timeout then
		--不管怎么说先更新超时时间
		dy_config["breaker"][bns][idc][policy_service]["ftime"] = math.floor(ngx.now())
		--先判断当前通过的流量比例是否达到最大值
		--如果是刚刚被封，last_ratio等于min_ratio
		local last_ratio = dy_config["breaker"][bns][idc][policy_service]["last_ratio"] or min_ratio
		local max_ratio = policy_config["max_ratio"] or 90
		--已经达到最大比例，直接解封
		if last_ratio >= max_ratio then
			--先把last_ratio设为nil，这样下次再封禁就会从最小流量开始
			dy_config["breaker"][bns][idc][policy_service]["last_ratio"] = nil
			--直接解封
			ngx.log(ngx.NOTICE, string.format("service is %s bns is %s idc is %s flow ratio is %d, unforbid the service",ufc_service_name, bns, idc, last_ratio))
			breaker.unforbid_service(policy_service, ufc_service_name, bns, idc)
			return false
		else
			--没有达到最大比例，继续放量
			local ratio_interval = policy_config["ratio_interval"] or 20
			now_ratio = last_ratio + ratio_interval
			ngx.log(ngx.NOTICE, string.format("service is %s, bns is %s, idc is %s, increase the flow ratio to %d", ufc_service_name, bns, idc, now_ratio))
		end
		--更新last_ratio
		dy_config["breaker"][bns][idc][policy_service]["last_ratio"] = now_ratio
	end
	--如果是刚被封，当前放量比例为min_ratio
	now_ratio = dy_config["breaker"][bns][idc][policy_service]["last_ratio"] or min_ratio
	--ngx.log(ngx.NOTICE, string.format("now flow ratio is %d", now_ratio))
	local drop_rate = 100 - now_ratio
	--这里应该是<=,math.random产生的是【1，100】的整数
	--要不然drop_rate是100的时候还会有流量放过去
	if drop_rate and math.random(100) <= drop_rate then
		--命中丢弃
		return true
	end

	return false
end

--[[
@comment 全局熔断触发后的操作(处理方案以及解封条件)
@comment 进入半断开状态, 从一定比例开始按xxx%通过流量，并统计成功率，超时时间到后成功率高于XX%继续放量，小于XX减少放量%。最终到达指定比例后解除封禁。
@param string ufc_service_name
@param string bns
@param string idc
@param table policy_config 
]]
function _M._PE6(from_service_name, ufc_service_name, bns, idc, policy_config) 
	local policy_service = _M._get_policy_service(from_service_name, ufc_service_name)

	local dy_config = ngx_worker_cache["dynamic_cache"][ufc_service_name]
	if not dy_config then
		return false
	end
	--先设一个封禁时长为更新时长，如果是在封禁第一个阶段后面会将其更新为封禁时长
	local forbid_timeout = policy_config["update_interval"] or 30
	local now_ratio = nil
	local min_ratio = policy_config["min_ratio"] or 0
	if not dy_config["breaker"][bns][idc][policy_service]["last_ratio"] then
		--说明是刚被封，设置初始比例，设置新的封禁时长
		forbid_timeout = policy_config["forbid_timeout"] or 30
		--dy_config["breaker"][bns][idc][policy_service]["last_ratio"] = min_ratio
		now_ratio = min_ratio
	end
	local is_timeout = breaker.check_service_forbid_timeout(policy_service, ufc_service_name, bns, idc, forbid_timeout)
	if is_timeout then
		--不管怎么说先更新超时时间
		dy_config["breaker"][bns][idc][policy_service]["ftime"] = math.floor(ngx.now())
		--先判断当前通过的流量比例是否达到最大值
		--如果是刚刚被封，last_ratio等于min_ratio
		local last_ratio = dy_config["breaker"][bns][idc][policy_service]["last_ratio"] or min_ratio
		local max_ratio = policy_config["max_ratio"] or 90
		--ngx.log(ngx.NOTICE, string.format("last ratio is %d, max radio is :%d",last_ratio, max_ratio))
		--已经达到最大比例，直接解封
		if last_ratio >= max_ratio then
			--先把last_ratio设为nil，这样下次再封禁就会从最小流量开始
			dy_config["breaker"][bns][idc][policy_service]["last_ratio"] = nil
			--直接解封
			ngx.log(ngx.NOTICE, string.format("service is %s bns is %s idc is %s flow ratio is %d, unforbid the service",ufc_service_name, bns, idc, last_ratio))
			breaker.unforbid_service(policy_service, ufc_service_name, bns, idc)
			--解封后还是重新开始计数好一点
			_M.reset_service_cnt(ufc_service_name, bns, idc)
			return false;
		else
			--没有达到最大比例，根据当前阶段的成功率判断是应该放量还是应该缩量
			local ratio_interval = policy_config["ratio_interval"] or 20
			local ratio = policy_config["ratio"] or 50
			local min_requests = 1 
			--应该把406从统计数据中去掉，但是并不用做特殊的处理，因为如果熔断了，后端状态码是 -，不会被request_callback函数统计
			local success_ratio = breaker.get_service_success_ratio(ufc_service_name, bns, idc, min_requests)
			--如果成功率小于配置中的比例就回退到上一阶段(把等于0的情况要除掉，因为被封禁就是为0)
			if success_ratio * 100 < ratio and now_ratio ~= 0 then
				now_ratio = last_ratio - ratio_interval
				--不能无限制的减少百分比，减到最小值就行了
				if now_ratio < min_ratio then
					now_ratio = min_ratio
				end
				ngx.log(ngx.NOTICE, string.format("service is %s bns is %s idc is %s success ratio is %f, now flow ratio is %d, we should reduce it to %d",ufc_service_name, bns, idc, success_ratio, last_ratio, now_ratio))
			else
				now_ratio = last_ratio + ratio_interval
				ngx.log(ngx.NOTICE, string.format("service is %s bns is %s idc is %s success ratio is %f, now flow ratio is %d, we should increase it to %d",ufc_service_name, bns, idc, success_ratio, last_ratio, now_ratio))
			end
			--下个阶段重新开始计数
			_M.reset_service_cnt(ufc_service_name, bns, idc)
		end
		--更新last_ratio
		dy_config["breaker"][bns][idc][policy_service]["last_ratio"] = now_ratio
	end
	--如果是刚被封，当前放量比例为min_ratio
	now_ratio = dy_config["breaker"][bns][idc][policy_service]["last_ratio"] or min_ratio
	--ngx.log(ngx.NOTICE, string.format("now ratio is %d", now_ratio))
	local drop_rate = 100 - now_ratio
	--这里应该是<=,math.random产生的是【1，100】的整数
	--要不然drop_rate是100的时候还会有流量放过去
	if drop_rate and math.random(100) <= drop_rate then
		--命中丢弃
		--ngx.log(ngx.DEBUG, string.format("logid %s, service %s bns %s idc %s PE4 request is drop", ngx.var.ufc_logid, ufc_service_name, bns, idc))
		return true
	end

	return false
end


--[[
@comment 重置service计数器
@param string bns
@param string idc
@param table policy_config 
]]
function _M.reset_service_cnt(ufc_service_name, bns, idc) 
	local types = {"success_cnt", "request_cnt", "fail_cnt"}
	for _, v in pairs(types) do
		breaker.change_service_cnt(ufc_service_name, bns, idc, v, 0)
	end
end


--[[
@comment 封禁service 
@param string bns
@param string idc
@param table policy_config 
]]
function _M._forbid_service(ufc_service_name, bns, platforms_idc) 
	local services = {"default"}
	local config = ngx_worker_cache["static_cache"]["services"][ufc_service_name]
	if config and config["breaker_policy"] then
		for service, _ in pairs(config["breaker_policy"]) do
			if service ~= "default" and service ~= "quote_group" then 
				table.insert(services, service)
			end
		end
	end

	for _, service_name in pairs(services) do
		breaker.forbid_service(service_name, ufc_service_name, bns, platforms_idc)

		--ngx.log(ngx.DEBUG, string.format("[logid=%s] [policy_service=%s] [ufc_service_name=%s] [bns=%s] [idc=%s]", ngx.var.ufc_logid, service_name, ufc_service_name, bns, idc))
	end

	--计数清除
	_M.reset_service_cnt(ufc_service_name, bns, platforms_idc)
	--check一下封禁计数有没有bug
	breaker.check_service_forbid_cnt(ufc_service_name, bns, platforms_idc)

	--上报监控
	moniter.service_collect(ngx_worker_cache["static_cache"]["configs"], ufc_service_name, platforms_idc, bns)
end


_M["fuc"] = {
	["_PB1"] = _M._PB1,
	["_PB2"] = _M._PB2,
	["_PB3"] = _M._PB3,
	["_PC1"] = _M._PC1,
	["_PC2"] = _M._PC2,
	["_PD1"] = _M._PD1,
	["_PD2"] = _M._PD2,
	["_PE1"] = _M._PE1,
	["_PE2"] = _M._PE2,
	["_PE3"] = _M._PE3,
	["_PE4"] = _M._PE4,
	["_PE5"] = _M._PE5,
	["_PE6"] = _M._PE6,
	["_PF1"] = _M._PF1
}

return _M

