local cjson = require "cjson.safe"
local utils = require "utils"
local conf = require "conf"
local redis = require "resty.redis"
local math = require "math"
local ufc   = require "ufclib.ufc"
local http = require "ufclib.Http"
local moniter = require "moniter"
local lock = require "lock"
local process = require "ngx.process"
local shared_memory = require "shared_memory"
local string = string
local tonumber = tonumber
local tostring = tostring
local table = table
local pairs = pairs

local _M = {
    ["doc"] = "sync ufc config from cloud/local to nginx worker cache",
    ["static_cache"] = {},
    ["dynamic_cache"] = {
		["shared_memmory_key"] = "start",
		["service_name"] = "start",
		["zone"] = "dynamic_cache",
		["backend_name"] = "start",
		["forbid_flag"] = false,
	},
	["not_shared_dynamic_cache"] = nil,
	["backend_keys"] = {},
	["meta_table"] = {},
    ["log_trace"] = {
        ["local"] = {},
        ["cloud"] = {}
	},
	["need_services"] = {},
	["need_bnss"] = {},
	["ufc_meta_info"] = nil,
    ["inited"] = false,

    ["forbid_list"] = nil,
    ["forbid_list_last"] = nil,
    
    ["upload_data"] = {},

    -- 此值为true,请求才能向这里面写信息
    ["upload_data_ok"] = true,   
 
    -- idc
    ["local_idc"] = nil,

    --本机ip
    ["local_ip"] = nil,
    --非法区域信息
    ["area"] = nil,
    ["ip_prefix_area_list"] = {},
    ["hostname_postfix_area_list"] = {},
	["ufc_log"] = {}
}

function _M.init_area()
    local fd = io.open("ufc/area.txt", "r")
    if not fd then
        local logid = ngx.var.ufc_logid or "-"
        ngx.log(ngx.WARN, string.format("[UFC_AREA_ERROR]logid %s open area.txt failed", logid))
        return
    end
    local i = 0
    for line in fd:lines() do
        if i == 0 then
            _M["area"] = line
        end

        if i == 1 then
            _M["hostname_postfix_area_list"] = utils.explode(";", line)
        end

        if i == 2 then
            _M["ip_prefix_area_list"] = utils.explode(";", line)
        end
        
        i = i + 1
        if 1 >= 3 then
            fd:close()
            return
        end
    end
end

--如果redis配置中有idc相关配置，用redis中的配置替换conf.lua中的配置
function _M.update_idc_conf()
	--一共有四项配置和机房有关idc_location, idc_map, redis_ufc_service, sandbox_redis_ufc_service
	if not _M["static_cache"] or not _M["static_cache"]["configs"] then
		return
	end

	if not _M["static_cache"]["configs"]["idc_conf"] then
		return
	end
	local idc_conf = _M["static_cache"]["configs"]["idc_conf"]
	if idc_conf["idc_location"] then
		conf["idc_location"] = idc_conf["idc_location"]
	end
	if idc_conf["idc_map"] then
		conf["idc_map"] = idc_conf["idc_map"]
	end
	if idc_conf["redis_ufc_service"] then
		conf["redis_ufc_service"] = idc_conf["redis_ufc_service"]
	end
	if idc_conf["sandbox_redis_ufc_service"] then
		conf["sandbox_redis_ufc_service"] = idc_conf["sandbox_redis_ufc_service"]
	end
end

function _M.init_local_idc()
    local s = utils.get_hostname()
    if string.find(s, ".baidu.com") then
        s = string.sub(s, 0, -11)
    end

    local t = utils.explode(".", s)
    if #t ~= 0 then
        _M["local_idc"] = t[#t]
    end
end


--定时淘汰need_bns, need_service
function _M.clear_needed_config(name)
	local time_now = ngx.now()
	local need_configs = _M[name]
	if not need_configs then
		return
	end

	for k, v in pairs(need_configs) do
		--一个小时没有流量就淘汰
		if v and type(v) == "number" then
			if time_now - v >= 3600 then
				need_configs[k] = nil
				--清除service相关数据
				if name == "need_services" then
					_M.delete_invalid_service_data(k)
					ngx.log(ngx.NOTICE, string.format("service %s, invalid data cleared", k))
					ngx.sleep(0)
				end
			end
		end
	end
end

--如果数据在共享内存中
--对于没有请求的service把缓存数据清理掉
function _M.delete_invalid_service_data(service_name)
	if not conf["meta_table"] then
		return
	end

	local service_tables = _M["meta_table"][service_name]
	if not service_tables then
		return
	end

	--把service中位数相关数据置0
	--如果不置0下面把backend table删掉之后会导致mid_cnt没法重置
	local dy_config = _M["dynamic_cache"][service_name]
	if not dy_config then
		goto done
	end

	if not dy_config["service"] then
		goto done
	end
	dy_config["service"]["mid_cost_time_sum"] = 0
	dy_config["service"]["mid_cnt"] = 0

	:: done ::

	--把共享内存中table相关key删掉
	--只删除backend信息相关的table(删掉也不会有问题)
	--删除其他的table数据可能会影响熔断逻辑
	local i = 0
	for backend, backend_tables in pairs(service_tables) do
		if backend ~= "ready" and backend ~= "start" then
			for _, backend_table in pairs(backend_tables) do
				shared_memory.delete_invalid_table(backend_table)
				--后端实例太多，清理的时候可能会卡住
				i = i + 1
				if i > 100 then
					i = 0
					ngx.sleep(0)
				end
			end
		end
	end
	_M["meta_table"][service_name] = nil

	--共享内存中backend计数数据删掉
	if not _M["backend_keys"][service_name] then
		return
	end

	local service_cfg = _M["static_cache"]["services"][service_name]
	if not service_cfg or not service_cfg["bns_list"] then
		return
	end

	local bns_list = {}
	for bns, _ in pairs(service_cfg["bns_list"]) do
		table.insert(bns_list, bns)
	end
	--small flow
	if service_cfg["small_flow"] then
		for _, small in pairs(service_cfg["small_flow"]) do
			if small["bns_list"] then
				for bns, _ in pairs(small["bns_list"]) do
					table.insert(bns_list, bns)
				end
			end
		end
	end

	i = 0
	for backend, _ in pairs(_M["backend_keys"][service_name]) do
		_M.delete_invalid_backend_data(nil, service_name, backend, bns_list)
		i = i + 1
		if i > 100 then
			i = 0
			ngx.sleep(0)
		end
	end

	_M["backend_keys"][service_name] = nil
end

--定时清除backend数据
function _M.clear_invalid_backend()
	local bnss = _M["static_cache"]["bnss"]
	local dy_config = _M["dynamic_cache"]
	local services = _M["static_cache"]["services"]
	for service_name, service_cfg in pairs(services) do
		if not dy_config[service_name] then
			goto done
		end
		local backends = nil
		if conf["meta_table"] then
			backends = _M["backend_keys"][service_name]
		else
			backends = dy_config[service_name]["backend"]
		end
		if backends then
			local bns_list = {}
			for bns, _ in pairs(service_cfg["bns_list"]) do
				table.insert(bns_list, bns)
			end
			--small flow
			if service_cfg["small_flow"] then
				for _, small in pairs(service_cfg["small_flow"]) do
					if small["bns_list"] then
						for bns, _ in pairs(small["bns_list"]) do
							table.insert(bns_list, bns)
						end
					end
				end
			end
			for backend, _ in pairs(backends) do
				local ok = false
				for _, bns in pairs(bns_list) do
					local backend_map = bnss[bns]["backend_map"]
					--有些机器有些老配置可能没有backend_map这个字段，兼容一下
					if not backend_map then
						ok = true
						break
					end

					if backend_map[backend] then
						ok = true
						break
					end
				end
				--如果实例已经下线直接把共享内存中相应的数据删掉
				if not ok then
					--是否还处于封禁阶段
					--只要有一个该后端还有一个bns被封着就不应该重置
					local backends_all_unfrobidden = true
					for _, bns in ipairs(bns_list) do
						--这个函数返回false说明后端被封禁了
						if not _M._is_backend_forbidden(service_name, backend, bns) then
							backends_all_unfrobidden = false
							break
						end
					end
					if backends_all_unfrobidden == true then
						_M.delete_invalid_backend_data(backends, service_name, backend, bns_list)
					end
				end
			end
		end
		--每个service下面的无效backend dynamic数据清理完之后协程切出去一次
		--高版本的openresty对sleep(0)有特殊的优化，不会频繁触发epoll_wait
		ngx.sleep(0)
		:: done ::
	end
	--定时淘汰更新need service, bns
	_M.clear_needed_config("need_services")
	_M.clear_needed_config("need_bnss")
end

--实例是否被封禁
function _M._is_backend_forbidden(ufc_service_name, backend, bns)
	--读写共享内存和luavm内存此处处理逻辑不同
	if conf["meta_table"] then
		if not shared_memory.get_dynamic_data5(ufc_service_name, "backend", backend, "forbidden", bns) then
			return true
		end
	else
		local dy_config = _M["dynamic_cache"][ufc_service_name]
		if not dy_config["backend"][backend] then  -- 说明还不存在dynamic里面
			return true
		end
	
		if not dy_config["backend"][backend]["forbidden"] then -- 不封禁
			return true
		end

		if not dy_config["backend"][backend]["forbidden"][bns] then -- 不封禁
			return true
		end
	end
	return false
end

--把下线后端的中位数耗时从service 中删掉
function _M.service_mid_costtime_sum_subtract(service_name, backend)
	local dy_config = _M["dynamic_cache"][service_name]
	if not dy_config then
		return
	end

	if not dy_config["backend"] then
		return
	end

	if not dy_config["service"] then
		return
	end
	local service_info = dy_config["service"]
	if not service_info["mid_cost_time_sum"] or not service_info["mid_cnt"] then
		return
	end
	local backend_data = dy_config["backend"][backend]

	if not backend_data or not backend_data["last_mid_response_times"] then
		return
	end
	service_info["mid_cost_time_sum"] = service_info["mid_cost_time_sum"] -  backend_data["last_mid_response_times"]
	service_info["mid_cnt"] = service_info["mid_cnt"] - 1
end

function _M.delete_invalid_backend_data(backends, service_name, backend, bns_list)
	--把下线后端的中位数耗时从service 中删掉
	_M.service_mid_costtime_sum_subtract(service_name, backend)
	if conf["meta_table"] then
		--把共享内存中的数据删掉
		if _M["backend_keys"][service_name] and _M["backend_keys"][service_name][backend] then
			shared_memory.delete_invalid_backend_data(service_name, backend, bns_list)
			_M["backend_keys"][service_name][backend] = nil
		end
		--缓存的table数据也要删掉
		if _M["meta_table"][service_name] and _M["meta_table"][service_name][backend] then
			_M["meta_table"][service_name][backend] = nil
		end
		--后端耗时数据放在额外的zone里面
		shared_memory.clear_backend_response_time(service_name, backend)
	else
		backends[backend] = nil
	end
end

function _M.upload_logs()
    --关闭
    _M["upload_data_ok"] = false

    local data = _M["upload_data"]

    --重置
    _M["upload_data"] = {}
	
	local max_slice_num = conf["cloud_log"]["max_slice_num"] or 1000
    local slices = {}
    for from_service, to_service_arr in pairs(data) do
        slices[from_service] = {}
        for to_service, ip_arr in pairs(to_service_arr) do
            slices[from_service][to_service] = {}

            local size = 0
            for ip, ip_data in pairs(ip_arr) do
                size = size + 1
                slices[from_service][to_service][ip] = ip_data

                if size >= max_slice_num then
                    --平均耗时
                    _M.handle_logs_cost_time(slices)

                    --upload
                    _M.logs_upload_to_cloud(slices)

                    slices[from_service][to_service] = {}
                    size = 0
                end
            end

            if size > 0 then
                --平均耗时
                _M.handle_logs_cost_time(slices)

                --upload
                _M.logs_upload_to_cloud(slices)
                slices[from_service][to_service] = {}
            end
        end
    end

    _M["upload_data_ok"] = true
end

function _M.handle_logs_cost_time(data)
    for from_service, to_service_arr in pairs(data) do
        for to_service, ip_arr in pairs(to_service_arr) do
            for ip, ip_data in pairs(ip_arr) do
                for status_key, time_arr in pairs(ip_data["cost_time_data"]) do
                    if time_arr and #time_arr > 0 then
                        local count = 0
                        local time_whole = 0
                        for _, time in pairs(time_arr) do
                            count = count + 1
                            time_whole = time_whole + time
                        end
                        ip_data["cost_time"][status_key] = time_whole/count
                    end
                end

                ip_data["cost_time_data"] = {}
            end
        end
    end
end

function _M.logs_upload_to_cloud(data)
    local body = cjson.encode(data) 
    local body_len = string.len(body)

    local ufc_headers = {}
    ufc_headers["x-ufc-self-service-name"] = "pcs-ufc-agent"

    local upload_headers = {}
    upload_headers["Content-Type"] = "text/plain"
    upload_headers["Content-Length"] = body_len

    local logid = ngx.time()
    for i=1, 2 do
        local response, err = ufc.bypass(conf["cloud_log"]["ufc_service"], logid, ufc_headers, true)
        if not err and response and response.host and response.port then
            local addr = {
                domain = response.host,
                port = response.port,
            }

            local ret, upload_err = http.post(addr, "/v1/ufc/logback?method=upload", upload_headers, body)
            ngx.log(ngx.NOTICE, string.format("upload_logs [upload_body_len:%d] [ret:%s] [err:%s]", body_len, cjson.encode(ret), cjson.encode(upload_err) ))
            if not upload_err then
                break
            end

            ufc.callback_fail(response)
        end
        
        ngx.log(ngx.WARN, string.format("upload_logs, ufc bypass failed, err:%s, response:%s", cjson.encode(err), cjson.encode(response)))
    end
end

function _M.is_inited()
    return _M["inited"]
end

function _M._init()
    _M["inited"] = true
end

--把cache table里面的数据写到磁盘上
function _M.save_cache_table(cache_table)
	--这个操作都是在特权进程上进行，阻塞也没有问题，不需要手动把协程切出去
	--手动切协程反倒会导致耗时过长
	for file_name, content in pairs(cache_table) do
		utils.basic_save(conf["meta_cache_dir"], file_name, content)
	end
	--保险起见把cache_table清掉
	table.clear(cache_table)
end

--cache_table不为空表示，先把需要保存的数据放缓存最后一次性写磁盘
function _M.save_item_local(config_type, config_name, config_content, cache_table)
	--stream模块不写磁盘
	if conf["stream"] == 1 then
		return
	end
	local file_name = string.format("%s-%s", config_type, config_name)
	utils.basic_save(conf["meta_cache_dir"], file_name, config_content, cache_table)
end

--cache_table不为空表示，先把需要保存的数据放缓存最后一次性写磁盘
function _M.save_config_local(config_type, config_content, cache_table)
	--stream模块不写磁盘
	if conf["stream"] == 1 then
		return
	end

	ngx.log(ngx.NOTICE, string.format("config type %s, start save to disk", config_type))
	utils.basic_save(conf["meta_cache_dir"], config_type , config_content, cache_table)
end

--cache_table不为空表示，先把需要保存的数据放缓存最后一次性写磁盘
function _M.save_ufc_meta_info(cache_table)
	--stream模块不写磁盘
	if conf["stream"] == 1 then
		return
	end

	ngx.log(ngx.NOTICE, "start save ufc meta info to disk")
	utils.basic_save(conf["meta_cache_dir"], "ufc_meta_info", _M["ufc_meta_info"], cache_table)
end

--cache_table不为空表示，先把需要保存的数据放缓存最后一次性写磁盘
function _M.save_need_info(force, cache_table)
	--stream模块不写磁盘
	if conf["stream"] == 1 then
		return
	end
	
	if not force and _M["config_update_time"] < conf["unimportant_config_update_interval"] then
		return
	end

	ngx.log(ngx.NOTICE, "start save needed info to disk")
	utils.basic_save(conf["meta_cache_dir"], "need_services", _M["need_services"], cache_table)
	utils.basic_save(conf["meta_cache_dir"], "need_bnss", _M["need_bnss"], cache_table)
end

function _M.load_one_local_config(config_type, config_name)
	if not _M["static_cache"] then
		_M["static_cache"] = {}
	end

	if not _M["static_cache"][config_type .. "s"] then
		_M["static_cache"][config_type .. "s"] = {}
	end

	local file_name = string.format("%s/%s-%s", conf["meta_cache_dir"], config_type, config_name)
	local file_fd = io.open(file_name, "r")
	if not file_fd then
		ngx.log(ngx.WARN, string.format("%s file not exist", file_name))
		return false
	end

	local content_encode = file_fd:read("*all")
	file_fd:close()

	local content = cjson.decode(content_encode)
	if not content then
		ngx.log(ngx.WARN, string.format("%s file format error", file_name))
		return false
	end

	_M["static_cache"][config_type .. "s"][config_name] = content
	return true
end

function _M.is_shared_memory_exist() 
	local global_mtime = shared_memory.get_global_mtime() 
	if global_mtime then 
		return true 
	end 
	return false 
end 

--读取磁盘上的本地配置文件
function _M._load_local_config()
	if _M.is_inited() then
		return conf["res"]["NONEED"]
	end
	

	local need_read_conf_cache_file = false
	local ufc_meta_info_encode, ufc_meta_info

	--首先读取ufc meta信息
	--只有ufc_meta_info 不存在的时候才去读取conf_cache_file 
	--其他的失败情况按读取本地配置失败处理
	--特权进程和worker进程都需要读ufc_meta_info
	local ufc_meta_file_name = string.format("%s/ufc_meta_info", conf["meta_cache_dir"])
	local ufc_meta_file_fd = io.open(ufc_meta_file_name, "r")
	if not ufc_meta_file_fd then
		ngx.log(ngx.WARN, "ufc_meta_info file not exist")
		need_read_conf_cache_file = true
		goto read_conf_cache_file
	end

	ufc_meta_info_encode = ufc_meta_file_fd:read("*all")
	ufc_meta_file_fd:close()

	ufc_meta_info = cjson.decode(ufc_meta_info_encode)
	if not ufc_meta_info then
		ngx.log(ngx.WARN, "ufc_meta_info file format error")
		return conf["res"]["FAIL"]
	end
	_M["ufc_meta_info"] = ufc_meta_info

	-- 特权进程/worker进程读磁盘数据前检查共享内存的数据存在，如果存在读共享内存
	if _M.is_shared_memory_exist() then
		ngx.log(ngx.WARN, string.format("load config from shared memory when restart, worker pid: %s, process type: %s", ngx.worker.pid(), process.type()))
		_M._init()
		return conf["res"]["SUCCESS"] 
	end 
	
	if process.type() == "worker" then
		_M.worker_process_load_local_config()
	else
		_M.pri_process_load_local_config()
	end
	---------------------
	--惰性加载失败，退化到读conf_cache_file 文件

	:: read_conf_cache_file ::
	if need_read_conf_cache_file == true then
		return _M.load_conf_cache_file()
	end
	return conf["res"]["SUCCESS"]
end

--特权进程加载本地配置
--加载磁盘上的所有配置，写到共享内存中
function _M.pri_process_load_local_config()
	--把磁盘上的配置文件读到内存中
	_M.load_one_type_local_config("service")
	_M.load_one_type_local_config("bns")
	_M.load_other_configs()

	--把配置文件写到共享内存
	local config_types = {"service", "bns", "config", "http_ip", "stream_ip"}
	for _, config_type in ipairs(config_types) do
		_M.save_one_type_config_shared_mem(config_type)
	end
end

function _M.load_one_type_local_config(config_type)
	if config_type ~= "service" and  config_type ~= "bns" then
		return
	end

	if not _M["ufc_meta_info"] or not _M["ufc_meta_info"][config_type .. "s"] then
		return
	end

	local config_names = _M["ufc_meta_info"][config_type .. "s"]
	for config_name, _ in pairs(config_names) do
		_M.load_one_local_config(config_type, config_name)
	end
end

--把配置的content和mtime写到共享内存中
--cnt 和 index信息在配置更新的时候会写进去
function _M.save_one_type_config_shared_mem(config_type)
	if not _M["static_cache"] or not _M["static_cache"][config_type .. "s"] then
		return
	end

	local configs = _M["static_cache"][config_type .. "s"]
	for config_name, config_content in pairs (configs) do
		if shared_memory.set_config_content(config_type, config_name, cjson.encode(config_content)) then 
			shared_memory.set_config_mtime(config_type, config_name, config_content["mtime"])
		end 

	end
end

--woker进程加载本地配置
--只加载需要用到的配置
function _M.worker_process_load_local_config()
	--读取需要加载的service, bns, idcmap name
	local need_services, need_bnss = _M.get_need_load_config_names()
	if not need_services or not need_bnss then
		ngx.log(ngx.WARN, "load needed load config names failed")
		return conf["res"]["FAIL"]
	end
	_M["need_bnss"] = need_bnss
	_M["need_services"] = need_services

	--加载需要加载的service, bns信息
	--这个不会返回失败，本地加载失败的配置项靠云端来就好了
	_M.load_needed_configs("service", need_services)
	_M.load_needed_configs("bns", need_bnss)

	--加载config，stream_ip, http_ip 信息
	--这个不会返回失败，本地加载失败的配置项靠云端来就好了
	_M.load_other_configs()
end


--从磁盘上面读取需要加载的配置名
function _M.get_need_load_config_names()
	local need_services_file_name = string.format("%s/need_services", conf["meta_cache_dir"])
	local need_bnss_file_name = string.format("%s/need_bnss", conf["meta_cache_dir"])
 
	local need_services_file_fd = io.open(need_services_file_name, "r")
	if not need_services_file_fd then
		ngx.log(ngx.WARN, string.format("file name: %s, need_services file not exist", need_services_file_name))
		return nil, nil
	end

	local need_services_encode = need_services_file_fd:read("*all")
	need_services_file_fd:close()

	local need_services = cjson.decode(need_services_encode)
	if not need_services then
		ngx.log(ngx.WARN, string.format("file name: %s, need_services file format error", need_services_file_name))
		return nil, nil
	end
	------------------------------------------------------

	local need_bnss_file_fd = io.open(need_bnss_file_name, "r")
	if not need_bnss_file_fd then
		ngx.log(ngx.WARN, string.format("file name: %s, need_bnss file not exist", need_bnss_file_name))
		return nil, nil
	end

	local need_bnss_encode = need_bnss_file_fd:read("*all")
	need_bnss_file_fd:close()

	local need_bnss = cjson.decode(need_bnss_encode)
	if not need_bnss then
		ngx.log(ngx.WARN, string.format("file name: %s, need_bnss file format error", need_bnss_file_name))
		return nil, nil
	end

	return need_services, need_bnss
end

--从磁盘上面加载需要的bns和service 信息
function _M.load_needed_configs(config_type, config_names)
	if not _M["static_cache"] then
		_M["static_cache"] = {}
	end

	if not _M["static_cache"][config_type .. "s"] then
		_M["static_cache"][config_type .. "s"] = {}
	end

	for config_name, _ in pairs(config_names) do
		_M.load_one_local_config(config_type, config_name)
	end
end

--从磁盘上面加载其他的config
function _M.load_other_configs()
	if not _M["static_cache"] then
		_M["static_cache"] = {}
	end

	local configs = {"configs", "http_ips", "stream_ips"}
	for _, config in pairs(configs) do
		if not _M["static_cache"][config] then
			_M["static_cache"][config] = {}
		end
		local config_file_name = string.format("%s/%s", conf["meta_cache_dir"], config)
		local config_file_fd = io.open(config_file_name, "r")
		if not config_file_fd then
			ngx.log(ngx.WARN, string.format("%s file not exist", config_file_name))
			goto next
		end

		local config_encode = config_file_fd:read("*all")
		config_file_fd:close()

		local config_content = cjson.decode(config_encode)
		if not config_content then
			ngx.log(ngx.WARN, string.format("%s file format error", config_file_name))
			goto next
		end

		_M["static_cache"][config] = config_content

		:: next ::
	end
end


--如果读取磁盘上面的meta信息失败，就退化到读conf_cache_file 文件
function _M.load_conf_cache_file()
    local cache_fname, fd, local_cache_encode, local_cache
    -- after inited, we can load config from worker cache directly
    if _M.is_inited() then
        return conf["res"]["NONEED"]
    end

    cache_fname = string.format("%s/conf_cache_file", conf["meta_cache_dir"])
    fd = io.open(cache_fname, "r")
    if not fd then
        return conf["res"]["FAIL"]
    end

    local_cache_encode = fd:read("*all")
    fd:close()

    local_cache = cjson.decode(local_cache_encode)
    if not local_cache then
        ngx.log(ngx.WARN, "local cache file format error")
        return conf["res"]["FAIL"]
    end
	_M["static_cache"] = local_cache


	--第一次得手动处理meta信息
	--代码第一次上线，需要把所有的配置都写磁盘
	_M["ufc_meta_info"] = {}
	_M["ufc_meta_info"]["services"] = {}
	_M["ufc_meta_info"]["bnss"] = {}
	local services = _M["ufc_meta_info"]["services"]
	local bnss = _M["ufc_meta_info"]["bnss"]
	for service, service_content in pairs(_M["static_cache"]["services"]) do 
		services[service] = 1
	end

	for bns, bns_content in pairs(_M["static_cache"]["bnss"]) do 
		bnss[bns] = 1
	end

	--woker进程工作到此结束，剩下写磁盘和写共享内存由特权进程来
	if process.type() == "worker" then
		return conf["res"]["SUCCESS"]
	end

	--写磁盘
	for service, service_content in pairs(_M["static_cache"]["services"]) do 
		_M.save_item_local("service", service, service_content)
	end
	for bns, bns_content in pairs(_M["static_cache"]["bnss"]) do 
		_M.save_item_local("bns", bns, bns_content)
	end
	_M.save_ufc_meta_info()
	_M.save_need_info(true)
	_M.save_config_local("configs", _M["static_cache"]["configs"])
	_M.save_config_local("http_ips", _M["static_cache"]["http_ips"])
	_M.save_config_local("stream_ips", _M["static_cache"]["stream_ips"])

	--写共享内存
	local config_types = {"service", "bns", "config", "http_ip", "stream_ip"}
	for _, config_type in ipairs(config_types) do
		_M.save_one_type_config_shared_mem(config_type)
	end
	return conf["res"]["SUCCESS"]
end


function _M._basic_cache_save_to_local()
    local cache_fname = conf["meta_cache_dir"] .. "/conf_cache_file"
    local tmp_cache_fname = string.format("%s/conf_cache_file_%s_%s",
        conf["meta_cache_dir"],
        ngx.now(),
        ngx.worker.pid()
    )
    local basic_cache_encode, fd

    basic_cache_encode = cjson.encode(_M["static_cache_syncing"])
    if not basic_cache_encode or string.len(basic_cache_encode) < 10 then
        ngx.log(ngx.WARN, "static_cache_syncing json encode fail")
        return conf["res"]["FAIL"]
    end

    fd = io.open(tmp_cache_fname, "w+")
    if fd == nil then
        ngx.log(ngx.WARN, "open tmp cache file failed, save lastest ufc config cache fail")
        return conf["res"]["FAIL"]
    end

    local rs = fd:write(basic_cache_encode)
    fd:close()

    if not rs then
        os.remove(tmp_cache_fname)
        ngx.log(ngx.WARN, "write cache file failed")
        return conf["res"]["FAIL"]
    end

    os.rename(tmp_cache_fname, cache_fname)
    return conf["res"]["SUCCESS"]
end

function _M._init_static_cache()
    --ngx.log(ngx.DEBUG, string.format("static cache is %s", cjson.encode(_M["static_cache"])))
    for services, config in pairs(_M["static_cache"]["services"]) do
        if config["bns_list"] then
            _M._set_auto_bns_percent(config["bns_list"])
        end
        if config["small_flow"] then
            for _, small_flow_config in pairs(config["small_flow"]) do
                if small_flow_config["bns_list"] then
                    _M._set_auto_bns_percent(small_flow_config["bns_list"])
                end
            end
        end
    end
end

--这里不用初始化具体负载均衡index，这样会导致轮询策略刚启动的时候所有机器index 都是一样的
function _M._init_one_dynamic_cache(service_name)
	if _M["not_shared_dynamic_cache"] == nil then
		_M["not_shared_dynamic_cache"] = {}
	end

	local config = _M["static_cache"]["services"][service_name]
	if _M["not_shared_dynamic_cache"][service_name] == nil then
		_M["not_shared_dynamic_cache"][service_name] = {}
		local dy_config = _M["not_shared_dynamic_cache"][service_name]
		if conf["meta_table"] then
			dy_config["current_index"] = {}
		end
	end
	if _M["dynamic_cache"][service_name] == nil then
		_M["dynamic_cache"][service_name] = {}
		local dy_config = _M["dynamic_cache"][service_name]
		dy_config["backend"] = {}
		if not conf["meta_table"] then
			dy_config["current_index"] = {}
		end
	end

end

function _M._fix_static_cache_syncing()
    local tmp_cache
    if _M["static_cache"] ~= nil then
        tmp_cache = utils.deepcopy(_M["static_cache"])
        _M["static_cache_syncing"] = tmp_cache

    else
        _M["static_cache_syncing"] = {
            ["services"] = {},
            ["bnss"] = {},
            ["global_config"] = {}
        }
    end
end

function _M._clear_static_cache_syncing()
    _M["static_cache_syncing"] = {
        ["services"] = {},
        ["bnss"] = {},
        ["global_config"] = {}
    }
end

--从共享内存同步数据到内存中
function _M.load_ip_mapping_data()
	--http模块和stream模块共享内存名称不一样
	local shared_memory_name 
	local shared_ip_data 
	if conf["stream"] == 0 then
		shared_ip_data = ngx.shared.http_ip
		shared_memory_name = "http_ip"
	else
		shared_ip_data = ngx.shared.stream_ip
		shared_memory_name = "stream_ip"
	end

	local ip_data_str = shared_ip_data:get(shared_memory_name)
	local ip_data_json = {}
	local err = nil
	if ip_data_str == nil then
		return
	end

	ip_data_json, err = cjson.decode(ip_data_str)
	if err ~= nil or ip_data_json == nil then
		ngx.log(ngx.WARN, string.format("get ip mapping data from shared memory failed, error is %s", err))
		return
	end

	--将共享内存中的动态映射关系写到内存中去
	local http_ips_in_shared_memory = ip_data_json["http_ips"]
	local stream_ips_in_shared_memory = ip_data_json["stream_ips"]

	local static_cache = _M["static_cache"]
	if not static_cache then
		ngx.log(ngx.WARN, "ngx_worker_cache[static_cache] is nil")
		return
	end

	local http_ips = static_cache["http_ips"]
	if not http_ips then
		ngx.log(ngx.WARN, "ngx_worker_cache[static_cache][http_ips] is nil")
		return
	end

	local stream_ips = static_cache["stream_ips"]
	if not stream_ips then
		ngx.log(ngx.WARN, "ngx_worker_cache[static_cache][stream_ips] is nil")
		return
	end

	--直接将共享内存中的数据覆盖过去即可
	for k, v in pairs(http_ips_in_shared_memory) do
		http_ips[k] = v
	end

	for k, v in pairs(stream_ips_in_shared_memory) do
		stream_ips[k] = v
	end

	return
end

function _M.log_tracer()
	local configs = _M["static_cache"]["configs"]
	if not configs then
		return
	end

	if not configs["trace"] then
		return
	end

	if not configs["trace"]["cloud"] then
		return
	end

	local limit = configs["trace"]["cloud"]["upload_limit"] or 1000

	local data = _M["log_trace"]["cloud"]
	_M["log_trace"]["cloud"] = {}

	if #data < 1 then
		return
	end

	local i = 0
	local logs = {}
	for _, v in pairs(data) do
		table.insert(logs, v)
		i = i + 1
		if i >= limit then
			i = 0
			_M.trace_upload(logs)
			logs = {}
		end
	end

	if i > 0 then
		_M.trace_upload(logs)
		logs = {}
	end

	data = nil 
end

--[[
    @comment 数据采集上报 
    @param table logs
]]
function _M.trace_upload(logs)
	local body = cjson.encode(logs) 
	local config = _M["static_cache"]["configs"]["trace"]["cloud"]
	local service = config["ufc_service"]
	local uri = config["uri"]

	local ufc_headers = {["x-ufc-self-service-name"] = "pcs-ufc-agent"}

	local upload_headers = {
		["Content-Type"] = "text/plain",
		["Content-Length"] = string.len(body)
	}

	local logid = ngx.time()
	for i=1, 2 do
		local re, err = ufc.bypass(service, logid, ufc_headers, true)
		if not err and re and re.host and re.port then
			local addr = {domain = re.host, port = re.port}
			local _, err = http.put(addr, uri, upload_headers, body)
			if not err then
				break
			end

			ufc.callback_fail(re)
		end

		ngx.log(ngx.WARN, string.format("upload trace logs, ufc bypass failed, err:%s, response:%s", cjson.encode(err), cjson.encode(re)))
    end
end

--非法请求,非法地域校检
function _M.request_verification(from_service, to_service)
    local ip_list = _M["ip_prefix_area_list"]
    local area = _M["area"]
    
    if area == nil or #ip_list == 0 then
        local logid = ngx.var.ufc_logid or "-"
        ngx.log(ngx.WARN, string.format("[UFC_AREA_ERROR] logid %s from_service %s to_service %s area info has lost", logid, from_service, to_service))
        conf.exit("E_DEGRADE_5XX")
        return false
    end

	--只需要看remote ip, 机房判断在start脚本中看过了
    local remote_ip = ngx.var.remote_addr
    local flag = 0 
    --127开头的前缀
    local sub_string_local = string.sub(remote_ip, 1, 3)
	for _, prefix in pairs(ip_list) do 
		local prefix_len = #prefix
        local sub_string = string.sub(remote_ip, 1, prefix_len)
        if area == "inland" then
            --内地黑名单
            if sub_string == prefix then
                local logid = ngx.var.ufc_logid or "-"
                ngx.log(ngx.WARN, string.format("[UFC_AREA_ERROR] logid %s from_service %s to_service %s remote ip %s match black list", logid, from_service, to_service, remote_ip))
                conf.exit("E_DEGRADE_5XX")
                return false
            end
        else
            --海外白名单，或者请求从本机发来
            if sub_string == prefix or sub_string_local == "127" then
                flag = 1
                break
            end
        end
    end
    
    if area ~= "inland" and flag ~= 1 then
        local logid = ngx.var.ufc_logid or "-"
        ngx.log(ngx.WARN, string.format("[UFC_AREA_ERROR] logid %s from_service %s to_service %s remote ip %s not match while list", logid, from_service, to_service, remote_ip))
        conf.exit("E_DEGRADE_5XX")
        return false
    end

    return true
end

return _M
