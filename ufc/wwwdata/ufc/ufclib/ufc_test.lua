local ufc = require "ufc"

-- direct request
-- ret: {
-- ["code"] = 200,
-- ["body"] = "",
-- ["headers"] = {
--  ["x"] = "x",
--  }
-- }

local service_name = "pcs-inner"
local uri = "/rest/2.0/"
local headers = {}
local post_param = "x=x&y=y"
local option = {
    ["connect_timeout"] = 300,
    ["read_timeout"] = 5000,
}
local ret, err = ufc.request(service_name, uri, headers, post_param, option)



-- bypass mode

local service_name = "pcs-inner"
local logid = "12345678"
ret, err = ufc.bypass(service_name, logid)

-- ret
-- {
-- ["host"] = "",
-- ["port"] = 6000,
-- ["status"] = 200
-- }

-- after use this backend, please tell me the result
