local http = require("ufclib.Http") -- put Http.lua to your own lua path
local cjson = require("cjson.safe")

local ufc = {
    ["version"] = 1,
    ["doc"] = "ufc sdk for lua",

    ["service_name"] = "",
    ["ufc_addr"] = {
        ["domain"] = "127.0.0.1",
        ["port"] = 8240,
    },
    ["backup_ufc_addr"] = {
        ["domain"] = "ufc-backup.baidu-int.com",
        ["port"] = 80,
    },
    ["connect_timeout"] = 300,
    ["read_timeout"] = 30000,
}

--function ufc.request(service_name, uri, headers, ori_post_param, option)
--    if type(headers) ~= type({}) then
--        return nil, "request headers is empty"
--    end
--
--    if (service_name ~= nil) then
--        headers["x-ufc-service-name"] = service_name;
--    end
--
--    local ret = nil
--    local err = nil
--    local post_param = ""
--    if type(ori_post_param) == type({}) then
--        post_param = ngx.encode_args(ori_post_param)
--    else
--        post_param = ori_post_param
--    end
--
--    if post_param == nil then
--        ret, err = http.get(ufc.ufc_addr, uri, headers, option)
--        if ret == nil then
--            err = nil
--            ret, err = http.get(ufc.backup_ufc_addr, uri, headers, option)
--        end
--        return ret, err
--    end
--    if headers["Content-Type"] == nil then
--        headers["Content-Type"] = "application/x-www-form-urlencoded"
--    end
--    if headers["Content-Length"] == nil then
--        headers["Content-Length"] = #post_param
--    end
--
--    ret, err = http.post(ufc.ufc_addr, uri, headers, post_param, option)
--    if ret == nil then
--        err = nil
--        ret, err = http.post(ufc.backup_ufc_addr, uri, headers, post_param, option)
--    end
--    return ret, err
--end

function ufc.bypass(service_name, logid, headers, use_local)
    if service_name == nil then
        return nil, "service not exist"
    end

    local uri = string.format("/bypass?cmd=get_backend&x-ufc-service-name=%s", service_name)
    if headers == nil then
        headers = {}
    end
    if logid ~= nil then
        headers["http-x-isis-logid"] = logid
    end
    local retry = false
    
    local option = {
        ["connect_timeout"] = ufc.connect_timeout,
        ["read_timeout"] = ufc.read_timeout,
    }
    

    local ret, err
    if use_local then
        ret, err = http.get(ufc.ufc_addr, uri, headers, option)
    else
        ret, err = http.get(ufc.backup_ufc_addr, uri, headers, option)
    end

    if err ~= nil then
        return nil, err
    end

    local response = cjson.decode(ret["body"])
    if response ~= nil and type(response) == "table" then
        return response, nil
    end

    ngx.log(ngx.WARN, string.format("backup_ufc_addr %s uri %s, request ufc_backup error", ufc.backup_ufc_addr, uri))
    return nil, "system err"

--    if err == nil then
--        response = cjson.decode(ret["body"])
--        if response == nil or response["status"] == 312 then
--            retry = true
--        end
--    else
--        retry = true
--    end
--
--    if retry == true then
--        ret, err = http.get(ufc.backup_ufc_addr, uri, headers, option)
--        if err ~= nil then
--            return nil, err
--        end
--        response = cjson.decode(ret["body"])
--        if response ~= nil then
--            response["retry"] = true
--        end
--    end
--
--    if response ~= nil then
--        return response, nil
--    end
--    return nil, "system err"
end

function ufc._callback(response, http_code)
    if response == nil then
        return false, "param err"
    end

    if type(response) ~= type({}) then
        return false, "param err"
    end

    if response["retry"] == true then
        return true, nil
    end

    local option = {
        ["connect_timeout"] = ufc.connect_timeout,
        ["read_timeout"] = ufc.read_timeout,
    }
    local uri = string.format("/bypass?cmd=callback")
    if http_code ~= nil then
        uri = string.format("%s&http_code=%s", uri, http_code)
    end
    local data = string.format("data=%s", cjson.encode(response))
    local headers = {
        ["Content-Type"] = "application/x-www-form-urlencoded",
        ["Content-Length"] = #data,
    }
    
    local ret, err = http.post(ufc.ufc_addr, uri, headers, data, option)
    if err ~= nil then
        return nil, err
    end
    return true, nil
end

function ufc.callback_success(response)
    return ufc._callback(response, 200)
end

function ufc.callback_fail(response)
    return ufc._callback(response, 555)
end

function ufc._urlencode(str)
    if str ~= nil then
        str = string.gsub (str, "\n", "\r\n")
        str = string.gsub (str, "([^%w ])",
        function (c) return string.format ("%%%02X", string.byte(c)) end)
        str = string.gsub (str, " ", "+")
    end
    return str
end

return ufc
