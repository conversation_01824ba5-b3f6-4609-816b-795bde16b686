package metrics

import (
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/valyala/fasthttp"
	"github.com/valyala/fasthttp/fasthttpadaptor"
)

// Collector collects and exposes metrics
type Collector struct {
	requestsTotal     *prometheus.CounterVec
	requestDuration   *prometheus.HistogramVec
	activeConnections *prometheus.GaugeVec
	circuitBreakerState *prometheus.GaugeVec
	backendHealth     *prometheus.GaugeVec
	registry          *prometheus.Registry
}

// NewCollector creates a new metrics collector
func NewCollector() *Collector {
	registry := prometheus.NewRegistry()
	
	collector := &Collector{
		requestsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "servicemesh_requests_total",
				Help: "Total number of requests",
			},
			[]string{"service", "backend", "status_code"},
		),
		requestDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "servicemesh_request_duration_seconds",
				Help:    "Request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"service", "backend"},
		),
		activeConnections: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "servicemesh_active_connections",
				Help: "Number of active connections",
			},
			[]string{"service", "backend"},
		),
		circuitBreakerState: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "servicemesh_circuit_breaker_state",
				Help: "Circuit breaker state (0=closed, 1=half-open, 2=open)",
			},
			[]string{"service", "backend"},
		),
		backendHealth: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "servicemesh_backend_health",
				Help: "Backend health status (1=healthy, 0=unhealthy)",
			},
			[]string{"service", "backend"},
		),
		registry: registry,
	}
	
	// Register metrics
	registry.MustRegister(collector.requestsTotal)
	registry.MustRegister(collector.requestDuration)
	registry.MustRegister(collector.activeConnections)
	registry.MustRegister(collector.circuitBreakerState)
	registry.MustRegister(collector.backendHealth)
	
	return collector
}

// RecordRequest records a request metric
func (c *Collector) RecordRequest(service, backend string, statusCode int, duration time.Duration) {
	statusCodeStr := strconv.Itoa(statusCode)
	
	c.requestsTotal.WithLabelValues(service, backend, statusCodeStr).Inc()
	c.requestDuration.WithLabelValues(service, backend).Observe(duration.Seconds())
}

// SetActiveConnections sets the number of active connections
func (c *Collector) SetActiveConnections(service, backend string, count int) {
	c.activeConnections.WithLabelValues(service, backend).Set(float64(count))
}

// SetCircuitBreakerState sets the circuit breaker state
func (c *Collector) SetCircuitBreakerState(service, backend string, state int) {
	c.circuitBreakerState.WithLabelValues(service, backend).Set(float64(state))
}

// SetBackendHealth sets the backend health status
func (c *Collector) SetBackendHealth(service, backend string, healthy bool) {
	value := 0.0
	if healthy {
		value = 1.0
	}
	c.backendHealth.WithLabelValues(service, backend).Set(value)
}

// Handler returns the metrics HTTP handler
func (c *Collector) Handler(ctx *fasthttp.RequestCtx) {
	handler := promhttp.HandlerFor(c.registry, promhttp.HandlerOpts{})
	fasthttpadaptor.NewFastHTTPHandler(handler)(ctx)
}

// GetRegistry returns the prometheus registry
func (c *Collector) GetRegistry() *prometheus.Registry {
	return c.registry
}

// RequestMetrics holds request-level metrics
type RequestMetrics struct {
	Service    string
	Backend    string
	StatusCode int
	Duration   time.Duration
	StartTime  time.Time
}

// NewRequestMetrics creates new request metrics
func NewRequestMetrics(service, backend string) *RequestMetrics {
	return &RequestMetrics{
		Service:   service,
		Backend:   backend,
		StartTime: time.Now(),
	}
}

// Finish finishes the request metrics recording
func (rm *RequestMetrics) Finish(collector *Collector, statusCode int) {
	rm.StatusCode = statusCode
	rm.Duration = time.Since(rm.StartTime)
	
	if collector != nil {
		collector.RecordRequest(rm.Service, rm.Backend, rm.StatusCode, rm.Duration)
	}
}

// ServiceMetrics holds service-level metrics
type ServiceMetrics struct {
	collector   *Collector
	serviceName string
}

// NewServiceMetrics creates new service metrics
func NewServiceMetrics(collector *Collector, serviceName string) *ServiceMetrics {
	return &ServiceMetrics{
		collector:   collector,
		serviceName: serviceName,
	}
}

// RecordBackendRequest records a backend request
func (sm *ServiceMetrics) RecordBackendRequest(backend string, statusCode int, duration time.Duration) {
	if sm.collector != nil {
		sm.collector.RecordRequest(sm.serviceName, backend, statusCode, duration)
	}
}

// UpdateBackendHealth updates backend health status
func (sm *ServiceMetrics) UpdateBackendHealth(backend string, healthy bool) {
	if sm.collector != nil {
		sm.collector.SetBackendHealth(sm.serviceName, backend, healthy)
	}
}

// UpdateCircuitBreakerState updates circuit breaker state
func (sm *ServiceMetrics) UpdateCircuitBreakerState(backend string, state int) {
	if sm.collector != nil {
		sm.collector.SetCircuitBreakerState(sm.serviceName, backend, state)
	}
}

// UpdateActiveConnections updates active connections count
func (sm *ServiceMetrics) UpdateActiveConnections(backend string, count int) {
	if sm.collector != nil {
		sm.collector.SetActiveConnections(sm.serviceName, backend, count)
	}
}
