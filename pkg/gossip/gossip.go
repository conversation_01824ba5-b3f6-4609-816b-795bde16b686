package gossip

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/hashicorp/memberlist"
	"go.uber.org/zap"
)

// ConfigUpdate represents a configuration update
type ConfigUpdate struct {
	Type      string      `json:"type"`
	Name      string      `json:"name"`
	Data      interface{} `json:"data"`
	Version   int64       `json:"version"`
	Timestamp time.Time   `json:"timestamp"`
}

// EventDelegate handles memberlist events
type EventDelegate struct {
	manager *Manager
	logger  *zap.Logger
}

// NotifyJoin is called when a node joins the cluster
func (ed *EventDelegate) NotifyJoin(node *memberlist.Node) {
	ed.logger.Info("Node joined cluster",
		zap.String("node", node.Name),
		zap.String("addr", node.Addr.String()))
}

// NotifyLeave is called when a node leaves the cluster
func (ed *EventDelegate) NotifyLeave(node *memberlist.Node) {
	ed.logger.Info("Node left cluster",
		zap.String("node", node.Name),
		zap.String("addr", node.Addr.String()))
}

// NotifyUpdate is called when a node's metadata is updated
func (ed *EventDelegate) NotifyUpdate(node *memberlist.Node) {
	ed.logger.Debug("Node updated",
		zap.String("node", node.Name),
		zap.String("addr", node.Addr.String()))
}

// Delegate handles gossip messages
type Delegate struct {
	manager *Manager
	logger  *zap.Logger
}

// NodeMeta returns metadata for the local node
func (d *Delegate) NodeMeta(limit int) []byte {
	meta := map[string]interface{}{
		"version":   time.Now().Unix(),
		"role":      "servicemesh",
		"timestamp": time.Now().Unix(),
	}

	data, _ := json.Marshal(meta)
	if len(data) > limit {
		return data[:limit]
	}
	return data
}

// NotifyMsg is called when a message is received
func (d *Delegate) NotifyMsg(msg []byte) {
	var update ConfigUpdate
	if err := json.Unmarshal(msg, &update); err != nil {
		d.logger.Error("Failed to unmarshal gossip message", zap.Error(err))
		return
	}

	d.manager.handleConfigUpdate(&update)
}

// GetBroadcasts returns messages to broadcast
func (d *Delegate) GetBroadcasts(overhead, limit int) [][]byte {
	return d.manager.getBroadcasts(overhead, limit)
}

// LocalState returns the local state
func (d *Delegate) LocalState(join bool) []byte {
	state := d.manager.getLocalState()
	data, _ := json.Marshal(state)
	return data
}

// MergeRemoteState merges remote state
func (d *Delegate) MergeRemoteState(buf []byte, join bool) {
	var state map[string]interface{}
	if err := json.Unmarshal(buf, &state); err != nil {
		d.logger.Error("Failed to unmarshal remote state", zap.Error(err))
		return
	}

	d.manager.mergeRemoteState(state)
}

// Manager manages gossip protocol
type Manager struct {
	config      *config.GossipConfig
	logger      *zap.Logger
	memberlist  *memberlist.Memberlist
	broadcasts  [][]byte
	localState  map[string]interface{}
	mu          sync.RWMutex
	updateChan  chan *ConfigUpdate
	subscribers map[string][]chan *ConfigUpdate
}

// NewManager creates a new gossip manager
func NewManager(cfg *config.GossipConfig, logger *zap.Logger) *Manager {
	return &Manager{
		config:      cfg,
		logger:      logger,
		broadcasts:  make([][]byte, 0),
		localState:  make(map[string]interface{}),
		updateChan:  make(chan *ConfigUpdate, 100),
		subscribers: make(map[string][]chan *ConfigUpdate),
	}
}

// Start starts the gossip manager
func (m *Manager) Start(ctx context.Context) error {
	if !m.config.Enable {
		m.logger.Info("Gossip protocol disabled")
		return nil
	}

	// Configure memberlist
	mlConfig := memberlist.DefaultLocalConfig()
	mlConfig.Name = fmt.Sprintf("servicemesh-%d", time.Now().Unix())
	mlConfig.BindAddr = m.config.BindAddr
	mlConfig.BindPort = m.config.BindPort
	mlConfig.Events = &EventDelegate{manager: m, logger: m.logger}
	mlConfig.Delegate = &Delegate{manager: m, logger: m.logger}
	// mlConfig.Logger = &memberlistLogger{m.logger}

	// Create memberlist
	ml, err := memberlist.Create(mlConfig)
	if err != nil {
		return fmt.Errorf("failed to create memberlist: %w", err)
	}

	m.memberlist = ml

	// Join existing cluster if seeds are provided
	if len(m.config.Seeds) > 0 {
		_, err := ml.Join(m.config.Seeds)
		if err != nil {
			m.logger.Warn("Failed to join cluster", zap.Error(err))
		} else {
			m.logger.Info("Joined cluster", zap.Strings("seeds", m.config.Seeds))
		}
	}

	// Start background goroutines
	go m.processUpdates(ctx)
	go m.syncPeriodically(ctx)

	m.logger.Info("Gossip manager started",
		zap.String("bind_addr", m.config.BindAddr),
		zap.Int("bind_port", m.config.BindPort))

	return nil
}

// Stop stops the gossip manager
func (m *Manager) Stop() error {
	if m.memberlist != nil {
		return m.memberlist.Shutdown()
	}
	return nil
}

// BroadcastUpdate broadcasts a configuration update
func (m *Manager) BroadcastUpdate(updateType, name string, data interface{}) error {
	update := &ConfigUpdate{
		Type:      updateType,
		Name:      name,
		Data:      data,
		Version:   time.Now().Unix(),
		Timestamp: time.Now(),
	}

	msg, err := json.Marshal(update)
	if err != nil {
		return fmt.Errorf("failed to marshal update: %w", err)
	}

	m.mu.Lock()
	m.broadcasts = append(m.broadcasts, msg)
	m.mu.Unlock()

	m.logger.Debug("Broadcasting config update",
		zap.String("type", updateType),
		zap.String("name", name))

	return nil
}

// Subscribe subscribes to configuration updates
func (m *Manager) Subscribe(updateType string) <-chan *ConfigUpdate {
	ch := make(chan *ConfigUpdate, 10)

	m.mu.Lock()
	if m.subscribers[updateType] == nil {
		m.subscribers[updateType] = make([]chan *ConfigUpdate, 0)
	}
	m.subscribers[updateType] = append(m.subscribers[updateType], ch)
	m.mu.Unlock()

	return ch
}

// GetMembers returns cluster members
func (m *Manager) GetMembers() []*memberlist.Node {
	if m.memberlist == nil {
		return nil
	}
	return m.memberlist.Members()
}

// handleConfigUpdate handles received configuration updates
func (m *Manager) handleConfigUpdate(update *ConfigUpdate) {
	m.logger.Debug("Received config update",
		zap.String("type", update.Type),
		zap.String("name", update.Name),
		zap.Int64("version", update.Version))

	// Send to update channel
	select {
	case m.updateChan <- update:
	default:
		m.logger.Warn("Update channel full, dropping update")
	}

	// Send to subscribers
	m.mu.RLock()
	subscribers := m.subscribers[update.Type]
	m.mu.RUnlock()

	for _, ch := range subscribers {
		select {
		case ch <- update:
		default:
			m.logger.Warn("Subscriber channel full, dropping update")
		}
	}
}

// getBroadcasts returns messages to broadcast
func (m *Manager) getBroadcasts(overhead, limit int) [][]byte {
	m.mu.Lock()
	defer m.mu.Unlock()

	if len(m.broadcasts) == 0 {
		return nil
	}

	// Return and clear broadcasts
	broadcasts := m.broadcasts
	m.broadcasts = make([][]byte, 0)

	return broadcasts
}

// getLocalState returns the local state
func (m *Manager) getLocalState() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	state := make(map[string]interface{})
	for k, v := range m.localState {
		state[k] = v
	}

	return state
}

// mergeRemoteState merges remote state
func (m *Manager) mergeRemoteState(state map[string]interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()

	for k, v := range state {
		m.localState[k] = v
	}
}

// processUpdates processes configuration updates
func (m *Manager) processUpdates(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case update := <-m.updateChan:
			m.logger.Debug("Processing config update",
				zap.String("type", update.Type),
				zap.String("name", update.Name))
			// Process the update (this would integrate with config manager)
		}
	}
}

// syncPeriodically performs periodic synchronization
func (m *Manager) syncPeriodically(ctx context.Context) {
	ticker := time.NewTicker(m.config.SyncInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			m.performSync()
		}
	}
}

// performSync performs synchronization with cluster
func (m *Manager) performSync() {
	if m.memberlist == nil {
		return
	}

	members := m.memberlist.Members()
	m.logger.Debug("Cluster sync", zap.Int("members", len(members)))

	// Perform any necessary synchronization logic here
}

// memberlistLogger adapts zap.Logger to memberlist.Logger interface
type memberlistLogger struct {
	logger *zap.Logger
}

func (l *memberlistLogger) Write(p []byte) (n int, err error) {
	l.logger.Debug(string(p))
	return len(p), nil
}
