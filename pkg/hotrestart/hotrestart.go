package hotrestart

import (
	"context"
	"fmt"
	"net"
	"os"
	"os/exec"
	"os/signal"
	"strconv"
	"sync"
	"syscall"
	"time"

	"go.uber.org/zap"
)

const (
	// Environment variables for hot restart
	EnvHotRestart     = "HOT_RESTART"
	EnvListenerFDs    = "LISTENER_FDS"
	EnvGracefulStop   = "GRACEFUL_STOP"
	
	// Signals
	SigReload = syscall.SIGUSR1
	SigStop   = syscall.SIGTERM
)

// Manager manages hot restart functionality
type Manager struct {
	logger        *zap.Logger
	listeners     []net.Listener
	servers       []Server
	pidFile       string
	gracefulStop  time.Duration
	mu            sync.RWMutex
	isChild       bool
	parentPID     int
}

// Server interface for servers that support graceful shutdown
type Server interface {
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	GetListener() net.Listener
}

// NewManager creates a new hot restart manager
func NewManager(logger *zap.Logger, pidFile string, gracefulStop time.Duration) *Manager {
	return &Manager{
		logger:       logger,
		listeners:    make([]net.Listener, 0),
		servers:      make([]Server, 0),
		pidFile:      pidFile,
		gracefulStop: gracefulStop,
		isChild:      os.Getenv(EnvHotRestart) == "1",
	}
}

// RegisterServer registers a server for hot restart
func (m *Manager) RegisterServer(server Server) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.servers = append(m.servers, server)
	if listener := server.GetListener(); listener != nil {
		m.listeners = append(m.listeners, listener)
	}
}

// Start starts the hot restart manager
func (m *Manager) Start(ctx context.Context) error {
	// Write PID file
	if err := m.writePIDFile(); err != nil {
		return fmt.Errorf("failed to write PID file: %w", err)
	}

	// Setup signal handlers
	m.setupSignalHandlers(ctx)

	// If this is a child process, inherit listeners
	if m.isChild {
		if err := m.inheritListeners(); err != nil {
			return fmt.Errorf("failed to inherit listeners: %w", err)
		}
		
		// Notify parent that child is ready
		if err := m.notifyParentReady(); err != nil {
			m.logger.Warn("Failed to notify parent process", zap.Error(err))
		}
	}

	m.logger.Info("Hot restart manager started",
		zap.Bool("is_child", m.isChild),
		zap.Int("parent_pid", m.parentPID))

	return nil
}

// Stop stops the hot restart manager
func (m *Manager) Stop() error {
	// Remove PID file
	if err := os.Remove(m.pidFile); err != nil && !os.IsNotExist(err) {
		m.logger.Warn("Failed to remove PID file", zap.Error(err))
	}

	return nil
}

// Reload triggers a hot restart
func (m *Manager) Reload() error {
	m.logger.Info("Starting hot restart...")

	// Get current executable path
	executable, err := os.Executable()
	if err != nil {
		return fmt.Errorf("failed to get executable path: %w", err)
	}

	// Prepare environment for child process
	env := os.Environ()
	env = append(env, fmt.Sprintf("%s=1", EnvHotRestart))
	env = append(env, fmt.Sprintf("%s=%s", EnvListenerFDs, m.getListenerFDs()))

	// Prepare file descriptors for inheritance
	files := make([]*os.File, 0, len(m.listeners))
	for _, listener := range m.listeners {
		if tcpListener, ok := listener.(*net.TCPListener); ok {
			file, err := tcpListener.File()
			if err != nil {
				return fmt.Errorf("failed to get listener file: %w", err)
			}
			files = append(files, file)
		}
	}

	// Start child process
	cmd := exec.Command(executable, os.Args[1:]...)
	cmd.Env = env
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.ExtraFiles = files

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start child process: %w", err)
	}

	m.logger.Info("Child process started", zap.Int("pid", cmd.Process.Pid))

	// Wait for child to be ready (with timeout)
	readyCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := m.waitForChildReady(readyCtx, cmd.Process.Pid); err != nil {
		m.logger.Error("Child process failed to start", zap.Error(err))
		cmd.Process.Kill()
		return err
	}

	m.logger.Info("Hot restart completed successfully")
	return nil
}

// setupSignalHandlers sets up signal handlers for hot restart
func (m *Manager) setupSignalHandlers(ctx context.Context) {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, SigReload, SigStop, syscall.SIGINT)

	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case sig := <-sigChan:
				switch sig {
				case SigReload:
					m.logger.Info("Received reload signal")
					if err := m.Reload(); err != nil {
						m.logger.Error("Hot restart failed", zap.Error(err))
					} else {
						// Graceful shutdown after successful reload
						m.gracefulShutdown()
					}
				case SigStop, syscall.SIGINT:
					m.logger.Info("Received stop signal")
					m.gracefulShutdown()
				}
			}
		}
	}()
}

// inheritListeners inherits listeners from parent process
func (m *Manager) inheritListeners() error {
	fdStr := os.Getenv(EnvListenerFDs)
	if fdStr == "" {
		return nil
	}

	fds := parseListenerFDs(fdStr)
	m.logger.Info("Inheriting listeners", zap.Ints("fds", fds))

	for i, fd := range fds {
		file := os.NewFile(uintptr(fd), fmt.Sprintf("listener_%d", i))
		listener, err := net.FileListener(file)
		if err != nil {
			return fmt.Errorf("failed to create listener from fd %d: %w", fd, err)
		}
		
		m.listeners = append(m.listeners, listener)
		file.Close() // Close the file descriptor, listener keeps the socket
	}

	return nil
}

// notifyParentReady notifies parent process that child is ready
func (m *Manager) notifyParentReady() error {
	parentPIDStr := os.Getenv("PARENT_PID")
	if parentPIDStr == "" {
		return nil
	}

	parentPID, err := strconv.Atoi(parentPIDStr)
	if err != nil {
		return err
	}

	m.parentPID = parentPID

	// Send signal to parent that child is ready
	process, err := os.FindProcess(parentPID)
	if err != nil {
		return err
	}

	return process.Signal(syscall.SIGUSR2)
}

// waitForChildReady waits for child process to be ready
func (m *Manager) waitForChildReady(ctx context.Context, childPID int) error {
	readyChan := make(chan os.Signal, 1)
	signal.Notify(readyChan, syscall.SIGUSR2)
	defer signal.Stop(readyChan)

	select {
	case <-ctx.Done():
		return fmt.Errorf("timeout waiting for child process to be ready")
	case <-readyChan:
		m.logger.Info("Child process is ready")
		return nil
	}
}

// gracefulShutdown performs graceful shutdown
func (m *Manager) gracefulShutdown() {
	m.logger.Info("Starting graceful shutdown")

	ctx, cancel := context.WithTimeout(context.Background(), m.gracefulStop)
	defer cancel()

	// Stop all servers gracefully
	var wg sync.WaitGroup
	for _, server := range m.servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			if err := s.Stop(ctx); err != nil {
				m.logger.Error("Error stopping server", zap.Error(err))
			}
		}(server)
	}

	// Wait for all servers to stop or timeout
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		m.logger.Info("Graceful shutdown completed")
	case <-ctx.Done():
		m.logger.Warn("Graceful shutdown timeout, forcing exit")
	}

	os.Exit(0)
}

// writePIDFile writes the current process PID to file
func (m *Manager) writePIDFile() error {
	pid := os.Getpid()
	return os.WriteFile(m.pidFile, []byte(strconv.Itoa(pid)), 0644)
}

// getListenerFDs returns file descriptors of listeners as string
func (m *Manager) getListenerFDs() string {
	fds := make([]string, 0, len(m.listeners))
	for i := range m.listeners {
		// File descriptors start from 3 (after stdin, stdout, stderr)
		fds = append(fds, strconv.Itoa(3+i))
	}
	return fmt.Sprintf("%v", fds)
}

// parseListenerFDs parses listener file descriptors from string
func parseListenerFDs(fdStr string) []int {
	// Simple parsing - in production you might want more robust parsing
	var fds []int
	// This is a simplified implementation
	// You would parse the actual format from getListenerFDs
	return fds
}

// IsChild returns true if this is a child process from hot restart
func (m *Manager) IsChild() bool {
	return m.isChild
}

// GetInheritedListener returns inherited listener by index
func (m *Manager) GetInheritedListener(index int) net.Listener {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if index < len(m.listeners) {
		return m.listeners[index]
	}
	return nil
}
