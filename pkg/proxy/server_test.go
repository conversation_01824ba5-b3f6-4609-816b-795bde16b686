package proxy

import (
	"context"
	"testing"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/chungzy/servicemesh-go/pkg/loadbalancer"
	"github.com/chungzy/servicemesh-go/pkg/logger"
	"github.com/chungzy/servicemesh-go/pkg/metrics"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/valyala/fasthttp"
)

func TestNewServer(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host:               "localhost",
			Port:               8080,
			ReadTimeout:        30 * time.Second,
			WriteTimeout:       30 * time.Second,
			IdleTimeout:        120 * time.Second,
			MaxRequestBodySize: 4 * 1024 * 1024,
			Concurrency:        256 * 1024,
		},
		Metrics: config.MetricsConfig{
			Enable: true,
			Path:   "/metrics",
		},
	}

	log, err := logger.NewDevelopmentLogger()
	require.NoError(t, err)

	lb := loadbalancer.NewLoadBalancer(cfg, log)
	metricsCollector := metrics.NewCollector()

	server := NewServer(cfg, log, lb, metricsCollector)
	assert.NotNil(t, server)
	assert.Equal(t, cfg, server.config)
	assert.Equal(t, log, server.logger)
	assert.Equal(t, lb, server.loadBalancer)
	assert.Equal(t, metricsCollector, server.metrics)
}

func TestHealthHandler(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host: "localhost",
			Port: 8080,
		},
		Metrics: config.MetricsConfig{
			Enable: true,
			Path:   "/metrics",
		},
	}

	log, err := logger.NewDevelopmentLogger()
	require.NoError(t, err)

	lb := loadbalancer.NewLoadBalancer(cfg, log)
	metricsCollector := metrics.NewCollector()

	server := NewServer(cfg, log, lb, metricsCollector)

	ctx := &fasthttp.RequestCtx{}
	server.healthHandler(ctx)

	assert.Equal(t, fasthttp.StatusOK, ctx.Response.StatusCode())
	assert.Equal(t, "application/json", string(ctx.Response.Header.ContentType()))
	assert.Contains(t, string(ctx.Response.Body()), "healthy")
}

func TestExtractServiceName(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host: "localhost",
			Port: 8080,
		},
	}

	log, err := logger.NewDevelopmentLogger()
	require.NoError(t, err)

	lb := loadbalancer.NewLoadBalancer(cfg, log)
	server := NewServer(cfg, log, lb, nil)

	tests := []struct {
		name           string
		setupRequest   func(*fasthttp.RequestCtx)
		expectedService string
	}{
		{
			name: "from header",
			setupRequest: func(ctx *fasthttp.RequestCtx) {
				ctx.Request.Header.Set("X-Service-Name", "user-service")
			},
			expectedService: "user-service",
		},
		{
			name: "from host",
			setupRequest: func(ctx *fasthttp.RequestCtx) {
				ctx.Request.Header.Set("Host", "user-service.example.com")
			},
			expectedService: "user-service",
		},
		{
			name: "from path",
			setupRequest: func(ctx *fasthttp.RequestCtx) {
				ctx.Request.SetRequestURI("/user-service/api/users")
			},
			expectedService: "user-service",
		},
		{
			name: "no service name",
			setupRequest: func(ctx *fasthttp.RequestCtx) {
				// No service indicators
			},
			expectedService: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := &fasthttp.RequestCtx{}
			tt.setupRequest(ctx)
			
			serviceName := server.extractServiceName(ctx)
			assert.Equal(t, tt.expectedService, serviceName)
		})
	}
}

func TestServerStartStop(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host:               "localhost",
			Port:               0, // Use random port for testing
			ReadTimeout:        1 * time.Second,
			WriteTimeout:       1 * time.Second,
			IdleTimeout:        1 * time.Second,
			MaxRequestBodySize: 1024,
			Concurrency:        10,
		},
	}

	log, err := logger.NewDevelopmentLogger()
	require.NoError(t, err)

	lb := loadbalancer.NewLoadBalancer(cfg, log)
	server := NewServer(cfg, log, lb, nil)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Start server in goroutine
	errChan := make(chan error, 1)
	go func() {
		errChan <- server.Start(ctx)
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Stop server
	stopCtx, stopCancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer stopCancel()

	err = server.Stop(stopCtx)
	assert.NoError(t, err)

	// Check that Start() returns without error
	select {
	case err := <-errChan:
		assert.NoError(t, err)
	case <-time.After(3 * time.Second):
		t.Fatal("Server did not stop within timeout")
	}
}
