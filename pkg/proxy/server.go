package proxy

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/chungzy/servicemesh-go/pkg/hotrestart"
	"github.com/chungzy/servicemesh-go/pkg/loadbalancer"
	"github.com/chungzy/servicemesh-go/pkg/metrics"
	"github.com/fasthttp/router"
	"github.com/valyala/fasthttp"
	"go.uber.org/zap"
)

// Server represents the proxy server
type Server struct {
	config       *config.Config
	logger       *zap.Logger
	server       *fasthttp.Server
	loadBalancer *loadbalancer.LoadBalancer
	metrics      *metrics.Collector
	router       *router.Router
	listener     net.Listener
	hotRestart   *hotrestart.Manager
}

// NewServer creates a new proxy server
func NewServer(cfg *config.Config, logger *zap.Logger, lb *loadbalancer.LoadBalancer, metrics *metrics.Collector) *Server {
	s := &Server{
		config:       cfg,
		logger:       logger,
		loadBalancer: lb,
		metrics:      metrics,
		router:       router.New(),
	}

	s.setupRoutes()
	s.setupServer()

	return s
}

// setupRoutes configures the routing
func (s *Server) setupRoutes() {
	// Health check endpoint
	s.router.GET("/health", s.healthHandler)

	// Metrics endpoint (if enabled)
	if s.config.Metrics.Enable {
		s.router.GET(s.config.Metrics.Path, s.metricsHandler)
	}

	// Catch-all proxy handler
	s.router.NotFound = s.proxyHandler
}

// setupServer configures the fasthttp server
func (s *Server) setupServer() {
	s.server = &fasthttp.Server{
		Handler:            s.router.Handler,
		ReadTimeout:        s.config.Server.ReadTimeout,
		WriteTimeout:       s.config.Server.WriteTimeout,
		IdleTimeout:        s.config.Server.IdleTimeout,
		MaxRequestBodySize: s.config.Server.MaxRequestBodySize,
		Concurrency:        s.config.Server.Concurrency,
		Logger:             &zapLogger{s.logger},
	}
}

// Start starts the proxy server
func (s *Server) Start(ctx context.Context) error {
	addr := fmt.Sprintf("%s:%d", s.config.Server.Host, s.config.Server.Port)

	s.logger.Info("Starting proxy server", zap.String("addr", addr))

	// Check if we have an inherited listener from hot restart
	var ln net.Listener
	var err error

	if s.hotRestart != nil && s.hotRestart.IsChild() {
		ln = s.hotRestart.GetInheritedListener(0)
		if ln != nil {
			s.logger.Info("Using inherited listener from hot restart")
		}
	}

	if ln == nil {
		ln, err = net.Listen("tcp", addr)
		if err != nil {
			return fmt.Errorf("failed to listen on %s: %w", addr, err)
		}
	}

	s.listener = ln

	go func() {
		<-ctx.Done()
		s.logger.Info("Shutting down proxy server")
		ln.Close()
	}()

	if err := s.server.Serve(ln); err != nil {
		if !strings.Contains(err.Error(), "use of closed network connection") {
			return fmt.Errorf("server error: %w", err)
		}
	}

	return nil
}

// Stop stops the proxy server
func (s *Server) Stop(ctx context.Context) error {
	return s.server.ShutdownWithContext(ctx)
}

// GetListener returns the server listener (for hot restart)
func (s *Server) GetListener() net.Listener {
	return s.listener
}

// SetHotRestartManager sets the hot restart manager
func (s *Server) SetHotRestartManager(manager *hotrestart.Manager) {
	s.hotRestart = manager
}

// healthHandler handles health check requests
func (s *Server) healthHandler(ctx *fasthttp.RequestCtx) {
	ctx.SetStatusCode(fasthttp.StatusOK)
	ctx.SetContentType("application/json")
	ctx.WriteString(`{"status":"healthy"}`)
}

// metricsHandler handles metrics requests
func (s *Server) metricsHandler(ctx *fasthttp.RequestCtx) {
	if s.metrics != nil {
		s.metrics.Handler(ctx)
	} else {
		ctx.SetStatusCode(fasthttp.StatusNotFound)
	}
}

// proxyHandler handles proxy requests
func (s *Server) proxyHandler(ctx *fasthttp.RequestCtx) {
	startTime := time.Now()

	// Extract service information from request
	serviceName := s.extractServiceName(ctx)
	if serviceName == "" {
		s.handleError(ctx, fasthttp.StatusBadRequest, "Service name not found")
		return
	}

	// Get backend from load balancer
	backend, err := s.loadBalancer.GetBackend(serviceName, ctx)
	if err != nil {
		s.logger.Error("Failed to get backend",
			zap.String("service", serviceName),
			zap.Error(err))
		s.handleError(ctx, fasthttp.StatusServiceUnavailable, "No available backend")
		return
	}

	// Proxy the request
	if err := s.proxyRequest(ctx, backend); err != nil {
		s.logger.Error("Failed to proxy request",
			zap.String("service", serviceName),
			zap.String("backend", backend.Address()),
			zap.Error(err))
		s.handleError(ctx, fasthttp.StatusBadGateway, "Proxy error")
		return
	}

	// Record metrics
	duration := time.Since(startTime)
	s.recordMetrics(serviceName, backend.Address(), ctx.Response.StatusCode(), duration)
}

// extractServiceName extracts service name from request
func (s *Server) extractServiceName(ctx *fasthttp.RequestCtx) string {
	// Try to get service name from header
	if serviceName := string(ctx.Request.Header.Peek("X-Service-Name")); serviceName != "" {
		return serviceName
	}

	// Try to get service name from host header
	host := string(ctx.Request.Header.Peek("Host"))
	if host != "" {
		// Extract service name from host (e.g., service.example.com -> service)
		parts := strings.Split(host, ".")
		if len(parts) > 0 {
			return parts[0]
		}
	}

	// Try to get service name from path
	path := string(ctx.Path())
	if strings.HasPrefix(path, "/") {
		parts := strings.Split(path[1:], "/")
		if len(parts) > 0 && parts[0] != "" {
			return parts[0]
		}
	}

	return ""
}

// proxyRequest proxies the request to the backend
func (s *Server) proxyRequest(ctx *fasthttp.RequestCtx, backend *loadbalancer.Backend) error {
	// Prepare the request
	req := &ctx.Request
	resp := &ctx.Response

	// Set the target URL
	req.SetRequestURI(fmt.Sprintf("http://%s%s", backend.Address(), req.RequestURI()))

	// Add proxy headers
	req.Header.Set("X-Forwarded-For", ctx.RemoteIP().String())
	req.Header.Set("X-Forwarded-Proto", "http")

	// Perform the request
	client := &fasthttp.Client{
		ReadTimeout:  s.config.LoadBalance.RetryTimeout,
		WriteTimeout: s.config.LoadBalance.RetryTimeout,
	}

	return client.Do(req, resp)
}

// handleError handles error responses
func (s *Server) handleError(ctx *fasthttp.RequestCtx, statusCode int, message string) {
	ctx.SetStatusCode(statusCode)
	ctx.SetContentType("application/json")
	ctx.WriteString(fmt.Sprintf(`{"error":"%s"}`, message))
}

// recordMetrics records request metrics
func (s *Server) recordMetrics(service, backend string, statusCode int, duration time.Duration) {
	if s.metrics != nil {
		s.metrics.RecordRequest(service, backend, statusCode, duration)
	}
}

// zapLogger adapts zap.Logger to fasthttp.Logger interface
type zapLogger struct {
	logger *zap.Logger
}

func (l *zapLogger) Printf(format string, args ...interface{}) {
	l.logger.Info(fmt.Sprintf(format, args...))
}
