package loadbalancer

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/stathat/consistent"
	"github.com/valyala/fasthttp"
	"go.uber.org/zap"
)

// Strategy represents load balancing strategy
type Strategy int

const (
	RoundRobin Strategy = iota
	Random
	ConsistentHash
	LeastConnections
)

// Backend represents a backend server
type Backend struct {
	host        string
	port        int
	weight      int
	connections int32
	healthy     bool
	lastCheck   time.Time
	mu          sync.RWMutex
}

// NewBackend creates a new backend
func NewBackend(host string, port, weight int) *Backend {
	return &Backend{
		host:      host,
		port:      port,
		weight:    weight,
		healthy:   true,
		lastCheck: time.Now(),
	}
}

// Address returns the backend address
func (b *Backend) Address() string {
	return fmt.Sprintf("%s:%d", b.host, b.port)
}

// IsHealthy returns whether the backend is healthy
func (b *Backend) IsHealthy() bool {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.healthy
}

// SetHealthy sets the backend health status
func (b *Backend) SetHealthy(healthy bool) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.healthy = healthy
	b.lastCheck = time.Now()
}

// Service represents a service with its backends
type Service struct {
	name     string
	backends []*Backend
	strategy Strategy
	index    int
	hash     *consistent.Consistent
	mu       sync.RWMutex
}

// NewService creates a new service
func NewService(name string, strategy Strategy) *Service {
	s := &Service{
		name:     name,
		backends: make([]*Backend, 0),
		strategy: strategy,
		hash:     consistent.New(),
	}

	if strategy == ConsistentHash {
		s.hash.NumberOfReplicas = 160
	}

	return s
}

// AddBackend adds a backend to the service
func (s *Service) AddBackend(backend *Backend) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.backends = append(s.backends, backend)

	if s.strategy == ConsistentHash {
		s.hash.Add(backend.Address())
	}
}

// RemoveBackend removes a backend from the service
func (s *Service) RemoveBackend(address string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	for i, backend := range s.backends {
		if backend.Address() == address {
			s.backends = append(s.backends[:i], s.backends[i+1:]...)
			break
		}
	}

	if s.strategy == ConsistentHash {
		s.hash.Remove(address)
	}
}

// GetBackend returns a backend based on the load balancing strategy
func (s *Service) GetBackend(ctx *fasthttp.RequestCtx) (*Backend, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if len(s.backends) == 0 {
		return nil, fmt.Errorf("no backends available for service %s", s.name)
	}

	// Filter healthy backends
	healthyBackends := make([]*Backend, 0)
	for _, backend := range s.backends {
		if backend.IsHealthy() {
			healthyBackends = append(healthyBackends, backend)
		}
	}

	if len(healthyBackends) == 0 {
		return nil, fmt.Errorf("no healthy backends available for service %s", s.name)
	}

	switch s.strategy {
	case RoundRobin:
		return s.roundRobinSelect(healthyBackends), nil
	case Random:
		return s.randomSelect(healthyBackends), nil
	case ConsistentHash:
		return s.consistentHashSelect(healthyBackends, ctx)
	case LeastConnections:
		return s.leastConnectionsSelect(healthyBackends), nil
	default:
		return s.roundRobinSelect(healthyBackends), nil
	}
}

// roundRobinSelect implements round-robin selection
func (s *Service) roundRobinSelect(backends []*Backend) *Backend {
	backend := backends[s.index%len(backends)]
	s.index++
	return backend
}

// randomSelect implements random selection
func (s *Service) randomSelect(backends []*Backend) *Backend {
	return backends[rand.Intn(len(backends))]
}

// consistentHashSelect implements consistent hash selection
func (s *Service) consistentHashSelect(backends []*Backend, ctx *fasthttp.RequestCtx) (*Backend, error) {
	// Use hash key from header or remote IP
	var hashKey string
	if key := string(ctx.Request.Header.Peek("Hash-Key")); key != "" {
		hashKey = key
	} else {
		hashKey = ctx.RemoteIP().String()
	}

	address, err := s.hash.Get(hashKey)
	if err != nil {
		return nil, err
	}

	// Find the backend with the selected address
	for _, backend := range backends {
		if backend.Address() == address {
			return backend, nil
		}
	}

	// Fallback to round-robin if not found
	return s.roundRobinSelect(backends), nil
}

// leastConnectionsSelect implements least connections selection
func (s *Service) leastConnectionsSelect(backends []*Backend) *Backend {
	var selected *Backend
	minConnections := int32(^uint32(0) >> 1) // Max int32

	for _, backend := range backends {
		backend.mu.RLock()
		connections := backend.connections
		backend.mu.RUnlock()

		if connections < minConnections {
			minConnections = connections
			selected = backend
		}
	}

	return selected
}

// LoadBalancer manages services and their backends
type LoadBalancer struct {
	config   *config.Config
	logger   *zap.Logger
	services map[string]*Service
	mu       sync.RWMutex
}

// NewLoadBalancer creates a new load balancer
func NewLoadBalancer(cfg *config.Config, logger *zap.Logger) *LoadBalancer {
	return &LoadBalancer{
		config:   cfg,
		logger:   logger,
		services: make(map[string]*Service),
	}
}

// GetBackend returns a backend for the given service
func (lb *LoadBalancer) GetBackend(serviceName string, ctx *fasthttp.RequestCtx) (*Backend, error) {
	lb.mu.RLock()
	service, exists := lb.services[serviceName]
	lb.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("service %s not found", serviceName)
	}

	return service.GetBackend(ctx)
}

// AddService adds a service to the load balancer
func (lb *LoadBalancer) AddService(serviceName string, strategy Strategy) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	lb.services[serviceName] = NewService(serviceName, strategy)
	lb.logger.Info("Service added", zap.String("service", serviceName))
}

// RemoveService removes a service from the load balancer
func (lb *LoadBalancer) RemoveService(serviceName string) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	delete(lb.services, serviceName)
	lb.logger.Info("Service removed", zap.String("service", serviceName))
}

// AddBackend adds a backend to a service
func (lb *LoadBalancer) AddBackend(serviceName, host string, port, weight int) error {
	lb.mu.RLock()
	service, exists := lb.services[serviceName]
	lb.mu.RUnlock()

	if !exists {
		return fmt.Errorf("service %s not found", serviceName)
	}

	backend := NewBackend(host, port, weight)
	service.AddBackend(backend)

	lb.logger.Info("Backend added",
		zap.String("service", serviceName),
		zap.String("backend", backend.Address()))

	return nil
}

// RemoveBackend removes a backend from a service
func (lb *LoadBalancer) RemoveBackend(serviceName, address string) error {
	lb.mu.RLock()
	service, exists := lb.services[serviceName]
	lb.mu.RUnlock()

	if !exists {
		return fmt.Errorf("service %s not found", serviceName)
	}

	service.RemoveBackend(address)

	lb.logger.Info("Backend removed",
		zap.String("service", serviceName),
		zap.String("backend", address))

	return nil
}

// UpdateServiceConfig updates service configuration
func (lb *LoadBalancer) UpdateServiceConfig(serviceConfig *config.ServiceConfig) {
	strategy := lb.getStrategy(serviceConfig.Strategy)

	lb.mu.Lock()
	service, exists := lb.services[serviceConfig.Name]
	if !exists {
		service = NewService(serviceConfig.Name, strategy)
		lb.services[serviceConfig.Name] = service
	}
	lb.mu.Unlock()

	// Update backends based on endpoints
	for _, endpoint := range serviceConfig.Endpoints {
		err := lb.AddBackend(serviceConfig.Name, endpoint.Host, endpoint.Port, endpoint.Weight)
		if err != nil {
			lb.logger.Error("Failed to add backend",
				zap.String("service", serviceConfig.Name),
				zap.String("endpoint", fmt.Sprintf("%s:%d", endpoint.Host, endpoint.Port)),
				zap.Error(err))
		}
	}

	lb.logger.Info("Service config updated", zap.String("service", serviceConfig.Name))
}

// getStrategy converts config strategy to Strategy enum
func (lb *LoadBalancer) getStrategy(strategy string) Strategy {
	switch strategy {
	case "round_robin":
		return RoundRobin
	case "random":
		return Random
	case "consistent_hash":
		return ConsistentHash
	case "least_conn":
		return LeastConnections
	default:
		return RoundRobin
	}
}

// StartHealthCheck starts health checking for all backends
func (lb *LoadBalancer) StartHealthCheck(ctx context.Context) {
	ticker := time.NewTicker(lb.config.LoadBalance.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			lb.performHealthCheck()
		}
	}
}

// performHealthCheck performs health check on all backends
func (lb *LoadBalancer) performHealthCheck() {
	lb.mu.RLock()
	services := make([]*Service, 0, len(lb.services))
	for _, service := range lb.services {
		services = append(services, service)
	}
	lb.mu.RUnlock()

	for _, service := range services {
		service.mu.RLock()
		backends := make([]*Backend, len(service.backends))
		copy(backends, service.backends)
		service.mu.RUnlock()

		for _, backend := range backends {
			go lb.checkBackendHealth(backend)
		}
	}
}

// checkBackendHealth checks the health of a single backend
func (lb *LoadBalancer) checkBackendHealth(backend *Backend) {
	client := &fasthttp.Client{
		ReadTimeout:  5 * time.Second,
		WriteTimeout: 5 * time.Second,
	}

	req := fasthttp.AcquireRequest()
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(req)
	defer fasthttp.ReleaseResponse(resp)

	url := fmt.Sprintf("http://%s%s", backend.Address(), lb.config.LoadBalance.HealthCheckPath)
	req.SetRequestURI(url)
	req.Header.SetMethod("GET")

	err := client.DoTimeout(req, resp, 5*time.Second)
	healthy := err == nil && resp.StatusCode() >= 200 && resp.StatusCode() < 300

	if backend.IsHealthy() != healthy {
		lb.logger.Info("Backend health status changed",
			zap.String("backend", backend.Address()),
			zap.Bool("healthy", healthy))
	}

	backend.SetHealthy(healthy)
}
