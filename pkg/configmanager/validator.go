package configmanager

import (
	"fmt"
	"net"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// DefaultValidator implements ConfigValidator interface
type DefaultValidator struct {
	serviceNameRegex *regexp.Regexp
	routePathRegex   *regexp.Regexp
}

// NewDefaultValidator creates a new default validator
func NewDefaultValidator() *DefaultValidator {
	return &DefaultValidator{
		serviceNameRegex: regexp.MustCompile(`^[a-zA-Z0-9][a-zA-Z0-9\-_]*[a-zA-Z0-9]$`),
		routePathRegex:   regexp.MustCompile(`^/[a-zA-Z0-9\-_/]*$`),
	}
}

// ValidateService validates service configuration
func (v *DefaultValidator) ValidateService(config *ServiceConfig) error {
	if config == nil {
		return fmt.Errorf("service config is nil")
	}
	
	// Validate service name
	if err := v.validateServiceName(config.Name); err != nil {
		return fmt.Errorf("invalid service name: %w", err)
	}
	
	// Validate namespace if provided
	if config.Namespace != "" {
		if err := v.validateNamespace(config.Namespace); err != nil {
			return fmt.Errorf("invalid namespace: %w", err)
		}
	}
	
	// Validate endpoints
	if len(config.Endpoints) == 0 {
		return fmt.Errorf("service must have at least one endpoint")
	}
	
	for i, endpoint := range config.Endpoints {
		if err := v.validateEndpoint(&endpoint); err != nil {
			return fmt.Errorf("invalid endpoint %d: %w", i, err)
		}
	}
	
	// Validate load balancer config
	if err := v.validateLoadBalancerConfig(&config.LoadBalancer); err != nil {
		return fmt.Errorf("invalid load balancer config: %w", err)
	}
	
	// Validate circuit breaker config
	if err := v.validateCircuitBreakerConfig(&config.CircuitBreaker); err != nil {
		return fmt.Errorf("invalid circuit breaker config: %w", err)
	}
	
	// Validate retry config
	if err := v.validateRetryConfig(&config.Retry); err != nil {
		return fmt.Errorf("invalid retry config: %w", err)
	}
	
	// Validate timeout config
	if err := v.validateTimeoutConfig(&config.Timeout); err != nil {
		return fmt.Errorf("invalid timeout config: %w", err)
	}
	
	// Validate rate limit config
	if err := v.validateRateLimitConfig(&config.RateLimit); err != nil {
		return fmt.Errorf("invalid rate limit config: %w", err)
	}
	
	// Validate health check config
	if err := v.validateHealthCheckConfig(&config.HealthCheck); err != nil {
		return fmt.Errorf("invalid health check config: %w", err)
	}
	
	return nil
}

// ValidateRoute validates route configuration
func (v *DefaultValidator) ValidateRoute(config *RouteConfig) error {
	if config == nil {
		return fmt.Errorf("route config is nil")
	}
	
	// Validate route ID
	if config.ID == "" {
		return fmt.Errorf("route ID is required")
	}
	
	// Validate route name
	if config.Name == "" {
		return fmt.Errorf("route name is required")
	}
	
	// Validate path
	if config.Path == "" {
		return fmt.Errorf("route path is required")
	}
	
	if !v.routePathRegex.MatchString(config.Path) {
		return fmt.Errorf("invalid route path format: %s", config.Path)
	}
	
	// Validate method
	validMethods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "*"}
	if config.Method != "" && !contains(validMethods, config.Method) {
		return fmt.Errorf("invalid HTTP method: %s", config.Method)
	}
	
	// Validate service
	if config.Service == "" {
		return fmt.Errorf("route service is required")
	}
	
	if err := v.validateServiceName(config.Service); err != nil {
		return fmt.Errorf("invalid route service name: %w", err)
	}
	
	// Validate rewrite config if provided
	if config.Rewrite != nil {
		if err := v.validateRewriteConfig(config.Rewrite); err != nil {
			return fmt.Errorf("invalid rewrite config: %w", err)
		}
	}
	
	// Validate priority
	if config.Priority < 0 {
		return fmt.Errorf("route priority must be non-negative")
	}
	
	return nil
}

// ValidateMiddleware validates middleware configuration
func (v *DefaultValidator) ValidateMiddleware(config *MiddlewareConfig) error {
	if config == nil {
		return fmt.Errorf("middleware config is nil")
	}
	
	// Validate name
	if config.Name == "" {
		return fmt.Errorf("middleware name is required")
	}
	
	// Validate type
	if config.Type == "" {
		return fmt.Errorf("middleware type is required")
	}
	
	// Validate priority
	if config.Priority < 0 {
		return fmt.Errorf("middleware priority must be non-negative")
	}
	
	// Type-specific validation
	switch config.Type {
	case "auth":
		return v.validateAuthMiddleware(config.Config)
	case "rate_limit":
		return v.validateRateLimitMiddleware(config.Config)
	case "cors":
		return v.validateCORSMiddleware(config.Config)
	case "logging":
		return v.validateLoggingMiddleware(config.Config)
	default:
		// Allow unknown middleware types for extensibility
		return nil
	}
}

// validateServiceName validates service name format
func (v *DefaultValidator) validateServiceName(name string) error {
	if name == "" {
		return fmt.Errorf("service name is required")
	}
	
	if len(name) > 63 {
		return fmt.Errorf("service name too long (max 63 characters)")
	}
	
	if !v.serviceNameRegex.MatchString(name) {
		return fmt.Errorf("service name must contain only alphanumeric characters, hyphens, and underscores")
	}
	
	return nil
}

// validateNamespace validates namespace format
func (v *DefaultValidator) validateNamespace(namespace string) error {
	if namespace == "" {
		return fmt.Errorf("namespace cannot be empty")
	}
	
	if len(namespace) > 63 {
		return fmt.Errorf("namespace too long (max 63 characters)")
	}
	
	if !v.serviceNameRegex.MatchString(namespace) {
		return fmt.Errorf("namespace must contain only alphanumeric characters, hyphens, and underscores")
	}
	
	return nil
}

// validateEndpoint validates endpoint configuration
func (v *DefaultValidator) validateEndpoint(endpoint *Endpoint) error {
	if endpoint.ID == "" {
		return fmt.Errorf("endpoint ID is required")
	}
	
	if endpoint.Address == "" {
		return fmt.Errorf("endpoint address is required")
	}
	
	// Validate IP address or hostname
	if ip := net.ParseIP(endpoint.Address); ip == nil {
		// Not an IP, check if it's a valid hostname
		if !isValidHostname(endpoint.Address) {
			return fmt.Errorf("invalid endpoint address: %s", endpoint.Address)
		}
	}
	
	// Validate port
	if endpoint.Port <= 0 || endpoint.Port > 65535 {
		return fmt.Errorf("invalid endpoint port: %d", endpoint.Port)
	}
	
	// Validate weight
	if endpoint.Weight < 0 {
		return fmt.Errorf("endpoint weight must be non-negative")
	}
	
	return nil
}

// validateLoadBalancerConfig validates load balancer configuration
func (v *DefaultValidator) validateLoadBalancerConfig(config *LoadBalancerConfig) error {
	validStrategies := []string{"round_robin", "random", "least_conn", "consistent_hash", "weighted_round_robin"}
	if !contains(validStrategies, config.Strategy) {
		return fmt.Errorf("invalid load balancer strategy: %s", config.Strategy)
	}
	
	// Validate hash key for consistent hash strategy
	if config.Strategy == "consistent_hash" && config.HashKey == "" {
		return fmt.Errorf("hash key is required for consistent_hash strategy")
	}
	
	return nil
}

// validateCircuitBreakerConfig validates circuit breaker configuration
func (v *DefaultValidator) validateCircuitBreakerConfig(config *CircuitBreakerConfig) error {
	if config.FailureThreshold < 0 {
		return fmt.Errorf("failure threshold must be non-negative")
	}
	
	if config.SuccessThreshold < 0 {
		return fmt.Errorf("success threshold must be non-negative")
	}
	
	if config.Timeout < 0 {
		return fmt.Errorf("timeout must be non-negative")
	}
	
	if config.MaxRequests < 0 {
		return fmt.Errorf("max requests must be non-negative")
	}
	
	return nil
}

// validateRetryConfig validates retry configuration
func (v *DefaultValidator) validateRetryConfig(config *RetryConfig) error {
	if config.MaxRetries < 0 {
		return fmt.Errorf("max retries must be non-negative")
	}
	
	if config.RetryDelay < 0 {
		return fmt.Errorf("retry delay must be non-negative")
	}
	
	validBackoffTypes := []string{"fixed", "exponential", "linear"}
	if config.BackoffType != "" && !contains(validBackoffTypes, config.BackoffType) {
		return fmt.Errorf("invalid backoff type: %s", config.BackoffType)
	}
	
	return nil
}

// validateTimeoutConfig validates timeout configuration
func (v *DefaultValidator) validateTimeoutConfig(config *TimeoutConfig) error {
	if config.Connect < 0 {
		return fmt.Errorf("connect timeout must be non-negative")
	}
	
	if config.Read < 0 {
		return fmt.Errorf("read timeout must be non-negative")
	}
	
	if config.Write < 0 {
		return fmt.Errorf("write timeout must be non-negative")
	}
	
	if config.Idle < 0 {
		return fmt.Errorf("idle timeout must be non-negative")
	}
	
	return nil
}

// validateRateLimitConfig validates rate limit configuration
func (v *DefaultValidator) validateRateLimitConfig(config *RateLimitConfig) error {
	if config.Rate < 0 {
		return fmt.Errorf("rate must be non-negative")
	}
	
	if config.Burst < 0 {
		return fmt.Errorf("burst must be non-negative")
	}
	
	if config.Burst > 0 && config.Burst < config.Rate {
		return fmt.Errorf("burst must be greater than or equal to rate")
	}
	
	return nil
}

// validateHealthCheckConfig validates health check configuration
func (v *DefaultValidator) validateHealthCheckConfig(config *HealthCheckConfig) error {
	if config.Path != "" && !strings.HasPrefix(config.Path, "/") {
		return fmt.Errorf("health check path must start with /")
	}
	
	if config.Interval < 0 {
		return fmt.Errorf("health check interval must be non-negative")
	}
	
	if config.Timeout < 0 {
		return fmt.Errorf("health check timeout must be non-negative")
	}
	
	if config.Method != "" {
		validMethods := []string{"GET", "POST", "HEAD"}
		if !contains(validMethods, config.Method) {
			return fmt.Errorf("invalid health check method: %s", config.Method)
		}
	}
	
	return nil
}

// validateRewriteConfig validates rewrite configuration
func (v *DefaultValidator) validateRewriteConfig(config *RewriteConfig) error {
	if config.Path != "" && !strings.HasPrefix(config.Path, "/") {
		return fmt.Errorf("rewrite path must start with /")
	}
	
	if config.Host != "" && !isValidHostname(config.Host) {
		return fmt.Errorf("invalid rewrite host: %s", config.Host)
	}
	
	if config.Scheme != "" {
		validSchemes := []string{"http", "https"}
		if !contains(validSchemes, config.Scheme) {
			return fmt.Errorf("invalid rewrite scheme: %s", config.Scheme)
		}
	}
	
	return nil
}

// validateAuthMiddleware validates auth middleware configuration
func (v *DefaultValidator) validateAuthMiddleware(config map[string]interface{}) error {
	// Add auth-specific validation logic here
	return nil
}

// validateRateLimitMiddleware validates rate limit middleware configuration
func (v *DefaultValidator) validateRateLimitMiddleware(config map[string]interface{}) error {
	// Add rate limit-specific validation logic here
	return nil
}

// validateCORSMiddleware validates CORS middleware configuration
func (v *DefaultValidator) validateCORSMiddleware(config map[string]interface{}) error {
	// Add CORS-specific validation logic here
	return nil
}

// validateLoggingMiddleware validates logging middleware configuration
func (v *DefaultValidator) validateLoggingMiddleware(config map[string]interface{}) error {
	// Add logging-specific validation logic here
	return nil
}

// Helper functions

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// isValidHostname checks if a string is a valid hostname
func isValidHostname(hostname string) bool {
	if len(hostname) == 0 || len(hostname) > 253 {
		return false
	}
	
	// Check if it's a valid URL
	if _, err := url.Parse("http://" + hostname); err != nil {
		return false
	}
	
	// Additional hostname validation can be added here
	return true
}
