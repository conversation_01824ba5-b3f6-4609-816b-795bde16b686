package configmanager

import (
	"context"
	"time"
)

// ConfigProvider defines the interface for configuration providers
type ConfigProvider interface {
	// Name returns the provider name
	Name() string
	
	// Priority returns the provider priority (higher number = higher priority)
	Priority() int
	
	// Watch starts watching for configuration changes
	Watch(ctx context.Context) (<-chan ConfigEvent, error)
	
	// Get retrieves configuration by key
	Get(ctx context.Context, key string) (*ConfigItem, error)
	
	// Set stores configuration (if provider supports writing)
	Set(ctx context.Context, key string, value interface{}) error
	
	// Delete removes configuration (if provider supports deletion)
	Delete(ctx context.Context, key string) error
	
	// List returns all configuration keys with optional prefix filter
	List(ctx context.Context, prefix string) ([]string, error)
	
	// Health checks if the provider is healthy
	Health(ctx context.Context) error
	
	// Close closes the provider
	Close() error
}

// ConfigConsumer defines the interface for configuration consumers
type ConfigConsumer interface {
	// Name returns the consumer name
	Name() string
	
	// ConfigKeys returns the configuration keys this consumer is interested in
	ConfigKeys() []string
	
	// OnConfigChange is called when configuration changes
	OnConfigChange(ctx context.Context, event ConfigEvent) error
	
	// Validate validates the configuration
	Validate(ctx context.Context, config *ConfigItem) error
}

// ConfigManager defines the interface for configuration management
type ConfigManager interface {
	// RegisterProvider registers a configuration provider
	RegisterProvider(provider ConfigProvider) error
	
	// UnregisterProvider unregisters a configuration provider
	UnregisterProvider(name string) error
	
	// RegisterConsumer registers a configuration consumer
	RegisterConsumer(consumer ConfigConsumer) error
	
	// UnregisterConsumer unregisters a configuration consumer
	UnregisterConsumer(name string) error
	
	// Get retrieves configuration from the highest priority provider
	Get(ctx context.Context, key string) (*ConfigItem, error)
	
	// Set stores configuration to all writable providers
	Set(ctx context.Context, key string, value interface{}) error
	
	// Delete removes configuration from all providers
	Delete(ctx context.Context, key string) error
	
	// Start starts the configuration manager
	Start(ctx context.Context) error
	
	// Stop stops the configuration manager
	Stop(ctx context.Context) error
	
	// Health returns the health status of all providers
	Health(ctx context.Context) map[string]error
}

// ConfigEvent represents a configuration change event
type ConfigEvent struct {
	Type      EventType   `json:"type"`
	Key       string      `json:"key"`
	Value     interface{} `json:"value"`
	OldValue  interface{} `json:"old_value,omitempty"`
	Source    string      `json:"source"`
	Timestamp time.Time   `json:"timestamp"`
	Version   int64       `json:"version"`
}

// EventType represents the type of configuration event
type EventType string

const (
	EventTypeCreate EventType = "create"
	EventTypeUpdate EventType = "update"
	EventTypeDelete EventType = "delete"
)

// ConfigItem represents a configuration item
type ConfigItem struct {
	Key       string      `json:"key"`
	Value     interface{} `json:"value"`
	Source    string      `json:"source"`
	Version   int64       `json:"version"`
	Timestamp time.Time   `json:"timestamp"`
	TTL       *time.Duration `json:"ttl,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// ServiceConfig represents service configuration
type ServiceConfig struct {
	Name            string                 `json:"name"`
	Namespace       string                 `json:"namespace,omitempty"`
	Version         string                 `json:"version,omitempty"`
	Endpoints       []Endpoint             `json:"endpoints"`
	LoadBalancer    LoadBalancerConfig     `json:"load_balancer"`
	CircuitBreaker  CircuitBreakerConfig   `json:"circuit_breaker"`
	Retry           RetryConfig            `json:"retry"`
	Timeout         TimeoutConfig          `json:"timeout"`
	RateLimit       RateLimitConfig        `json:"rate_limit"`
	HealthCheck     HealthCheckConfig      `json:"health_check"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
	Tags            []string               `json:"tags,omitempty"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// Endpoint represents a service endpoint
type Endpoint struct {
	ID       string            `json:"id"`
	Address  string            `json:"address"`
	Port     int               `json:"port"`
	Weight   int               `json:"weight"`
	Healthy  bool              `json:"healthy"`
	Metadata map[string]string `json:"metadata,omitempty"`
	Tags     []string          `json:"tags,omitempty"`
}

// LoadBalancerConfig represents load balancer configuration
type LoadBalancerConfig struct {
	Strategy    string            `json:"strategy"`    // round_robin, random, least_conn, consistent_hash
	HashKey     string            `json:"hash_key,omitempty"`    // for consistent_hash
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// CircuitBreakerConfig represents circuit breaker configuration
type CircuitBreakerConfig struct {
	Enabled          bool          `json:"enabled"`
	FailureThreshold int           `json:"failure_threshold"`
	SuccessThreshold int           `json:"success_threshold"`
	Timeout          time.Duration `json:"timeout"`
	MaxRequests      int           `json:"max_requests"`
}

// RetryConfig represents retry configuration
type RetryConfig struct {
	Enabled     bool          `json:"enabled"`
	MaxRetries  int           `json:"max_retries"`
	RetryDelay  time.Duration `json:"retry_delay"`
	BackoffType string        `json:"backoff_type"` // fixed, exponential, linear
}

// TimeoutConfig represents timeout configuration
type TimeoutConfig struct {
	Connect time.Duration `json:"connect"`
	Read    time.Duration `json:"read"`
	Write   time.Duration `json:"write"`
	Idle    time.Duration `json:"idle"`
}

// RateLimitConfig represents rate limiting configuration
type RateLimitConfig struct {
	Enabled bool   `json:"enabled"`
	Rate    int    `json:"rate"`    // requests per second
	Burst   int    `json:"burst"`   // burst size
	Key     string `json:"key"`     // rate limit key (ip, user, etc.)
}

// HealthCheckConfig represents health check configuration
type HealthCheckConfig struct {
	Enabled  bool          `json:"enabled"`
	Path     string        `json:"path"`
	Interval time.Duration `json:"interval"`
	Timeout  time.Duration `json:"timeout"`
	Method   string        `json:"method"`
	Headers  map[string]string `json:"headers,omitempty"`
}

// RouteConfig represents routing configuration
type RouteConfig struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Path        string                 `json:"path"`
	Method      string                 `json:"method"`
	Service     string                 `json:"service"`
	Rewrite     *RewriteConfig         `json:"rewrite,omitempty"`
	Headers     map[string]string      `json:"headers,omitempty"`
	Middleware  []string               `json:"middleware,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Priority    int                    `json:"priority"`
	Enabled     bool                   `json:"enabled"`
}

// RewriteConfig represents URL rewrite configuration
type RewriteConfig struct {
	Path   string `json:"path,omitempty"`
	Host   string `json:"host,omitempty"`
	Scheme string `json:"scheme,omitempty"`
}

// MiddlewareConfig represents middleware configuration
type MiddlewareConfig struct {
	Name     string                 `json:"name"`
	Type     string                 `json:"type"`
	Config   map[string]interface{} `json:"config"`
	Enabled  bool                   `json:"enabled"`
	Priority int                    `json:"priority"`
}

// ConfigValidator defines the interface for configuration validation
type ConfigValidator interface {
	// ValidateService validates service configuration
	ValidateService(config *ServiceConfig) error
	
	// ValidateRoute validates route configuration
	ValidateRoute(config *RouteConfig) error
	
	// ValidateMiddleware validates middleware configuration
	ValidateMiddleware(config *MiddlewareConfig) error
}

// ConfigTransformer defines the interface for configuration transformation
type ConfigTransformer interface {
	// Transform transforms configuration from one format to another
	Transform(ctx context.Context, from, to string, data interface{}) (interface{}, error)
	
	// SupportedFormats returns supported transformation formats
	SupportedFormats() []string
}

// ConfigWatcher defines the interface for watching configuration changes
type ConfigWatcher interface {
	// Watch starts watching for configuration changes
	Watch(ctx context.Context, keys []string) (<-chan ConfigEvent, error)
	
	// Stop stops watching
	Stop() error
}

// ConfigCache defines the interface for configuration caching
type ConfigCache interface {
	// Get retrieves cached configuration
	Get(key string) (*ConfigItem, bool)
	
	// Set stores configuration in cache
	Set(key string, item *ConfigItem, ttl time.Duration)
	
	// Delete removes configuration from cache
	Delete(key string)
	
	// Clear clears all cached configuration
	Clear()
	
	// Size returns the number of cached items
	Size() int
}

// ConfigMetrics defines the interface for configuration metrics
type ConfigMetrics interface {
	// RecordConfigChange records a configuration change
	RecordConfigChange(source, key string, eventType EventType)
	
	// RecordConfigError records a configuration error
	RecordConfigError(source, operation string, err error)
	
	// RecordProviderHealth records provider health status
	RecordProviderHealth(provider string, healthy bool)
	
	// RecordConsumerLatency records consumer processing latency
	RecordConsumerLatency(consumer string, duration time.Duration)
}
