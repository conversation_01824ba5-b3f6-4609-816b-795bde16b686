package configmanager

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"go.uber.org/zap"
)

// DefaultConfigManager implements ConfigManager interface
type DefaultConfigManager struct {
	logger    *zap.Logger
	providers map[string]ConfigProvider
	consumers map[string]ConfigConsumer
	cache     ConfigCache
	metrics   ConfigMetrics
	validator ConfigValidator
	
	// Event handling
	eventChan   chan ConfigEvent
	stopChan    chan struct{}
	wg          sync.WaitGroup
	
	// Synchronization
	mu sync.RWMutex
	
	// Configuration
	config ManagerConfig
}

// ManagerConfig represents configuration manager settings
type ManagerConfig struct {
	EventBufferSize   int           `json:"event_buffer_size"`
	CacheTTL          time.Duration `json:"cache_ttl"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	MaxRetries        int           `json:"max_retries"`
	RetryDelay        time.Duration `json:"retry_delay"`
}

// NewDefaultConfigManager creates a new default configuration manager
func NewDefaultConfigManager(logger *zap.Logger, config ManagerConfig) *DefaultConfigManager {
	if config.EventBufferSize == 0 {
		config.EventBufferSize = 1000
	}
	if config.CacheTTL == 0 {
		config.CacheTTL = 5 * time.Minute
	}
	if config.HealthCheckInterval == 0 {
		config.HealthCheckInterval = 30 * time.Second
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.RetryDelay == 0 {
		config.RetryDelay = time.Second
	}

	return &DefaultConfigManager{
		logger:    logger,
		providers: make(map[string]ConfigProvider),
		consumers: make(map[string]ConfigConsumer),
		cache:     NewMemoryCache(),
		metrics:   NewPrometheusMetrics(),
		validator: NewDefaultValidator(),
		eventChan: make(chan ConfigEvent, config.EventBufferSize),
		stopChan:  make(chan struct{}),
		config:    config,
	}
}

// RegisterProvider registers a configuration provider
func (m *DefaultConfigManager) RegisterProvider(provider ConfigProvider) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	name := provider.Name()
	if _, exists := m.providers[name]; exists {
		return fmt.Errorf("provider %s already registered", name)
	}
	
	m.providers[name] = provider
	m.logger.Info("Configuration provider registered", zap.String("provider", name))
	
	return nil
}

// UnregisterProvider unregisters a configuration provider
func (m *DefaultConfigManager) UnregisterProvider(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	provider, exists := m.providers[name]
	if !exists {
		return fmt.Errorf("provider %s not found", name)
	}
	
	if err := provider.Close(); err != nil {
		m.logger.Warn("Error closing provider", zap.String("provider", name), zap.Error(err))
	}
	
	delete(m.providers, name)
	m.logger.Info("Configuration provider unregistered", zap.String("provider", name))
	
	return nil
}

// RegisterConsumer registers a configuration consumer
func (m *DefaultConfigManager) RegisterConsumer(consumer ConfigConsumer) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	name := consumer.Name()
	if _, exists := m.consumers[name]; exists {
		return fmt.Errorf("consumer %s already registered", name)
	}
	
	m.consumers[name] = consumer
	m.logger.Info("Configuration consumer registered", zap.String("consumer", name))
	
	return nil
}

// UnregisterConsumer unregisters a configuration consumer
func (m *DefaultConfigManager) UnregisterConsumer(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if _, exists := m.consumers[name]; !exists {
		return fmt.Errorf("consumer %s not found", name)
	}
	
	delete(m.consumers, name)
	m.logger.Info("Configuration consumer unregistered", zap.String("consumer", name))
	
	return nil
}

// Get retrieves configuration from the highest priority provider
func (m *DefaultConfigManager) Get(ctx context.Context, key string) (*ConfigItem, error) {
	// Check cache first
	if item, found := m.cache.Get(key); found {
		return item, nil
	}
	
	// Get providers sorted by priority
	providers := m.getSortedProviders()
	
	var lastErr error
	for _, provider := range providers {
		item, err := provider.Get(ctx, key)
		if err != nil {
			lastErr = err
			m.metrics.RecordConfigError(provider.Name(), "get", err)
			continue
		}
		
		if item != nil {
			// Cache the result
			m.cache.Set(key, item, m.config.CacheTTL)
			return item, nil
		}
	}
	
	if lastErr != nil {
		return nil, lastErr
	}
	
	return nil, fmt.Errorf("configuration key %s not found", key)
}

// Set stores configuration to all writable providers
func (m *DefaultConfigManager) Set(ctx context.Context, key string, value interface{}) error {
	item := &ConfigItem{
		Key:       key,
		Value:     value,
		Timestamp: time.Now(),
		Version:   time.Now().Unix(),
	}
	
	// Validate configuration if possible
	if m.validator != nil {
		if serviceConfig, ok := value.(*ServiceConfig); ok {
			if err := m.validator.ValidateService(serviceConfig); err != nil {
				return fmt.Errorf("validation failed: %w", err)
			}
		}
	}
	
	providers := m.getSortedProviders()
	var errors []error
	
	for _, provider := range providers {
		if err := provider.Set(ctx, key, value); err != nil {
			errors = append(errors, fmt.Errorf("provider %s: %w", provider.Name(), err))
			m.metrics.RecordConfigError(provider.Name(), "set", err)
		} else {
			item.Source = provider.Name()
		}
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("failed to set configuration: %v", errors)
	}
	
	// Update cache
	m.cache.Set(key, item, m.config.CacheTTL)
	
	// Send event
	event := ConfigEvent{
		Type:      EventTypeUpdate,
		Key:       key,
		Value:     value,
		Source:    "manager",
		Timestamp: time.Now(),
		Version:   item.Version,
	}
	
	select {
	case m.eventChan <- event:
	default:
		m.logger.Warn("Event channel full, dropping event", zap.String("key", key))
	}
	
	return nil
}

// Delete removes configuration from all providers
func (m *DefaultConfigManager) Delete(ctx context.Context, key string) error {
	providers := m.getSortedProviders()
	var errors []error
	
	for _, provider := range providers {
		if err := provider.Delete(ctx, key); err != nil {
			errors = append(errors, fmt.Errorf("provider %s: %w", provider.Name(), err))
			m.metrics.RecordConfigError(provider.Name(), "delete", err)
		}
	}
	
	// Remove from cache
	m.cache.Delete(key)
	
	// Send event
	event := ConfigEvent{
		Type:      EventTypeDelete,
		Key:       key,
		Source:    "manager",
		Timestamp: time.Now(),
	}
	
	select {
	case m.eventChan <- event:
	default:
		m.logger.Warn("Event channel full, dropping event", zap.String("key", key))
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("failed to delete configuration: %v", errors)
	}
	
	return nil
}

// Start starts the configuration manager
func (m *DefaultConfigManager) Start(ctx context.Context) error {
	m.logger.Info("Starting configuration manager")
	
	// Start watching providers
	for _, provider := range m.providers {
		m.wg.Add(1)
		go m.watchProvider(ctx, provider)
	}
	
	// Start event processor
	m.wg.Add(1)
	go m.processEvents(ctx)
	
	// Start health checker
	m.wg.Add(1)
	go m.healthChecker(ctx)
	
	m.logger.Info("Configuration manager started")
	return nil
}

// Stop stops the configuration manager
func (m *DefaultConfigManager) Stop(ctx context.Context) error {
	m.logger.Info("Stopping configuration manager")
	
	close(m.stopChan)
	
	// Wait for goroutines to finish
	done := make(chan struct{})
	go func() {
		m.wg.Wait()
		close(done)
	}()
	
	select {
	case <-done:
		m.logger.Info("Configuration manager stopped")
	case <-ctx.Done():
		m.logger.Warn("Configuration manager stop timeout")
	}
	
	// Close all providers
	for _, provider := range m.providers {
		if err := provider.Close(); err != nil {
			m.logger.Warn("Error closing provider", zap.String("provider", provider.Name()), zap.Error(err))
		}
	}
	
	return nil
}

// Health returns the health status of all providers
func (m *DefaultConfigManager) Health(ctx context.Context) map[string]error {
	m.mu.RLock()
	providers := make([]ConfigProvider, 0, len(m.providers))
	for _, provider := range m.providers {
		providers = append(providers, provider)
	}
	m.mu.RUnlock()
	
	health := make(map[string]error)
	for _, provider := range providers {
		err := provider.Health(ctx)
		health[provider.Name()] = err
		m.metrics.RecordProviderHealth(provider.Name(), err == nil)
	}
	
	return health
}

// getSortedProviders returns providers sorted by priority (highest first)
func (m *DefaultConfigManager) getSortedProviders() []ConfigProvider {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	providers := make([]ConfigProvider, 0, len(m.providers))
	for _, provider := range m.providers {
		providers = append(providers, provider)
	}
	
	sort.Slice(providers, func(i, j int) bool {
		return providers[i].Priority() > providers[j].Priority()
	})
	
	return providers
}

// watchProvider watches a provider for configuration changes
func (m *DefaultConfigManager) watchProvider(ctx context.Context, provider ConfigProvider) {
	defer m.wg.Done()
	
	eventChan, err := provider.Watch(ctx)
	if err != nil {
		m.logger.Error("Failed to watch provider", zap.String("provider", provider.Name()), zap.Error(err))
		return
	}
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-m.stopChan:
			return
		case event, ok := <-eventChan:
			if !ok {
				m.logger.Warn("Provider event channel closed", zap.String("provider", provider.Name()))
				return
			}
			
			// Forward event to main event channel
			select {
			case m.eventChan <- event:
			default:
				m.logger.Warn("Event channel full, dropping event from provider",
					zap.String("provider", provider.Name()),
					zap.String("key", event.Key))
			}
		}
	}
}

// processEvents processes configuration events
func (m *DefaultConfigManager) processEvents(ctx context.Context) {
	defer m.wg.Done()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-m.stopChan:
			return
		case event := <-m.eventChan:
			m.handleEvent(ctx, event)
		}
	}
}

// handleEvent handles a configuration event
func (m *DefaultConfigManager) handleEvent(ctx context.Context, event ConfigEvent) {
	m.logger.Debug("Processing configuration event",
		zap.String("type", string(event.Type)),
		zap.String("key", event.Key),
		zap.String("source", event.Source))
	
	// Update cache
	if event.Type == EventTypeDelete {
		m.cache.Delete(event.Key)
	} else if item, ok := event.Value.(*ConfigItem); ok {
		m.cache.Set(event.Key, item, m.config.CacheTTL)
	}
	
	// Notify interested consumers
	m.notifyConsumers(ctx, event)
	
	// Record metrics
	m.metrics.RecordConfigChange(event.Source, event.Key, event.Type)
}

// notifyConsumers notifies consumers about configuration changes
func (m *DefaultConfigManager) notifyConsumers(ctx context.Context, event ConfigEvent) {
	m.mu.RLock()
	consumers := make([]ConfigConsumer, 0, len(m.consumers))
	for _, consumer := range m.consumers {
		consumers = append(consumers, consumer)
	}
	m.mu.RUnlock()
	
	for _, consumer := range consumers {
		// Check if consumer is interested in this key
		interested := false
		for _, key := range consumer.ConfigKeys() {
			if key == event.Key || key == "*" {
				interested = true
				break
			}
		}
		
		if !interested {
			continue
		}
		
		// Notify consumer
		start := time.Now()
		if err := consumer.OnConfigChange(ctx, event); err != nil {
			m.logger.Error("Consumer failed to handle config change",
				zap.String("consumer", consumer.Name()),
				zap.String("key", event.Key),
				zap.Error(err))
		}
		
		duration := time.Since(start)
		m.metrics.RecordConsumerLatency(consumer.Name(), duration)
	}
}

// healthChecker periodically checks provider health
func (m *DefaultConfigManager) healthChecker(ctx context.Context) {
	defer m.wg.Done()
	
	ticker := time.NewTicker(m.config.HealthCheckInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-m.stopChan:
			return
		case <-ticker.C:
			health := m.Health(ctx)
			for provider, err := range health {
				if err != nil {
					m.logger.Warn("Provider health check failed",
						zap.String("provider", provider),
						zap.Error(err))
				}
			}
		}
	}
}
