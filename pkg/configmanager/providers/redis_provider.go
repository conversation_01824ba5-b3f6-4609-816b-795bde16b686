package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/configmanager"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// RedisProvider implements ConfigProvider for Redis
type RedisProvider struct {
	name     string
	priority int
	client   *redis.Client
	logger   *zap.Logger
	config   RedisConfig

	// Pub/Sub for watching changes
	pubsub   *redis.PubSub
	stopChan chan struct{}
}

// RedisConfig represents Redis provider configuration
type RedisConfig struct {
	Addr         string        `json:"addr"`
	Password     string        `json:"password"`
	DB           int           `json:"db"`
	PoolSize     int           `json:"pool_size"`
	MinIdleConns int           `json:"min_idle_conns"`
	DialTimeout  time.Duration `json:"dial_timeout"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	KeyPrefix    string        `json:"key_prefix"`
	Channel      string        `json:"channel"`
}

// NewRedisProvider creates a new Redis configuration provider
func NewRedisProvider(name string, priority int, config RedisConfig, logger *zap.Logger) (*RedisProvider, error) {
	if config.PoolSize == 0 {
		config.PoolSize = 10
	}
	if config.MinIdleConns == 0 {
		config.MinIdleConns = 2
	}
	if config.DialTimeout == 0 {
		config.DialTimeout = 5 * time.Second
	}
	if config.ReadTimeout == 0 {
		config.ReadTimeout = 3 * time.Second
	}
	if config.WriteTimeout == 0 {
		config.WriteTimeout = 3 * time.Second
	}
	if config.KeyPrefix == "" {
		config.KeyPrefix = "servicemesh:config:"
	}
	if config.Channel == "" {
		config.Channel = "servicemesh:config:changes"
	}

	client := redis.NewClient(&redis.Options{
		Addr:         config.Addr,
		Password:     config.Password,
		DB:           config.DB,
		PoolSize:     config.PoolSize,
		MinIdleConns: config.MinIdleConns,
		DialTimeout:  config.DialTimeout,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &RedisProvider{
		name:     name,
		priority: priority,
		client:   client,
		logger:   logger,
		config:   config,
		stopChan: make(chan struct{}),
	}, nil
}

// Name returns the provider name
func (p *RedisProvider) Name() string {
	return p.name
}

// Priority returns the provider priority
func (p *RedisProvider) Priority() int {
	return p.priority
}

// Watch starts watching for configuration changes
func (p *RedisProvider) Watch(ctx context.Context) (<-chan configmanager.ConfigEvent, error) {
	eventChan := make(chan configmanager.ConfigEvent, 100)

	// Subscribe to Redis pub/sub channel
	p.pubsub = p.client.Subscribe(ctx, p.config.Channel)

	go func() {
		defer close(eventChan)
		defer p.pubsub.Close()

		ch := p.pubsub.Channel()

		for {
			select {
			case <-ctx.Done():
				return
			case <-p.stopChan:
				return
			case msg, ok := <-ch:
				if !ok {
					return
				}

				event, err := p.parseEvent(msg.Payload)
				if err != nil {
					p.logger.Error("Failed to parse Redis event", zap.Error(err))
					continue
				}

				select {
				case eventChan <- *event:
				case <-ctx.Done():
					return
				case <-p.stopChan:
					return
				}
			}
		}
	}()

	return eventChan, nil
}

// Get retrieves configuration by key
func (p *RedisProvider) Get(ctx context.Context, key string) (*configmanager.ConfigItem, error) {
	redisKey := p.config.KeyPrefix + key

	data, err := p.client.Get(ctx, redisKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // Key not found
		}
		return nil, fmt.Errorf("failed to get key %s: %w", key, err)
	}

	var value interface{}
	if err := json.Unmarshal([]byte(data), &value); err != nil {
		// If JSON unmarshal fails, treat as string
		value = data
	}

	// Get TTL
	ttl, err := p.client.TTL(ctx, redisKey).Result()
	if err != nil {
		p.logger.Warn("Failed to get TTL", zap.String("key", key), zap.Error(err))
	}

	item := &configmanager.ConfigItem{
		Key:       key,
		Value:     value,
		Source:    p.name,
		Timestamp: time.Now(),
		Version:   time.Now().Unix(),
	}

	if ttl > 0 {
		item.TTL = &ttl
	}

	return item, nil
}

// Set stores configuration
func (p *RedisProvider) Set(ctx context.Context, key string, value interface{}) error {
	redisKey := p.config.KeyPrefix + key

	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	if err := p.client.Set(ctx, redisKey, data, 0).Err(); err != nil {
		return fmt.Errorf("failed to set key %s: %w", key, err)
	}

	// Publish change event
	event := configmanager.ConfigEvent{
		Type:      configmanager.EventTypeUpdate,
		Key:       key,
		Value:     value,
		Source:    p.name,
		Timestamp: time.Now(),
		Version:   time.Now().Unix(),
	}

	if err := p.publishEvent(ctx, event); err != nil {
		p.logger.Warn("Failed to publish event", zap.Error(err))
	}

	return nil
}

// Delete removes configuration
func (p *RedisProvider) Delete(ctx context.Context, key string) error {
	redisKey := p.config.KeyPrefix + key

	// Get old value for event
	oldValue, _ := p.Get(ctx, key)

	if err := p.client.Del(ctx, redisKey).Err(); err != nil {
		return fmt.Errorf("failed to delete key %s: %w", key, err)
	}

	// Publish change event
	event := configmanager.ConfigEvent{
		Type:      configmanager.EventTypeDelete,
		Key:       key,
		Source:    p.name,
		Timestamp: time.Now(),
	}

	if oldValue != nil {
		event.OldValue = oldValue.Value
	}

	if err := p.publishEvent(ctx, event); err != nil {
		p.logger.Warn("Failed to publish event", zap.Error(err))
	}

	return nil
}

// List returns all configuration keys with optional prefix filter
func (p *RedisProvider) List(ctx context.Context, prefix string) ([]string, error) {
	pattern := p.config.KeyPrefix + prefix + "*"

	keys, err := p.client.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to list keys: %w", err)
	}

	// Remove key prefix
	result := make([]string, len(keys))
	for i, key := range keys {
		result[i] = strings.TrimPrefix(key, p.config.KeyPrefix)
	}

	return result, nil
}

// Health checks if the provider is healthy
func (p *RedisProvider) Health(ctx context.Context) error {
	return p.client.Ping(ctx).Err()
}

// Close closes the provider
func (p *RedisProvider) Close() error {
	close(p.stopChan)

	if p.pubsub != nil {
		p.pubsub.Close()
	}

	return p.client.Close()
}

// publishEvent publishes a configuration change event
func (p *RedisProvider) publishEvent(ctx context.Context, event configmanager.ConfigEvent) error {
	data, err := json.Marshal(event)
	if err != nil {
		return err
	}

	return p.client.Publish(ctx, p.config.Channel, data).Err()
}

// parseEvent parses a Redis pub/sub message into a ConfigEvent
func (p *RedisProvider) parseEvent(payload string) (*configmanager.ConfigEvent, error) {
	var event configmanager.ConfigEvent
	if err := json.Unmarshal([]byte(payload), &event); err != nil {
		return nil, err
	}
	return &event, nil
}

// SetWithTTL stores configuration with TTL
func (p *RedisProvider) SetWithTTL(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	redisKey := p.config.KeyPrefix + key

	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	if err := p.client.Set(ctx, redisKey, data, ttl).Err(); err != nil {
		return fmt.Errorf("failed to set key %s with TTL: %w", key, err)
	}

	// Publish change event
	event := configmanager.ConfigEvent{
		Type:      configmanager.EventTypeUpdate,
		Key:       key,
		Value:     value,
		Source:    p.name,
		Timestamp: time.Now(),
		Version:   time.Now().Unix(),
	}

	if err := p.publishEvent(ctx, event); err != nil {
		p.logger.Warn("Failed to publish event", zap.Error(err))
	}

	return nil
}

// Exists checks if a key exists
func (p *RedisProvider) Exists(ctx context.Context, key string) (bool, error) {
	redisKey := p.config.KeyPrefix + key

	count, err := p.client.Exists(ctx, redisKey).Result()
	if err != nil {
		return false, fmt.Errorf("failed to check key existence: %w", err)
	}

	return count > 0, nil
}

// GetMultiple retrieves multiple configurations
func (p *RedisProvider) GetMultiple(ctx context.Context, keys []string) (map[string]*configmanager.ConfigItem, error) {
	if len(keys) == 0 {
		return make(map[string]*configmanager.ConfigItem), nil
	}

	redisKeys := make([]string, len(keys))
	for i, key := range keys {
		redisKeys[i] = p.config.KeyPrefix + key
	}

	values, err := p.client.MGet(ctx, redisKeys...).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get multiple keys: %w", err)
	}

	result := make(map[string]*configmanager.ConfigItem)
	for i, value := range values {
		if value == nil {
			continue
		}

		key := keys[i]
		var parsedValue interface{}

		if strValue, ok := value.(string); ok {
			if err := json.Unmarshal([]byte(strValue), &parsedValue); err != nil {
				parsedValue = strValue
			}
		} else {
			parsedValue = value
		}

		result[key] = &configmanager.ConfigItem{
			Key:       key,
			Value:     parsedValue,
			Source:    p.name,
			Timestamp: time.Now(),
			Version:   time.Now().Unix(),
		}
	}

	return result, nil
}
