package configmanager

import (
	"sync"
	"time"
)

// MemoryCache implements ConfigCache interface using in-memory storage
type MemoryCache struct {
	items map[string]*cacheItem
	mu    sync.RWMutex
}

// cacheItem represents a cached configuration item
type cacheItem struct {
	item      *ConfigItem
	expiresAt time.Time
}

// NewMemoryCache creates a new memory cache
func NewMemoryCache() *MemoryCache {
	cache := &MemoryCache{
		items: make(map[string]*cacheItem),
	}
	
	// Start cleanup goroutine
	go cache.cleanup()
	
	return cache
}

// Get retrieves cached configuration
func (c *MemoryCache) Get(key string) (*ConfigItem, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	item, exists := c.items[key]
	if !exists {
		return nil, false
	}
	
	// Check if expired
	if time.Now().After(item.expiresAt) {
		// Remove expired item
		go func() {
			c.mu.Lock()
			delete(c.items, key)
			c.mu.Unlock()
		}()
		return nil, false
	}
	
	return item.item, true
}

// Set stores configuration in cache
func (c *MemoryCache) Set(key string, item *ConfigItem, ttl time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.items[key] = &cacheItem{
		item:      item,
		expiresAt: time.Now().Add(ttl),
	}
}

// Delete removes configuration from cache
func (c *MemoryCache) Delete(key string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	delete(c.items, key)
}

// Clear clears all cached configuration
func (c *MemoryCache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.items = make(map[string]*cacheItem)
}

// Size returns the number of cached items
func (c *MemoryCache) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	return len(c.items)
}

// cleanup removes expired items periodically
func (c *MemoryCache) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		c.mu.Lock()
		now := time.Now()
		for key, item := range c.items {
			if now.After(item.expiresAt) {
				delete(c.items, key)
			}
		}
		c.mu.Unlock()
	}
}

// GetStats returns cache statistics
func (c *MemoryCache) GetStats() CacheStats {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	stats := CacheStats{
		Size: len(c.items),
	}
	
	now := time.Now()
	for _, item := range c.items {
		if now.After(item.expiresAt) {
			stats.Expired++
		}
	}
	
	return stats
}

// CacheStats represents cache statistics
type CacheStats struct {
	Size    int `json:"size"`
	Expired int `json:"expired"`
}

// LRUCache implements ConfigCache interface using LRU eviction
type LRUCache struct {
	capacity int
	items    map[string]*lruItem
	head     *lruItem
	tail     *lruItem
	mu       sync.RWMutex
}

// lruItem represents an LRU cache item
type lruItem struct {
	key       string
	item      *ConfigItem
	expiresAt time.Time
	prev      *lruItem
	next      *lruItem
}

// NewLRUCache creates a new LRU cache
func NewLRUCache(capacity int) *LRUCache {
	cache := &LRUCache{
		capacity: capacity,
		items:    make(map[string]*lruItem),
	}
	
	// Create dummy head and tail nodes
	cache.head = &lruItem{}
	cache.tail = &lruItem{}
	cache.head.next = cache.tail
	cache.tail.prev = cache.head
	
	// Start cleanup goroutine
	go cache.cleanup()
	
	return cache
}

// Get retrieves cached configuration
func (c *LRUCache) Get(key string) (*ConfigItem, bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	item, exists := c.items[key]
	if !exists {
		return nil, false
	}
	
	// Check if expired
	if time.Now().After(item.expiresAt) {
		c.removeItem(item)
		return nil, false
	}
	
	// Move to front (most recently used)
	c.moveToFront(item)
	
	return item.item, true
}

// Set stores configuration in cache
func (c *LRUCache) Set(key string, item *ConfigItem, ttl time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	if existing, exists := c.items[key]; exists {
		// Update existing item
		existing.item = item
		existing.expiresAt = time.Now().Add(ttl)
		c.moveToFront(existing)
		return
	}
	
	// Create new item
	newItem := &lruItem{
		key:       key,
		item:      item,
		expiresAt: time.Now().Add(ttl),
	}
	
	c.items[key] = newItem
	c.addToFront(newItem)
	
	// Check capacity and evict if necessary
	if len(c.items) > c.capacity {
		c.evictLRU()
	}
}

// Delete removes configuration from cache
func (c *LRUCache) Delete(key string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	if item, exists := c.items[key]; exists {
		c.removeItem(item)
	}
}

// Clear clears all cached configuration
func (c *LRUCache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.items = make(map[string]*lruItem)
	c.head.next = c.tail
	c.tail.prev = c.head
}

// Size returns the number of cached items
func (c *LRUCache) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	return len(c.items)
}

// moveToFront moves an item to the front of the list
func (c *LRUCache) moveToFront(item *lruItem) {
	c.removeFromList(item)
	c.addToFront(item)
}

// addToFront adds an item to the front of the list
func (c *LRUCache) addToFront(item *lruItem) {
	item.prev = c.head
	item.next = c.head.next
	c.head.next.prev = item
	c.head.next = item
}

// removeFromList removes an item from the list
func (c *LRUCache) removeFromList(item *lruItem) {
	item.prev.next = item.next
	item.next.prev = item.prev
}

// removeItem removes an item from both the map and list
func (c *LRUCache) removeItem(item *lruItem) {
	delete(c.items, item.key)
	c.removeFromList(item)
}

// evictLRU evicts the least recently used item
func (c *LRUCache) evictLRU() {
	if c.tail.prev != c.head {
		c.removeItem(c.tail.prev)
	}
}

// cleanup removes expired items periodically
func (c *LRUCache) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		c.mu.Lock()
		now := time.Now()
		
		// Collect expired items
		var expiredItems []*lruItem
		for _, item := range c.items {
			if now.After(item.expiresAt) {
				expiredItems = append(expiredItems, item)
			}
		}
		
		// Remove expired items
		for _, item := range expiredItems {
			c.removeItem(item)
		}
		
		c.mu.Unlock()
	}
}
