package configmanager

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// PrometheusMetrics implements ConfigMetrics interface using Prometheus
type PrometheusMetrics struct {
	configChanges     *prometheus.CounterVec
	configErrors      *prometheus.CounterVec
	providerHealth    *prometheus.GaugeVec
	consumerLatency   *prometheus.HistogramVec
	cacheHits         *prometheus.CounterVec
	cacheMisses       *prometheus.CounterVec
	cacheSize         *prometheus.GaugeVec
}

// NewPrometheusMetrics creates a new Prometheus metrics collector
func NewPrometheusMetrics() *PrometheusMetrics {
	return &PrometheusMetrics{
		configChanges: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "configmanager_config_changes_total",
				Help: "Total number of configuration changes",
			},
			[]string{"source", "key", "event_type"},
		),
		configErrors: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "configmanager_config_errors_total",
				Help: "Total number of configuration errors",
			},
			[]string{"source", "operation"},
		),
		providerHealth: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "configmanager_provider_health",
				Help: "Health status of configuration providers (1=healthy, 0=unhealthy)",
			},
			[]string{"provider"},
		),
		consumerLatency: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "configmanager_consumer_latency_seconds",
				Help:    "Latency of configuration consumer processing",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"consumer"},
		),
		cacheHits: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "configmanager_cache_hits_total",
				Help: "Total number of cache hits",
			},
			[]string{"cache_type"},
		),
		cacheMisses: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "configmanager_cache_misses_total",
				Help: "Total number of cache misses",
			},
			[]string{"cache_type"},
		),
		cacheSize: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "configmanager_cache_size",
				Help: "Current size of configuration cache",
			},
			[]string{"cache_type"},
		),
	}
}

// RecordConfigChange records a configuration change
func (m *PrometheusMetrics) RecordConfigChange(source, key string, eventType EventType) {
	m.configChanges.WithLabelValues(source, key, string(eventType)).Inc()
}

// RecordConfigError records a configuration error
func (m *PrometheusMetrics) RecordConfigError(source, operation string, err error) {
	m.configErrors.WithLabelValues(source, operation).Inc()
}

// RecordProviderHealth records provider health status
func (m *PrometheusMetrics) RecordProviderHealth(provider string, healthy bool) {
	value := 0.0
	if healthy {
		value = 1.0
	}
	m.providerHealth.WithLabelValues(provider).Set(value)
}

// RecordConsumerLatency records consumer processing latency
func (m *PrometheusMetrics) RecordConsumerLatency(consumer string, duration time.Duration) {
	m.consumerLatency.WithLabelValues(consumer).Observe(duration.Seconds())
}

// RecordCacheHit records a cache hit
func (m *PrometheusMetrics) RecordCacheHit(cacheType string) {
	m.cacheHits.WithLabelValues(cacheType).Inc()
}

// RecordCacheMiss records a cache miss
func (m *PrometheusMetrics) RecordCacheMiss(cacheType string) {
	m.cacheMisses.WithLabelValues(cacheType).Inc()
}

// RecordCacheSize records the current cache size
func (m *PrometheusMetrics) RecordCacheSize(cacheType string, size int) {
	m.cacheSize.WithLabelValues(cacheType).Set(float64(size))
}

// NoOpMetrics implements ConfigMetrics interface with no-op operations
type NoOpMetrics struct{}

// NewNoOpMetrics creates a new no-op metrics collector
func NewNoOpMetrics() *NoOpMetrics {
	return &NoOpMetrics{}
}

// RecordConfigChange records a configuration change (no-op)
func (m *NoOpMetrics) RecordConfigChange(source, key string, eventType EventType) {}

// RecordConfigError records a configuration error (no-op)
func (m *NoOpMetrics) RecordConfigError(source, operation string, err error) {}

// RecordProviderHealth records provider health status (no-op)
func (m *NoOpMetrics) RecordProviderHealth(provider string, healthy bool) {}

// RecordConsumerLatency records consumer processing latency (no-op)
func (m *NoOpMetrics) RecordConsumerLatency(consumer string, duration time.Duration) {}

// InMemoryMetrics implements ConfigMetrics interface with in-memory storage
type InMemoryMetrics struct {
	configChanges   map[string]int64
	configErrors    map[string]int64
	providerHealth  map[string]bool
	consumerLatency map[string][]time.Duration
}

// NewInMemoryMetrics creates a new in-memory metrics collector
func NewInMemoryMetrics() *InMemoryMetrics {
	return &InMemoryMetrics{
		configChanges:   make(map[string]int64),
		configErrors:    make(map[string]int64),
		providerHealth:  make(map[string]bool),
		consumerLatency: make(map[string][]time.Duration),
	}
}

// RecordConfigChange records a configuration change
func (m *InMemoryMetrics) RecordConfigChange(source, key string, eventType EventType) {
	metricKey := source + ":" + key + ":" + string(eventType)
	m.configChanges[metricKey]++
}

// RecordConfigError records a configuration error
func (m *InMemoryMetrics) RecordConfigError(source, operation string, err error) {
	metricKey := source + ":" + operation
	m.configErrors[metricKey]++
}

// RecordProviderHealth records provider health status
func (m *InMemoryMetrics) RecordProviderHealth(provider string, healthy bool) {
	m.providerHealth[provider] = healthy
}

// RecordConsumerLatency records consumer processing latency
func (m *InMemoryMetrics) RecordConsumerLatency(consumer string, duration time.Duration) {
	if m.consumerLatency[consumer] == nil {
		m.consumerLatency[consumer] = make([]time.Duration, 0)
	}
	m.consumerLatency[consumer] = append(m.consumerLatency[consumer], duration)
	
	// Keep only last 100 measurements
	if len(m.consumerLatency[consumer]) > 100 {
		m.consumerLatency[consumer] = m.consumerLatency[consumer][1:]
	}
}

// GetConfigChanges returns configuration change counts
func (m *InMemoryMetrics) GetConfigChanges() map[string]int64 {
	result := make(map[string]int64)
	for k, v := range m.configChanges {
		result[k] = v
	}
	return result
}

// GetConfigErrors returns configuration error counts
func (m *InMemoryMetrics) GetConfigErrors() map[string]int64 {
	result := make(map[string]int64)
	for k, v := range m.configErrors {
		result[k] = v
	}
	return result
}

// GetProviderHealth returns provider health status
func (m *InMemoryMetrics) GetProviderHealth() map[string]bool {
	result := make(map[string]bool)
	for k, v := range m.providerHealth {
		result[k] = v
	}
	return result
}

// GetConsumerLatency returns consumer latency measurements
func (m *InMemoryMetrics) GetConsumerLatency() map[string][]time.Duration {
	result := make(map[string][]time.Duration)
	for k, v := range m.consumerLatency {
		result[k] = make([]time.Duration, len(v))
		copy(result[k], v)
	}
	return result
}

// GetAverageConsumerLatency returns average consumer latency
func (m *InMemoryMetrics) GetAverageConsumerLatency(consumer string) time.Duration {
	latencies := m.consumerLatency[consumer]
	if len(latencies) == 0 {
		return 0
	}
	
	var total time.Duration
	for _, latency := range latencies {
		total += latency
	}
	
	return total / time.Duration(len(latencies))
}

// Reset resets all metrics
func (m *InMemoryMetrics) Reset() {
	m.configChanges = make(map[string]int64)
	m.configErrors = make(map[string]int64)
	m.providerHealth = make(map[string]bool)
	m.consumerLatency = make(map[string][]time.Duration)
}
