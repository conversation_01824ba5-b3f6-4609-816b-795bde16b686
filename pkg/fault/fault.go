package fault

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/valyala/fasthttp"
	"go.uber.org/zap"
)

// FaultType represents the type of fault injection
type FaultType int

const (
	FaultTypeDelay FaultType = iota
	FaultTypeAbort
	FaultTypeRewrite
)

// FaultRule represents a fault injection rule
type FaultRule struct {
	ID          string            `json:"id"`
	ServiceName string            `json:"service_name"`
	Percentage  float64           `json:"percentage"`
	FaultType   FaultType         `json:"fault_type"`
	DelayMs     int               `json:"delay_ms"`
	AbortCode   int               `json:"abort_code"`
	Headers     map[string]string `json:"headers"`
	Enabled     bool              `json:"enabled"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// ShouldApply determines if the fault should be applied based on percentage
func (fr *FaultRule) ShouldApply() bool {
	if !fr.Enabled {
		return false
	}
	return rand.Float64()*100 < fr.Percentage
}

// MatchesRequest checks if the rule matches the request
func (fr *FaultRule) MatchesRequest(ctx *fasthttp.RequestCtx, serviceName string) bool {
	if !fr.Enabled {
		return false
	}
	
	if fr.ServiceName != "" && fr.ServiceName != serviceName {
		return false
	}
	
	// Check header matching
	for key, value := range fr.Headers {
		if string(ctx.Request.Header.Peek(key)) != value {
			return false
		}
	}
	
	return true
}

// Injector handles fault injection
type Injector struct {
	config *config.FaultConfig
	logger *zap.Logger
	rules  map[string]*FaultRule
	mu     sync.RWMutex
}

// NewInjector creates a new fault injector
func NewInjector(cfg *config.FaultConfig, logger *zap.Logger) *Injector {
	return &Injector{
		config: cfg,
		logger: logger,
		rules:  make(map[string]*FaultRule),
	}
}

// AddRule adds a fault injection rule
func (fi *Injector) AddRule(rule *FaultRule) {
	fi.mu.Lock()
	defer fi.mu.Unlock()
	
	rule.CreatedAt = time.Now()
	rule.UpdatedAt = time.Now()
	fi.rules[rule.ID] = rule
	
	fi.logger.Info("Fault injection rule added",
		zap.String("id", rule.ID),
		zap.String("service", rule.ServiceName),
		zap.Float64("percentage", rule.Percentage))
}

// RemoveRule removes a fault injection rule
func (fi *Injector) RemoveRule(ruleID string) {
	fi.mu.Lock()
	defer fi.mu.Unlock()
	
	delete(fi.rules, ruleID)
	
	fi.logger.Info("Fault injection rule removed", zap.String("id", ruleID))
}

// UpdateRule updates a fault injection rule
func (fi *Injector) UpdateRule(rule *FaultRule) {
	fi.mu.Lock()
	defer fi.mu.Unlock()
	
	if existing, exists := fi.rules[rule.ID]; exists {
		rule.CreatedAt = existing.CreatedAt
	}
	rule.UpdatedAt = time.Now()
	fi.rules[rule.ID] = rule
	
	fi.logger.Info("Fault injection rule updated",
		zap.String("id", rule.ID),
		zap.String("service", rule.ServiceName))
}

// GetRule gets a fault injection rule by ID
func (fi *Injector) GetRule(ruleID string) (*FaultRule, bool) {
	fi.mu.RLock()
	defer fi.mu.RUnlock()
	
	rule, exists := fi.rules[ruleID]
	return rule, exists
}

// GetRules gets all fault injection rules
func (fi *Injector) GetRules() map[string]*FaultRule {
	fi.mu.RLock()
	defer fi.mu.RUnlock()
	
	rules := make(map[string]*FaultRule)
	for id, rule := range fi.rules {
		rules[id] = rule
	}
	
	return rules
}

// InjectFault injects fault into the request if applicable
func (fi *Injector) InjectFault(ctx *fasthttp.RequestCtx, serviceName string) error {
	if !fi.config.Enable {
		return nil
	}
	
	fi.mu.RLock()
	rules := make([]*FaultRule, 0, len(fi.rules))
	for _, rule := range fi.rules {
		if rule.MatchesRequest(ctx, serviceName) {
			rules = append(rules, rule)
		}
	}
	fi.mu.RUnlock()
	
	// Apply the first matching rule that should be applied
	for _, rule := range rules {
		if rule.ShouldApply() {
			return fi.applyFault(ctx, rule)
		}
	}
	
	return nil
}

// applyFault applies the fault injection
func (fi *Injector) applyFault(ctx *fasthttp.RequestCtx, rule *FaultRule) error {
	fi.logger.Debug("Applying fault injection",
		zap.String("rule_id", rule.ID),
		zap.String("service", rule.ServiceName),
		zap.String("fault_type", fi.faultTypeToString(rule.FaultType)))
	
	switch rule.FaultType {
	case FaultTypeDelay:
		return fi.applyDelay(ctx, rule)
	case FaultTypeAbort:
		return fi.applyAbort(ctx, rule)
	case FaultTypeRewrite:
		return fi.applyRewrite(ctx, rule)
	default:
		return fmt.Errorf("unknown fault type: %d", rule.FaultType)
	}
}

// applyDelay applies delay fault injection
func (fi *Injector) applyDelay(ctx *fasthttp.RequestCtx, rule *FaultRule) error {
	if rule.DelayMs > 0 {
		delay := time.Duration(rule.DelayMs) * time.Millisecond
		fi.logger.Debug("Injecting delay", 
			zap.Duration("delay", delay),
			zap.String("rule_id", rule.ID))
		
		time.Sleep(delay)
	}
	return nil
}

// applyAbort applies abort fault injection
func (fi *Injector) applyAbort(ctx *fasthttp.RequestCtx, rule *FaultRule) error {
	statusCode := rule.AbortCode
	if statusCode == 0 {
		statusCode = fasthttp.StatusInternalServerError
	}
	
	fi.logger.Debug("Injecting abort",
		zap.Int("status_code", statusCode),
		zap.String("rule_id", rule.ID))
	
	ctx.SetStatusCode(statusCode)
	ctx.SetContentType("application/json")
	ctx.WriteString(fmt.Sprintf(`{"error":"Fault injection: request aborted","rule_id":"%s"}`, rule.ID))
	
	return fmt.Errorf("request aborted by fault injection")
}

// applyRewrite applies rewrite fault injection
func (fi *Injector) applyRewrite(ctx *fasthttp.RequestCtx, rule *FaultRule) error {
	// Rewrite functionality can be implemented based on specific requirements
	fi.logger.Debug("Injecting rewrite", zap.String("rule_id", rule.ID))
	
	// Example: modify headers
	for key, value := range rule.Headers {
		ctx.Request.Header.Set(key, value)
	}
	
	return nil
}

// faultTypeToString converts fault type to string
func (fi *Injector) faultTypeToString(faultType FaultType) string {
	switch faultType {
	case FaultTypeDelay:
		return "delay"
	case FaultTypeAbort:
		return "abort"
	case FaultTypeRewrite:
		return "rewrite"
	default:
		return "unknown"
	}
}

// Manager manages fault injection across services
type Manager struct {
	injectors map[string]*Injector
	config    *config.FaultConfig
	logger    *zap.Logger
	mu        sync.RWMutex
}

// NewManager creates a new fault injection manager
func NewManager(cfg *config.FaultConfig, logger *zap.Logger) *Manager {
	return &Manager{
		injectors: make(map[string]*Injector),
		config:    cfg,
		logger:    logger,
	}
}

// GetInjector gets or creates an injector for a service
func (fm *Manager) GetInjector(serviceName string) *Injector {
	fm.mu.RLock()
	injector, exists := fm.injectors[serviceName]
	fm.mu.RUnlock()
	
	if exists {
		return injector
	}
	
	fm.mu.Lock()
	defer fm.mu.Unlock()
	
	// Double-check after acquiring write lock
	if injector, exists := fm.injectors[serviceName]; exists {
		return injector
	}
	
	injector = NewInjector(fm.config, fm.logger)
	fm.injectors[serviceName] = injector
	
	return injector
}

// InjectFault injects fault for a service
func (fm *Manager) InjectFault(ctx *fasthttp.RequestCtx, serviceName string) error {
	if !fm.config.Enable {
		return nil
	}
	
	injector := fm.GetInjector(serviceName)
	return injector.InjectFault(ctx, serviceName)
}

// AddRule adds a fault injection rule to a service
func (fm *Manager) AddRule(serviceName string, rule *FaultRule) {
	injector := fm.GetInjector(serviceName)
	injector.AddRule(rule)
}

// RemoveRule removes a fault injection rule from a service
func (fm *Manager) RemoveRule(serviceName, ruleID string) {
	fm.mu.RLock()
	injector, exists := fm.injectors[serviceName]
	fm.mu.RUnlock()
	
	if exists {
		injector.RemoveRule(ruleID)
	}
}

// GetAllRules gets all fault injection rules across all services
func (fm *Manager) GetAllRules() map[string]map[string]*FaultRule {
	fm.mu.RLock()
	defer fm.mu.RUnlock()
	
	result := make(map[string]map[string]*FaultRule)
	for serviceName, injector := range fm.injectors {
		result[serviceName] = injector.GetRules()
	}
	
	return result
}

// StartMonitoring starts monitoring fault injection
func (fm *Manager) StartMonitoring(ctx context.Context) {
	if !fm.config.Enable {
		return
	}
	
	ticker := time.NewTicker(60 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			fm.logStatistics()
		}
	}
}

// logStatistics logs fault injection statistics
func (fm *Manager) logStatistics() {
	fm.mu.RLock()
	defer fm.mu.RUnlock()
	
	totalRules := 0
	enabledRules := 0
	
	for serviceName, injector := range fm.injectors {
		rules := injector.GetRules()
		serviceRules := len(rules)
		serviceEnabled := 0
		
		for _, rule := range rules {
			if rule.Enabled {
				serviceEnabled++
			}
		}
		
		totalRules += serviceRules
		enabledRules += serviceEnabled
		
		if serviceRules > 0 {
			fm.logger.Debug("Fault injection statistics",
				zap.String("service", serviceName),
				zap.Int("total_rules", serviceRules),
				zap.Int("enabled_rules", serviceEnabled))
		}
	}
	
	if totalRules > 0 {
		fm.logger.Info("Global fault injection statistics",
			zap.Int("total_rules", totalRules),
			zap.Int("enabled_rules", enabledRules))
	}
}
