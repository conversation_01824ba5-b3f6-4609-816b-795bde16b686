package circuitbreaker

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/chungzy/servicemesh-go/pkg/config"
	"github.com/sony/gobreaker"
	"go.uber.org/zap"
)

// State represents circuit breaker state
type State int

const (
	StateClosed State = iota
	StateHalfOpen
	StateOpen
)

// CircuitBreaker wraps gobreaker with additional functionality
type CircuitBreaker struct {
	name     string
	breaker  *gobreaker.CircuitBreaker
	config   *config.CircuitConfig
	logger   *zap.Logger
	metrics  *Metrics
}

// Metrics holds circuit breaker metrics
type Metrics struct {
	Requests      uint64
	TotalSuccess  uint64
	TotalFailures uint64
	ConsecutiveSuccesses uint64
	ConsecutiveFailures  uint64
	mu            sync.RWMutex
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(name string, cfg *config.CircuitConfig, logger *zap.Logger) *CircuitBreaker {
	cb := &CircuitBreaker{
		name:    name,
		config:  cfg,
		logger:  logger,
		metrics: &Metrics{},
	}

	settings := gobreaker.Settings{
		Name:        name,
		MaxRequests: cfg.MaxRequests,
		Interval:    cfg.Interval,
		Timeout:     cfg.Timeout,
		ReadyToTrip: cb.readyToTrip,
		OnStateChange: cb.onStateChange,
	}

	cb.breaker = gobreaker.NewCircuitBreaker(settings)
	return cb
}

// Execute executes a function with circuit breaker protection
func (cb *CircuitBreaker) Execute(fn func() (interface{}, error)) (interface{}, error) {
	result, err := cb.breaker.Execute(fn)
	
	cb.updateMetrics(err == nil)
	
	return result, err
}

// Call is an alias for Execute for compatibility
func (cb *CircuitBreaker) Call(fn func() (interface{}, error)) (interface{}, error) {
	return cb.Execute(fn)
}

// State returns the current state of the circuit breaker
func (cb *CircuitBreaker) State() State {
	switch cb.breaker.State() {
	case gobreaker.StateClosed:
		return StateClosed
	case gobreaker.StateHalfOpen:
		return StateHalfOpen
	case gobreaker.StateOpen:
		return StateOpen
	default:
		return StateClosed
	}
}

// Name returns the circuit breaker name
func (cb *CircuitBreaker) Name() string {
	return cb.name
}

// GetMetrics returns current metrics
func (cb *CircuitBreaker) GetMetrics() Metrics {
	cb.metrics.mu.RLock()
	defer cb.metrics.mu.RUnlock()
	return *cb.metrics
}

// readyToTrip determines when the circuit breaker should trip
func (cb *CircuitBreaker) readyToTrip(counts gobreaker.Counts) bool {
	failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
	return counts.Requests >= uint32(cb.config.FailureThreshold) && 
		   failureRatio >= 0.6 // 60% failure rate
}

// onStateChange is called when the circuit breaker state changes
func (cb *CircuitBreaker) onStateChange(name string, from gobreaker.State, to gobreaker.State) {
	cb.logger.Info("Circuit breaker state changed",
		zap.String("name", name),
		zap.String("from", from.String()),
		zap.String("to", to.String()))
}

// updateMetrics updates internal metrics
func (cb *CircuitBreaker) updateMetrics(success bool) {
	cb.metrics.mu.Lock()
	defer cb.metrics.mu.Unlock()
	
	cb.metrics.Requests++
	
	if success {
		cb.metrics.TotalSuccess++
		cb.metrics.ConsecutiveSuccesses++
		cb.metrics.ConsecutiveFailures = 0
	} else {
		cb.metrics.TotalFailures++
		cb.metrics.ConsecutiveFailures++
		cb.metrics.ConsecutiveSuccesses = 0
	}
}

// Manager manages multiple circuit breakers
type Manager struct {
	config   *config.CircuitConfig
	logger   *zap.Logger
	breakers map[string]*CircuitBreaker
	mu       sync.RWMutex
}

// NewManager creates a new circuit breaker manager
func NewManager(cfg *config.CircuitConfig, logger *zap.Logger) *Manager {
	return &Manager{
		config:   cfg,
		logger:   logger,
		breakers: make(map[string]*CircuitBreaker),
	}
}

// GetCircuitBreaker gets or creates a circuit breaker for the given name
func (m *Manager) GetCircuitBreaker(name string) *CircuitBreaker {
	m.mu.RLock()
	cb, exists := m.breakers[name]
	m.mu.RUnlock()
	
	if exists {
		return cb
	}
	
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Double-check after acquiring write lock
	if cb, exists := m.breakers[name]; exists {
		return cb
	}
	
	cb = NewCircuitBreaker(name, m.config, m.logger)
	m.breakers[name] = cb
	
	m.logger.Info("Circuit breaker created", zap.String("name", name))
	
	return cb
}

// RemoveCircuitBreaker removes a circuit breaker
func (m *Manager) RemoveCircuitBreaker(name string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	delete(m.breakers, name)
	m.logger.Info("Circuit breaker removed", zap.String("name", name))
}

// GetAllCircuitBreakers returns all circuit breakers
func (m *Manager) GetAllCircuitBreakers() map[string]*CircuitBreaker {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make(map[string]*CircuitBreaker)
	for name, cb := range m.breakers {
		result[name] = cb
	}
	
	return result
}

// ExecuteWithCircuitBreaker executes a function with circuit breaker protection
func (m *Manager) ExecuteWithCircuitBreaker(name string, fn func() (interface{}, error)) (interface{}, error) {
	cb := m.GetCircuitBreaker(name)
	return cb.Execute(fn)
}

// StartMonitoring starts monitoring circuit breakers
func (m *Manager) StartMonitoring(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			m.logMetrics()
		}
	}
}

// logMetrics logs metrics for all circuit breakers
func (m *Manager) logMetrics() {
	m.mu.RLock()
	breakers := make(map[string]*CircuitBreaker)
	for name, cb := range m.breakers {
		breakers[name] = cb
	}
	m.mu.RUnlock()
	
	for name, cb := range breakers {
		metrics := cb.GetMetrics()
		state := cb.State()
		
		m.logger.Info("Circuit breaker metrics",
			zap.String("name", name),
			zap.String("state", m.stateToString(state)),
			zap.Uint64("requests", metrics.Requests),
			zap.Uint64("successes", metrics.TotalSuccess),
			zap.Uint64("failures", metrics.TotalFailures),
			zap.Uint64("consecutive_successes", metrics.ConsecutiveSuccesses),
			zap.Uint64("consecutive_failures", metrics.ConsecutiveFailures))
	}
}

// stateToString converts state to string
func (m *Manager) stateToString(state State) string {
	switch state {
	case StateClosed:
		return "closed"
	case StateHalfOpen:
		return "half-open"
	case StateOpen:
		return "open"
	default:
		return "unknown"
	}
}

// ServiceCircuitBreaker provides service-level circuit breaking
type ServiceCircuitBreaker struct {
	manager     *Manager
	serviceName string
}

// NewServiceCircuitBreaker creates a service-level circuit breaker
func NewServiceCircuitBreaker(manager *Manager, serviceName string) *ServiceCircuitBreaker {
	return &ServiceCircuitBreaker{
		manager:     manager,
		serviceName: serviceName,
	}
}

// ExecuteRequest executes a request with circuit breaker protection
func (scb *ServiceCircuitBreaker) ExecuteRequest(backendAddr string, fn func() (interface{}, error)) (interface{}, error) {
	// Use service + backend as circuit breaker name
	cbName := fmt.Sprintf("%s:%s", scb.serviceName, backendAddr)
	return scb.manager.ExecuteWithCircuitBreaker(cbName, fn)
}

// IsBackendAvailable checks if a backend is available (circuit breaker is not open)
func (scb *ServiceCircuitBreaker) IsBackendAvailable(backendAddr string) bool {
	cbName := fmt.Sprintf("%s:%s", scb.serviceName, backendAddr)
	cb := scb.manager.GetCircuitBreaker(cbName)
	return cb.State() != StateOpen
}

// GetBackendState returns the circuit breaker state for a backend
func (scb *ServiceCircuitBreaker) GetBackendState(backendAddr string) State {
	cbName := fmt.Sprintf("%s:%s", scb.serviceName, backendAddr)
	cb := scb.manager.GetCircuitBreaker(cbName)
	return cb.State()
}
