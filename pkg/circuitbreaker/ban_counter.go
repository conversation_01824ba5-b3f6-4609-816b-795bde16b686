package circuitbreaker

import (
	"sync"
	"time"
)

// BanReason represents the reason for banning a backend
type BanReason int

const (
	BanReasonErrorRate BanReason = iota
	BanReasonLatency
	BanReasonCustom
	BanReasonManual
)

func (r BanReason) String() string {
	switch r {
	case BanReasonErrorRate:
		return "error_rate"
	case BanReasonLatency:
		return "latency"
	case BanReasonCustom:
		return "custom"
	case BanReasonManual:
		return "manual"
	default:
		return "unknown"
	}
}

// BanCounter tracks ban-related metrics for a backend
type BanCounter struct {
	// Basic counters
	totalRequests    int64
	errorRequests    int64
	timeoutRequests  int64
	
	// Latency tracking
	totalLatency     time.Duration
	maxLatency       time.Duration
	latencyHistory   []time.Duration
	
	// Error code tracking
	errorCodes       map[int]int64
	
	// Ban state
	isBanned         bool
	banReason        BanReason
	banStartTime     time.Time
	banDuration      time.Duration
	banCount         int64
	
	// Adaptive parameters
	baselineLatency  time.Duration
	errorRateWindow  []bool
	windowSize       int
	windowIndex      int
	
	// Configuration
	config           *BanConfig
	
	// Synchronization
	mu               sync.RWMutex
	
	// Timestamps
	lastRequestTime  time.Time
	lastErrorTime    time.Time
	createdAt        time.Time
}

// BanConfig represents configuration for ban counter
type BanConfig struct {
	// Error rate based banning
	ErrorRateThreshold    float64       `json:"error_rate_threshold"`    // 0.5 = 50%
	ErrorRateWindowSize   int           `json:"error_rate_window_size"`  // 100 requests
	MinRequestsForBan     int64         `json:"min_requests_for_ban"`    // 20 requests
	
	// Latency based banning
	LatencyThreshold      time.Duration `json:"latency_threshold"`       // 5s
	LatencyMultiplier     float64       `json:"latency_multiplier"`      // 3.0x baseline
	LatencyWindowSize     int           `json:"latency_window_size"`     // 50 requests
	
	// 5XX error specific
	FiveXXThreshold       float64       `json:"five_xx_threshold"`       // 0.3 = 30%
	FiveXXWindowSize      int           `json:"five_xx_window_size"`     // 50 requests
	
	// Ban duration and recovery
	InitialBanDuration    time.Duration `json:"initial_ban_duration"`    // 30s
	MaxBanDuration        time.Duration `json:"max_ban_duration"`        // 300s
	BanMultiplier         float64       `json:"ban_multiplier"`          // 2.0x
	
	// Recovery parameters
	RecoverySuccessCount  int           `json:"recovery_success_count"`  // 5 successful requests
	RecoveryTestInterval  time.Duration `json:"recovery_test_interval"`  // 10s
	RecoveryTestRatio     float64       `json:"recovery_test_ratio"`     // 0.1 = 10% traffic
	
	// Custom rules
	CustomRules           []CustomBanRule `json:"custom_rules"`
}

// CustomBanRule represents a custom ban rule
type CustomBanRule struct {
	Name        string        `json:"name"`
	Condition   string        `json:"condition"`   // "status_code == 503"
	Threshold   float64       `json:"threshold"`   // 0.2 = 20%
	WindowSize  int           `json:"window_size"` // 30 requests
	BanDuration time.Duration `json:"ban_duration"`
	Enabled     bool          `json:"enabled"`
}

// NewBanCounter creates a new ban counter
func NewBanCounter(config *BanConfig) *BanCounter {
	if config == nil {
		config = DefaultBanConfig()
	}
	
	return &BanCounter{
		errorCodes:      make(map[int]int64),
		errorRateWindow: make([]bool, config.ErrorRateWindowSize),
		latencyHistory:  make([]time.Duration, 0, config.LatencyWindowSize),
		windowSize:      config.ErrorRateWindowSize,
		config:          config,
		createdAt:       time.Now(),
	}
}

// DefaultBanConfig returns default ban configuration
func DefaultBanConfig() *BanConfig {
	return &BanConfig{
		ErrorRateThreshold:    0.5,
		ErrorRateWindowSize:   100,
		MinRequestsForBan:     20,
		LatencyThreshold:      5 * time.Second,
		LatencyMultiplier:     3.0,
		LatencyWindowSize:     50,
		FiveXXThreshold:       0.3,
		FiveXXWindowSize:      50,
		InitialBanDuration:    30 * time.Second,
		MaxBanDuration:        300 * time.Second,
		BanMultiplier:         2.0,
		RecoverySuccessCount:  5,
		RecoveryTestInterval:  10 * time.Second,
		RecoveryTestRatio:     0.1,
	}
}

// RecordRequest records a request and its result
func (bc *BanCounter) RecordRequest(statusCode int, latency time.Duration, isError bool) {
	bc.mu.Lock()
	defer bc.mu.Unlock()
	
	now := time.Now()
	bc.lastRequestTime = now
	bc.totalRequests++
	
	// Record error
	if isError {
		bc.errorRequests++
		bc.lastErrorTime = now
		bc.errorCodes[statusCode]++
	}
	
	// Record latency
	bc.totalLatency += latency
	if latency > bc.maxLatency {
		bc.maxLatency = latency
	}
	
	// Update latency history
	if len(bc.latencyHistory) >= bc.config.LatencyWindowSize {
		bc.latencyHistory = bc.latencyHistory[1:]
	}
	bc.latencyHistory = append(bc.latencyHistory, latency)
	
	// Update error rate window
	bc.errorRateWindow[bc.windowIndex] = isError
	bc.windowIndex = (bc.windowIndex + 1) % bc.windowSize
	
	// Update baseline latency (moving average)
	if bc.totalRequests > 0 {
		bc.baselineLatency = bc.totalLatency / time.Duration(bc.totalRequests)
	}
}

// ShouldBan determines if the backend should be banned
func (bc *BanCounter) ShouldBan() (bool, BanReason) {
	bc.mu.RLock()
	defer bc.mu.RUnlock()
	
	// Already banned
	if bc.isBanned {
		return true, bc.banReason
	}
	
	// Not enough requests
	if bc.totalRequests < bc.config.MinRequestsForBan {
		return false, BanReasonErrorRate
	}
	
	// Check error rate
	if bc.shouldBanByErrorRate() {
		return true, BanReasonErrorRate
	}
	
	// Check 5XX error rate
	if bc.shouldBanByFiveXXRate() {
		return true, BanReasonErrorRate
	}
	
	// Check latency
	if bc.shouldBanByLatency() {
		return true, BanReasonLatency
	}
	
	// Check custom rules
	if reason := bc.shouldBanByCustomRules(); reason != BanReasonCustom {
		return true, reason
	}
	
	return false, BanReasonErrorRate
}

// shouldBanByErrorRate checks if should ban by overall error rate
func (bc *BanCounter) shouldBanByErrorRate() bool {
	windowErrors := 0
	windowRequests := 0
	
	for _, isError := range bc.errorRateWindow {
		windowRequests++
		if isError {
			windowErrors++
		}
	}
	
	if windowRequests < int(bc.config.MinRequestsForBan) {
		return false
	}
	
	errorRate := float64(windowErrors) / float64(windowRequests)
	return errorRate >= bc.config.ErrorRateThreshold
}

// shouldBanByFiveXXRate checks if should ban by 5XX error rate
func (bc *BanCounter) shouldBanByFiveXXRate() bool {
	fiveXXCount := int64(0)
	for code, count := range bc.errorCodes {
		if code >= 500 && code < 600 {
			fiveXXCount += count
		}
	}
	
	if bc.totalRequests < bc.config.MinRequestsForBan {
		return false
	}
	
	fiveXXRate := float64(fiveXXCount) / float64(bc.totalRequests)
	return fiveXXRate >= bc.config.FiveXXThreshold
}

// shouldBanByLatency checks if should ban by latency
func (bc *BanCounter) shouldBanByLatency() bool {
	if len(bc.latencyHistory) < bc.config.LatencyWindowSize/2 {
		return false
	}
	
	// Calculate recent average latency
	recentLatency := time.Duration(0)
	recentCount := len(bc.latencyHistory)
	if recentCount > 10 {
		recentCount = 10 // Use last 10 requests
	}
	
	for i := len(bc.latencyHistory) - recentCount; i < len(bc.latencyHistory); i++ {
		recentLatency += bc.latencyHistory[i]
	}
	recentLatency /= time.Duration(recentCount)
	
	// Check absolute threshold
	if recentLatency >= bc.config.LatencyThreshold {
		return true
	}
	
	// Check relative threshold (compared to baseline)
	if bc.baselineLatency > 0 {
		multiplier := float64(recentLatency) / float64(bc.baselineLatency)
		return multiplier >= bc.config.LatencyMultiplier
	}
	
	return false
}

// shouldBanByCustomRules checks custom ban rules
func (bc *BanCounter) shouldBanByCustomRules() BanReason {
	// TODO: Implement custom rule evaluation
	// This would involve parsing and evaluating custom conditions
	return BanReasonCustom
}

// Ban bans the backend for the specified reason
func (bc *BanCounter) Ban(reason BanReason) {
	bc.mu.Lock()
	defer bc.mu.Unlock()
	
	if bc.isBanned {
		return // Already banned
	}
	
	bc.isBanned = true
	bc.banReason = reason
	bc.banStartTime = time.Now()
	bc.banCount++
	
	// Calculate ban duration (exponential backoff)
	duration := bc.config.InitialBanDuration
	if bc.banCount > 1 {
		for i := int64(1); i < bc.banCount; i++ {
			duration = time.Duration(float64(duration) * bc.config.BanMultiplier)
			if duration > bc.config.MaxBanDuration {
				duration = bc.config.MaxBanDuration
				break
			}
		}
	}
	bc.banDuration = duration
}

// Unban unbans the backend
func (bc *BanCounter) Unban() {
	bc.mu.Lock()
	defer bc.mu.Unlock()
	
	bc.isBanned = false
	bc.banReason = BanReasonErrorRate
	bc.banStartTime = time.Time{}
	bc.banDuration = 0
}

// IsBanned returns if the backend is currently banned
func (bc *BanCounter) IsBanned() bool {
	bc.mu.RLock()
	defer bc.mu.RUnlock()
	
	if !bc.isBanned {
		return false
	}
	
	// Check if ban has expired
	if time.Since(bc.banStartTime) >= bc.banDuration {
		return false
	}
	
	return true
}

// ShouldAllowRecoveryTest determines if a recovery test should be allowed
func (bc *BanCounter) ShouldAllowRecoveryTest() bool {
	bc.mu.RLock()
	defer bc.mu.RUnlock()
	
	if !bc.isBanned {
		return true
	}
	
	// Check if enough time has passed for recovery test
	timeSinceBan := time.Since(bc.banStartTime)
	return timeSinceBan >= bc.config.RecoveryTestInterval
}

// Clear clears all counters and resets state
func (bc *BanCounter) Clear() {
	bc.mu.Lock()
	defer bc.mu.Unlock()
	
	bc.totalRequests = 0
	bc.errorRequests = 0
	bc.timeoutRequests = 0
	bc.totalLatency = 0
	bc.maxLatency = 0
	bc.latencyHistory = bc.latencyHistory[:0]
	bc.errorCodes = make(map[int]int64)
	bc.errorRateWindow = make([]bool, bc.config.ErrorRateWindowSize)
	bc.windowIndex = 0
	bc.baselineLatency = 0
	bc.lastRequestTime = time.Time{}
	bc.lastErrorTime = time.Time{}
	
	// Reset ban state
	bc.isBanned = false
	bc.banReason = BanReasonErrorRate
	bc.banStartTime = time.Time{}
	bc.banDuration = 0
	bc.banCount = 0
}

// GetStats returns current statistics
func (bc *BanCounter) GetStats() BanStats {
	bc.mu.RLock()
	defer bc.mu.RUnlock()
	
	errorRate := float64(0)
	if bc.totalRequests > 0 {
		errorRate = float64(bc.errorRequests) / float64(bc.totalRequests)
	}
	
	avgLatency := time.Duration(0)
	if bc.totalRequests > 0 {
		avgLatency = bc.totalLatency / time.Duration(bc.totalRequests)
	}
	
	return BanStats{
		TotalRequests:   bc.totalRequests,
		ErrorRequests:   bc.errorRequests,
		ErrorRate:       errorRate,
		AvgLatency:      avgLatency,
		MaxLatency:      bc.maxLatency,
		IsBanned:        bc.isBanned,
		BanReason:       bc.banReason.String(),
		BanCount:        bc.banCount,
		BanTimeLeft:     bc.getBanTimeLeft(),
		ErrorCodes:      bc.copyErrorCodes(),
	}
}

// getBanTimeLeft returns remaining ban time
func (bc *BanCounter) getBanTimeLeft() time.Duration {
	if !bc.isBanned {
		return 0
	}
	
	elapsed := time.Since(bc.banStartTime)
	if elapsed >= bc.banDuration {
		return 0
	}
	
	return bc.banDuration - elapsed
}

// copyErrorCodes returns a copy of error codes map
func (bc *BanCounter) copyErrorCodes() map[int]int64 {
	result := make(map[int]int64)
	for code, count := range bc.errorCodes {
		result[code] = count
	}
	return result
}

// BanStats represents ban counter statistics
type BanStats struct {
	TotalRequests int64             `json:"total_requests"`
	ErrorRequests int64             `json:"error_requests"`
	ErrorRate     float64           `json:"error_rate"`
	AvgLatency    time.Duration     `json:"avg_latency"`
	MaxLatency    time.Duration     `json:"max_latency"`
	IsBanned      bool              `json:"is_banned"`
	BanReason     string            `json:"ban_reason"`
	BanCount      int64             `json:"ban_count"`
	BanTimeLeft   time.Duration     `json:"ban_time_left"`
	ErrorCodes    map[int]int64     `json:"error_codes"`
}
