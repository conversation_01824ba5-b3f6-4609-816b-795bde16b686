package circuitbreaker

import (
	"context"
	"math/rand"
	"sync"
	"time"

	"go.uber.org/zap"
)

// AdaptiveBanManager manages adaptive banning for backends
type AdaptiveBanManager struct {
	serviceName string
	banCounters map[string]*BanCounter
	config      *BanConfig
	logger      *zap.Logger

	// Recovery testing
	recoveryTests map[string]*RecoveryTest

	// Adaptive parameters
	globalStats *GlobalStats

	// Synchronization
	mu sync.RWMutex

	// Background tasks
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// RecoveryTest tracks recovery testing for a banned backend
type RecoveryTest struct {
	backend      string
	startTime    time.Time
	testCount    int
	successCount int
	failureCount int
	lastTestTime time.Time
	isActive     bool
}

// GlobalStats tracks global statistics for adaptive behavior
type GlobalStats struct {
	totalBackends   int
	healthyBackends int
	bannedBackends  int
	avgErrorRate    float64
	avgLatency      time.Duration
	lastUpdateTime  time.Time
	mu              sync.RWMutex
}

// NewAdaptiveBanManager creates a new adaptive ban manager
func NewAdaptiveBanManager(serviceName string, config *BanConfig, logger *zap.Logger) *AdaptiveBanManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &AdaptiveBanManager{
		serviceName:   serviceName,
		banCounters:   make(map[string]*BanCounter),
		config:        config,
		logger:        logger,
		recoveryTests: make(map[string]*RecoveryTest),
		globalStats:   &GlobalStats{},
		ctx:           ctx,
		cancel:        cancel,
	}
}

// Start starts the adaptive ban manager
func (abm *AdaptiveBanManager) Start() {
	abm.wg.Add(1)
	go abm.backgroundTasks()
}

// Stop stops the adaptive ban manager
func (abm *AdaptiveBanManager) Stop() {
	abm.cancel()
	abm.wg.Wait()
}

// GetOrCreateBanCounter gets or creates a ban counter for a backend
func (abm *AdaptiveBanManager) GetOrCreateBanCounter(backend string) *BanCounter {
	abm.mu.RLock()
	counter, exists := abm.banCounters[backend]
	abm.mu.RUnlock()

	if exists {
		return counter
	}

	abm.mu.Lock()
	defer abm.mu.Unlock()

	// Double-check after acquiring write lock
	if counter, exists := abm.banCounters[backend]; exists {
		return counter
	}

	counter = NewBanCounter(abm.config)
	abm.banCounters[backend] = counter

	abm.logger.Debug("Created ban counter for backend",
		zap.String("service", abm.serviceName),
		zap.String("backend", backend))

	return counter
}

// RecordRequest records a request result for a backend
func (abm *AdaptiveBanManager) RecordRequest(backend string, statusCode int, latency time.Duration) {
	counter := abm.GetOrCreateBanCounter(backend)
	isError := statusCode >= 400

	counter.RecordRequest(statusCode, latency, isError)

	// Check if should ban
	if shouldBan, reason := counter.ShouldBan(); shouldBan {
		abm.banBackend(backend, reason)
	}

	// Update global stats
	abm.updateGlobalStats()
}

// IsBackendBanned checks if a backend is banned
func (abm *AdaptiveBanManager) IsBackendBanned(backend string) bool {
	abm.mu.RLock()
	counter, exists := abm.banCounters[backend]
	abm.mu.RUnlock()

	if !exists {
		return false
	}

	return counter.IsBanned()
}

// ShouldAllowRequest determines if a request should be allowed to a backend
func (abm *AdaptiveBanManager) ShouldAllowRequest(backend string) bool {
	abm.mu.RLock()
	counter, exists := abm.banCounters[backend]
	abm.mu.RUnlock()

	if !exists {
		return true // No counter means no restrictions
	}

	// If not banned, allow
	if !counter.IsBanned() {
		return true
	}

	// If banned, check if recovery test should be allowed
	return abm.shouldAllowRecoveryTest(backend)
}

// shouldAllowRecoveryTest determines if a recovery test should be allowed
func (abm *AdaptiveBanManager) shouldAllowRecoveryTest(backend string) bool {
	abm.mu.RLock()
	counter := abm.banCounters[backend]
	test, hasTest := abm.recoveryTests[backend]
	abm.mu.RUnlock()

	if counter == nil {
		return true
	}

	// Check if counter allows recovery test
	if !counter.ShouldAllowRecoveryTest() {
		return false
	}

	// If no active recovery test, start one
	if !hasTest || !test.isActive {
		abm.startRecoveryTest(backend)
		return true
	}

	// Use recovery test ratio to determine if request should be allowed
	return rand.Float64() < abm.config.RecoveryTestRatio
}

// startRecoveryTest starts a recovery test for a backend
func (abm *AdaptiveBanManager) startRecoveryTest(backend string) {
	abm.mu.Lock()
	defer abm.mu.Unlock()

	abm.recoveryTests[backend] = &RecoveryTest{
		backend:      backend,
		startTime:    time.Now(),
		lastTestTime: time.Now(),
		isActive:     true,
	}

	abm.logger.Info("Started recovery test for backend",
		zap.String("service", abm.serviceName),
		zap.String("backend", backend))
}

// RecordRecoveryResult records the result of a recovery test request
func (abm *AdaptiveBanManager) RecordRecoveryResult(backend string, success bool) {
	abm.mu.Lock()
	test, exists := abm.recoveryTests[backend]
	if !exists {
		abm.mu.Unlock()
		return
	}

	test.testCount++
	test.lastTestTime = time.Now()

	if success {
		test.successCount++
	} else {
		test.failureCount++
	}
	abm.mu.Unlock()

	// Check if recovery test is complete
	if test.successCount >= abm.config.RecoverySuccessCount {
		abm.completeRecoveryTest(backend, true)
	} else if test.failureCount >= abm.config.RecoverySuccessCount {
		abm.completeRecoveryTest(backend, false)
	}
}

// completeRecoveryTest completes a recovery test
func (abm *AdaptiveBanManager) completeRecoveryTest(backend string, success bool) {
	abm.mu.Lock()
	test, exists := abm.recoveryTests[backend]
	if !exists {
		abm.mu.Unlock()
		return
	}

	test.isActive = false
	abm.mu.Unlock()

	if success {
		// Unban the backend
		abm.unbanBackend(backend)
		abm.logger.Info("Recovery test successful, unbanning backend",
			zap.String("service", abm.serviceName),
			zap.String("backend", backend),
			zap.Int("success_count", test.successCount))
	} else {
		// Keep banned, maybe increase ban duration
		abm.logger.Info("Recovery test failed, keeping backend banned",
			zap.String("service", abm.serviceName),
			zap.String("backend", backend),
			zap.Int("failure_count", test.failureCount))
	}
}

// banBackend bans a backend
func (abm *AdaptiveBanManager) banBackend(backend string, reason BanReason) {
	abm.mu.RLock()
	counter := abm.banCounters[backend]
	abm.mu.RUnlock()

	if counter == nil {
		return
	}

	counter.Ban(reason)

	abm.logger.Warn("Backend banned",
		zap.String("service", abm.serviceName),
		zap.String("backend", backend),
		zap.String("reason", reason.String()))
}

// unbanBackend unbans a backend
func (abm *AdaptiveBanManager) unbanBackend(backend string) {
	abm.mu.RLock()
	counter := abm.banCounters[backend]
	abm.mu.RUnlock()

	if counter == nil {
		return
	}

	counter.Unban()

	// Clear recovery test
	abm.mu.Lock()
	delete(abm.recoveryTests, backend)
	abm.mu.Unlock()

	abm.logger.Info("Backend unbanned",
		zap.String("service", abm.serviceName),
		zap.String("backend", backend))
}

// ClearBackendCounters clears counters for a specific backend
func (abm *AdaptiveBanManager) ClearBackendCounters(backend string) {
	abm.mu.RLock()
	counter := abm.banCounters[backend]
	abm.mu.RUnlock()

	if counter != nil {
		counter.Clear()
		abm.logger.Info("Cleared counters for backend",
			zap.String("service", abm.serviceName),
			zap.String("backend", backend))
	}
}

// ClearAllCounters clears all counters
func (abm *AdaptiveBanManager) ClearAllCounters() {
	abm.mu.Lock()
	defer abm.mu.Unlock()

	for _, counter := range abm.banCounters {
		counter.Clear()
	}

	// Clear recovery tests
	abm.recoveryTests = make(map[string]*RecoveryTest)

	abm.logger.Info("Cleared all counters",
		zap.String("service", abm.serviceName))
}

// GetBackendStats returns statistics for a specific backend
func (abm *AdaptiveBanManager) GetBackendStats(backend string) *BanStats {
	abm.mu.RLock()
	counter := abm.banCounters[backend]
	abm.mu.RUnlock()

	if counter == nil {
		return nil
	}

	stats := counter.GetStats()
	return &stats
}

// GetAllStats returns statistics for all backends
func (abm *AdaptiveBanManager) GetAllStats() map[string]*BanStats {
	abm.mu.RLock()
	defer abm.mu.RUnlock()

	result := make(map[string]*BanStats)
	for backend, counter := range abm.banCounters {
		stats := counter.GetStats()
		result[backend] = &stats
	}

	return result
}

// updateGlobalStats updates global statistics
func (abm *AdaptiveBanManager) updateGlobalStats() {
	abm.globalStats.mu.Lock()
	defer abm.globalStats.mu.Unlock()

	abm.mu.RLock()
	totalBackends := len(abm.banCounters)
	bannedBackends := 0
	totalErrorRate := float64(0)
	totalLatency := time.Duration(0)

	for _, counter := range abm.banCounters {
		stats := counter.GetStats()
		if stats.IsBanned {
			bannedBackends++
		}
		totalErrorRate += stats.ErrorRate
		totalLatency += stats.AvgLatency
	}
	abm.mu.RUnlock()

	abm.globalStats.totalBackends = totalBackends
	abm.globalStats.bannedBackends = bannedBackends
	abm.globalStats.healthyBackends = totalBackends - bannedBackends

	if totalBackends > 0 {
		abm.globalStats.avgErrorRate = totalErrorRate / float64(totalBackends)
		abm.globalStats.avgLatency = totalLatency / time.Duration(totalBackends)
	}

	abm.globalStats.lastUpdateTime = time.Now()
}

// backgroundTasks runs background maintenance tasks
func (abm *AdaptiveBanManager) backgroundTasks() {
	defer abm.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-abm.ctx.Done():
			return
		case <-ticker.C:
			abm.performMaintenance()
		}
	}
}

// performMaintenance performs periodic maintenance tasks
func (abm *AdaptiveBanManager) performMaintenance() {
	// Update global stats
	abm.updateGlobalStats()

	// Check for expired bans
	abm.checkExpiredBans()

	// Clean up old recovery tests
	abm.cleanupRecoveryTests()

	// Adaptive configuration adjustment
	abm.adaptiveConfigAdjustment()
}

// checkExpiredBans checks for expired bans and unbans backends
func (abm *AdaptiveBanManager) checkExpiredBans() {
	abm.mu.RLock()
	expiredBackends := make([]string, 0)

	for backendAddr, counter := range abm.banCounters {
		if counter.isBanned && !counter.IsBanned() {
			expiredBackends = append(expiredBackends, backendAddr)
		}
	}
	abm.mu.RUnlock()

	// Unban expired backends
	for _, backendAddr := range expiredBackends {
		abm.unbanBackend(backendAddr)
	}
}

// cleanupRecoveryTests cleans up old recovery tests
func (abm *AdaptiveBanManager) cleanupRecoveryTests() {
	abm.mu.Lock()
	defer abm.mu.Unlock()

	now := time.Now()
	for backendAddr, test := range abm.recoveryTests {
		// Remove tests older than 5 minutes
		if !test.isActive && now.Sub(test.lastTestTime) > 5*time.Minute {
			delete(abm.recoveryTests, backendAddr)
		}

	}
}

// adaptiveConfigAdjustment adjusts configuration based on global stats
func (abm *AdaptiveBanManager) adaptiveConfigAdjustment() {
	abm.globalStats.mu.RLock()
	stats := *abm.globalStats
	abm.globalStats.mu.RUnlock()

	// If too many backends are banned, relax the thresholds
	if stats.totalBackends > 0 {
		banRatio := float64(stats.bannedBackends) / float64(stats.totalBackends)

		if banRatio > 0.5 { // More than 50% banned
			abm.logger.Warn("High ban ratio detected, consider relaxing thresholds",
				zap.String("service", abm.serviceName),
				zap.Float64("ban_ratio", banRatio),
				zap.Int("banned_backends", stats.bannedBackends),
				zap.Int("total_backends", stats.totalBackends))
		}
	}
}
