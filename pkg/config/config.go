package config

import (
	"time"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server      ServerConfig      `mapstructure:"server"`
	LoadBalance LoadBalanceConfig `mapstructure:"loadbalance"`
	Circuit     CircuitConfig     `mapstructure:"circuit"`
	Gossip      GossipConfig      `mapstructure:"gossip"`
	Redis       RedisConfig       `mapstructure:"redis"`
	Metrics     MetricsConfig     `mapstructure:"metrics"`
	Logging     LoggingConfig     `mapstructure:"logging"`
	Fault       FaultConfig       `mapstructure:"fault"`
}

// ServerConfig represents server configuration
type ServerConfig struct {
	Host               string        `mapstructure:"host"`
	Port               int           `mapstructure:"port"`
	ReadTimeout        time.Duration `mapstructure:"read_timeout"`
	WriteTimeout       time.Duration `mapstructure:"write_timeout"`
	IdleTimeout        time.Duration `mapstructure:"idle_timeout"`
	MaxRequestBodySize int           `mapstructure:"max_request_body_size"`
	Concurrency        int           `mapstructure:"concurrency"`
}

// LoadBalanceConfig represents load balancing configuration
type LoadBalanceConfig struct {
	Strategy            string        `mapstructure:"strategy"`
	HealthCheckPath     string        `mapstructure:"health_check_path"`
	HealthCheckInterval time.Duration `mapstructure:"health_check_interval"`
	MaxRetries          int           `mapstructure:"max_retries"`
	RetryTimeout        time.Duration `mapstructure:"retry_timeout"`
}

// CircuitConfig represents circuit breaker configuration
type CircuitConfig struct {
	MaxRequests      uint32        `mapstructure:"max_requests"`
	Interval         time.Duration `mapstructure:"interval"`
	Timeout          time.Duration `mapstructure:"timeout"`
	FailureThreshold uint32        `mapstructure:"failure_threshold"`
	SuccessThreshold uint32        `mapstructure:"success_threshold"`
}

// GossipConfig represents gossip protocol configuration
type GossipConfig struct {
	Enable       bool          `mapstructure:"enable"`
	BindAddr     string        `mapstructure:"bind_addr"`
	BindPort     int           `mapstructure:"bind_port"`
	Seeds        []string      `mapstructure:"seeds"`
	SyncInterval time.Duration `mapstructure:"sync_interval"`
}

// RedisConfig represents Redis configuration
type RedisConfig struct {
	Addr     string `mapstructure:"addr"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

// MetricsConfig represents metrics configuration
type MetricsConfig struct {
	Enable bool   `mapstructure:"enable"`
	Path   string `mapstructure:"path"`
	Port   int    `mapstructure:"port"`
}

// LoggingConfig represents logging configuration
type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// FaultConfig represents fault injection configuration
type FaultConfig struct {
	Enable bool `mapstructure:"enable"`
}

// ServiceConfig represents service configuration
type ServiceConfig struct {
	Name       string            `json:"name"`
	Endpoints  []Endpoint        `json:"endpoints"`
	Strategy   string            `json:"strategy"` // round_robin, random, consistent_hash, least_conn
	Timeout    time.Duration     `json:"timeout"`
	MaxRetries int               `json:"max_retries"`
	Metadata   map[string]string `json:"metadata,omitempty"`
}

// Endpoint represents a service endpoint
type Endpoint struct {
	ID       string            `json:"id"`
	Host     string            `json:"host"`
	Port     int               `json:"port"`
	Weight   int               `json:"weight"`
	Healthy  bool              `json:"healthy"`
	Metadata map[string]string `json:"metadata,omitempty"`
}

// LoadConfig loads configuration from file
func LoadConfig(configFile string) (*Config, error) {
	viper.SetConfigFile(configFile)
	viper.SetConfigType("yaml")

	// Set default values
	setDefaults()

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

func setDefaults() {
	// Server defaults
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "120s")
	viper.SetDefault("server.max_request_body_size", 4*1024*1024)
	viper.SetDefault("server.concurrency", 256*1024)

	// Load balance defaults
	viper.SetDefault("loadbalance.strategy", "round_robin")
	viper.SetDefault("loadbalance.health_check_path", "/health")
	viper.SetDefault("loadbalance.health_check_interval", "30s")
	viper.SetDefault("loadbalance.max_retries", 3)
	viper.SetDefault("loadbalance.retry_timeout", "5s")

	// Circuit breaker defaults
	viper.SetDefault("circuit.max_requests", 100)
	viper.SetDefault("circuit.interval", "60s")
	viper.SetDefault("circuit.timeout", "30s")
	viper.SetDefault("circuit.failure_threshold", 5)
	viper.SetDefault("circuit.success_threshold", 3)

	// Gossip defaults
	viper.SetDefault("gossip.enable", true)
	viper.SetDefault("gossip.bind_addr", "0.0.0.0")
	viper.SetDefault("gossip.bind_port", 7946)
	viper.SetDefault("gossip.sync_interval", "30s")

	// Metrics defaults
	viper.SetDefault("metrics.enable", true)
	viper.SetDefault("metrics.path", "/metrics")
	viper.SetDefault("metrics.port", 9090)

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")

	// Fault injection defaults
	viper.SetDefault("fault.enable", false)
}
