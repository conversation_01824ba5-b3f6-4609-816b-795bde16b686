# 🚀 Go 微服务网关实战项目

> **面向求职者的硬核工程项目** - 从零构建生产级微服务网关，掌握大厂必备技术栈

这是一个**完全开源的教学项目**，专为提升程序员就业竞争力而设计。通过实现一个高性能的微服务网关，你将掌握：

- 🏗️ **系统架构设计** - 微服务网关的完整架构
- ⚡ **高性能编程** - Go 语言高并发最佳实践
- 🔧 **工程化能力** - 配置管理、监控、部署等
- 📊 **可观测性** - 指标收集、链路追踪、日志
- 🛡️ **稳定性保障** - 熔断、限流、重试等
- 🔄 **运维能力** - 热重启、灰度发布等

## 🎯 为什么选择这个项目？

### 💼 求职竞争力提升
- ✅ **简历亮点**：生产级微服务网关项目经验
- ✅ **面试加分**：深度技术理解 + 实战经验
- ✅ **技能证明**：高并发、分布式系统设计能力
- ✅ **开源贡献**：GitHub 项目展示技术实力

### 🚀 核心技术能力
- **🏗️ 系统架构**：微服务网关完整架构设计
- **⚡ 高性能编程**：Go 语言 + fasthttp，支持 10万+ QPS
- **🔄 负载均衡**：4种算法实现（轮询、随机、一致性哈希、最少连接）
- **🛡️ 稳定性保障**：熔断器、限流、重试机制
- **📊 可观测性**：Prometheus 监控 + 分布式链路追踪
- **🔧 工程化**：配置热更新、热重启、零停机部署

### 🎓 学习价值
- **📚 完整教程**：从零到一的详细实现指南
- **💡 最佳实践**：生产级代码质量和架构设计
- **🔍 深度解析**：不仅讲 How，更讲 Why
- **🎯 面试准备**：技术深度 + 项目展示技巧

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client        │    │   ServiceMesh   │    │   Backend       │
│                 │───▶│                 │───▶│   Services      │
│                 │    │   Proxy         │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  Configuration  │
                       │  Management     │
                       │                 │
                       │ ┌─────────────┐ │
                       │ │   Redis     │ │
                       │ └─────────────┘ │
                       │ ┌─────────────┐ │
                       │ │   Gossip    │ │
                       │ └─────────────┘ │
                       └─────────────────┘
```

## 🚀 快速开始

### 📋 环境要求
- **Go 1.21+**：支持泛型和最新特性
- **Redis**：配置存储（可选）
- **Docker**：容器化部署（可选）

### ⚡ 5分钟快速体验

1. **克隆项目**
```bash
git clone https://github.com/chungzy/servicemesh-go.git
cd servicemesh-go
```

2. **安装依赖**
```bash
go mod tidy
```

3. **运行演示**
```bash
# 新手演示
go run examples/beginner_demo.go

# 或者启动完整网关
go run cmd/servicemesh/main.go -config configs/config.yaml
```

4. **验证运行**
```bash
# 健康检查
curl http://localhost:8080/health

# 查看监控指标
curl http://localhost:8080/metrics
```

### 📚 学习资源
- 📖 **[学习路径](docs/LEARNING_PATH.md)** - 完整的技能提升指南
- 🚀 **[快速开始](docs/QUICK_START.md)** - 详细的入门教程
- 🎯 **[面试指南](docs/INTERVIEW_GUIDE.md)** - 面试准备和技巧
- 🎓 **[课程大纲](docs/COURSE_OUTLINE.md)** - 系统化学习计划

### Docker Deployment

1. **Deploy with Docker Compose**
```bash
make deploy
```

This will start:
- ServiceMesh instances (2 nodes for HA)
- Redis for configuration storage
- Prometheus for metrics collection
- Grafana for visualization
- Example backend services

2. **View logs**
```bash
make deploy-logs
```

3. **Stop deployment**
```bash
make deploy-stop
```

## Configuration

### Basic Configuration

The main configuration file is `configs/config.yaml`:

```yaml
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"

loadbalance:
  strategy: "round_robin"
  health_check_interval: "30s"
  max_retries: 3

circuit:
  max_requests: 100
  failure_threshold: 5
  timeout: "30s"

gossip:
  enable: true
  bind_port: 7946
  seeds: ["node1:7946", "node2:7946"]

redis:
  addr: "localhost:6379"
  db: 0
```

### Service Configuration

Services are configured in Redis with the following structure:

```json
{
  "name": "user-service",
  "bns_list": {
    "user-bns": 100
  },
  "strategy": 1,
  "timeout": "5s",
  "max_fail": 3
}
```

### BNS Configuration

Backend Node Service (BNS) configurations define the actual backend instances:

```json
{
  "name": "user-bns",
  "bns_nodes": [
    {
      "host": "*************",
      "port": 8080,
      "weight": 100
    }
  ]
}
```

## Load Balancing Strategies

### 1. Round Robin (Strategy: 1, 2)
Distributes requests evenly across all healthy backends.

### 2. Random (Strategy: 3, 4)
Randomly selects a healthy backend for each request.

### 3. Consistent Hash (Strategy: 7, 9)
Uses consistent hashing to ensure requests from the same client go to the same backend.

### 4. Least Connections
Routes requests to the backend with the fewest active connections.

## Circuit Breaking

The circuit breaker protects services from cascading failures:

- **Closed**: Normal operation, requests pass through
- **Open**: Failures detected, requests are rejected immediately
- **Half-Open**: Testing if the service has recovered

Configuration:
```yaml
circuit:
  max_requests: 100        # Max requests in half-open state
  failure_threshold: 5     # Failures needed to open circuit
  timeout: "30s"          # Time before trying half-open
```

## Fault Injection

For testing service resilience:

### Delay Injection
```go
rule := &fault.FaultRule{
    ID:         "delay-test",
    ServiceName: "user-service",
    Percentage: 10.0,
    FaultType:  fault.FaultTypeDelay,
    DelayMs:    1000,
    Enabled:    true,
}
```

### Abort Injection
```go
rule := &fault.FaultRule{
    ID:         "abort-test",
    ServiceName: "user-service", 
    Percentage: 5.0,
    FaultType:  fault.FaultTypeAbort,
    AbortCode:  500,
    Enabled:    true,
}
```

## Monitoring

### Metrics

ServiceMesh exposes Prometheus metrics at `/metrics`:

- `servicemesh_requests_total`: Total number of requests
- `servicemesh_request_duration_seconds`: Request duration histogram
- `servicemesh_circuit_breaker_state`: Circuit breaker states
- `servicemesh_backend_health`: Backend health status

### Health Checks

Health check endpoint: `GET /health`

Response:
```json
{
  "status": "healthy"
}
```

## API Usage

### Service Routing

Requests are routed based on:

1. **X-Service-Name header**
```bash
curl -H "X-Service-Name: user-service" http://localhost:8080/api/users
```

2. **Host header**
```bash
curl -H "Host: user-service.example.com" http://localhost:8080/api/users
```

3. **URL path**
```bash
curl http://localhost:8080/user-service/api/users
```

### Configuration Management

ServiceMesh automatically loads configuration from Redis and synchronizes via Gossip protocol.

## Development

### Project Structure

```
servicemesh-go/
├── cmd/servicemesh/          # Main application
├── pkg/                      # Public packages
│   ├── proxy/               # HTTP proxy server
│   ├── loadbalancer/        # Load balancing strategies
│   ├── circuitbreaker/      # Circuit breaker implementation
│   ├── config/              # Configuration management
│   ├── gossip/              # Gossip protocol
│   ├── metrics/             # Metrics collection
│   └── fault/               # Fault injection
├── internal/app/            # Application logic
├── configs/                 # Configuration files
├── deployments/             # Deployment configurations
└── Makefile                 # Build automation
```

### Building

```bash
# Build for current platform
make build

# Build for all platforms
make build-all

# Build Docker image
make docker
```

### Testing

```bash
# Run tests
make test

# Run tests with coverage
make coverage

# Run benchmarks
make bench
```

### Code Quality

```bash
# Format code
make fmt

# Lint code
make lint

# Vet code
make vet

# Security scan
make security
```

## Performance

ServiceMesh is designed for high performance:

- **Zero-copy networking** with fasthttp
- **Efficient load balancing** with minimal overhead
- **Lock-free hot paths** where possible
- **Connection pooling** for backend requests
- **Metrics collection** with minimal impact

Typical performance characteristics:
- **Throughput**: 100K+ RPS per instance
- **Latency**: Sub-millisecond proxy overhead
- **Memory**: Low memory footprint with efficient pooling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run `make all` to ensure quality
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Inspired by the original OpenResty/Lua ServiceMesh implementation
- Built with excellent Go libraries: fasthttp, memberlist, prometheus, etc.
- Thanks to the Go community for the amazing ecosystem
