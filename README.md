# ServiceMesh Go

A high-performance service mesh implementation in Go, inspired by the original OpenResty/Lua version. This service mesh provides load balancing, circuit breaking, configuration management, fault injection, and monitoring capabilities.

## Features

### Core Capabilities
- **High-Performance Proxy**: Built with fasthttp for maximum throughput
- **Load Balancing**: Multiple strategies (Round Robin, Random, Consistent Hash, Least Connections)
- **Circuit Breaking**: Automatic failure detection and recovery
- **Configuration Management**: Dynamic configuration updates via Redis and Gossip protocol
- **Fault Injection**: Testing resilience with delay, abort, and rewrite faults
- **Monitoring**: Prometheus metrics and health checks
- **Service Discovery**: Integration with BNS (Baidu Naming Service) style configuration

### Advanced Features
- **Gossip Protocol**: Distributed configuration synchronization
- **Health Checking**: Automatic backend health monitoring
- **Metrics Collection**: Comprehensive observability
- **Hot Configuration Reload**: Zero-downtime configuration updates
- **Multi-tenancy**: Service isolation and routing

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client        │    │   ServiceMesh   │    │   Backend       │
│                 │───▶│                 │───▶│   Services      │
│                 │    │   Proxy         │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  Configuration  │
                       │  Management     │
                       │                 │
                       │ ┌─────────────┐ │
                       │ │   Redis     │ │
                       │ └─────────────┘ │
                       │ ┌─────────────┐ │
                       │ │   Gossip    │ │
                       │ └─────────────┘ │
                       └─────────────────┘
```

## Quick Start

### Prerequisites
- Go 1.21+
- Redis (for configuration storage)
- Docker & Docker Compose (for containerized deployment)

### Local Development

1. **Clone the repository**
```bash
git clone https://github.com/chungzy/servicemesh-go.git
cd servicemesh-go
```

2. **Install dependencies**
```bash
make deps
```

3. **Run tests**
```bash
make test
```

4. **Build the application**
```bash
make build
```

5. **Run locally**
```bash
make run
```

### Docker Deployment

1. **Deploy with Docker Compose**
```bash
make deploy
```

This will start:
- ServiceMesh instances (2 nodes for HA)
- Redis for configuration storage
- Prometheus for metrics collection
- Grafana for visualization
- Example backend services

2. **View logs**
```bash
make deploy-logs
```

3. **Stop deployment**
```bash
make deploy-stop
```

## Configuration

### Basic Configuration

The main configuration file is `configs/config.yaml`:

```yaml
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"

loadbalance:
  strategy: "round_robin"
  health_check_interval: "30s"
  max_retries: 3

circuit:
  max_requests: 100
  failure_threshold: 5
  timeout: "30s"

gossip:
  enable: true
  bind_port: 7946
  seeds: ["node1:7946", "node2:7946"]

redis:
  addr: "localhost:6379"
  db: 0
```

### Service Configuration

Services are configured in Redis with the following structure:

```json
{
  "name": "user-service",
  "bns_list": {
    "user-bns": 100
  },
  "strategy": 1,
  "timeout": "5s",
  "max_fail": 3
}
```

### BNS Configuration

Backend Node Service (BNS) configurations define the actual backend instances:

```json
{
  "name": "user-bns",
  "bns_nodes": [
    {
      "host": "*************",
      "port": 8080,
      "weight": 100
    }
  ]
}
```

## Load Balancing Strategies

### 1. Round Robin (Strategy: 1, 2)
Distributes requests evenly across all healthy backends.

### 2. Random (Strategy: 3, 4)
Randomly selects a healthy backend for each request.

### 3. Consistent Hash (Strategy: 7, 9)
Uses consistent hashing to ensure requests from the same client go to the same backend.

### 4. Least Connections
Routes requests to the backend with the fewest active connections.

## Circuit Breaking

The circuit breaker protects services from cascading failures:

- **Closed**: Normal operation, requests pass through
- **Open**: Failures detected, requests are rejected immediately
- **Half-Open**: Testing if the service has recovered

Configuration:
```yaml
circuit:
  max_requests: 100        # Max requests in half-open state
  failure_threshold: 5     # Failures needed to open circuit
  timeout: "30s"          # Time before trying half-open
```

## Fault Injection

For testing service resilience:

### Delay Injection
```go
rule := &fault.FaultRule{
    ID:         "delay-test",
    ServiceName: "user-service",
    Percentage: 10.0,
    FaultType:  fault.FaultTypeDelay,
    DelayMs:    1000,
    Enabled:    true,
}
```

### Abort Injection
```go
rule := &fault.FaultRule{
    ID:         "abort-test",
    ServiceName: "user-service", 
    Percentage: 5.0,
    FaultType:  fault.FaultTypeAbort,
    AbortCode:  500,
    Enabled:    true,
}
```

## Monitoring

### Metrics

ServiceMesh exposes Prometheus metrics at `/metrics`:

- `servicemesh_requests_total`: Total number of requests
- `servicemesh_request_duration_seconds`: Request duration histogram
- `servicemesh_circuit_breaker_state`: Circuit breaker states
- `servicemesh_backend_health`: Backend health status

### Health Checks

Health check endpoint: `GET /health`

Response:
```json
{
  "status": "healthy"
}
```

## API Usage

### Service Routing

Requests are routed based on:

1. **X-Service-Name header**
```bash
curl -H "X-Service-Name: user-service" http://localhost:8080/api/users
```

2. **Host header**
```bash
curl -H "Host: user-service.example.com" http://localhost:8080/api/users
```

3. **URL path**
```bash
curl http://localhost:8080/user-service/api/users
```

### Configuration Management

ServiceMesh automatically loads configuration from Redis and synchronizes via Gossip protocol.

## Development

### Project Structure

```
servicemesh-go/
├── cmd/servicemesh/          # Main application
├── pkg/                      # Public packages
│   ├── proxy/               # HTTP proxy server
│   ├── loadbalancer/        # Load balancing strategies
│   ├── circuitbreaker/      # Circuit breaker implementation
│   ├── config/              # Configuration management
│   ├── gossip/              # Gossip protocol
│   ├── metrics/             # Metrics collection
│   └── fault/               # Fault injection
├── internal/app/            # Application logic
├── configs/                 # Configuration files
├── deployments/             # Deployment configurations
└── Makefile                 # Build automation
```

### Building

```bash
# Build for current platform
make build

# Build for all platforms
make build-all

# Build Docker image
make docker
```

### Testing

```bash
# Run tests
make test

# Run tests with coverage
make coverage

# Run benchmarks
make bench
```

### Code Quality

```bash
# Format code
make fmt

# Lint code
make lint

# Vet code
make vet

# Security scan
make security
```

## Performance

ServiceMesh is designed for high performance:

- **Zero-copy networking** with fasthttp
- **Efficient load balancing** with minimal overhead
- **Lock-free hot paths** where possible
- **Connection pooling** for backend requests
- **Metrics collection** with minimal impact

Typical performance characteristics:
- **Throughput**: 100K+ RPS per instance
- **Latency**: Sub-millisecond proxy overhead
- **Memory**: Low memory footprint with efficient pooling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run `make all` to ensure quality
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Inspired by the original OpenResty/Lua ServiceMesh implementation
- Built with excellent Go libraries: fasthttp, memberlist, prometheus, etc.
- Thanks to the Go community for the amazing ecosystem
